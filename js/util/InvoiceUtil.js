/**
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
const INVOICE_CONTENT = ["", "明细", "办公用品", "电脑配件", "耗材"]

class InvoiceUtil {
    static getInvoiceDetailText(invoiceContent) {
        let text = invoiceContent || ''
        try {
            switch (text) {
                case '0':
                    return INVOICE_CONTENT[0]
                    break;
                case '1':
                    return INVOICE_CONTENT[1]
                    break;
                case '2':
                    return INVOICE_CONTENT[2]
                    break;
                case '3':
                    return INVOICE_CONTENT[3]
                    break;
                case '4':
                    return INVOICE_CONTENT[4]
                    break;
                default:
                    return text;
            }
        } catch (e) { }
        return text
    }
}
export default InvoiceUtil;