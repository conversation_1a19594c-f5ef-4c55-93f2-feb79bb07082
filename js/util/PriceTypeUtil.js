/**
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
const PRICE_CONTENT = {
    'SALE': '销售价格',
    'POINTS': '礼品积分兑换',
    'GIFTS': '赠品价格',
    'OTHER': '其他',
    'P1': '零售无折扣',
    'P3': '优惠顾客折扣',
    '23': '星级顾客价格',
    'R1': '纯现金兑换',
    'RH': '纯积分兑换',
    'RG': '积分加现金兑换',
    'F3': '非卖品',
    'C6': '合约价格',
}

class PriceTypeUtil {
    static getPriceTextFromType(type) {
        let text = typeof type === 'string' ? type : '';
        try {
            if (PRICE_CONTENT[type]) {
                text = PRICE_CONTENT[type]
            }
        } catch (e) {
            console.log('PriceTypeUtil，err======', type)
        }
        return text
    }
}
export default PriceTypeUtil;