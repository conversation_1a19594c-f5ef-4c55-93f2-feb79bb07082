/**
 *
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/4/26.
 */
// import {Util} from 'rn-yunxi';
import {
  Util,
  GlobalLoading,
  SensorsData,
  MobilePush,
  DeviceInfo
} from "rn-yunxi";
import {
  DeviceEventEmitter,
  Alert,
  InteractionManager,
  Platform
} from "react-native";

const Buffer = require("buffer").Buffer;
import UserStore from "../store/User";
import AddressStore from "../store/Address";
import ShopCartStore from "../store/ShopCart";
import OrderListStore from "../store/OrderList";
import QuickOrderStore from "../store/QuickOrder";
import MessageStore from "../store/MessageCenter";
import HomePageStore from "../store/HomePage";

const NetUtil = Util.NetUtil;
const StorageUtil = Util.StorageUtil;

const KEY_LOCAL_USER_ADDRESS = "KEY_LOCAL_USER_ADDRESS";
const KEY_USER_LIMITS = "KEY_USER_LIMITS";
const KEY_USER_INFO = "KEY_USER_INFO";
const KEY_USER_ID = "KEY_USER_ID";
const KEY_COMPANY_INFO = "KEY_COMPANY_INFO";
const KEY_USER_TYPE = "KEY_USER_TYPE"; //用户角色
const KEY_ACCOUNT = "KEY_ACCOUNT";
const KEY_LAST_LOGIN_ACCOUNT = "KEY_LAST_LOGIN_ACCOUNT";
const KEY_HOME_PAGE = "KEY_HOME_PAGE";
const KEY_ORG_LIST = "KEY_ORG_LIST"; //分销商下单经销商列表
const KEY_CHOOSE_ORG = "KEY_CHOOSE_ORG";
const KEY_DEALER_ORG_LIST = "KEY_DEALER_ORG_LIST"; //经销商下提货组织列表

const PAGE_SIZE = 10; //统一的pagesize
let API_ERROR_ALERT_STATE = {
  showAuthError: false, //防止多次显示错误框
  showHttpStateError: false,
  showNetStateError: false
};

//统一处理接口数据是否有效
NetUtil.checkApiError = function checkApiError(result, url, option) {
  // Log('checkApiError', result?result.resultCode:'', option);
  if (option && option.handleCheckApiErrorCustom) {
    if (option.handleCheckApiErrorCustom(result, url)) {
      return false;
    }
  }
  if (option && option.uncheckApiError === true) {
    //不验证接口是否有校
    return false;
  }
  if (result && result.resultCode + "" === 0 + "") {
    return false;
  } else {
    // Log('checkApiError', url, result, option);
    if (option && option.showError == false) {
      return false;
    }
    GlobalLoading.hide();
    InteractionManager.runAfterInteractions(() => {
      if (result && result.resultCode + "" === 500 + "") {
        Alert.alert(null, "服务器开小差!", [{ text: "确定" }]);
      } else {
        Alert.alert(
          null,
          result && result.resultMsg ? result.resultMsg : "服务器开小差!",
          [{ text: "确定" }]
        );
      }
    });
    return true;
  }
};
//统一处理http错误码问题
NetUtil.handleHttpStatusError = function handleHttpStatusError(err, option) {
  Log("handleHttpStatusError", err);
  if (option && option.showError == false) {
    return;
  }
  if (!API_ERROR_ALERT_STATE.showHttpStateError) {
    API_ERROR_ALERT_STATE.showHttpStateError = true;
    let reset = setTimeout(() => {
      API_ERROR_ALERT_STATE.showHttpStateError = false;
    }, 10 * 1000);
    GlobalLoading.hide();
    InteractionManager.runAfterInteractions(() => {
      Alert.alert(
        null,
        "服务器出错了!",
        [
          {
            text: "确定",
            onPress: () => {
              clearTimeout(reset);
              API_ERROR_ALERT_STATE.showHttpStateError = false;
            }
          }
        ],
        { cancelable: false }
      );
    });
  }
};

//统一处理401授权问题
NetUtil.handleAuthError = function handleAuthError(err, option) {
  // alert(err.resultMsg);
  if (option && option.showError == false) {
    return;
  }
  if (!API_ERROR_ALERT_STATE.showAuthError) {
    API_ERROR_ALERT_STATE.showAuthError = true;
    let reset = setTimeout(() => {
      API_ERROR_ALERT_STATE.showAuthError = false;
    }, 10 * 1000);
    GlobalLoading.hide();

    InteractionManager.runAfterInteractions(() => {
      Api.logout()
        .then()
        .catch(e => {});
      Alert.alert(
        null,
        "登录过期",
        [
          {
            text: "重新登录",
            onPress: () => {
              clearTimeout(reset);
              DeviceEventEmitter.emit("auth_require");
              API_ERROR_ALERT_STATE.showAuthError = false;
            }
          }
        ],
        { cancelable: false }
      );
    });
  }

  //
};
//统一处理http网络错误
NetUtil.handleNetError = function handleNetError(err, option) {
  Log("handleHttpStatusError", err);
  if (option && option.showError == false) {
    return;
  }
  if (!API_ERROR_ALERT_STATE.showNetStateError) {
    API_ERROR_ALERT_STATE.showNetStateError = true;
    let reset = setTimeout(() => {
      API_ERROR_ALERT_STATE.showNetStateError = false;
    }, 10 * 1000);
    GlobalLoading.hide();
    InteractionManager.runAfterInteractions(() => {
      // setTimeout(() => {
      Alert.alert(
        null,
        "网络出错了!",
        [
          {
            text: "确定",
            onPress: () => {
              clearTimeout(reset);
              API_ERROR_ALERT_STATE.showNetStateError = false;
            }
          }
        ],
        { cancelable: false }
      );
    });
  }
};

async function callApi(
  url,
  params = {},
  option = {
    //其它选择
    headers: {}, //请求头
    method: NetUtil.GET, //请求方式
    showLoading: false, //全局loading
    resultCallBack: null, //请求结束中途处理回调
    handleOption: {
      showError: true, //是否显示错误框
      uncheckApiError: false, //是否跳过checkApiError
      handleCheckApiErrorCustom: null //自定义处理
    }
  }
) {
  if (option.showLoading) {
    GlobalLoading.show();
  }
  let result;
  let validateResult = false;
  if (!option) {
    option = {};
  }
  if (!option.headers) {
    option.headers = {};
  }
  if (!option.handleOption) {
    option.handleOption = { showError: true, uncheckApiError: false };
  }
  let token = await StorageUtil.getString(StorageUtil.KEY_TOKEN, "");
  let headers = {};
  let dealerId = null;
  try {
    let isDealer = UserStore.isDealer();
    if (isDealer) {
      dealerId = UserStore.userId ? UserStore.userId : "";
    } else {
      let selectOrg = await Api.getChooseOrgItem();
      dealerId = selectOrg.id;
    }
  } catch (err) {
    Log(err);
  }
  headers = Object.assign(
    headers,
    {
      reqId: new Date().getTime(),
      os: Platform.OS,
      osVersion: DeviceInfo.getSystemVersion(),
      appVersion: DeviceInfo.getVersion(),
      appBuildNum: DeviceInfo.getBuildNumber(),
      // appId:DeviceInfo.getBundleId(),
      auth: token,
      Accept: "application/json",
      appCode: Config.APP_CODE,
      appId: Config.APP_ID,
      tenantId: Config.TENANT_ID,
      //channelId: Config.CHANNEL_ID, //TODO 因为购物车暂时只能用ID=1，所以写死这个
      channelId: 1,
      channelCode: Config.CHANNEL_CODE,
      terminalType: Config.TERMINAL_TYPE
    },
    { orgId: dealerId ? dealerId : "" },
    option.headers
  );
  option.headers = headers;
  try {
    if (option.method === NetUtil.GET) {
      result = await NetUtil.get(
        url,
        params,
        option.headers,
        option.handleOption
      );
    } else if (option.method === NetUtil.POST) {
      result = await NetUtil.post(
        url,
        params,
        option.headers,
        option.handleOption
      );
    } else if (option.method === NetUtil.POST_JSON) {
      result = await NetUtil.postJson(
        url,
        params,
        option.headers,
        option.handleOption
      );
    } else if (option.method === NetUtil.PUT) {
      result = await NetUtil.put(
        url,
        params,
        option.headers,
        option.handleOption
      );
    } else if (option.method === NetUtil.DELETE) {
      result = await NetUtil.delete(
        url,
        params,
        option.headers,
        option.handleOption
      );
    } else if (option.method === NetUtil.DELETE_JSON) {
      result = await NetUtil.reqeustJsonBody(
        url,
        params,
        option.method,
        option.headers,
        option.handleOption
      );
    } else if (option.method === NetUtil.PUT_JSON) {
      result = await NetUtil.reqeustJsonBody(
        url,
        params,
        option.method,
        option.headers,
        option.handleOption
      );
    }

    validateResult =
      result &&
      result.resultCode === 0 &&
      (url.indexOf(Config.HOST_TRADE) >= 0 ||
        url.indexOf(Config.HOST_SYS) >= 0 ||
        url.indexOf(Config.HOST_VERIFY) >= 0 ||
        url.indexOf(Config.HOST_SEARCH) >= 0 ||
        url.indexOf(Config.HOST_MGMT) >= 0 ||
        url.indexOf(Config.HOST) >= 0 ||
        url.indexOf("mock") >= 0);

    if (option.resultCallBack && result) {
      await option.resultCallBack(result);
    }
    if (option.showLoading) {
      GlobalLoading.hide();
    }
    if (Log.LOG_API_RESULT) {
      Log(
        "call api fun",
        url,
        result,
        "validateResult>>",
        validateResult,
        "handleOption>>",
        option.handleOption
      );
    }
  } catch (err) {
    if (Log.LOG_API_RESULT) {
      Log("call api fun catch error");
    }
    GlobalLoading.hide();
    return Promise.reject(err);
  }
  if (option.showLoading) {
    GlobalLoading.hide();
  }
  if (!result) {
    // Log('=================',url,params,option,result);
    return Promise.reject("api error");
  } else if (!validateResult && !option.handleOption.uncheckApiError) {
    //api 域名检验和基本格式检验
    Log("call api reject", result);
    return Promise.reject(result);
  }
  // Log('call api fun end');
  return result;
}

class Api {
  /**
   * 获取本地保存的token
   */
  static getLocalToken() {
    return StorageUtil.getString(StorageUtil.KEY_TOKEN);
  }

  /**
   * 获取登陆手机号
   * @return {Promise}
   */
  static getLoginUser() {
    return StorageUtil.getString(StorageUtil.KEY_MOBILE, null);
  }

  /**
   * 获取登陆用户
   * @return {Promise}
   */
  static getLoginAccountUser() {
    return StorageUtil.getString(KEY_ACCOUNT, null);
  }

  /**
   * 获取最后登陆用户
   * @return {Promise}
   */
  static getLastLoginAccount() {
    return StorageUtil.getString(KEY_LAST_LOGIN_ACCOUNT, null);
  }

  /**
   * 登出
   */
  static async logout() {
    // await StorageUtil.remove(StorageUtil.KEY_MOBILE);
    // await StorageUtil.remove(KEY_ACCOUNT);
    if (UserStore.userId) {
      MobilePush.unsetAlias(Config.CURRENT_HOST + KEY_USER_ID);
    }
    await StorageUtil.remove(StorageUtil.KEY_TOKEN);
    await StorageUtil.remove(KEY_LOCAL_USER_ADDRESS);
    await StorageUtil.remove(KEY_USER_INFO);
    await StorageUtil.remove(KEY_ORG_LIST);
    await StorageUtil.remove(KEY_CHOOSE_ORG);
    await StorageUtil.remove(KEY_DEALER_ORG_LIST);
    await StorageUtil.remove(KEY_USER_LIMITS);
    await UserStore.clearData();
    await HomePageStore.clearData();
    await QuickOrderStore.clearData();
    await ShopCartStore.clearData();
    await AddressStore.clearData();
    await MessageStore.clearData();
  }

  /**
   * 登陆
   * @param params
   * @returns {Promise}null
   */
  static userLogin(
    params = { userCode: "", verifyCode: "" },
    selectTab,
    encrypt
  ) {
    let userType = "";
    if (params.userPassword) {
      let newPassword = new Buffer(params.userPassword)
        .toString("base64")
        .replace("=", "");
      userType = "jxsApp";
      params.userPassword = encrypt
        ? newPassword
        : Util.EncryptUtil.rstr2b64(params.userPassword);
      params.loginType = encrypt ? "namePwd" : "nameMobile";
      params.loginFlag = "1";
    } else {
      userType = "fxs";
      params.loginType = "mobile";
      params.loginFlag = "2";
    }

    params.loginSource = "2";
    params.trench = "app";
    if(params.isAddParams) {
      params.loginFlag = 3
      delete params.isAddParams
    }
    let url =
      Config.HOST_VERIFY + `/api/v1/auth/pearlriver/user/${userType}/auth`;
    return callApi(url, params, {
      method: NetUtil.POST,
      showLoading: true,
      handleOption:
        selectTab == 0
          ? {
              showError: true,
              uncheckApiError: false,
              handleCheckApiErrorCustom: (result, url) => {
                if (
                  result &&
                  (result.resultCode + "" == "10007" ||
                    result.resultCode + "" == "10013" ||
                    result.resultCode + "" == "10011" ||
                    result.resultCode + "" == "10022")
                ) {
                  return true;
                } else {
                  return false;
                }
              }
            }
          : null,
      resultCallBack: async result => {
        if (result && result.data && result.data.auth) {
          QuickOrderStore.clearData();
          ShopCartStore.clearData();
          AddressStore.clearData();
          UserStore.cleanTakeDeliveryOrg();
          UserStore.setToken(result.data.auth);
          await StorageUtil.saveString(StorageUtil.KEY_TOKEN, result.data.auth);
          if (selectTab == 1) {
            await StorageUtil.saveString(
              StorageUtil.KEY_MOBILE,
              params.userCode
            );
          } else {
            await StorageUtil.saveString(KEY_ACCOUNT, params.userCode);
          }
          await StorageUtil.saveString(KEY_LAST_LOGIN_ACCOUNT, params.userCode);
          try {
            await UserStore.getUserInfo();
            await UserStore.getCompanyInfo();
            await UserStore.getUserId();
            await UserStore.getUserLimits();
          } catch (e) {}
        }
      }
    });
  }

  /**
   * 分销商登录获取经销商列表
   *
   */
  static loadOrgList() {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/distributor/dealer/list";
    return callApi(
      url,
      {},
      {
        method: NetUtil.GET,
        showLoading: false,
        resultCallBack: async result => {
          if (result && result.data) {
            await StorageUtil.saveJsonObject(KEY_ORG_LIST, result.data);
          }
        }
      }
    );
  }

  /**
   * 经销商登录获取用户经销商列表
   * @param params
   * @return {promise}
   */
  static loadDealerList() {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/dealer/dealer/list";
    return callApi(url, {}, { method: NetUtil.GET, showLoading: false });
  }
    /**
   * 失焦获取经销商绑定手机
   * @param params
   * @return {promise}
   */
  static getCustomerInfo(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/member/member/account";
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

    /**
   * 判断是否需要发送验证码
   * @param params
   * @return {promise}
   */
  static getMemberValidateFrequency(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/member/member/getMemberValidateFrequency";
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }


    /**
   * 获取手机验证码
   * @param params
   * @return {promise}
   */
    static sendMsg(params) {
      let url = Config.HOST_VERIFY + "/api/v1/auth/pearlriver/verify/sms/send/returnKey";
      return callApi(url, params, { method: NetUtil.POST, showLoading: false });
    }

  /**
   * 经销商获取全部提货组织列表
   */
  static loadDealerOrgList() {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/dealer/org/list";
    return callApi(
      url,
      {},
      {
        method: NetUtil.GET,
        showLoading: false,
        resultCallBack: async result => {
          if (result && result.data) {
            await StorageUtil.saveJsonObject(KEY_DEALER_ORG_LIST, result.data);
          }
        }
      }
    );
  }

  /**
   * 我的预存款，营销费用－APP端
   */
  static getTakeDeliveryInfo(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/comm/account/appaccount/get";
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 获取本地分销商登录获取经销商列表
   *
   */
  static getLocalOrgList() {
    return StorageUtil.getJsonObject(KEY_ORG_LIST);
  }

  /**
   *
   *保存选择的经销商
   */
  static async saveChooseOrgItem(item) {
    // Log('保存的经销商===',item)
    await StorageUtil.saveJsonObject(KEY_CHOOSE_ORG, item);
  }

  /**
   * 获取本地选择的经销商
   */
  static getChooseOrgItem() {
    return StorageUtil.getJsonObject(KEY_CHOOSE_ORG);
  }

  /**
   *  获取用户信息
   */
  static getUserInfo() {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/member/account/detail/get";
    return callApi(
      url,
      {},
      {
        method: NetUtil.GET,
        showLoading: false,
        resultCallBack: async result => {
          if (result && result.data) {
            await StorageUtil.saveJsonObject(KEY_USER_INFO, result.data);
            UserStore.setUserInfo(result.data);
          }
        }
      }
    );
  }

  /**
   * 获取用户Id
   * @returns {Promise<*>}
   */
  static getUserId() {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/user/getUserId";
    return callApi(
      url,
      {},
      {
        method: NetUtil.GET,
        showLoading: false,
        resultCallBack: async result => {
          if (result && result.data) {
            await StorageUtil.saveString(KEY_USER_ID, result.data);
            await UserStore.setUserId(result.data);
            await MobilePush.setAlias(Config.CURRENT_HOST + result.data);
          }
        }
      }
    );
  }

  /**
   * 获取本地获取用户Id
   */
  static getLocalUserId() {
    return StorageUtil.getString(KEY_USER_ID, null);
  }

  /***
   * 更换经销商后获取用户信息
   * @param params
   * @return {promise}
   */
  static getNewUserInfo(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/member/account/detail";
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

  /**
   *  获取登陆公司信息
   *  @param params
   *  @return {promise}
   */
  static getCompanyInfo(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/member/member/account";
    return callApi(url, params, {
      method: NetUtil.GET,
      showLoading: false,
      resultCallBack: async result => {
        if (result && result.data) {
          await StorageUtil.saveJsonObject(KEY_COMPANY_INFO, result.data);
          UserStore.setCompanyInfo(result.data);
        }
      }
    });
  }

  /**
   * 激活经商
   * @param mobile
   * @return {Promise}
   */
  static activeAccount(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/member/active";
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/user/active';
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true,
      handleOption: {
        showError: true,
        uncheckApiError: false,
        handleCheckApiErrorCustom: (result, url) => {
          if (result && result.resultCode + "" == "1005") {
            return true;
          } else {
            return false;
          }
        }
      }
    });
  }

  /**
   * 忘记密码
   * @param mobile
   * @return {Promise}
   */
  static resetPassword(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/member/findPwd";
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/user/active';
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   *  获取本地用户信息
   */
  static getLocalUserInfo() {
    return StorageUtil.getJsonObject(KEY_USER_INFO, null);
  }

  /**
   *  获取本地用户权
   */
  static getLocalUserLimits() {
    return StorageUtil.getJsonObject(KEY_USER_LIMITS, null);
  }

  /**
   *  获取本地公司信息
   */
  static getLocalCompanyInfo() {
    return StorageUtil.getJsonObject(KEY_COMPANY_INFO, null);
  }

  /**
   *  获取本地首页数据
   */
  static getHomePageData() {
    return StorageUtil.getJsonObject(KEY_HOME_PAGE, null);
  }

  /**
   *  保存本地首页数据
   */
  static async setHomePageData(data) {
    await StorageUtil.saveJsonObject(KEY_HOME_PAGE, data);
  }

  /**
   * 手机校验，发送验证码之前调用
   * @param params
   * @returns {Promise}
   */
  static checkPhone(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/member/check/phone";
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 发送登录验证码-发送手机验证码接口  ---->  两个接口不同分开写
   * @param params
   * @returns {Promise}
   */
  static obtainLoginCheckCode(params) {
    let url = Config.HOST_VERIFY + "/api/v1/auth/pearlriver/verify/sms/send";
    return callApi(url, params, { method: NetUtil.POST, showLoading: true });
  }

  /**
   * 经销商激活/忘记密码发送验证码
   * @param prarms
   * @return {promise}
   */
  static obtainResetPasswordSmsCode(params) {
    let url = Config.HOST_SYS + "/api/v1/pearlriver/sms/send";
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

  /**
   *  校验手机号码是否被注册
   * @param {*} params
   */
  static verifyPhoneIsregister(params, isHandleOption) {
    let url = Config.HOST_TRADE + "/api/v1/yundt/trade/member/mobile/exist";
    let handleOption = null;
    if (isHandleOption) {
      handleOption = {
        showError: false,
        uncheckApiError: true
      };
    }
    return callApi(url, params, {
      method: NetUtil.GET,
      showLoading: true,
      isHandleOption
    });
  }

  /**
   * 会员中心-收货地址列表接口
   * @param params
   * @returns {Promise}
   */
  static obtainAddressList(params, showLoading = false) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/comm/dealer/core/address/list";
    // url = 'http://*************:8004/mocking/pearlriver-dealer-portal/api/v1/pearlriver/comm/dealer/core/address/list';
    // Log(params);
    params.pageSize = PAGE_SIZE;
    return callApi(url, params, {
      method: NetUtil.GET,
      showLoading: showLoading,
      resultCallBack: async result => {
        await StorageUtil.saveJsonObject(
          KEY_LOCAL_USER_ADDRESS,
          result.data.list
        );
      }
    });
  }

  /**
   * 会员中心-交易管理-修改默认收货地址状态接口
   * @param params
   * @returns {Promise}
   */
  static setDefaultAddress(params = { addressId: "", status: 1 }) {
    let url =
      Config.HOST_TRADE + "/api/v1/yundt/trade/member/address/status/update";
    return callApi(url, params, { method: NetUtil.PUT, showLoading: true });
  }

  /**
   * 首页分类数据
   * @param params
   * @returns {Promise}
   */
  static getHomeCategory() {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/item/catalog/app/list";
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/item/catalog/app/list';
    return callApi(url, {}, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 首页获取热门推荐商品
   * @param params
   * @returns {Promise}
   */
  static getHotGoods(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/item/recommend/list";
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/item/recommend/list';
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 首页获取广告数据
   * @returns {Promise}
   */
  static getHomeAd() {
    let params = { channelId: 2 };
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/mall/banner/index/list";
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/item/advertisement/list';
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 保存历史搜索
   * @param params
   * @returns {Promise}
   */
  static saveHistorySearch(key, value) {
    StorageUtil.saveJsonObject(key, value)
      .then()
      .catch();
  }

  static getHistorySearch(key, value) {
    return StorageUtil.getJsonObject(key, value);
  }

  /**
   * 保存银行名和汇款人
   * @param key
   * @param value
   */
  static saveHistoryNameAndBankName(key, value) {
    StorageUtil.saveJsonObject(key, value)
      .then()
      .catch();
  }

  static getHistoryNameAndBankName(key, value) {
    return StorageUtil.getJsonObject(key, value);
  }

  /**
   * 加载商品列表
   * @params params
   * @return {promise}
   *
   */
  static loadGoodsListData(params) {
    if (params) {
      params.pageSize = PAGE_SIZE;
    }
    let url = Config.HOST_SEARCH + "/api/v1/pearlriver/trade/item/list";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: false
    });
  }

  /**
   * 加入或取消常购
   * @params params
   * @return {promise}
   *
   */
  static handleOftenGoods(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/item/often/batch/update";
    // url = ' http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/item/often/batch/update';
    return callApi(url, params, {
      method: NetUtil.PUT_JSON,
      showLoading: true
    });
  }

  /**
   * 商品详情
   * @param params
   * @returns {Promise}
   */
  static getGoodsDetail(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/item/detail/get";
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/item/detail/get';
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 获取提货组织列表
   * @param params
   * @returns {Promise}
   */
  static getPickOrgList(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/item/org/list";
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/item/org/list';
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 获取奖项列表
   * @param params
   * @returns {Promise}
   */
  static getAwardList(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/item/awards/list";
    // let  url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/item/awards/list';
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: false
    });
  }

  /**
   * 获取折价
   * @param params
   * @return {promise}
   */
  static getDiscountPrice(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/item/discount/get";
    // let  url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/item/discount/get';
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: false,
      handleOption: {
        showError: false,
        uncheckApiError: true
      }
    });
  }

  /**
   * 获取产品类型列表
   * @param params
   * @return {promise}
   */
  static loadGoodsPropertyData() {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/item/property/list";
    return callApi(url, {}, { method: NetUtil.GET, showLoading: true });
  }

  /**
   * 获取渠道列表
   * @param params
   * @returns {Promise}
   */
  static getChannelList(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/item/channel/list";
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/item/channel/list';
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 获取订单列表
   * */
  static getOrderList(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/order/list-by-page";
    if (params) {
      params.pageSize = PAGE_SIZE;
    }
    //url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/order/list-by-page';
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 分销商订单列表
   * */
  static getDistributorOrderList(params) {
    let url =
      Config.HOST_TRADE +
      "/api/v1/pearlriver/trade/order/list-distributor-by-page";
    if (params) {
      params.pageSize = PAGE_SIZE;
    }
    //url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/order/list-distributor-by-page';
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 取消订单
   * */
  static cancelOrder(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/order/cancel";
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/order/list-by-page';
    return callApi(url, params, { method: NetUtil.PUT, showLoading: true });
  }

  /**
   * 再次购买
   * */
  static buyAgain(orderId) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/cart/rebuy/" + orderId;
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/order/list-by-page';
    return callApi(url, {}, { method: NetUtil.GET, showLoading: true });
  }

  /**
   * 确认收货
   * */
  static confirmReceive(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/order/receive";
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/order/list-by-page';
    return callApi(url, params, { method: NetUtil.PUT, showLoading: true });
  }

  /**
   * 获取快捷下单列表
   * @param params
   * @return {promise}
   */
  static loadQuickData(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/item/often/list";
    // let url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/item/often/list';
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 移除商品
   * @params params
   * @return {promise}
   */
  static removeGoods(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/item/often/batch/delete";
    return callApi(url, params, {
      method: NetUtil.DELETE_JSON,
      showLoading: true
    });
  }

  /**
   * 常购列表结算
   * @params params
   * @return {promise}
   *
   */
  static confirmOftenOrder(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/order/quick-buy";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   * 获取订单详情
   * */
  static getOrderDetail(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/order/detail";
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/order/detail';
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

  /**
   * 获取提货单列表
   * */
  static getDeliveryDetail(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/delivery/detail";
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/delivery/dealer/detail';
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

  /**
   * 获取物流轨迹
   * */
  static getLogisticsOrbit(params) {
    let url = Config.HOST_MGMT + "/api/v1/pearlriver/comm/logistics/orbit/get";
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/delivery/dealer/detail';
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

  /**
   * 获取消息列表
   * */
  static getMessageList(params) {
    params.pageSize = PAGE_SIZE;
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/message/list";
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/message/list';
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

  /**
   * 标记消息为已读
   * @param params
   * @return {promise}
   */
  static readMessage(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/message/updateRead";
    return callApi(url, params, { method: NetUtil.PUT, showLoading: false });
  }

  static getUnreadMessageCount(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/message/countUnRead";
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 获取发票
   * */
  static getInvoice(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/comm/dealer/core/invoice";
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/comm/dealer/core/invoice';
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

  /**
   * 加入购物车
   * */
  static addShopCart(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/cart/add";
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/cart/add';
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   * 购物车列表
   * */
  static getShopCartList() {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/cart/list";
    // url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/cart/list';
    return callApi(url, {}, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 修改数量
   * @param params  json:{"shoppingCartNo":"购物车编号","itemNum"："商品数量"}
   * @return {Promise}
   */
  static updateShopCartItemNum(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/cart/num/update";
    return callApi(url, params, {
      method: NetUtil.PUT_JSON,
      showLoading: true
    });
  }

  /**
   * 查询、检查是否已有相同商品
   * @param params
   * @return {promise}
   *
   */
  static checkIsSameItem(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/cart/cart-exist";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   * 更新规格
   * @param params  json:{"shoppingCartNo":"购物车编号","orgId": "供货商id", "awardsId": "奖项id","salesChannelId":"销售渠道id","withPlateFlag":"带板标识"}
   * @return {Promise}
   */
  static updateShopCartItem(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/cart/update";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   * 删除购物车
   * @param params
   * @return {Promise}
   */
  static deleteShopCartItems(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/cart/delete";
    return callApi(url, params, {
      method: NetUtil.DELETE_JSON,
      showLoading: true
    });
  }

  /**
   * 立即购买
   * @param params
   * @return {Promise}
   */
  static quickBuy(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/order/quick-buy";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   * 获取转开单详情
   * @param params
   * @return {Promise}
   */
  static getTurnOrder(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/order/transfer/detail";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   * 转开单
   * @param params
   * @return {Promise}
   */
  static turnOrder(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/order/transfer";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   * 获取商品价格
   * @param params json:{itemId:,orgId:}
   * @return {Promise}
   */
  static getPrice(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/item/price/get";
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

  /**
   * 更新规格
   * @param params  json:["shoppingCartNos",]
   * @return {Promise}
   */
  static confirmOrder(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/order/confirm";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   * 获取我的预存款，营销费用，费用赠酒，瓶盖赠酒帐务列表
   * */
  static getAccountTypeList(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/comm/account/list";
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

  /**
   * 费用赠酒-批量查询商品列表
   * */
  static getChargeWineList(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/item/account/batch/list";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   * 提交订单
   * @param params
   * @return {Promise}
   */
  static submitOrder(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/order/submit";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true,
      handleOption: {
        showError: true,
        uncheckApiError: false,
        handleCheckApiErrorCustom: (result, url) => {
          if (result && result.resultCode + "" == "1001") {
            return true;
          } else {
            return false;
          }
        }
      }
    });
  }

  /**
   * 购物车-提交订单
   * @param params
   * @return {Promise}
   */
  static submitShopCartOrder(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/order/submit-from-cart";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   * 获取收银台信息
   * @param orderId
   * @return {Promise}
   */
  static cashier(order) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/pay/cashier/";

    return callApi(url, order, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   * 传统支付
   * @param params
   * @return {Promise}
   */
  static traditionalPay(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/pay/traditional-pay";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   * 检查是否有未缴纳费用
   * @param params
   * @return {Promise}
   */
  static checkNotPayment(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/pay/checkNotPaymentDetail";
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

  /**
   * 支付
   * @param params
   * @returns {Promise}
   */
  static payOrder(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/pay/obtain-voucher";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   * 查询支付状态
   * @param params
   * @returns {Promise}
   */
  static checkPayStatus(params) {
    let url =
      Config.HOST_TRADE + `/api/v1/pearlriver/trade/pay/status/query/${params}`;
    return callApi(
      url,
      {},
      {
        method: NetUtil.GET,
        showLoading: false
      }
    );
  }

  /**
   * 费用单支付
   * @param params
   * @return {promise}
   */

  static costSinglePayment(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/pay/create-payment-order/";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   * 回瓶列表
   * @param params
   * @return {promise}
   */
  static getRecycleBottleListData(params) {
    if (params) {
      params.pageSize = PAGE_SIZE;
    }
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/bottle/apply/list";
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 回瓶验收单详情
   * @param params
   * @return {promise}
   */
  static getBottleDetail(params) {
    let url =
      Config.HOST_TRADE +
      "/api/v1/pearlriver/trade/bottle/apply/getBottleDetail";
    //url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/bottle/apply/getBottleDetail';
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 提货单信息
   * @param params
   * @return {promise}
   */
  static getBillOfLadingInfo(params) {
    let url =
      Config.HOST_TRADE +
      "/api/v1/pearlriver/trade/bottle/apply/delivery/order/detail/get";
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 提货单组织列表
   * @param params
   * @return {promise}
   */
  static getOrgInfo() {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/bottle/apply/supplier/list";
    return callApi(url, {}, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 提货单地址列表
   * @param params
   * @return {promise}
   */
  static getAdressListData() {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/comm/dealer/core/address/list";
    return callApi(url, {}, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 提货单瓶种类
   * @param params
   * @return {promise}
   */
  static getBoxTypeData(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/bottle/apply/box/type/list";
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 散瓶包装规格列表
   * @param params
   * @return {promise}
   */
  static getSpecsOfBottlePackingData(params) {
    let url =
      Config.HOST_TRADE +
      "/api/v1/pearlriver/trade/bottle/apply/pack/spec/list";
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 回瓶申请
   * @param params
   * @return {promise}
   */
  static submitApplyClick(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/bottle/apply/save";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   * 更新回瓶申请
   * @param params
   */
  static upDateApplyClick(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/bottle/apply/update";
    return callApi(url, params, {
      method: NetUtil.PUT_JSON,
      showLoading: true
    });
  }

  /**
   * 费用缴纳详情
   * @param params paymentType费用类型(1：装车费；2：托板使用费；3：纸箱费 )
   * @return {promise}
   */
  static getPaymentDetail(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/comm/payment/detail/get";
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 费用缴纳详情列表
   * @param params
   * @return {promise}
   */
  static getPaymentDetailList(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/comm/payment/detail/list-by-page";
    //url = 'http://*************:8004/mocking/pearlriver-trade-b2b-application/api/v1/pearlriver/comm/payment/detail/list-by-page';
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 通过组织id获取未缴纳费用
   * @param params
   * @return {Promise<*>}
   */
  static getUnpayList(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/comm/payment/detail/list-by-page";
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 查询订单合并支付信息
   * @param params orderId 订单ID
   * @return {promise}
   */

  static getMergerOrderList(orderId) {
    let url =
      Config.HOST_TRADE +
      "/api/v1/pearlriver/trade/order/query-merge-info/" +
      orderId;
    return callApi(url, {}, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 添加物流评价信息
   * @param params deliveryOrderId:提货单ID,evaluate:评分,evaluateDetail:评价详情
   * @return {promise}
   */
  static evaluation(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/logistics/evaluation";
    return callApi(url, params, { method: NetUtil.POST, showLoading: false });
  }

  /**
   * 获取评价物流信息
   * @param params
   * @return {promise}
   */
  static getEvaluationInfo(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/logistics/evaluation/info";
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

  /**
   * 获取门店核销列表
   * @param params
   * @return {promise}
   */
  static getTradeOrderList(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/coupon/order/list";
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   *  获取奖券列表
   */
  static getCouponList() {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/activity/coupon/list/md";
    return callApi(url, null, { method: NetUtil.GET, showLoading: true });
  }

  /**
   *  获取收支记录
   */
  static getRecords(params) {
    let url =
      Config.HOST_INTEGRAL + "/api/v1/pearlriver/activity/coupon/history/list/md";
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

  /**
   *  获取我的补货单
   */
  static getCouponOrderList(params) {
    let url =
      Config.HOST_INTEGRAL + "/api/v1/pearlriver/activity/coupon/order/list";
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

  /**
   *  取消订单
   */
  static cancelCouponOrder(params) {
    let url =
      Config.HOST_INTEGRAL + "/api/v1/pearlriver/activity/coupon/order/cancel/md";
    return callApi(url, params, { method: NetUtil.POST, showLoading: true });
  }

  /**
   *  确认收货
   */
  static comfirmConponOrder(params) {
    let url =
      Config.HOST_INTEGRAL + "/api/v1/pearlriver/activity/coupon/order/comfirm/md";
    return callApi(url, params, { method: NetUtil.POST, showLoading: true });
  }

  /**
   *  申请补货
   */
  static applyReplenish(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/activity/coupon/order/add/md";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   *  根据id查询地址详情
   */
  static getAddress(params) {
    let url =
      Config.HOST_TRADE +
      "/api/v1/pearlriver/comm/dealer/core/address/queryDetailById";
    return callApi(url, params, { method: NetUtil.GET });
  }

  /**
   *  确认发货
   */
  static deliverOrder(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/activity/coupon/order/deliver/md";
    return callApi(url, params, { method: NetUtil.POST, showLoading: true });
  }

  /**
   *  在某组织下的奖券列表
   */
  static getCouponsByOrgId(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/coupon/list/md";
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

  /**
   *  申请转入
   */
  static applyTransfer(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/coupon/save/md";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   *  在某组织下的奖券列表
   */
  static getTransferList(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/coupon/apply/list";
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

  /**
   *  取消转入
   */
  static cancelTransfer(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/coupon/apply/cancel/md";
    return callApi(url, params, { method: NetUtil.PUT, showLoading: true });
  }

  /**
   *  获取奖券转入详情
   */
  static getTransferDetail(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/coupon/detail/get";
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

  static getPriceList(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/item/price/list";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: false
    });
  }

  /**
   * 检查是否有未缴纳费用
   * @param params
   * @return {Promise<*>}
   */
  static checkUnPay(params) {
    let url =
      Config.HOST_TRADE +
      "/api/v1/pearlriver/comm/payment/checkNotPaymentDetail";
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }
  /**
   * 订单自动审核
   * @param params
   * @return {Promise<*>}
   */
  static ajaxAutoOrder(params) {
    let url =
      Config.HOST_TRADE +
      "/api/v1/pearlriver/trade/order/saveAuditOrder";
      return callApi(url, params, { method: NetUtil.POST_JSON, showLoading: true });
  }
  /**
   * 删除回瓶单
   * @param params
   */
  static deletRecycleBottle(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/bottle/apply/delete";
    return callApi(url, params, { method: NetUtil.DELETE, showLoading: true });
  }

  /**
   * 作废回瓶单
   * @param params
   */
  static cancelRecycleBottle(params) {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/bottle/apply/cancel";
    return callApi(url, params, { method: NetUtil.PUT, showLoading: true });
  }

  /**
   * 版本检查
   * @returns {Promise<*>}
   */
  static checkVersion() {
    let url = Config.HOST_SYS + "/api/v1/pearlriver/app/version/detail";
    let params = {
      byname: Platform.OS == "ios" ? "b2b_ios" : "b2b_android"
    };
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  

  /**
   * 获取权限
   * @returns {Promise<*>}
   */
  static obtainUserLimits() {
    let url = Config.HOST_SYS + "/api/v1/pearlriver/sys/menu/list";

    return callApi(
      url,
      {},
      {
        method: NetUtil.GET,
        showLoading: false,
        resultCallBack: async result => {
          await StorageUtil.saveJsonObject(KEY_USER_LIMITS, result.data);
        }
      }
    );
  }

  /**
   * 获取按金余额
   */
  static getMarginBalanceList(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/comm/account/deposit";
    // let url ='http://*************:8004/mocking/pearlriver-trade-b2b-application /api/v1/pearlriver/comm/deposit/get';
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

  /**
   * 有按押金是弹框
   * @param params
   * @return {Promise<*>}
   */
  static getMarginBalanceListDetail(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/comm/account/deposit";
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 申请售后
   * */
  static applyAfterSale(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/after/submit";
    return callApi(url, params, {
      method: NetUtil.POST_JSON,
      showLoading: true
    });
  }

  /**
   * 售后列表
   * */
  static getAfterSaleList(params) {
    params.pageSize = PAGE_SIZE;
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/after/queryList";
    return callApi(url, params, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 售后详情
   * */
  static getAfterSaleDetail(afterId) {
    let url =
      Config.HOST_TRADE +
      `/api/v1/pearlriver/trade/after/queryDetail/${afterId}`;
    return callApi(url, null, { method: NetUtil.GET, showLoading: true });
  }

  /**
   * 取消售后
   * */
  static cancelAfterSale(afterId) {
    let url =
      Config.HOST_TRADE + `/api/v1/pearlriver/trade/after/cancel/${afterId}`;
    return callApi(url, null, { method: NetUtil.PUT, showLoading: true });
  }

  /**
   * 更新售后详情
   * */
  static updateAfterSale(params) {
    let url = Config.HOST_TRADE + "/api/v1/pearlriver/trade/after/update";
    return callApi(url, params, {
      method: NetUtil.PUT_JSON,
      showLoading: true
    });
  }

  /**
   * 获取 托板承载规格
   * */
  static getPalletStruList() {
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/bottle/palletStru/list";
    return callApi(url, null, { method: NetUtil.GET, showLoading: false });
  }

/**
 * 新的 获取包装子规格列表 关联父规格
 * */
 static getSubPackSpecListChild(parentId) {
  let url = Config.HOST_TRADE + `/api/v1/pearlriver/trade/bottle/apply/pack/spec/list/child?parentId=${parentId}`;
  return callApi(url, null, { method:NetUtil.GET, showLoading:false });
}


  /**
   * 获取用户须知
   * */
  static getUserNotice() {
    let url = Config.HOST_SYS + `/api/v1/pearlriver/user/notice/query?type=1`;
    return callApi(url, null, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 标记用户须知已读
   * */
  static postUseNoticeReaded() {
    let url = Config.HOST_SYS + "/api/v1/pearlriver/user/notice/add?type=1";
    return callApi(
      url,
      {},
      {
        method: NetUtil.POST_JSON,
        showLoading: false
      }
    );
  }

  /**
   * 获取是否已读用户须知
   * */
  static getUserNoticeReadStatus() {
    let url = Config.HOST_SYS + `/api/v1/pearlriver/user/notice/check?type=1`;
    return callApi(url, null, { method: NetUtil.GET, showLoading: false });
  }

  /**
   * 获取经销商基本信息
   * */
  static getDealerUserInfo() {
      let url = Config.HOST_TRADE + `/api/v1/pearlriver/trade/member/dealer/detail`;
      return callApi(url, null, { method: NetUtil.GET, showLoading: false });
  }


   /**
     * 新的收支记录
     * @param null
     * @returns {Promise}
     */
    static listMd(beginTime,endTime) {

      let url =
       Config.HOST_INTEGRAL + "/api/v1/pearlriver/activity/coupon/history/everyday/list/md";
     return callApi(url, {beginTime:beginTime,endTime:endTime}, { method:NetUtil.GET, showLoading:true });
 }
 
    /**
    * 上传图片
    * @param null
    * @returns {Promise}
    */
  static uploadImage(showLoading = false) {
    let url = Config.HOST_SYS + '/api/v1/huieryun/objectstorage/policy/pearlriver/sts';
    return callApi(url, null, { method: NetUtil.GET, showLoading: showLoading });

  }
  /**
    * 查看分销商上传的附件
    * @param null
    * @returns {Promise}
    */
  static getListOrderFile(orderId) {
    let url = Config.HOST_TRADE + '/api/v1/pearlriver/trade/order/list-orderFile';
    return callApi(url, { orderId: orderId }, { method: NetUtil.GET, showLoading: false });
  }
  /**
     * 下载欠款单
     */
  static getArrearageInfo(orderNo) {
    let url = Config.HOST_TRADE + '/api/v1/pearlriver/trade/order/export/arrearage/info';
    return callApi(url, { orderNo: orderNo }, { method: NetUtil.GET, showLoading: false });
  }

  /**
     * 上传欠款单
     * @param null
     * @returns {Promise}
     */
  static uploadFile(params) {
    let url = Config.HOST_TRADE + '/api/v1/pearlriver/trade/order/ChargeSales/uploadFile';
    return callApi(url, params, { method: NetUtil.POST_JSON, showLoading: true });
  }
  /**
      * 查看欠款单
      * @param null
      * @returns {Promise}
      */
  static chargeSalesInfo(orderId) {
    let url = Config.HOST_TRADE + '/api/v1/pearlriver/trade/order/chargeSales/info';
    return callApi(url, { orderId: orderId }, { method: NetUtil.GET, showLoading: true });
  }

  /**
   * 下载欠款单前处理
   * @param null
   * @returns {Promise}
   */
  static checkArrearage(orderNo) {
    let url = Config.HOST_TRADE + '/api/v1/pearlriver/trade/order/export/arrearage/check';
    return callApi(url, { orderNo: orderNo }, {
      method: NetUtil.GET,
      showLoading: false,
    });
  }
  /**
    * 上传图片
    * @param null
    * @returns {Promise}
    */
  static uploadImage(showLoading = false) {
    let url = Config.HOST_SYS + '/api/v1/huieryun/objectstorage/policy/pearlriver/sts';
    return callApi(url, null, { method: NetUtil.GET, showLoading: showLoading });

  }
  /**
    * 查看分销商上传的附件
    * @param null
    * @returns {Promise}
    */
  static getListOrderFile(orderId) {
    let url = Config.HOST_TRADE + '/api/v1/pearlriver/trade/order/list-orderFile';
    return callApi(url, { orderId: orderId }, { method: NetUtil.GET, showLoading: false });
  }
  /**
     * 下载欠款单
     */
  static getArrearageInfo(orderNo) {
    let url = Config.HOST_TRADE + '/api/v1/pearlriver/trade/order/export/arrearage/info';
    return callApi(url, { orderNo: orderNo }, { method: NetUtil.GET, showLoading: false });
  }

  /**
     * 上传欠款单
     * @param null
     * @returns {Promise}
     */
  static uploadFile(params) {
    let url = Config.HOST_TRADE + '/api/v1/pearlriver/trade/order/ChargeSales/uploadFile';
    return callApi(url, params, { method: NetUtil.POST_JSON, showLoading: true });
  }
  /**
      * 查看欠款单
      * @param null
      * @returns {Promise}
      */
  static chargeSalesInfo(orderId) {
    let url = Config.HOST_TRADE + '/api/v1/pearlriver/trade/order/chargeSales/info';
    return callApi(url, { orderId: orderId }, { method: NetUtil.GET, showLoading: true });
  }

  /**
   * 下载欠款单前处理
   * @param null
   * @returns {Promise}
   */
  static checkArrearage(orderNo) {
    let url = Config.HOST_TRADE + '/api/v1/pearlriver/trade/order/export/arrearage/check';
    return callApi(url, { orderNo: orderNo }, {
      method: NetUtil.GET,
      showLoading: false,
    });
  }

  /**
 * 订单自动审核
 * @param null
 * @returns {Promise}
 */
  static saveAuditOrder(orderNoList) {
    //'https://qa.s.zhujiangbeer.com/pearlriver-trade-b2b-application/api/v1/pearlriver/trade/order/saveAuditOrder'
    let url =
      Config.HOST_TRADE + "/api/v1/pearlriver/trade/order/saveAuditOrder";
    return callApi(url, orderNoList, { method: NetUtil.POST_JSON, showLoading: false });
  }

  /**
    * 新的收支记录
    * @param null
    * @returns {Promise}
    */
  static listMd(beginTime, endTime) {
    let params = {};
    if (beginTime != null && beginTime != '') {
      params.beginTime = beginTime;
    }
    if (endTime != null && endTime != '') {
      params.endTime = endTime;
    }
    let url =
      Config.HOST_INTEGRAL + "/api/v1/pearlriver/activity/coupon/history/everyday/list/md";
    return callApi(url, params, { method: NetUtil.GET, showLoading: true });
  }

   /**
     * 新的收支记录
     * @param null
     * @returns {Promise}
     */
    static listMd(beginTime,endTime) {
      let params = {};
      if(beginTime!=null && beginTime!=''){
        params.beginTime = beginTime;
      }
      if(endTime!=null && endTime!=''){
        params.endTime = endTime;
      }
      let url =
       Config.HOST_INTEGRAL + "/api/v1/pearlriver/activity/coupon/history/everyday/list/md";
     return callApi(url, params, { method:NetUtil.GET, showLoading:true });
 }

   /**
   * 获取未读消息数
   * */
    static getUserNoticeNum(param) {
      let url = Config.HOST_TRADE + `/api/v1/pearlriver/comm/notice/getCountTips`;
      return callApi(url, param, { method: NetUtil.GET, showLoading: false });
    }
  
    /**
     * 获取公告列表
     * */
    static getUserNoticeList(param) {
      let url = Config.HOST_TRADE + `/api/v1/pearlriver/comm/notice/list`;
      return callApi(url, param, { method: NetUtil.GET, showLoading: false });
    }
  
    /**
     * 公告已读
     */
    static postUserNoticeRead(param) {
      let url = Config.HOST_TRADE + `/api/v1/pearlriver/comm/notice/detail`;
      return callApi(url, param, { method: NetUtil.GET, showLoading: false });
    }
}

export default Api;
