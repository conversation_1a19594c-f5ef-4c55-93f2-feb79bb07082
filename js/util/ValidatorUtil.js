/**
 *  Created by lao<PERSON>ji<PERSON><PERSON> on 2018/1/12.
 */
import Toast from 'react-native-root-toast';
import Validator from 'vdjs';

/**
 * 校验组件
 * vdjs使用文档 https://github.com/mengdu/validator.js
 */
class ValidatorUtil {
    static validate(data, rule, showToast=true) {
        let valid = Validator.validate(data, rule);
        if (valid.first()) {
            if (showToast) {
                Toast.show(valid.first(), {
                    duration: Toast.durations.SHORT,
                    position: Toast.positions.CENTER,
                    shadow: true,
                    animation: true,
                    hideOnPress: true,
                    delay: 0,
                    backgroundColor: '#000000'
                });
            }
            return false
        }
        return true;

    }

    static validatorInputValue(value, isFocus) {
        value = value + '';
        let verify = false;
        let regEx = /^[1-9][0-9]*$/;
        verify = regEx.test(value);
        if (isFocus && value.length == 0) {
            verify = true
        }
        return verify;
    }

    static validatorNumberValue(value, isFocus,integerLength=9,decimalsLength=2) {
        value = value + '';
        let verify = false;
        let regEx1 = new RegExp(`^[1-9][0-9]{0,${integerLength-1}}$`);
        let regEx2 = new RegExp(`^[0-9]{1,${integerLength}}[.][0-9]{0,${decimalsLength}}$`);
        let result1=regEx1.test(value);
        let result2=regEx2.test(value);
        Log('result',result1,result2);
        verify=result1||result2;
        if (isFocus && value.length == 0) {
            verify = true
        }else if(decimalsLength>0&&isFocus&&value==='0'){
            verify=true
        }
        return verify;
    }

    // 中文、英文、数字的组合
    static validatorNameValue(value, isFocus) {
        value = value + '';
        let verify = false;
        let regEx = /^[a-zA-Z0-9\u4e00-\u9fa5]+$/;
        verify = regEx.test(value);
        if (isFocus && value.length == 0) {
            verify = true
        }
        return verify;
    }
}

export default ValidatorUtil;