/**
 *
 * Created by <PERSON> on 2018/2/26.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */

class H5Util {

    /**
     * 瓶箱余额
     * @return {string}
     */
    static getBottleBoxSurplusUrl(){
        return Config.HOST+'/pearlriver-h5-tpl/html/bottleBoxSurplus.html'
    }

    /**
     * 瓶盖赠酒
     * @return {string}
     */
    static getCapGiveUrl(){
        return Config.HOST+'/pearlriver-h5-tpl/html/capGive.html'
    }

    /**
     * 费用赠酒
     * @return {string}
     */
    static getExpenseSurplusUrl(){
        return Config.HOST+'/pearlriver-h5-tpl/html/expenseSurplus.html'
    }

    /**
     * 评论物流
     * @return {string}
     */
    static getLogisticsUrl(){
        return Config.HOST+'/pearlriver-h5-tpl/html/logistics.html'
    }

    /**
     * 地图轨迹
     * @return {string}
     */
    static getMapUrl(deliveryId,status){
        return Config.HOST+'/pearlriver-h5-tpl/html/map.html?deliveryId='+deliveryId+'&status='+status;
    }
}
export default H5Util;