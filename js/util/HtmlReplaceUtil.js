/**
 *
 * Created by <PERSON> on 2017/12/5.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */

class HtmlReplaceUtil {
    static replaceStyleFun(matchsrc) {
        //let matchsrc = '<p>\n\t（美醇）500ml\n</p>\n<p>\n\t<img src="https://zn-image.oss-cn-shenzhen.aliyuncs.com/supplychain-dev/test/undefined/AB2pwpkwfj.jpg" alt="" />\n</p>\n<p>\n\t<img src="https://zn-image.oss-cn-shenzhen.aliyuncs.com/supplychain-dev/test/undefined/eMakDsX23H.jpg" alt="" />\n</p>\n<p>\n\t<img src="https://zn-image.oss-cn-shenzhen.aliyuncs.com/supplychain-dev/test/undefined/73TTw8dhPc.jpg" alt="" />\n</p>\n<p>\n\t<img src="https://zn-image.oss-cn-shenzhen.aliyuncs.com/supplychain-dev/test/undefined/5jHe2bskd5.jpg" alt="" />\n</p>';
        // let reg = /(<img).*?(src.*?=.*?['"](.*?)['"]).*?(\/>|>)/g;
        // let b = matchsrc.replace(reg, (text, p1, p2, p3, p4) => {
        //     let result = `${p1} src="${Constant.ossImgUrl(p3, width)}" style="max-width=100%" ${p4}`
        //     return result;
        // })
        // console.log('==============', b);
    }

    static addHead(str) {
        return '<head><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"><style>img{max-width:100% !important;}video{max-width:100% !important;}</style></head>' + str
    }

}
export default HtmlReplaceUtil;