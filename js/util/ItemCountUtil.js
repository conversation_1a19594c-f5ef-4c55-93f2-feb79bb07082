export default class ItemCountUtil {
    static validatorCountValue(value, purchaseQuantity,) {
        try {
            let newValue = parseInt(value);
            let multiple = parseInt(purchaseQuantity.multiple);
            let isMandatcory = purchaseQuantity.isMandatory;

            if (multiple>0 && isMandatcory){
                if (newValue % multiple == 0) {
                    //输入数量 整板数量处理
                    return newValue
                } else {
                    if (newValue < multiple) {
                        //输入数量 小于整板数量处理
                        return multiple;
                    } else {
                        //输入数量 大于整板数量处理
                        let result=Math.ceil(newValue / multiple) * multiple;
                        if(result>99999999){
                            return result-multiple;
                        }else{
                            return result;
                        }

                    }
                }
            }else {
                return value;
            }

            //
            // if (!multiple) {
            //     return value;
            // } else {
            //     if (newValue % multiple == 0) {
            //         //输入数量 整板数量处理
            //         return newValue
            //     } else {
            //         if (newValue < multiple) {
            //             //输入数量 小于整板数量处理
            //             return multiple;
            //         } else {
            //             //输入数量 大于整板数量处理
            //             let result=Math.ceil(newValue / multiple) * multiple;
            //             if(result>99999999){
            //                 return result-multiple;
            //             }else{
            //                 return result;
            //             }
            //
            //         }
            //     }
            // }
        } catch (err) {
            return purchaseQuantity.count ? purchaseQuantity.count : ''
        }
    }

    /**
     * 数字转换
     * @param value
     * @param isFocus
     * @param enableZero
     * @return {string|*}
     */
    static transformInputValue(value, isFocus,enableZero,isZeroFourGood) {
        value = value + '';
        // if (isZeroFourGood && isFocus){
        //    return value;
        // }

        if (isZeroFourGood){

            if(enableZero){
                if(value.startsWith('0') && !value.startsWith('0.')&&value.length>1){
                    value=value.substring(1,value.length);
                    // Log('checkInputValue',value);
                }
            }

        }else {
            if(enableZero){
                if(value.startsWith('0') &&value.length>1){
                    value=value.substring(1,value.length);
                    // Log('checkInputValue',value);
                }
            }
        }

        return value;
    }

    /**
     *
     * @param value
     * @param isFocus
     * @param enableZero
     * @return {boolean}
     */
    static validatorInputValue(value, isFocus,enableZero,isZeroFourGood) {
        value = value + '';
        let verify = false;
        let regEx = /^[1-9][0-9]*$/;
        if(enableZero){

            regEx=/^[0-9][0-9]*$/;
        }

        if(isZeroFourGood){
          regEx = /^\d+\.?\d{0,2}$/;
        }
        verify = regEx.test(value);
        if (isFocus && value.length == 0) {
            verify = true
        }else if(value.startsWith('0')&&value.length<3&&enableZero){
            verify=true;
        }
        return verify;
    }
}

