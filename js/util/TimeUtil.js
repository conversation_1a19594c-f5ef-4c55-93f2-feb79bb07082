/**
 *
 * Created by <PERSON> on 2017/12/26.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */

export default class TimeUtil {
    /**
     * 倒计时计算
     * @param endDate
     * @return {*}
     */
    static getDateData(endDate) {
        // endDate = '2017-10-14 16:13:00'
        // Log(typeof endDate,'000000000000')
        if (typeof endDate == 'string') {
            //处理2000-10-10 10:00:00，因为js Date构造函数是接收改格式 20001010 10:00:00
            endDate = endDate.replace(/-/g, "/");
        }

        let diff = (Date.parse(new Date(endDate)) - Date.parse(new Date)) / 1000;
        // Log(endDate,diff,'++++++++++',new Date(endDate).getTime())
        if (diff <= 0) {
            return false;
        }
        const timeLeft = {
            years: 0,
            days: 0,
            hours: 0,
            min: 0,
            sec: 0,
            millisec: 0,
        };

        if (diff >= (365.25 * 86400)) {
            timeLeft.years = Math.floor(diff / (365.25 * 86400));
            diff -= timeLeft.years * 365.25 * 86400;
        }
        if (diff >= 86400) {
            timeLeft.days = Math.floor(diff / 86400);
            diff -= timeLeft.days * 86400;
        }
        if (diff >= 3600) {
            timeLeft.hours = Math.floor(diff / 3600);
            diff -= timeLeft.hours * 3600;
        }
        if (diff >= 60) {
            timeLeft.min = Math.floor(diff / 60);
            diff -= timeLeft.min * 60;
        }
        timeLeft.sec = diff;
        return timeLeft;
    }

}

