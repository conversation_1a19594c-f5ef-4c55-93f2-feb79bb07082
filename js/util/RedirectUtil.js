/**
 *
 * Created by xiaowz on 2017/10/11.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import {
    Linking,
} from 'react-native';
class RedirectUtil {
    static redirectFun(redirectObj, context) {
        try {
            const newObj = JSON.parse(redirectObj)
            const { page, params } = newObj;
            if (page && page === 'CommonWebView' && params && params.url && params.appWebView === false) {
                Linking.openURL(url).catch((err) => {
                });
            } else {
                context.navigate(page, params)
            }
        } catch (e) {
            Log('RedirectUtilError' + e)    
        }
    }
}
export default RedirectUtil;