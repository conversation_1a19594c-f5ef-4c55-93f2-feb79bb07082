/**
 * Created by lao.ji<PERSON><PERSON> on 2017/4/20.
 */

import {
    Dimensions,
    Platform,
    StyleSheet,
} from 'react-native';
import { Util, SensorsData } from 'rn-yunxi';
const {width, height} = Dimensions.get('window');
const { StorageUtil } = Util;
/**
 * 全局常量
 */
// UI设计以iPhone6宽度为标准模板 () 6p:414
const UI_STANDARD = 375;
//iPhone X
const ISIPHONEX = Dimensions.get('window').width == 375 && Dimensions.get('window').height == 812

function scaleFontSizeFunc(size) {
    if (Platform.OS == 'ios' && Dimensions.get('window').width == 320) {
        //iphone 5s
        return size - 1;
    }
    return size;
}

function scaleFunc(width) {
    return Dimensions.get('window').width / UI_STANDARD * width;
}
class Constant {

    static ISIPHONEX=ISIPHONEX;
    //长度自适应
    static scale(width) {
        return scaleFunc(width);
    }

    static ossImgUrl(url, width) {
        if (url && width) {
            width = width * Dimensions.get('window').scale;
            if (url.indexOf() <= 0 && url.indexOf('aliyuncs.com') > -1 && url.indexOf('x-oss-process=image') < 0) {
                url = `${url}?x-oss-process=image/resize,w_${parseInt(width)}`
            }
        }
        return url;
    }

    static scaleFontSize(size) {
        return scaleFontSizeFunc(size);
    }


    //字体
    static fontSizeXSmall = scaleFontSizeFunc(10);//超小号字体
    static fontSizeSmall = scaleFontSizeFunc(12);//小号
    static fontSizeNormal = scaleFontSizeFunc(14);//默认
    static fontSizeBig = scaleFontSizeFunc(16);//默认
    static fontSizeCaption = scaleFontSizeFunc(18);//标题
    static fontSizeLarge = scaleFontSizeFunc(20);//大号
    static fontSizeHeader = scaleFontSizeFunc(24);//头部
    static fontSizeSuperLarge = scaleFontSizeFunc(25);//超大头部
    //颜色
    static colorDefault = '#FFFFFF';
    static colorBackgroundDefault = '#F3F3F3'; //背景颜色

    static colorPrimary = '#DF0522';
    static colorPrimaryTap = '#cc2b17';
    static colorDivider = '#ddd8d8';//分隔线
    static colorHit = '#cbc';
    static colorLightGray = '#dddddddd';
    static colorDisable = '#888';//不可点击 颜色
    static colorTransparent = '#00000000'; //背景透明
    static colorTranslucence = '#00000088';
    static colorPlaceholder = '#AFAFAF';//输入框placehoder文字颜色
    static colorOrange = '#FFA000';

    //字体颜色
    static colorTxtDefault = '#333333';
    // 默认灰色
    static colorTxtGrayDefault = '#808080';
    // 默认分割线
    static colorDividerDefault = '#E5E5E5';


    static colorTxtPrimary = '#DF0522';   //主体文字 颜色
    static colorTxtTitle = '#333333';     //主要文字颜色，如标题
    static colorTxtContent = '#666666';     //内容文字颜色，如商品文字
    static colorTxtAlert = '#999999';     //辅助文字颜色，如提醒类文字
    static colorTxtGreen = '#2cb95e';     //绿色字体
    //分割线尺寸
    static sizeDividerNormal = StyleSheet.hairlineWidth;//分隔线大小
    static sizeDividerLarge = 1;
    static sizeMarginDefault =scaleFunc(12);
    static sizeCellHeight = scaleFunc(44);

    static sizeHeaderWithSafeArea = (Platform.OS === 'ios' && ISIPHONEX) ? 88 : Platform.OS === 'ios' ? 64 : 50;
    static sizeSafeArea = (Platform.OS === 'ios' && ISIPHONEX) ? 44 : Platform.OS === 'ios' ? 20 : 0;

    // static sizeHeaderMarginTop = Platform.OS === 'ios' ? ISIPHONEX ? 35 : 20 : 0
    // static sizeHeaderContent = Platform.OS === 'ios' ? 44 : 50;
    static sizeHeader = Platform.OS === 'ios'  ? 44 : 50;
    static sizeHeaderMarginTop =  0
    static sizeHeaderContent = Platform.OS === 'ios' ? 44 : 50;
    static strCheckPrice = "点击查看价格";

    static isAndroid = Platform.OS === 'android'
}

/**
 * 全局样式
 */
class GlobalStyle {

    //主字体样式（标题）
    static styleTxtDefault = {
        fontSize: Constant.fontSizeNormal,
        color: Constant.colorTxtTitle,
    };

    //主题颜色字体 （标题）

    static stylePrimaryTxtDefault = {
        fontSize: Constant.fontSizeNormal,
        color: Constant.colorTxtPrimary,
    };

    //内容字体样式 （内容）
    static styleTxtContent = {
        fontSize: Constant.fontSizeSmall,
        color: Constant.colorTxtContent
    }


    //分割线样式
    static styleDividerDefault = {
        width: '100%',
        height: Constant.sizeDividerNormal,
        backgroundColor: Constant.colorDivider,
    }

    static styleSeparatorDefault = {
        width: '100%',
        height: 10,
        backgroundColor: Constant.colorTransparent,
    }

    //订单button样式

    static styleButtonTxt = {
        color: '#666666',
        fontSize: 12,
        textAlign: 'center',
        // fontWeight: 'bold'
    }
    static styleButtonDefault = {

        height: Constant.scale(28),
        minWidth: Constant.scale(72),
        borderRadius: Constant.scale(4),
        borderWidth: Constant.sizeDividerNormal,
        borderColor: '#E1E1E1',
        marginRight: Constant.sizeMarginDefault
    }

    static styleButtonPrimaryTxt = {
        color: Constant.colorTxtPrimary,
        fontSize: 12,
        textAlign: 'center',
        // fontWeight: 'bold'
    }
    static styleButtonPrimary = {
        height: Constant.scale(28),
        minWidth: Constant.scale(72),
        borderRadius: Constant.scale(4),
        borderWidth: Constant.sizeDividerNormal,
        borderColor: Constant.colorPrimary,
        marginRight: Constant.sizeMarginDefault,
    }

    static styleButtonDisableTxt = {
        color: '#cccccc',
        fontSize: 12,
        textAlign: 'center',
        // fontWeight: 'bold'
    }
    static styleButtonDisable = {
        height: Constant.scale(28),
        minWidth: Constant.scale(72),
        borderRadius: Constant.scale(4),
        borderWidth: Constant.sizeDividerNormal,
        marginRight: Constant.sizeMarginDefault,
        backgroundColor: '#f2f2f2'
    }
    static InputFromCell = {
        viewMargin: Constant.sizeMarginDefault,
        placeholderTextColor:Constant.colorPlaceholder,
        lineMargin:{
            width: width-Constant.sizeMarginDefault,
            height: StyleSheet.hairlineWidth,
            backgroundColor: '#ddd8d8',
            position: 'absolute',
            bottom: StyleSheet.hairlineWidth,
            left: Constant.sizeMarginDefault
        }

    }

    static LabelCell = {
        arrowIcon:require('./module/img/ic_center_right_arrow.png'),
        arrowStyle:{
            width: Constant.scale(6),
            height: Constant.scale(12),
            marginLeft:Constant.scale(8)
        },
        viewMargin: Constant.sizeMarginDefault,
        placeholderTextColor:Constant.colorPlaceholder,
        lineMargin:{
            width: width-Constant.sizeMarginDefault,
            height: StyleSheet.hairlineWidth,
            backgroundColor: '#ddd8d8',
            marginLeft: Constant.sizeMarginDefault
        },
        lineAll:{
            width: '100%',
            height: StyleSheet.hairlineWidth,
            backgroundColor: '#ddd8d8',

        }

    }

    static appRefreshStyle = {
        tintColor: Constant.colorPrimary,
        title: "用力加载...",
        titleColor: Constant.colorPrimary,
        progressBackgroundColor: Constant.colorPrimary,
        colors: ['white'],

    }

}

/**
 * 全局配置
 */

const Config = {
    EVENT_SCREEN_CHANGE: "onScreenChange",
    EVENT_GLOBAL_STATE_CHANGE: "onGlobalStateChange",
    HOST_CONFIG: {
        'ZP_DEV': {//开发
            HOST: 'http://dev.zhujiangbeer.com',
            CURRENT_HOST: 'dev_',
            HOST_MGMT: 'http://dev.zhujiangbeer.com/pearlriver-mgmt-b2b-application',
            HOST_TRADE: 'http://dev.zhujiangbeer.com/pearlriver-trade-b2b-application',
            HOST_SYS: 'http://dev.zhujiangbeer.com/pearlriver-sys-application',
            HOST_VERIFY: 'http://dev.zhujiangbeer.com/huieryun-identity',
            HOST_SEARCH: 'http://dev.zhujiangbeer.com/pearlriver-search-application',
            HOST_LARGE_PAY: 'http://dev.zhujiangbeer.com/nuskin-b2c-web-pc/wholesaleLogin',
            HOST_H5: 'http://dev.zhujiangbeer.com/nuskin-b2c-web-wx/#',
            HOST_SENSORS_SA: 'http://dev.zhujiangbeer.com/sa?project=data',
            HOST_SENSORS_CONFIG: 'http://dev.zhujiangbeer.com/config?project=data',
            HOST_INTEGRAL: 'http://dev.zhujiangbeer.com/pearlriver-trade-integral-application',
        },
        'ZP_TEST': {//开发
            HOST: 'http://test.zhujiangbeer.com',
            CURRENT_HOST: 'test_',
            HOST_MGMT: 'http://test.zhujiangbeer.com/pearlriver-mgmt-b2b-application',
            HOST_TRADE: 'http://test.zhujiangbeer.com/pearlriver-trade-b2b-application',
            HOST_SYS: 'http://test.zhujiangbeer.com/pearlriver-sys-application',
            HOST_VERIFY: 'http://test.zhujiangbeer.com/huieryun-identity',
            HOST_SEARCH: 'http://test.zhujiangbeer.com/pearlriver-search-application',
            HOST_LARGE_PAY: 'http://test.zhujiangbeer.com/nuskin-b2c-web-pc/wholesaleLogin',
            HOST_H5: 'http://test.zhujiangbeer.com/nuskin-b2c-web-wx/#',
            HOST_SENSORS_SA: 'http://test.zhujiangbeer.com/sa?project=data',
            HOST_SENSORS_CONFIG: 'http://test.zhujiangbeer.com/config?project=data',
            HOST_INTEGRAL: 'http://test.zhujiangbeer.com/pearlriver-trade-integral-application',
        },
        'DEV': {//开发
            HOST: 'http://demo.dtyunxi.cn:8700',
            HOST_MGMT: 'http://demo.dtyunxi.cn:8700/pearlriver-mgmt-b2b-application',
            HOST_TRADE: 'http://demo.dtyunxi.cn:8700/pearlriver-trade-b2b-application',
            HOST_SYS: 'http://demo.dtyunxi.cn:8700/pearlriver-sys-application',
            HOST_VERIFY: 'http://demo.dtyunxi.cn:8700/huieryun-identity',
            HOST_SEARCH: 'http://demo.dtyunxi.cn:8700/pearlriver-search-application',
            HOST_LARGE_PAY: 'http://demo.dtyunxi.cn:8700/nuskin-b2c-web-pc/wholesaleLogin',
            HOST_H5: 'http://demo.dtyunxi.cn:8700/nuskin-b2c-web-wx/#',
            HOST_SENSORS_SA: 'http://demo.dtyunxi.cn:8700/sa?project=data',
            HOST_SENSORS_CONFIG: 'http://demo.dtyunxi.cn:8700/config?project=data',
            HOST_INTEGRAL: 'http://demo.dtyunxi.cn:8700/pearlriver-trade-integral-application',
        },
        'TEST': {//测试
            HOST: 'http://test.dtyunxi.cn:8700',
            HOST_MGMT: 'http://test.dtyunxi.cn:8700/pearlriver-mgmt-b2b-application',
            HOST_TRADE: 'http://test.dtyunxi.cn:8700/pearlriver-trade-b2b-application',
            HOST_SYS: 'http://test.dtyunxi.cn:8700/pearlriver-sys-application',
            HOST_VERIFY: 'http://test.dtyunxi.cn:8700/huieryun-identity',
            HOST_SEARCH: 'http://test.dtyunxi.cn:8700/pearlriver-search-application',
            HOST_LARGE_PAY: 'http://test.dtyunxi.cn:8700/nuskin-b2c-web-pc/wholesaleLogin',
            HOST_H5: 'http://test.dtyunxi.cn:8700/nuskin-b2c-web-wx/#',
            HOST_SENSORS_SA: 'http://test.dtyunxi.cn:8700/sa?project=data',
            HOST_SENSORS_CONFIG: 'http://test.dtyunxi.cn:8700/config?project=data',
            HOST_INTEGRAL: 'http://test.dtyunxi.cn:8700/pearlriver-trade-integral-application',
        },
        'QABAK': {
            HOST: 'https://qa.s.zhujiangbeer.com/',
            CURRENT_HOST: 'qa_',
            HOST_MGMT: 'https://qa.s.zhujiangbeer.com/pearlriver-mgmt-b2b-application',
            HOST_TRADE: 'https://qa.s.zhujiangbeer.com/pearlriver-trade-b2b-application',
            HOST_SYS: 'https://qa.s.zhujiangbeer.com/pearlriver-sys-application',
            HOST_VERIFY: 'https://qa.s.zhujiangbeer.com/huieryun-identity',
            HOST_SEARCH: 'https://qa.s.zhujiangbeer.com/pearlriver-search-application',
            HOST_LARGE_PAY: 'https://qa.s.zhujiangbeer.com/nuskin-b2c-web-pc/wholesaleLogin',
            HOST_H5: 'https://qa.s.zhujiangbeer.com/nuskin-b2c-web-wx/#',
            HOST_SENSORS_SA: 'https://qa.s.zhujiangbeer.com/sa?project=data',
            HOST_SENSORS_CONFIG: 'https://qa.s.zhujiangbeer.com/config?project=data',
            HOST_INTEGRAL: 'https://qa.s.zhujiangbeer.com/pearlriver-trade-integral-application',
        },
        'QA': {
            HOST: 'https://k8s-qa.zhujiangbeer.com/',
            CURRENT_HOST: 'k8s-qa_',
            HOST_MGMT: 'https://k8s-qa.zhujiangbeer.com/pearlriver-mgmt-b2b-application',
            HOST_TRADE: 'https://k8s-qa.zhujiangbeer.com/pearlriver-trade-b2b-application',
            HOST_SYS: 'https://k8s-qa.zhujiangbeer.com/pearlriver-sys-application',
            HOST_VERIFY: 'https://k8s-qa.zhujiangbeer.com/huieryun-identity',
            HOST_SEARCH: 'https://k8s-qa.zhujiangbeer.com/pearlriver-search-application',
            HOST_LARGE_PAY: 'https://k8s-qa.zhujiangbeer.com/nuskin-b2c-web-pc/wholesaleLogin',
            HOST_H5: 'https://k8s-qa.zhujiangbeer.com/nuskin-b2c-web-wx/#',
            HOST_SENSORS_SA: 'https://k8s-qa.zhujiangbeer.com/sa?project=data',
            HOST_SENSORS_CONFIG: 'https://k8s-qa.zhujiangbeer.com/config?project=data',
            HOST_INTEGRAL: 'https://k8s-qa.zhujiangbeer.com/pearlriver-trade-integral-application',
        },
        'PRO':{
            HOST: 'https://s.zhujiangbeer.com',
            CURRENT_HOST: 's_',
            HOST_MGMT: 'https://s.zhujiangbeer.com/pearlriver-mgmt-b2b-application',
            HOST_TRADE: 'https://s.zhujiangbeer.com/pearlriver-trade-b2b-application',
            HOST_SYS: 'https://s.zhujiangbeer.com/pearlriver-sys-application',
            HOST_VERIFY: 'https://s.zhujiangbeer.com/huieryun-identity',
            HOST_SEARCH: 'https://s.zhujiangbeer.com/pearlriver-search-application',
            HOST_H5: 'https://s.zhujiangbeer.com/nuskin-b2c-web-wx/#',
            HOST_SENSORS_SA: 'https://s.zhujiangbeer.com/sa?project=data',
            HOST_SENSORS_CONFIG: 'https://s.zhujiangbeer.com/config?project=data',
            HOST_INTEGRAL: 'https://s.zhujiangbeer.com/pearlriver-trade-integral-application',
        }
    },


    LOCAL_SEARCH_KEYWORD: "local_search_keyword",
    LOCAL_NAME_BANKNAME: "local_name_bankName",
    LOCAL_GOODS_DETAIL_KEYWORD: "local_goodsDetail_keyword",
    //全局header参数
    APP_CODE: "2",
    APP_ID: "6",
    TENANT_ID: "1",
    CHANNEL_ID: "2",
    TERMINAL_TYPE: "b1",
    CHANNEL_CODE: "02",
    //环境配置
    HOST_KEY: "ZP_TEST",
    isCanChangeEvn: true,//是否可以切换环境
    isShowEvn: true,//是否显示当前环境
    WECHAT_APP_KEY: 'wx84b4d83101d38d47',
    //新API
    HOST: 'http://test.zhujiangbeer.com',
    CURRENT_HOST: 'test_',
    HOST_MGMT: 'http://test.zhujiangbeer.com/pearlriver-mgmt-b2b-application',
    HOST_TRADE: 'http://test.zhujiangbeer.com/pearlriver-trade-b2b-application',
    HOST_SYS: 'http://test.zhujiangbeer.com/pearlriver-sys-application',
    HOST_VERIFY: 'http://test.zhujiangbeer.com/huieryun-identity',
    HOST_SEARCH: 'http://test.zhujiangbeer.com/pearlriver-search-application',
    HOST_LARGE_PAY: 'http://test.zhujiangbeer.com/nuskin-b2c-web-pc/wholesaleLogin',
    HOST_H5: 'http://test.zhujiangbeer.com/nuskin-b2c-web-wx/#',
    HOST_SENSORS_SA: 'http://test.zhujiangbeer.com/sa?project=data',
    HOST_SENSORS_CONFIG: 'http://test.zhujiangbeer.com/config?project=data',
    HOST_INTEGRAL: 'http://test.zhujiangbeer.com/pearlriver-trade-integral-application',

};

Config.HOST_INIT = false;
//设置Api host
Config.setHostWithHostKey = (host) => {
    Config.HOST_KEY = host;
    Config.HOST = Config.HOST_CONFIG[host].HOST;
    Config.CURRENT_HOST = Config.HOST_CONFIG[host].CURRENT_HOST;
    Config.HOST_MGMT = Config.HOST_CONFIG[host].HOST_MGMT;
    Config.HOST_SYS = Config.HOST_CONFIG[host].HOST_SYS;
    Config.HOST_TRADE = Config.HOST_CONFIG[host].HOST_TRADE;
    Config.HOST_VERIFY = Config.HOST_CONFIG[host].HOST_VERIFY;
    Config.HOST_SEARCH = Config.HOST_CONFIG[host].HOST_SEARCH;
    Config.HOST_LARGE_PAY = Config.HOST_CONFIG[host].HOST_LARGE_PAY;
    Config.HOST_H5 = Config.HOST_CONFIG[host].HOST_H5;
    Config.HOST_SENSORS_SA = Config.HOST_CONFIG[host].HOST_SENSORS_SA;
    Config.HOST_SENSORS_CONFIG = Config.HOST_CONFIG[host].HOST_SENSORS_CONFIG;
    Config.HOST_INTEGRAL = Config.HOST_CONFIG[host].HOST_INTEGRAL;

    // SensorsData.init(Config.HOST_SENSORS_SA, Config.HOST_SENSORS_CONFIG, false);
    console.log('setHostWithHostKey', Config.HOST);
    Config.HOST_INIT = true;
};
//在可配置host状态下,异步可读取最后保存的host key，并配置api host
Config.setHost = async () => {
    console.log('init host')
    Config.HOST_INIT = false;
    let { host } = await StorageUtil.getJsonObject('key_host_api', { host: Config.HOST_KEY });
    Config.setHostWithHostKey(host);
};

Config.changeHost = async (host) => {
    // let {isTest} = await StorageUtil.getJsonObject('key_host', {host: host});

    await StorageUtil.saveJsonObject('key_host_api', { host: host })
};
/**
 * 全局Log
 * @param params
 * @constructor
 */
const Log = (...params) => {
    if (GLOBAL.__DEV__) {
        console.log(params);
    }
}
Log.LOG_API_RESULT = true;//打印api Log控制
import Api from './util/Api';

global.Log = Log;
global.Api = Api;
global.Config = Config;
global.Constant = Constant;
global.GlobalStyle = GlobalStyle;
global.ISIPHONEX = ISIPHONEX;

