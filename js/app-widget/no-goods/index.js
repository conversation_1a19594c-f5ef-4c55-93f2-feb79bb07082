/**
 * Created by lu.jiarong on 2017/5/2.
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    Text,
    Image
} from 'react-native';
import { Widget } from 'rn-yunxi';
const { width, height } = Dimensions.get('window');
import ICON from './icon.png';
//模块声名并导出
export default class NoGoods extends Component {

    //默认属性
    static defaultProps = {

    };
    //构造函数
    constructor(props) {
        super(props);
        this.state = { //状态机变量声明
        }
    }

    //渲染
    render() {

        return (
            <View
                style={styles.container}
            >
                <View style={styles.lineStyle} />
                <Image
                    style={{ width: 19, height: 14, marginLeft: 10, }}
                    source={ICON}
                />
                <Text style={{
                    color: '#999999',
                    fontSize: Constant.fontSizeNormal,
                    marginLeft: 5,
                    marginRight: 10,
                }}
                >暂无商品</Text>
                <View style={styles.lineStyle} />
            </View>

        );
    }

};

const styles = StyleSheet.create({

    container: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'white',
        height: 58
    },
    lineStyle: {
        marginRight: 8,
        marginLeft: 8,
        backgroundColor: Constant.colorLightGray,
        width: '27%',
        height: 0.5,
    },

});
