'use strict';

import React, { Component, PropTypes } from 'react';
import {
	TouchableOpacity,
	View,
	Image,
	TextInput,
	StyleSheet,
	PixelRatio,
	Platform,
} from 'react-native';

/**
 * 数量选择，带输入框
 */
export default class QuantitySelector extends Component {
	constructor(props) {
		super(props);
		this.state = {
			minusDisabled: Number(this.props.value) <= this.props.min || this.props.minusDisabled,
			plusDisabled: Number(this.props.value) >= this.props.max || this.props.plusDisabled,
			value: this.props.value != null ? this.props.value.toString() : null,
		}
		this.timer = null;
	}
	static propTypes = {
		minusDisabled: React.PropTypes.bool,
		plusDisabled: React.PropTypes.bool,
		minusIcon: Image.propTypes.source,
		minusDisabledIcon: Image.propTypes.source,
		plusIcon: Image.propTypes.source,
		plusDisabledIcon: Image.propTypes.source,
		inputProps: React.PropTypes.object,
		style: React.PropTypes.object,
		value: React.PropTypes.number,
		min: React.PropTypes.number,
		max: React.PropTypes.number,
		increment: React.PropTypes.number,
		debounce: React.PropTypes.number,
		onChange: React.PropTypes.func,
		onPlus: React.PropTypes.func,
		onMinus: React.PropTypes.func,
		isCanChange: React.PropTypes.func
	}
	static defaultProps = {
		minusDisabled: false, // 是否禁用
		plusDisabled: false, // 是否禁用
		minusIcon: require('./img/minus.png'), //减号图片
		minusDisabledIcon: require('./img/minus_d.png'),//减号不可点击图片
		plusIcon: require('./img/plus.png'),//加号图片
		plusDisabledIcon: require('./img/plus_d.png'),//加号不可点击图片
		inputProps: {},	// 输入框参数
		style: {},	// 样式
		min: 1,	// 最小值
		max: 999999,	// 最大值
		value: null,		// 默认值
		increment: 1,		// 增量
		debounce: 0,		// 延迟
		onChange: () => { },	// 监听方法
		onPlus: () => { },	// 监听方法
		onMinus: () => { },	// 监听方法
		isCanChange: () => { return true; }
	}
	plus() {
		if (this.props.isCanChange() == false) return;
		let value = (this.state.value == null || this.state.value.trim() == "" ? this.props.min : parseInt(this.state.value, 10) + this.props.increment)
		value = this._validate(value);
		this._change(value);
		this.props.onPlus && this.props.onPlus(value);
	}
	minus() {
		if (this.props.isCanChange() == false) return;
		let value = (this.state.value == null ? this.props.min : parseInt(this.state.value, 10) - this.props.increment);
		value = this._validate(value);
		this._change(value);
		this.props.onMinus && this.props.onMinus(value);
	}
	onChangeText(value) {
		value = this._validate(value);
		if (this.props.isCanChange() == false) {
			value = this.state.value;
		}
		this.setState({
			minusDisabled: value <= this.props.min,
			plusDisabled: value >= this.props.max,
			value: value.toString()
		});
		if (this.props.debounce != 0) {
			this.timer = setTimeout(() => {
				if (value == this.state.value) this.props.onChange(value);
			}, this.props.debounce);
		} else {
			this.props.onChange(value);
		}
	}
	_onBlur() {
		const value = this._validate(this.state.value);
		if (this.props.isCanChange() == false) {
			return;
		}
		this.setState({
			minusDisabled: this.props.minusDisabled || value <= this.props.min,
			plusDisabled: this.props.plusDisabled || value >= this.props.max,
			value: value.toString()
		});
		//失去焦点的时候，如果是空''，则置1,属性nullToOne开启功能
		if (this.props && this.props.nullToOne) {
			if (value.toString().trim() == '') {
				this.setState({ value: '1' });
				this.props.onChange(1);
			}
		}
		this.props.inputProps && this.props.inputProps.onBlur && this.props.inputProps.onBlur(value);
	}
	_change(value) {
		this.setState({
			minusDisabled: value <= this.props.min,
			plusDisabled: value >= this.props.max,
			value: value.toString()
		});
		this.props.onChange(value);
	}
	_validate(value) {
		try {
			value = parseInt(value, 10)
		} catch (ex) {
			console.log(ex)
			value = this.props.min;
		}
		if (isNaN(value)) value = '';
		if (value.toString() != '' && value.toString() != null) {
			value = (value < this.props.min ? this.props.min : value);
			value = (value > this.props.max ? this.props.max : value);
		}
		return value;
	}
	componentWillReceiveProps(nextProps) {
		this.setState({
			minusDisabled: Number(nextProps.value) <= nextProps.min || nextProps.minusDisabled,
			plusDisabled: Number(nextProps.value) >= nextProps.max || nextProps.plusDisabled,
			value: nextProps.value != null ? nextProps.value.toString() : null,
		});
	}
	componentWillUnmount() {
		clearTimeout(this.timer);
	}
	render() {
		const minusImage = this.state.minusDisabled ? this.props.minusDisabledIcon : this.props.minusIcon;
		const plusImage = this.state.plusDisabled ? this.props.plusDisabledIcon : this.props.plusIcon;
		return (
			<View style={[{ flexDirection: 'row' }, this.props.style]}>
				{
					this.state.minusDisabled == true ?
						<TouchableOpacity onPress={() => { }}>
							<Image source={minusImage} style={{ width: Constant.scale(23.5), height: Constant.scale(21) }} />
						</TouchableOpacity>
						:
						<TouchableOpacity ref='minus' onPress={this.minus.bind(this)}>
							<Image source={minusImage} style={{ width: Constant.scale(23.5), height: Constant.scale(21) }} />
						</TouchableOpacity>
				}
				<View style={{ width: Constant.scale(31.5), height: Constant.scale(21), borderTopWidth: Constant.sizeDividerNormal,  borderBottomWidth: Constant.sizeDividerNormal, borderColor: Constant.colorDividerDefault }}>
					<TextInput ref='input'
						keyboardType='numeric'
						underlineColorAndroid={'transparent'}
						style={{ flex: 1, fontSize: Constant.fontSizeNormal, color: '#21202F', textAlign: 'center', paddingVertical: 0, }} autoCorrect={false}
						blurOnSubmit={true}
						editable={(!this.props.minusDisabled || !this.props.plusDisabled)}
						value={this.state.value}
						onChangeText={this.onChangeText.bind(this)}
						onBlur={this._onBlur.bind(this)}
						{...this.props.inputProps}
					/>
				</View>

				{
					this.state.plusDisabled == true ?
						<TouchableOpacity onPress={() => { }}>
							<Image source={plusImage} style={{ width: Constant.scale(23.5), height: Constant.scale(21) }} />
						</TouchableOpacity>
						:
						<TouchableOpacity ref='plus' onPress={this.plus.bind(this)}>
							<Image source={plusImage} style={{ width: Constant.scale(23.5), height: Constant.scale(21), }} />
						</TouchableOpacity>
				}
			</View>
		)
	}
}
