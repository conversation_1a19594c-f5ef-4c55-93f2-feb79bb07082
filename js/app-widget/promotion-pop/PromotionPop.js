/**
 * Created by z<PERSON><PERSON>.ha<PERSON><PERSON> on 2017.6.8.
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    TextInput,
    ScrollView
} from 'react-native';

var { width, height } = Dimensions.get('window');
import { ReactNavComponent, Widget } from 'rn-yunxi';
const { Text, } = Widget;

//模块声名并导出
export default class PromotionPop extends Component {
    //属性声名
    static propTypes = {

    };
    //默认属性
    static defaultProps = {

    };
    //构造函数
    constructor(props) {
        super(props);
        this.state = { //状态机变量声明
        }
    }

    renderScroll(listData) {
        if (listData.length > 5) {
            return (
                <ScrollView style={{ height: 300, width: '100%' }}>
                    {listData.map((gift, i) => {
                        const { name, lowestLimitAmt, stockCount } = gift;
                        let stockText = '（已赠完）'
                        if (stockCount > 0) {
                            stockText = '（赠完即止）'
                        }
                        if (lowestLimitAmt > 0) {
                            title = '满赠';
                            detail = '满' + lowestLimitAmt + '赠' + name + stockText
                        } else {
                            title = '买赠';
                            detail = '购买即赠' + name + stockText
                        }
                        return (
                            <View
                                key={i}
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'flex-start',
                                    width: '100%',
                                    marginTop: Constant.sizeMarginDefault,
                                    borderBottomColor: Constant.colorDivider,
                                    borderBottomWidth: Constant.sizeDividerNormal,
                                    paddingBottom: Constant.sizeMarginDefault,
                                    height: Constant.scale(41),
                                }}
                            >
                                <Text style={{
                                    fontSize: Constant.fontSizeNormal,
                                    borderColor: Constant.colorTxtPrimary,
                                    borderWidth: Constant.sizeDividerNormal,
                                    borderRadius: 5,
                                    padding: 2,
                                    color: Constant.colorTxtPrimary,
                                    marginRight: 5,
                                }}>{title}</Text>
                                <Text
                                    numberOfLines={1}
                                    style={{
                                        color: Constant.colorTxtTitle,
                                        fontSize: Constant.fontSizeNormal,
                                        width: '90%'
                                    }}
                                >{detail}{detail}{detail}</Text>
                            </View>
                        )
                    })}

                </ScrollView>
            )
        } else {
            return (
                <View>
                    {listData.map((gift, i) => {
                        const { name, lowestLimitAmt, stockCount } = gift;
                        let stockText = '（已赠完）'
                        if (stockCount > 0) {
                            stockText = '（赠完即止）'
                        }
                        if (lowestLimitAmt > 0) {
                            title = '满赠';
                            detail = '满' + lowestLimitAmt + '赠' + name + stockText
                        } else {
                            title = '买赠';
                            detail = '购买即赠' + name + stockText
                        }
                        return (
                            <View
                                key={i}
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'flex-start',
                                    width: '100%',
                                    marginTop: Constant.sizeMarginDefault,
                                    borderBottomColor: Constant.colorDivider,
                                    borderBottomWidth: Constant.sizeDividerNormal,
                                    paddingBottom: Constant.sizeMarginDefault,
                                    height: Constant.scale(41),
                                }}
                            >
                                <Text style={{
                                    fontSize: Constant.fontSizeNormal,
                                    borderColor: Constant.colorTxtPrimary,
                                    borderWidth: Constant.sizeDividerNormal,
                                    borderRadius: 5,
                                    padding: 2,
                                    color: Constant.colorTxtPrimary,
                                    marginRight: 5,
                                }}>{title}</Text>
                                <Text
                                    numberOfLines={1}
                                    style={{
                                        color: Constant.colorTxtTitle,
                                        fontSize: Constant.fontSizeNormal,
                                        width: '90%'
                                    }}
                                >{detail}{detail}{detail}</Text>
                            </View>
                        )
                    })}

                </View>
            )
        }
    }

    //渲染模版
    render() {
        const { popTitle, listData } = this.props
        return (
            <View style={styles.container}>
                <View style={{ padding: 10 }}>
                    <Text style={{
                        color: Constant.colorTxtTitle,
                        fontSize: Constant.fontSizeNormal,
                    }}>{popTitle}</Text>
                </View>
                    {this.renderScroll(listData)}
            </View>
        );
    }



};

const styles = StyleSheet.create({
    container: {
        width: '100%',
        borderTopWidth: Constant.sizeDividerNormal,
        borderColor: Constant.colorDivider,
        backgroundColor: 'white',
        alignItems: 'center',
        paddingLeft: Constant.sizeMarginDefault,
        paddingRight: Constant.sizeMarginDefault,
        backgroundColor: 'white'
    },

});
