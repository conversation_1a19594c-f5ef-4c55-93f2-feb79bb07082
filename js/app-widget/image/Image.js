/**
 * Created by lu.jiarong on 2017/5/2.
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    Text,
} from 'react-native';
import { Widget } from 'rn-yunxi';

const { width, height } = Dimensions.get('window');
const DEFAULT_IMAGE = require('../../module/img/img_default.png');

//模块声名并导出
export default class Image extends Component {

    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
    }


    //渲染
    render() {
        return (
            <Widget.ImageView
                defaultImageSrc={DEFAULT_IMAGE}
                defaultBackgroundColor={"#dddddd"}
                {...this.props}
            />
        )

    }
};

