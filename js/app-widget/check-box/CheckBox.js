/**
 * react-native-check-box
 * Checkbox component for react native, it works on iOS and Android
 * https://github.com/crazycodeboy/react-native-check-box
 * Email:<EMAIL>
 * Blog:http://jiapenghui.com
 * @flow
 */

import React, { Component } from "react";
import {
  StyleSheet,
  View,
  Image,
  Text,
  TouchableHighlight
} from "react-native";
const CHECK_IMG = require("./selected.png");
const UN_SELECT = require("./unselect.png");

import { Widget } from "rn-yunxi";

/**
 * isChecked 是否选中
 * rightText 左边文字
 */
export default class CheckBox extends Component {
  render() {
    let size = Constant.scale(15);
    if (this.props.boxSize) {
      size = this.props.boxSize;
    }
    return (
      <Widget.CheckBox
        disabled={this.props.disabled != undefined ? this.props.disabled : true}
        checkedImage={
          <Image
            source={CHECK_IMG}
            style={{ width: size, height: size }}
            resizeMode="contain"
          />
        }
        unCheckedImage={
          <Image
            source={UN_SELECT}
            style={{ width: size, height: size }}
            resizeMode="contain"
          />
        }
        {...this.props}
      />
    );
  }
}
