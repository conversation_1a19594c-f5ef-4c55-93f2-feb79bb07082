/**
 * Created by xiaowz on 2017/7/10.
 *
 *
 * 用法
 *  <TextInputView
 *  style={{marginTop: Constant.sizeMarginDefault,marginBottom: Constant.sizeMarginDefault}}
 *  placeholder="请您在此描述问题"
 *  maxLength={500}   （必须）
 *  onChangeText={(text)=>{this.setState({returnRemark: text});}}
 *  remarkText={this.state.returnRemark}  （必须）

 />
 *
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    TextInput,
    Platform
} from 'react-native';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import PropTypes from 'prop-types';
const {width, height} = Dimensions.get('window');
const {Text} =Widget;


//模块声名并导出
export default class TextInputView extends ReactNavComponent {

    //属性声名
    static propTypes = {
        maxLength:PropTypes.any,        //字数
        placeholder:PropTypes.string,   //背景文字
        minHeight:PropTypes.any,   //高度
        style:PropTypes.any,
        onChangeText:PropTypes.any,
        remarkText:PropTypes.any.isRequired,
        inputStyle:PropTypes.any,
        value:PropTypes.any,
        textCount:PropTypes.bool


    };
    //默认属性
    static defaultProps = {


    };
    //构造函数
    constructor(props) {
        super(props);
        //状态机变量声明
        this.state = {
            text: this.props.value ? this.props.value:'',


        };
    }

    //渲染
    //渲染
    render() {
        return (
            <View style={styles.container}>
                <View style={[styles.inputViewStyle,this.props.style,{minHeight:this.props.minHeight}]}>
                    <TextInput
                        style={[styles.inputTextStyle,this.props.inputStyle,{minHeight:this.props.minHeight}]}
                        placeholder={this.props.placeholder ? this.props.placeholder :'请输入'}
                        multiline={true}
                        paddingVertical={0}
                        selectionColor = {Constant.colorPrimary}
                        textAlignVertical={'top'}
                        placeholderTextColor={'#b2b2b2'}
                        underlineColorAndroid={'transparent'}
                        maxLength={this.props.maxLength ? this.props.maxLength :100}
                        defaultValue = {this.state.text}
                        onChangeText={
                            this.props.onChangeText ?
                                this.props.onChangeText
                                :
                                (text) => {
                                    this.setState({
                                        text: text
                                    })
                                }

                        }


                    />
                    {
                        this.props.textCount ?
                            <Text style={{position: 'absolute', bottom: 5, right: 10, fontSize: 14}}>{this.props.remarkText ? this.props.remarkText.length :this.state.text.length}/{this.props.maxLength?this.props.maxLength :100}</Text>
                            :
                            null
                    }
                </View>
            </View>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        // justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#FFFFFF',
    },
    inputViewStyle: {
        width: width - 2*Constant.sizeMarginDefault,
        minHeight: Constant.scale(100),
        // backgroundColor: 'pink',
        padding: Constant.sizeMarginDefault,
        borderWidth: 1,
        borderColor: Constant.colorDivider,
        borderRadius: 4,
        paddingBottom: Constant.scale(30)

    },
    inputTextStyle: {

        // flex:1,
        fontSize: 14,
        color: Constant.colorTxtContent,
        width: '100%',
        minHeight:  Constant.scale(100),
        // padding: 0,

    }
});
