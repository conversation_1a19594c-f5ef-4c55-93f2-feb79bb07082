'use strict';


/**
 *
 *
 *
 *
 * 点击打开弹窗
<TouchableOpacity
    onPress={() => {
        this.zoomView && this.zoomView.showModel(index)
    }}
    key={index}
>

//使用
 <ZoomModel
    images={this.state.data.imgs}
    ref={(zoomView) => { this.zoomView = zoomView; }}
/>

 */

import React, { Component } from 'react'
import {
    View,
    StyleSheet,
    Image,
    Modal,
    TouchableOpacity
} from 'react-native';
import { ReactNavComponent, Widget,  } from 'rn-yunxi';
import PropTypes from 'prop-types';
const { Text,ImageViewer } = Widget;
//import ImageViewer from 'react-native-image-zoom-viewer';
const DEFAULT_IMAGE =require( '../../module/img/img_default.png');

export default class ZoomModel extends Component {
    static defaultProps = {
        images: ['https://avatars2.githubusercontent.com/u/7970947?v=3&s=460',],
    };
    static propTypes = {
        nowState: PropTypes.number,
        types: PropTypes.array,
        index: PropTypes.number
    };

    constructor(props) {
        super(props);
        this.state = {
            zoomModel: false,
            index: 0
        };
    }

    showModel(index = 0) {
        this.setState({ zoomModel: true, index })
    }

    closeModel() {
        this.setState({ zoomModel: false })
    }

    renderZoomImage(imgs) {
        if (this.props.images && this.props.images.length) {
            let images = [];
            for (let url of this.props.images) {
                images.push({ url })
            }
            return (
                <ImageViewer
                    onClick={() => {
                        this.closeModel()
                    }
                    }
                    onCancel={() => this.closeModel()}
                    imageUrls={images}
                    index={this.state.index}
                    loadingRender={()=>{return(<Image source={DEFAULT_IMAGE}/>)}}
                />
            )
        } else {
            return <View />
        }

    }


    render() {
        return (
            <Modal
                animationType={"fade"}
                transparent={false}
                visible={this.state.zoomModel}
                onRequestClose={() => this.closeModel()}
            >
                {this.renderZoomImage()}
                {false && <TouchableOpacity
                    style={{
                        position: 'absolute',
                        top: 20,
                        left: 20,
                    }}
                    onPress={() => { this.closeModel() }}
                >
                    <Image
                        source={DEFAULT_IMAGE}
                        style={{ width: 27, height: 27 }}
                    />
                </TouchableOpacity>}
            </Modal>
        )



    }
};

const styles = StyleSheet.create({
    topView: {
        flexDirection: 'row',
        flex: 1,
        paddingLeft: 20,
        paddingRight: 20,
        backgroundColor: 'white'
    },
    image: {
        width: 20,
        height: 20
    },

});


