/**
 *
 * Created by xiaowz on 2018/5/8.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    Image,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    ListView,
    Platform,
    Modal
} from 'react-native';
import {ReactNavComponent, Widget} from 'rn-yunxi';

const {width, height} = Dimensions.get('window');
const {Text, CommonListView, Button,CaptchaView} = Widget;
import PropTypes from 'prop-types';
//模块声名并导出
export default class ModalCaptchaView extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {
        modalVisible:PropTypes.bool.isRequired,    //显示隐藏
        title: PropTypes.string,        //标题             必须
        content: PropTypes.any, //内容
        // inputText: PropTypes.any.isRequired,    //输入框内容        必须
        cancelClick: PropTypes.func,    //取消方法          必须
        defaultClick: PropTypes.func,   //确定方法          必须
        // onChangeText:PropTypes.func.isRequired     // 输入完成回调方法  必须

    };
    //默认属性
    static defaultProps = {
        modalVisible:false
    };

    //构造函数
    constructor(props) {
        super(props);
        //状态机变量声明
        this.state = {
            captchaStr:'', //组件返回的str
            inputStr:'', //输入的str
            // modalVisible:false,
            showErrorMessage:false
        };
    }

    componentWillReceiveProps(nextProps) {
        this.setState({
            inputStr:'', //输入的str
            showErrorMessage:false
        })
    }

    //渲染
    render() {
        return (

            <Modal
                visible={this.props.modalVisible}
                animationType={'fade'}
                transparent={true}
                onRequestClose={() => {

                }}>
                <TouchableOpacity activeOpacity={1} onPress={()=>this.closekeyBoardClick()} style={styles.modalViewStyle}>
                    <View style={styles.dialogViewStyle}>
                        <Text style={{
                            marginTop: Platform.OS === 'ios' ? Constant.scale(25) : Constant.scale(15),
                            fontSize: 18,
                            fontWeight: '500'
                        }}>
                            {this.props.title ? this.props.title : '请输入验证码'}
                        </Text>

                        <View style={{
                            flexDirection:'row',
                            alignItems:'center',
                            padding:Constant.sizeMarginDefault
                        }}>
                        <TextInput
                            ref = "testInput"
                            style={styles.textInputStyle}
                            placeholder="请输入"
                            underlineColorAndroid={'transparent'}
                            maxLength={30}
                            onChangeText={
                                (text)=>{
                                  this.setState({
                                      inputStr:text
                                  })
                                }
                            }
                        />
                            <CaptchaView onRegionChange={(event)=>{
                                this.setState({
                                    captchaStr:event.checkCode
                                })
                            }}>

                            </CaptchaView>

                        </View>

                        <View style={{minHeight:Constant.scale(15)}}>
                        {
                            this.state.showErrorMessage ?
                                <Text style={{textAlign:'center',fontSize:Constant.fontSizeSmall,color:Constant.colorPrimary}}>请输入正确的验证密码</Text> :null
                        }
                        </View>
                        <View
                            style={{
                                width: '100%',
                                height: Constant.sizeDividerNormal,
                                marginTop: Constant.scale(5),
                                backgroundColor: 'rgba(65,65,65,0.4)'
                            }}>
                        </View>

                        <View style={{flex: 1, flexDirection: 'row', justifyContent: 'space-between'}}>
                            <TouchableOpacity style={{
                                width: '49.99%',
                                height: Constant.scale(45),
                                justifyContent: 'center',
                                alignItems: 'center'
                            }} onPress={() => {
                                this.cancelBtnClick()
                            }}>
                                <Text style={{fontSize: 18, color: '#333333'}}>取消</Text>
                            </TouchableOpacity>

                            <View style={{
                                width: Constant.sizeDividerNormal,
                                height: '100%',
                                backgroundColor: 'rgba(65,65,65,0.4)'
                            }}></View>

                            <TouchableOpacity style={{
                                width: '49.5%',
                                height: Constant.scale(45),
                                justifyContent: 'center',
                                alignItems: 'center'
                            }} onPress={() => this.defaultBtnClick()}>
                                <Text style={{fontSize: 18, color: '#FA3D4F'}}>
                                    确定
                                </Text>
                            </TouchableOpacity>
                        </View>


                    </View>
                </TouchableOpacity>
            </Modal>
        );
    }

    cancelBtnClick = () => {
        if (this.props.cancelClick) {
            this.props.cancelClick();
        }


    }

    closekeyBoardClick = ()=>{
        this.refs.testInput.blur();
    }
    defaultBtnClick = () => {
        // Log('======',typeof this.state.captchaStr)

        if (this.state.inputStr != '' &&this.state.captchaStr != ''&&((this.state.inputStr).toLowerCase()== (this.state.captchaStr).toLowerCase())){
            this.setState({
                showErrorMessage:false
            })
            if (this.props.defaultClick) {
                this.props.defaultClick();
            }
        }else {
            this.setState({
                showErrorMessage:true
            })
        }
    }

};
const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    modalViewStyle: {
        marginTop: 0,
        marginLeft: 0,
        width: width,
        height: height,
        backgroundColor: 'rgba(0,0,0,0.4)',
        alignItems: 'center',
    },
    dialogViewStyle: {
        marginTop: height * 0.35,
        width: '80%',
        height: Constant.scale(170),
        backgroundColor: 'rgba(255,255,255,1)',
        borderRadius: Platform.OS === 'ios' ? Constant.scale(5) : Constant.scale(5),
        alignItems: 'center',
        justifyContent: 'center'

    },

    textInputStyle: {
        // marginLeft: Constant.scale(15),
        marginRight: Constant.scale(15),
        // width: Constant.scale(270),
        height: Constant.scale(36),
        // marginTop: Constant.scale(15),
        borderWidth: Constant.sizeDividerNormal,
        borderColor: 'rgb(145,145,145)',
        borderRadius: Constant.scale(4),
        padding: Constant.scale(8),
        fontSize: 14,
        flex:1
    }
});
