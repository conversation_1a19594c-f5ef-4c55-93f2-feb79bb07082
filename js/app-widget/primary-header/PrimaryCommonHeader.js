/**
 * Created by xiaowz on 2017/6/13.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    Image,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    ListView,
    Platform
} from 'react-native';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import {SafeAreaView} from 'react-navigation'
const {width, height} = Dimensions.get('window');
const Text = Widget.Text;
import PropTypes from 'prop-types';
const BackIcon = require('./ic_red_back.png');
//模块声名并导出
import { withNavigation } from 'react-navigation';
@withNavigation
export default class PrimaryCommonHeader extends Component {
    //属性声名
    static propTypes = {
        navigation: PropTypes.any,
        showBackAction: PropTypes.bool,
        backAction: PropTypes.func,
        title: PropTypes.string,
        defaultStatusBar: PropTypes.bool,
        rightTitle: PropTypes.string,
        rightAction: PropTypes.func,
        contentView:PropTypes.element,
        rightIcon: PropTypes.element,

    };
    //默认属性
    static defaultProps = {
        title: '',
        defaultStatusBar:false,
        showBackAction:true,


    };
    //构造函数
    constructor(props) {
        super(props);
        this.state = { //状态机变量声明
        }
    }

    //渲染
    render() {
        return (
            <SafeAreaView style={{flex:1,backgroundColor:'white'}}>

            <View style={styles.container}>
                <View style={[styles.headerView,this.props.style]}>
                    {Platform.OS === 'ios' ? <StatusBar barStyle={this.props.defaultStatusBar ? 'default' : 'light-content'} /> :
                        <StatusBar backgroundColor={'transparent'} barStyle={'dark-content'} translucent={true} />
                    }
                    <View  style={styles.header}>
                        <View style={styles.titleView}>
                            <Text style={styles.title}>{this.props.title}</Text>
                        </View>
                        {this.props.showBackAction?<TouchableOpacity
                            onPress={() => {
                                if (this.props.backAction) {
                                    this.props.backAction();

                                } else {
                                    if (this.props.navigation) {
                                        this.props.callback && this.props.callback()
                                        this.props.navigation.goBack();
                                    }
                                }
                            }}>
                            <View style={styles.backView}>
                                <Image resizeMode={'contain'} style={{width:Constant.scale(12),height:Constant.scale(20)}} source={BackIcon}/>
                            </View>
                        </TouchableOpacity>:<View/>}
                        <View style={styles.contentView}>
                            {this.props.contentView ? this.props.contentView : null}
                        </View>

                        {this.props.rightTitle ? <TouchableOpacity onPress={() => {
                            if (this.props.rightAction) {
                                this.props.rightAction();
                            }
                        }}>
                            <View style={styles.rightView}>
                                <Text style={this.props.rightTextStyle}>{this.props.rightTitle}</Text>
                            </View>
                        </TouchableOpacity> : null}
                        {this.props.rightIcon}
                    </View>
                </View>
                {/*<View style={GlobalStyle.styleDividerDefault}/>*/}
                {
                    this.props.children
                }
            </View>
            </SafeAreaView>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Constant.colorBackgroundDefault
    },

    headerView: {
        backgroundColor: Constant.colorPrimary,
        height: Constant.sizeHeader+(Platform.OS=='android'?StatusBar.currentHeight:0),
        borderBottomWidth:Constant.sizeDividerNormal,
        borderColor:Constant.colorDivider
    },
    header: {
        // justifyContent: 'space-between',
        width: width,
        marginTop: Constant.sizeHeaderMarginTop+(Platform.OS=='android'?StatusBar.currentHeight:0),
        flexDirection: 'row',
        height: Constant.sizeHeaderContent,
        // resizeMode:'contain'
    },
    contentView:{
        justifyContent:'center',
        flex:1
    },
    titleView: {
        left: 0,
        top: 0,
        height: Constant.sizeHeaderContent,
        position: 'absolute',
        width: width,
        justifyContent: 'center',
        alignItems: 'center'
    },
    title: {
        fontSize: Constant.fontSizeCaption,
        color:'white'
    },
    backView: {
        justifyContent: 'center',
        paddingLeft: 10,
        width: 34,
        flex: 1,
    },
    rightView: {
        flex: 1,
        justifyContent: 'center',
        paddingRight: 10,
        alignItems: 'center',
        minWidth: 50
    }
});