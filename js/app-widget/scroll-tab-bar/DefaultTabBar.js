import React, { Component } from 'react';
import {
    ViewPropTypes,
    StyleSheet,
    View,
    TouchableOpacity,
    TouchableNativeFeedback,
    Platform,
    Animated
} from 'react-native';
import PropTypes from 'prop-types';
import { ReactNavComponent, Widget } from 'rn-yunxi';
const { Text } = Widget;

export default class DefaultTabBar extends Component {

    static propTypes = {
        goToPage: PropTypes.func,
        activeTab: PropTypes.number,
        tabs: PropTypes.array,
        backgroundColor: PropTypes.string,
        activeTextColor: PropTypes.string,
        inactiveTextColor: PropTypes.string,
        textStyle: Text.propTypes.style,
        tabStyle: ViewPropTypes.style,
        renderTab: PropTypes.func,
        underlineStyle: ViewPropTypes.style,
    }

    static defaultProps = {
        activeTextColor: Constant.colorTxtPrimary,
        inactiveTextColor: '#666666',
        backgroundColor: null,
    }


    renderTab(name, page, isTabActive, onPressHandler) {
        const { activeTextColor, inactiveTextColor, textStyle, } = this.props;
        const textColor = isTabActive ? activeTextColor : inactiveTextColor;
        const fontWeight = isTabActive ? 'bold' : 'normal';
        const viewStyle = styles.tab;
        const defalutTextStyle = isTabActive ? [styles.textStyle, {
            borderBottomWidth: 2,
            borderColor: '#DF0522',
            color: textColor,
            //fontWeight
        }] : [styles.textStyle, {
            color: textColor,
            //fontWeight
        }];
        const borderView = isTabActive ? {
            borderBottomWidth: 2,
            borderColor: '#DF0522', height: Constant.scale(44),
            alignItems: 'center',
            justifyContent: 'center',
            paddingTop: Constant.sizeMarginDefault
        } : {
            height: Constant.scale(44),
            alignItems: 'center',
            justifyContent: 'center',
            paddingTop: Constant.sizeMarginDefault
        }


        if (Platform.OS !== 'ios') {
            return <TouchableNativeFeedback
                delayPressIn={0}
                background={TouchableNativeFeedback.SelectableBackground()}
                key={name + page}
                accessible={true}
                accessibilityLabel={name}
                accessibilityTraits='button'
                onPress={() => onPressHandler(page)}
            >
                <View style={viewStyle}>
                    <Text
                        numberOfLines={1}
                        style={[defalutTextStyle, textStyle,]}
                    >
                        {name}
                    </Text>
                </View>
            </TouchableNativeFeedback>
        }

        return <TouchableOpacity
            key={name + page}
            accessible={true}
            accessibilityLabel={name}
            accessibilityTraits='button'
            onPress={() => onPressHandler(page)}
        >
            <View style={viewStyle}>
                <View style={borderView}>
                    <Text
                        numberOfLines={1}
                        style={[defalutTextStyle, textStyle,]}
                    >
                        {name}
                    </Text>
                </View>
            </View>
        </TouchableOpacity>;
    }

    render() {
        return (
            <View style={[styles.tabs, { flexDirection: 'row', borderBottomWidth: Constant.sizeDividerNormal, borderColor: Constant.colorDivider }]}>
                {this.props.tabs.map((name, page) => {
                    const isTabActive = this.props.activeTab === page;
                    const renderTab = this.props.renderTab || this.renderTab;
                    return this.renderTab(name, page, isTabActive, this.props.goToPage);
                })}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    tab: {
        alignItems: 'center',
        justifyContent: 'center',
        //marginLeft: 10,
        flex: 1,
    },
    tabs: {
        height: Constant.scale(44),
        flexDirection: 'row',
        justifyContent: 'space-around',
        borderWidth: 1,
        borderTopWidth: 0,
        borderLeftWidth: 0,
        borderRightWidth: 0,
        borderColor: '#ccc',
        backgroundColor: 'white'
    },
    textStyle: {
        height: Constant.scale(44),
        alignItems: 'center',
        justifyContent: 'center',
        paddingTop: Constant.sizeMarginDefault,
        // fontWeight:'bold'
    },

});