/**
 * Created by whw on 2017/6/14.
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TextInput,
    TouchableOpacity,
    Image,
    Platform
} from 'react-native';
import { Widget } from 'rn-yunxi';
const { width, height } = Dimensions.get('window');
const Text = Widget.Text;
import PropTypes from 'prop-types';
//模块声名并导出
export default class CommonSearchBar extends Component {
    //属性声名
    static propTypes = {
        onChange: PropTypes.func,//文本改变方法
        text: PropTypes.string,//文本值
        clear: PropTypes.func,//清除方法
        onFilter: PropTypes.func,//取消方法
        onSearch: PropTypes.func,//提交
        placeholder: PropTypes.string,
        placeholderTextColor: PropTypes.string,
        editable: PropTypes.bool,
    };
    //默认属性
    static defaultProps = {
        isOnFocus: false, //是否自动聚焦
        editable: true,//是否能编辑
        cancelBtnStyle: {},//搜索取消按钮样式
        textInputStyle: {},//输入框样式
    };

    componentWillReceiveProps(nextProps) {

        this.setState({
            text: nextProps.text
        })

    }

    //构造函数
    constructor(props) {
        super(props);
        this.state = { //状态机变量声明
            text: '',
            focus: false
        }

        this.textInput = null;
    }

    onChange = (text) => {
        this.setState({ text: text }, () => {
            if (this.props.onChange) {
                this.props.onChange(this.state.text);
            }
        });

    }

    //清除按钮
    clear = () => {
        this.setState({ text: '' });
        if (this.props.clear) {
            this.props.clear('');
        }
    }

    //搜索
    onSearch = (text) => {
        this.textInput.blur();
        if (this.props.onSearch) {
            this.props.onSearch(text);
        }
    }

    componentDidMount() {

    }

    //筛选
    onFilter = () => {
        this.textInput.blur();
        if (this.props.onFilter) {
            this.props.onFilter();
        }
    }

    //聚焦
    onFocus = () => {
        this.setState({ focus: true },()=>{
            this.props.onFocus &&  this.props.onFocus()
        });
    }

    //失去焦点
    onBlur = () => {
        this.setState({ focus: false });
    }
    //
    onPress = () => {
        if (this.props.onPress) {
            this.props.onPress();
        }
    }

    //搜索按钮点击
    onSearchBtnClick = ()=>{
        this.textInput.blur();
        this.props.onSearchBtnClick &&  this.props.onSearchBtnClick()
    }
    //渲染
    render() {
        return (
            <View
                style={[{ margin: 10, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }, this.props.style]}>
                {
                    this.props.onPress ? <TouchableOpacity
                        style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}
                        onPress={this.onPress}
                    >
                        <View style={[styles.searchBarTxtInputStyle, { flex: 10 }, this.props.searchBarTxtInputStyle]}>
                            <Image style={{ width: 13, height: 13, marginLeft: 10 }} source={require('./ic_search.png')} />
                            <Text style={[{ flex: 10, marginLeft: 10, fontSize: 13 ,color: this.props.placeholderTextColor ? this.props.placeholderTextColor : Constant.colorTxtAlert}, this.props.textInputStyle]}>{this.props.placeholder}</Text>
                        </View>
                    </TouchableOpacity> :
                        <View style={{ flex: 1, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                            <View style={[styles.searchBarTxtInputStyle, { flex: 10 }, this.props.searchBarTxtInputStyle]}>
                                <Image style={{ width: 13, height: 13, marginLeft: 10 }}
                                    source={require('./ic_search.png')} />
                                <TextInput
                                    style={[{ flex: 10, marginLeft: 10, fontSize: 13 }, Platform.OS == 'os' ? {} : { padding: 0 }, this.props.textInputStyle]}
                                    ref={(ref) => { this.textInput = ref; }}
                                    underlineColorAndroid="transparent"
                                    onChangeText={(text) => { this.onChange(text) }}
                                    value={this.state.text}
                                    selectionColor={this.props.selectionColor || '#FA3D4F'}
                                    autoFocus={this.props.isOnFocus}
                                    editable={this.props.editable}
                                    onFocus={this.onFocus}
                                    onBlur={this.onBlur}
                                    placeholder={this.props.placeholder}
                                    placeholderTextColor={this.props.placeholderTextColor}
                                    maxLength={this.props.maxLength || 1000}
                                />
                                {this.state.text == '' ? null : <TouchableOpacity
                                    style={{ alignItems: 'center', justifyContent: 'center', marginRight: 10 }}
                                    onPress={this.clear}>
                                    <Image style={{ width: 15, height: 15 }} source={require('./ic_clear.png')} />
                                </TouchableOpacity>}
                            </View>
                            <TouchableOpacity style={{ alignItems: 'center' }}
                                onPress={
                                    () => this.onSearchBtnClick(this.state.text)}>
                                <Text style={[{ fontSize: 14, color: '#777', width: 35, marginLeft: 10 }, this.props.cancelBtnStyle]}>
                                    {/*{this.state.focus || (this.state.text && this.state.text.length > 0) ? '搜索' : '搜索'}*/}
                                    搜索
                                </Text>
                            </TouchableOpacity>
                        </View>
                }
            </View>
        );
    }
};


const styles = StyleSheet.create({
    searchBarTxtInputStyle: {
        backgroundColor: 'rgb(247,247,247)',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 40,
        borderColor: Constant.colorDivider,
        borderWidth: Constant.sizeDividerNormal,
        height: 27,
    }
});