/**
 * Created by whw on 2017/11/13.
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    Image,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    ListView,
    Platform
} from 'react-native';
import { ReactNavComponent, Widget } from 'rn-yunxi';
import CodeInput from './CodeInput';

const { width, height } = Dimensions.get('window');
const { Text, CommonListView, Button } = Widget;
import PropTypes from 'prop-types';
//模块声名并导出
export default class OrderButton extends ReactNavComponent {

    //属性声名
    static propTypes = {
        confirm: PropTypes.func,
        cancel: PropTypes.func,
        title: PropTypes.string,
        codeSize: PropTypes.number,
        codeLength: PropTypes.number,
        space: PropTypes.number,
        codeInputStyle: PropTypes.any
    };
    //默认属性

    static defaultProps = {
        btnStyle: 0,
    };
    //构造函数
    constructor(props) {
        super(props);
        //状态机变量声明
        this.state = {
            codeLength: this.props.codeLength ? this.props.codeLength : 4,
            deliveryCode: ''
        };
    }

    cancel = ()=> {
        if (this.props.cancel)
        {
            this.props.cancel();
        }
    }

    confirm = ()=> {
        if (this.state.deliveryCode < this.state.codeLength)
        {
            return ;
        }
        if (this.props.confirm)
        {
            this.props.confirm(this.state.deliveryCode);
        }
    }

    //渲染
    render() {


        return (
            <View
                activeOpaticy={1}
                style={{width: width, height: height, alignItems:'center', marginTop:Constant.scale(200)}}>
                <View style={{width: Constant.scale(280), height: Constant.scale(162), backgroundColor:'#FFFFFF'}}>
                    <Text
                        style={{fontWeight:'600',color:Constant.colorTxtTitle, fontSize:16,marginTop:Constant.scale(15), width:'100%', textAlign:'center'}}>{this.props.title ? this.props.title : '请输入提货码'}</Text>
                    <View style={{flex:1, justifyContent:'center'}}>
                        <CodeInput
                             keyboardType={'default'}
                            activeColor='#999999'
                            inactiveColor='#999999'
                            autoFocus={true}
                            ignoreCase={true}
                            inputPosition='center'
                            size={this.props.codeSize ? this.props.codeSize : 45}
                            codeLength={this.state.codeLength}
                            space={this.props.space ? this.props.space : 10}
                            onFulfill={(code) => {this.setState({deliveryCode: code})}}
                            codeInputStyle={[{ borderWidth: 1.5, color:'#333'},this.props.codeInputStyle]}
                        />
                    </View>
                    <View style={{width:'100%', height: 0.5, backgroundColor:Constant.colorDivider}}/>
                    <View style={{width: '100%', height: Constant.scale(40), flexDirection:'row'}}>
                        <Button
                            onPress={this.cancel}
                            underlayColor={'#FFFFFF'}
                            underlayTxtColor={Constant.colorTxtTitle}
                            txtStyle={{color: Constant.colorTxtTitle, fontSize: 16, textAlign: 'center'}}
                            style={{
                            height: '100%', width:'49.25%', backgroundColor: '#FFFFFF',
                        }}

                        >
                            取消
                        </Button>
                        <View style={{width:0.5, height: '100%', backgroundColor:Constant.colorDivider}}/>
                        <Button
                            onPress={this.confirm}
                            underlayColor={'#FFFFFF'}
                            underlayTxtColor={Constant.colorTxtPrimary}
                            txtStyle={{color: Constant.colorTxtPrimary, fontSize: 16, textAlign: 'center'}}
                            style={{
                            height: '100%', width:'49.25%', backgroundColor: '#FFFFFF',
                        }}
                        >
                            确定
                        </Button>
                    </View>
                </View>

            </View>

        );
    }

};
const styles = StyleSheet.create({
    container: {
        flex: 1,
    }
});