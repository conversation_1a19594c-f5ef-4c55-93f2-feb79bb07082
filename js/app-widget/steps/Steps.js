'use strict';


// <Steps
//    nowState={1}
//    types: ['提交审核', '同意退款', '退款完成'],
// />

import React, { Component } from 'react'
import {
    View,
    StyleSheet,
    Image,
} from 'react-native';
import { ReactNavComponent, Widget } from 'rn-yunxi';
import PropTypes from 'prop-types';
const { Text, } = Widget;
import SEL from './selected.png';
const GRAY = '#e6e6e6'

export default class Steps extends Component {
    static defaultProps = {
        types: ['提交审核', '同意退款', '退款完成'],
        nowState: 0,
        lineHeight: 3,
        lineColor: Constant.colorTxtGreen,
        textColor: Constant.colorTxtGreen,
        pointSize: 10,
        imageSize: 18,
        imageSource: SEL,
    };
    static propTypes = {
        nowState: PropTypes.number,
        types: PropTypes.array,
    };

    constructor(props) {
        super(props);
        this.state = {
        };
    }

    render() {
        const { types, nowState, lineHeight, lineColor, textColor, pointSize, imageSource, imageSize } = this.props;
        return (
            <View style={styles.topView}>
                {types.map((obj, i) => {
                    let pointView = <Image
                        style={{ width: imageSize, height: imageSize }}
                        source={imageSource}
                    />;
                    let lineStyle = {
                        height: lineHeight,
                        flex: 1,
                        backgroundColor: lineColor,
                    };
                    let textStyle = {
                        color: textColor,
                        fontSize: Constant.fontSizeXSmall,
                    }
                    if (this.props.nowState < i) {
                        textStyle = {
                            color: GRAY,
                            fontSize: Constant.fontSizeXSmall,
                        }
                    }
                    if (this.props.nowState < i) {
                        pointView = <View style={{ backgroundColor: GRAY, width: pointSize, height: pointSize, borderRadius: pointSize / 2 }} />
                    } else if (this.props.nowState > i) {
                        pointView = <View style={{ backgroundColor: textColor, width: pointSize, height: pointSize, borderRadius: pointSize / 2 }} />
                    }
                    let leftLine = <View style={lineStyle} />
                    let rightLine = leftLine = <View style={lineStyle} />
                    if (i === 0) {
                        leftLine = <View style={[lineStyle, { backgroundColor: 'white' }]} />
                    } else if (this.props.nowState < i) {
                        leftLine = <View style={[lineStyle, { backgroundColor: GRAY }]} />
                    }

                    if (types.length - 1 === i) {
                        rightLine = <View style={[lineStyle, { backgroundColor: 'white' }]} />
                    } else if (this.props.nowState - 1 < i) {
                        rightLine = <View style={[lineStyle, { backgroundColor: GRAY }]} />
                    }

                    return (
                        <View
                            key={i}
                            style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                flex: 1
                            }}
                        >
                            <View
                                style={{
                                    flexDirection: 'row',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    minHeight: 50
                                }}>
                                {leftLine}
                                {pointView}
                                {rightLine}
                            </View>
                            <Text style={textStyle}>{obj}</Text>
                        </View>
                    )
                })}
            </View>
        )
    }
};

const styles = StyleSheet.create({
    topView: {
        flexDirection: 'row',
        flex: 1,
        paddingLeft: 20,
        paddingRight: 20,
        backgroundColor: 'white'
    },
    image: {
        width: 20,
        height: 20
    },

});