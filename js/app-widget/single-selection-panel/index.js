/**
 * Created by lao<PERSON>ji<PERSON><PERSON> on 2017/5/2.
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    Dimensions
} from 'react-native';
import { Widget } from 'rn-yunxi';
const { width, height } = Dimensions.get('window');
const Text = Widget.Text;
import PropTypes from 'prop-types';
//模块声名并导出
export default class SingleSelectionPanel extends Component {
    //属性声名
    static propTypes = {
        title: PropTypes.string,
        data: PropTypes.array,
        disableIndex: PropTypes.array,
        selectIndex: PropTypes.number,
        onSelectItem: PropTypes.func,

    };
    //默认属性
    static defaultProps = {
        title: "大小",
        data: ['sm', 'm', 'l', 'xl', 'xxl'],
        selectIndex: -1,
        disableIndex: [],

    };
    //构造函数
    constructor(props) {
        super(props);
        this.state = { //状态机变量声明
            selectIndex: this.props.selectIndex,
        }
    }

    componentWillReceiveProps(nextProps){

        if(nextProps.selectIndex != this.state.selectIndex && nextProps.selectIndex == -1){ //
            this.setState({
                selectIndex: nextProps.selectIndex
            })
        }
    }

    //渲染
    render() {
        const { data, selectIndex } = this.props;
        return (
            <View>
                <Text style={styles.title}>{this.props.title}</Text>
                <View style={styles.container}>
                    {
                        !data ? null : data.map((item, index) => {
                            let disable = this.props.disableIndex.indexOf(index) > -1;
                            return (
                                <Widget.Button
                                    onPress={() => {
                                        if (!disable) {
                                            if (this.state.selectIndex != index) {
                                                this.setState({ selectIndex: index }, () => {
                                                if (this.props.onSelectItem) {
                                                    this.props.onSelectItem(item.id, this.state.selectIndex, true);
                                                }
                                                });

                                            } else {
                                                this.setState({ selectIndex: -1 }, () => {
                                                if (this.props.onSelectItem) {
                                                    this.props.onSelectItem(item.id, this.state.selectIndex, false);
                                                }
                                                });

                                            }
                                        }
                                    }}
                                    key={index}
                                    style={[index == this.state.selectIndex ? styles.itemSelectStyle : styles.itemStyle, disable && styles.itemDisableStyle]}
                                    txtStyle={disable ? styles.itemTxtDisableStyle : index == this.state.selectIndex ? styles.itemTxtSelectStyle : styles.itemTxtStyle}
                                    underlayColor={Constant.colorTxtPrimary}
                                    underlayTxtColor={'white'}
                                > {item.name || item.value}</Widget.Button>
                            )
                        })
                    }
                </View>
            </View >
        );
    }
};

const styles = StyleSheet.create({
    title: {
        marginLeft: Constant.sizeMarginDefault,
        marginBottom: Constant.sizeMarginDefault,
        marginTop: Constant.scale(40),
        color: Constant.colorTxtContent,
        fontSize: Constant.fontSizeSmall,
    },
    container: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        marginLeft: 0,
        marginRight: 10,
        marginTop: Constant.scale(10),
    },
    itemStyle: {
        marginRight: 8,
        marginLeft: 8,
        marginBottom: 8,
        borderColor: Constant.colorLightGray,
        borderRadius: 4,
        borderWidth: Constant.sizeDividerNormal,
        height: 34,
        justifyContent: 'center',
        alignSelf: 'center',
    },
    itemTxtStyle: {
        textAlignVertical: 'center',
        textAlign: 'center',
        color: Constant.colorTxtContent,
        flex: 0,
        fontSize: Constant.fontSizeNormal
    },
    itemSelectStyle: {
        marginRight: 8,
        marginLeft: 8,
        marginBottom: 8,
        borderColor: Constant.colorTxtPrimary,
        borderRadius: 4,
        borderWidth: Constant.sizeDividerNormal,
        height: 34,
        justifyContent: 'center',
        alignSelf: 'center',
    },
    itemTxtSelectStyle: {
        textAlign: 'center',
        color: Constant.colorTxtPrimary,
        flex: 0,
        textAlignVertical: 'center',
        fontSize: Constant.fontSizeNormal
    },
    itemDisableStyle: {
        marginRight: 8,
        marginLeft: 8,
        marginBottom: 8,
        borderColor: Constant.colorTransparent,
        borderRadius: 4,
        borderWidth: Constant.sizeDividerNormal,
        height: 34,
        justifyContent: 'center',
        alignSelf: 'center',
    },
    itemTxtDisableStyle: {
        textAlign: 'center',
        color: Constant.colorLightGray,
        flex: 0,
        textAlignVertical: 'center',
        fontSize: Constant.fontSizeNormal
    },
});
