function changeTwoDecimal_f(x) {
  try {
    let f_x1 = parseFloat(x);
    if (isNaN(f_x1)) {
      //alert('function:changeTwoDecimal->parameter error');
      // Log('function:changeTwoDecimal->parameter error=====' + x)
      return "0.00";
    }
    // Log('f_x1',x,f_x1)
    let f_x = Math.round(x * 100) / 100;
    let s_x = f_x.toString();
    let pos_decimal = s_x.indexOf(".");
    if (pos_decimal < 0) {
      pos_decimal = s_x.length;
      s_x += ".";
    }
    while (s_x.length <= pos_decimal + 2) {
      s_x += "0";
    }
    return s_x;
  } catch (e) {
    console.log("---PriceTextErr--", e);
    return "0.00";
  }
}

/**
 * 加逗号
 * @param {*} s
 */
function addPoint(s) {
  try {
    let l = s
      .split(".")[0]
      .split("")
      .reverse();
    let t = "";
    for (i = 0; i < l.length; i++) {
      t += l[i] + ((i + 1) % 3 == 0 && i + 1 != l.length ? "," : "");
    }
    return t
      .split("")
      .reverse()
      .join("");
  } catch (error) {
    return s;
  }
}

function test(price) {
  let str = null;
  str = changeTwoDecimal_f(price);
  // Log('changeTwoDecimal_f', str);
  try {
    str = str.match(/(-)?(\d*)\.(\d*)/);
    // Log('match', str);
  } catch (error) {}
  // Log('after', price, str);
  console.log("====toMoney====", price, `${addPoint(str[2])}.${str[3]}`);
}

test(31.5);
test(42);
test(38);
