/**
 * Created by lu.jiarong on 2017/5/2.
 *
 import AppWidget from '../../app-widget';
 const {  PriceText } = AppWidget;

 <PriceText
 price={'123.45'}
 size={1}
 />

 size={1}    红色大字
 ={2}    红色小字
 ={3}    灰色小字
 ={4}    白色 14号
 *
 */
import React, { Component } from "react";
import { StyleSheet, View, Dimensions } from "react-native";
import UserStore from "../../store/User";
import { Widget } from "rn-yunxi";
import { inject, observer } from "mobx-react/native";

const { Text } = Widget;
const { width, height } = Dimensions.get("window");
//模块声名并导出
export default class PriceText extends Component {
  //默认属性
  static defaultProps = {
    price: 0.0,
    size: 1,
    priceSize: null,
    rmbSize: null,
    unit: null,
    textStyle: null
  };

  //构造函数
  constructor(props) {
    super(props);
    this.state = {
      //状态机变量声明
      unit: this.props.unit ? this.props.unit : UserStore.getSymbol()
    };
  }

  changeTwoDecimal_f(x) {
    try {
      let f_x1 = parseFloat(x);
      if (isNaN(f_x1)) {
        //alert('function:changeTwoDecimal->parameter error');
        // Log('function:changeTwoDecimal->parameter error=====' + x)
        return "0.00";
      }
      // Log('f_x1',x,f_x1)
      let f_x = Math.round(x * 100) / 100;
      let s_x = f_x.toString();
      let pos_decimal = s_x.indexOf(".");
      if (pos_decimal < 0) {
        pos_decimal = s_x.length;
        s_x += ".";
      }
      while (s_x.length <= pos_decimal + 2) {
        s_x += "0";
      }
      return s_x;
    } catch (e) {
      console.log("---PriceTextErr--", e);
      return "0.00";
    }
  }

  /**
   * 加逗号
   * @param {*} s
   */
  addPoint(s) {
    try {
      let l = s
        .split(".")[0]
        .split("")
        .reverse();
      let t = "";
      for (i = 0; i < l.length; i++) {
        t += l[i] + ((i + 1) % 3 == 0 && i + 1 != l.length ? "," : "");
      }
      return t
        .split("")
        .reverse()
        .join("");
    } catch (error) {
      return s;
    }
  }

  //渲染
  render() {
    const { price, size, priceSize, rmbSize } = this.props;

    let rmbStyle = null;
    let priceStyle = null;
    if (priceSize && rmbSize) {
      rmbStyle = rmbSize;
      priceStyle = priceSize;
    } else if (size === 1) {
      rmbStyle = styles.rmbBig;
      priceStyle = styles.priceBig;
    } else if (size === 2) {
      rmbStyle = styles.rmbSmall;
      priceStyle = styles.priceSmall;
    } else if (size === 4) {
      rmbStyle = styles.rmbWhite;
      priceStyle = styles.priceWhite;
    } else if (size === 5) {
      rmbStyle = styles.rmbBlack;
      priceStyle = styles.priceBlack;
    } else if (size === 6) {
      rmbStyle = styles.rmbGray;
      priceStyle = styles.priceGray;
    } else if (size === 7) {
      rmbStyle = styles.rmbGraySmall;
      priceStyle = styles.priceGraySmall;
    } else if (size === 8) {
      rmbStyle = styles.rmbAlert;
      priceStyle = styles.priceAlert;
    } else {
      rmbStyle = styles.rmbAlertSmall;
      priceStyle = styles.priceAlertSmall;
    }
    // if (price < 0) {
    //     return (
    //         <Text
    //             style={[rmbStyle, this.props.textStyle]}>
    //             {this.props.unit}
    //         <Text
    //             style={[priceStyle, this.props.textStyle]}>0</Text></Text>
    //     )
    // }
    let str = null;
    str = this.changeTwoDecimal_f(price);
    // Log('changeTwoDecimal_f', str);
    try {
      str = str.match(/(-)?(\d*)\.(\d*)/);
      // Log('match', str);
    } catch (error) {}
    // Log('after', price, str);
    return (
      <Text
        style={[rmbStyle, this.props.textStyle, { fontFamily: "System" }]}
        allowFontScaling={false}
      >
        {" "}
        {this.state.unit} {str[1]}{" "}
        <Text
          allowFontScaling={false}
          style={[priceStyle, this.props.textStyle]}
        >
          {this.addPoint(str[2])}.
        </Text>
        {str[3]}
      </Text>
    );
  }
}

const styles = StyleSheet.create({
  rmbBig: {
    color: Constant.colorTxtPrimary,
    fontSize: Constant.fontSizeNormal
  },
  priceBig: {
    color: Constant.colorTxtPrimary,
    fontSize: Constant.fontSizeCaption
  },
  rmbSmall: {
    color: Constant.colorTxtPrimary,
    fontSize: Constant.fontSizeSmall
  },
  priceSmall: {
    color: Constant.colorTxtPrimary,
    fontSize: Constant.fontSizeNormal
  },

  rmbGray: {
    color: Constant.colorTxtContent,
    fontSize: Constant.fontSizeNormal
  },
  priceGray: {
    color: Constant.colorTxtContent,
    fontSize: Constant.fontSizeNormal
  },

  rmbGraySmall: {
    color: Constant.colorTxtContent,
    fontSize: Constant.fontSizeSmall
  },
  priceGraySmall: {
    color: Constant.colorTxtContent,
    fontSize: Constant.fontSizeSmall
  },
  rmbWhite: {
    color: "#FFFFFF",
    fontSize: Constant.fontSizeNormal,
    backgroundColor: "transparent"
  },
  priceWhite: {
    color: "#FFFFFF",
    fontSize: Constant.fontSizeNormal,
    backgroundColor: "transparent"
  },
  rmbBlack: {
    color: Constant.colorTxtTitle,
    fontSize: Constant.fontSizeNormal,
    backgroundColor: "transparent"
  },
  priceBlack: {
    color: Constant.colorTxtTitle,
    fontSize: Constant.fontSizeNormal,
    backgroundColor: "transparent"
  },

  rmbAlertSmall: {
    color: Constant.colorTxtAlert,
    fontSize: Constant.fontSizeSmall
  },
  priceAlertSmall: {
    color: Constant.colorTxtAlert,
    fontSize: Constant.fontSizeSmall
  },

  rmbAlert: {
    color: Constant.colorTxtAlert,
    fontSize: Constant.fontSizeNormal
  },
  priceAlert: {
    color: Constant.colorTxtAlert,
    fontSize: Constant.fontSizeNormal
  }
});
