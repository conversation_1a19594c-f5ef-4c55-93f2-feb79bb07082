/**
 * Created by neal on 2017/6/7.
 */

import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    ScrollView,
    Dimensions,
    StatusBar,
    TouchableOpacity,
    InteractionManager,
    Platform,
    UIManager
} from 'react-native';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import PropTypes from 'prop-types';
import CheckBox from '../check-box/CheckBox';
import _ from 'lodash';
import {inject, observer} from 'mobx-react/native';
import {toJS} from 'mobx';

const {Text, LabelCell} = Widget;

const {height, width} = Dimensions.get('window');

@observer
export default class ChannelPanel extends Component {

    //属性声名
    static propTypes = {
        showHeader: PropTypes.bool,
        data: PropTypes.any,
        salesChannelCode: PropTypes.any,
        subSalesChannelCode: PropTypes.any,
        onConfirm: PropTypes.func,
    };

    static defaultProps = {
        showHeader: false,

    };

    constructor(props) {
        super(props);
        this.state = {
            selectParentIndex: 0,
            selectChildrenIndex:-1,//默认不选择
        }

    }

    componentDidMount() {
        this.checkSelectIndex()

    }

    componentWillReceiveProps(nextProps) {
        // Log('测试是否进入渠道赋值00',nextProps)
        // if (this.props.data && nextProps){
        //     this.checkSelectIndex()
        // }
    }

    checkSelectIndex() {

        let selectParentIndex = 0;
        let selectChildrenIndex = -1;
        if (this.props.data && this.props.data.length>0) {

            for (let pIndex in this.props.data) {

                let pItem = _.get(this.props, `data[${pIndex}]`);
                if (pItem) {

                    if (pItem.value == this.props.salesChannelCode) {
                        selectParentIndex = pIndex;
                        if (pItem.children) {
                            for (let cIndex in pItem.children) {
                                let cItem = _.get(pItem, `children[${cIndex}]`);
                                if (cItem.value == this.props.subSalesChannelCode) {
                                    selectChildrenIndex = cIndex;
                                    break;
                                }
                            }
                        }
                        this.setState({
                            selectParentIndex: selectParentIndex,
                            selectChildrenIndex: selectChildrenIndex,
                        }, () => {
                            Log('设置渠道选择index', selectParentIndex, selectChildrenIndex)
                        });
                        break;
                    }
                }

            }

        }
    }

    getResult = () => {
        let result = null;
        let pArray = this.props.data;
        if (pArray && pArray.length > 0 && this.state.selectParentIndex >= 0) {
            let pItem = pArray[this.state.selectParentIndex];
            if (pItem) {
                //返回数据结构
                result = {
                    channelName: {
                        salesChannelName: null,
                        subSalesChannelName: null,
                    },
                    channelId: {
                        subSalesChannelCode: null,
                        salesChannelCode: null,
                    }
                };
                //下面是数据封装
                result.channelName.salesChannelName = pItem.label;
                result.channelId.salesChannelCode = pItem.value;
                if (this.state.selectChildrenIndex >= 0 && pItem.children && pItem.children.length > 0) {
                    let cItem = pItem.children[this.state.selectChildrenIndex];
                    if (cItem) {
                        result.channelName.subSalesChannelName = cItem.label;
                        result.channelId.subSalesChannelCode = cItem.value;
                    }
                }
            }
        }
        return result;
    }

    /**
     * 确定操作
     */
    onConfirm = () => {
        Widget.Popup.hide();
        let pArray = this.props.data;
        if (pArray && pArray.length > 0 && this.state.selectParentIndex >= 0) {
            let pItem = pArray[this.state.selectParentIndex];
            if (pItem) {
                //返回数据结构
                let result = {
                    channelName: {
                        salesChannelName: null,
                        subSalesChannelName: null,
                    },
                    channelId: {
                        subSalesChannelCode: null,
                        salesChannelCode: null,
                    }
                };
                //下面是数据封装
                result.channelName.salesChannelName = pItem.label;
                result.channelId.salesChannelCode = pItem.value;
                if (this.state.selectChildrenIndex >= 0 && pItem.children && pItem.children.length > 0) {
                    let cItem = pItem.children[this.state.selectChildrenIndex];
                    if (cItem) {
                        result.channelName.subSalesChannelName = cItem.label;
                        result.channelId.subSalesChannelCode = cItem.value;
                    }
                }
                this.props.onConfirm && this.props.onConfirm(result);
            }
        }

    }

    onChange = ()=>{
        // let pArray = this.props.data;
        // if (pArray && pArray.length > 0 && this.state.selectParentIndex >= 0) {
        //     let pItem = pArray[this.state.selectParentIndex];
        //     if (pItem) {
        //         //返回数据结构
        //         let result = {
        //             channelName: {
        //                 salesChannelName: null,
        //                 subSalesChannelName: null,
        //             },
        //             channelId: {
        //                 subSalesChannelCode: null,
        //                 salesChannelCode: null,
        //             }
        //         };
        //         //下面是数据封装
        //         result.channelName.salesChannelName = pItem.label;
        //         result.channelId.salesChannelCode = pItem.value;
        //         if (this.state.selectChildrenIndex >= 0 && pItem.children && pItem.children.length > 0) {
        //             let cItem = pItem.children[this.state.selectChildrenIndex];
        //             if (cItem) {
        //                 result.channelName.subSalesChannelName = cItem.label;
        //                 result.channelId.subSalesChannelCode = cItem.value;
        //             }
        //         }
        //         this.props.onConfirm && this.props.onConfirm(result);
        //     }
        // }
    }


    render() {
        let pArray = this.props.data;
        let cArray = null;
        if (pArray && pArray.length > 0) {
            cArray = _.get(this.props, `data[${this.state.selectParentIndex}].children`);
        }
        let childUnSelect = this.state.selectChildrenIndex === -1;

        return (
            <View
                style={{
                    backgroundColor: Constant.colorBackgroundDefault,
                    maxHeight: height / 2.5
                }}>
                {
                    Platform.OS=='android'?<StatusBar backgroundColor={'#000000'} translucent={true}  />:null
                }
                {this.props.showHeader ? <View
                    style={{
                        flexDirection: 'row',
                        height: Constant.scale(44),
                        backgroundColor: 'white',
                        alignItems: 'center',
                        justifyContent: 'center'
                    }}>
                    <TouchableOpacity
                        onPress={() => {
                            Widget.Popup.hide();
                        }}>
                        <Text style={styles.headerBtnText}>取消</Text>
                    </TouchableOpacity>
                    <Text
                        style={{
                            flex: 1,
                            textAlign: 'center',
                            fontSize: Constant.fontSizeCaption
                        }}>渠道选择</Text>

                    <TouchableOpacity
                        onPress={() => {
                            this.onConfirm();
                        }}>
                        <Text style={styles.headerBtnText}>确定</Text>
                    </TouchableOpacity>
                </View> : null}

                <View style={GlobalStyle.styleDividerDefault}/>
                <View style={[{flexDirection: 'row'}]}>
                    <ScrollView style={{flex: 1}}>
                        {
                            pArray && pArray.map((item, index) => {
                                let isSelect = index == this.state.selectParentIndex;

                                return <LabelCell
                                    titleTextStyle={isSelect ? {color: Constant.colorTxtPrimary} : null}
                                    onClick={() => {
                                        this.setState({selectParentIndex: index, selectChildrenIndex: -1},()=>this.onChange())
                                    }}
                                    key={index}
                                    title={item.label}
                                    rightIcon={isSelect ? <CheckBox isChecked={isSelect}/> : <View/>}
                                />
                            })
                        }
                    </ScrollView>
                    <View style={{width: StyleSheet.hairlineWidth, backgroundColor: '#ddd8d8', height: '100%'}}/>

                    <ScrollView style={{flex: 1}}>
                        {
                            cArray && cArray.length > 0 ? <LabelCell
                                titleTextStyle={null}
                                onClick={() => {
                                    this.setState({selectChildrenIndex: -1},()=>this.onChange())
                                }}
                                title={'不选择'}
                                rightIcon={childUnSelect ? <CheckBox isChecked={childUnSelect}/> : <View/>}/> : null

                        }
                        {
                            cArray && cArray.map((item, index) => {
                            let isSelect = index == this.state.selectChildrenIndex;
                            return <LabelCell
                            titleTextStyle={isSelect ? {color: Constant.colorTxtPrimary} : null}
                            onClick={() => {
                            if (this.state.selectChildrenIndex == index) {
                                this.setState({selectChildrenIndex: -1},()=>this.onChange())
                            } else {
                                this.setState({selectChildrenIndex: index},()=>this.onChange())
                            }
                        }}
                            key={index}
                            title={item.label}
                            rightIcon={isSelect ? <CheckBox isChecked={isSelect}/> : <View/>}
                            />
                        })
                        }
                            </ScrollView>
                            </View>
                            </View>
                            );
                            }
                        }

                        const styles = StyleSheet.create({
                        headerBtnText: {
                        width: 70,
                        textAlign: 'center',
                    }
                    });