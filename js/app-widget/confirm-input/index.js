import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    TextInput,
    Dimensions
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget} from 'rn-yunxi';

const {width, height} = Dimensions.get('window');

const {Text} = Widget;
/**
 * 确定输入
 */
export default class ConfirmInput extends ReactNavComponent {
    //属性声名
    static propTypes = {
        isFocus:PropType.func,
    };
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
            focus: false,
        };
        this.inputText = null;
    }

    componentWillMount() {
    }

    componentDidMount() {
    }

    componentWillUnmount() {
    }

    setInputRef(ref) {
        this.inputText = ref;
    }

    onFocus = () => {
        this.setState({focus: true})
        this.props.isFocus&&this.props.isFocus(true);
    }

    onBlur = () => {
        this.setState({focus: false});
        this.props.isFocus&&this.props.isFocus(false);
    }

    //渲染
    render() {

        return (
            <View
                style={{
                    flexDirection: 'row'
                }}>
                <TextInput
                    ref={(ref) => {
                        this.setInputRef(ref)
                    }}
                    style={[{
                        padding: 0,
                        marginRight: Constant.sizeMarginDefault,
                        paddingLeft: Constant.sizeMarginDefault,
                        borderColor: Constant.colorDividerDefault,
                        borderWidth: Constant.sizeDividerNormal,
                        minWidth: Constant.scale(90),
                        height: Constant.scale(21),
                        fontSize:14
                    }, this.state.focus && {borderColor: Constant.colorPrimary, borderWidth: Constant.sizeDividerNormal * 2}]}
                    onBlur={this.onBlur}
                    onFocus={this.onFocus}
                    keyboardType={'numeric'}
                    underlineColorAndroid="transparent"
                    selectionColor={Constant.colorPrimary}
                    maxLength={8}
                    {...this.props}
                />
                {this.state.focus ?

                    <TouchableOpacity
                        onPress={() => {
                        }}
                    >
                        <Image
                            style={{
                                width: Constant.scale(21),
                                height: Constant.scale(21)
                            }}
                            source={require('./input_ok.png')}/>
                    </TouchableOpacity>
                    :
                    <View style={{width: Constant.scale(21)}}/>
                }


            </View>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },

});
