import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    Image,
    StatusBar,
    TouchableOpacity,
    Platform,
} from 'react-native';

import { Widget } from 'rn-yunxi';
const { width, height } = Dimensions.get('window');
const BackIcon = require('./back.png');
const Text = Widget.Text;
import PropTypes from 'prop-types';
//模块声名并导出
import { withNavigation,SafeAreaView } from 'react-navigation';
@withNavigation
export default class Header extends Component {
    //属性声名
    static propTypes = {
        navigation: PropTypes.any,
        showBackAction: PropTypes.bool,
        backAction: PropTypes.func,
        title: PropTypes.string,
        defaultStatusBar: PropTypes.bool,
        rightTitle: PropTypes.string,
        rightAction: PropTypes.func,
        rightIcon: PropTypes.element,
        containerStyles:PropTypes.any,
        headerViewStyles:PropTypes.any,
        titleView:PropTypes.any,
        backIconSrc:PropTypes.number,
        showDivider: PropTypes.bool

    };
    //默认属性
    static defaultProps = {
        title: '',
        defaultStatusBar: true,
        showBackAction: true,
        showDivider: true


    };
    //构造函数
    constructor(props) {
        super(props);
        this.state = { //状态机变量声明
        }
    }

    //渲染
    render() {
        return (
            <SafeAreaView style={{flex:1,backgroundColor:'white'}}>
            <View style={[styles.container,this.props.containerStyles]}>
                <View style={[styles.headerView,this.props.headerViewStyles]}>
                    {Platform.OS === 'ios' ? <StatusBar barStyle={this.props.defaultStatusBar ? 'default' : 'light-content'} /> :
                        <StatusBar backgroundColor={'transparent'} barStyle={'dark-content'} translucent={true} />
                    }
                    <View style={styles.header}>
                        <View style={styles.titleView}>
                            {this.props.titleView?this.props.titleView: <Text style={styles.title}>{this.props.title}</Text>}

                        </View>
                        {this.props.showBackAction ? <TouchableOpacity
                            onPress={() => {
                                if (this.props.backAction) {
                                    this.props.backAction();

                                } else {
                                    if (this.props.navigation) {
                                        this.props.navigation.goBack();
                                    }
                                }
                            }}>
                            <View style={styles.backView}>
                                <Image style={{width:22,height:22}} source={this.props.backIconSrc?this.props.backIconSrc:BackIcon} />
                            </View>
                        </TouchableOpacity> : <View />}


                        {this.props.rightTitle ? <TouchableOpacity onPress={() => {
                            if (this.props.rightAction) {
                                this.props.rightAction();
                            }
                        }}>
                            <View style={styles.rightView}>
                                <Text style={this.props.rightTextStyle}>{this.props.rightTitle}</Text>
                            </View>
                        </TouchableOpacity> : null}
                        
                            {this.props.rightIcon}

                    </View>
                </View>

                {
                    this.props.showDivider == true ?   <View style={GlobalStyle.styleDividerDefault} /> : null
                }
                {
                    this.props.children
                }
            </View>
            </SafeAreaView>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Constant.colorBackgroundDefault,
        width: width,
        height: height,
    },

    headerView: {
        backgroundColor: '#FFFFFF',
        height: Constant.sizeHeader+(Platform.OS=='android'?StatusBar.currentHeight:0),
    },
    header: {
        justifyContent: 'space-between',
        width: width,
        marginTop: Constant.sizeHeaderMarginTop+(Platform.OS=='android'?StatusBar.currentHeight:0),
        flexDirection: 'row',
        height: Constant.sizeHeaderContent,
        alignItems:'center',
        paddingRight:Constant.scale(15)
    },
    titleView: {
        left: 0,
        top: 0,
        height: Constant.sizeHeaderContent,
        position: 'absolute',
        width: width,
        justifyContent: 'center',
        alignItems: 'center',
    },
    title: {
        fontSize: Constant.fontSizeCaption,
    },
    backView: {
        justifyContent: 'center',
        paddingLeft: 10,
        width: 50,
        flex: 1,
    },
    rightView: {
        flex: 1,
        justifyContent: 'center',
        paddingRight: 0,
        alignItems: 'flex-end',
        minWidth: 50
    }
});
