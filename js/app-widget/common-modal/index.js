import React, { Component } from 'react';
import {
	StyleSheet,
	Text,
	View,
	Modal,
    Platform,
    Dimensions
} from 'react-native';
const {width, height} = Dimensions.get('window');

/**
 * 一个通用的modal弹层
 */
export default class CommonModal extends Component {
	constructor(props) {
		super(props);
		this.state = {
			modalVisible: this.props.visible !== undefined ? this.props.visible : false,
		}
	}
	static PropTypes = {
		animationType: React.PropTypes.string,
		onRequestClose: React.PropTypes.func,
		transparent: React.PropTypes.bool,
		containerStyle: React.PropTypes.object
	}

	static defaultProps = {
		animationType: 'fade',
		transparent: true
	}

	show() {
		this._setModalVisible(true);
	}

	hide() {
		this._setModalVisible(false);
	}

	_setModalVisible(visible) {
		this.setState({ modalVisible: visible });
	}

	render() {
		return (
			<Modal animationType={this.props.animationType}
				transparent={this.props.transparent}
				visible={this.state.modalVisible}
				supportedOrientations={['portrait', 'landscape']}
				onRequestClose={() => { this._setModalVisible(false) }}>
				<View style={[styles.container, this.props.containerStyle]}>
					<Text style={{ position: 'absolute', width: width, height: height, top: 0 }}
						onPress={() => {
							this.hide();
							this.props.closeCallBack && this.props.closeCallBack();
						}}
					/>

					{
						this.props.children
					}

				</View>
			</Modal>
		);
	}
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: 'rgba(0, 0, 0, 0.4)',
	},
});