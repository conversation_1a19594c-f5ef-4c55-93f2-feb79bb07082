import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    Image,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    ListView,
    Platform,
    Modal
} from 'react-native';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import PropTypes from 'prop-types';
const {width, height} = Dimensions.get('window');
const {Text, CommonListView, Button} = Widget;
//模块声名并导出
export default class LogisticsState extends Component {

    static propTypes = {
        dataArray: PropTypes.array,
    };

    //默认属性
    static defaultProps = {
        dataArray: [
            { content: '快件签收', time: '2017-12-23 16:19:00' },
            { content: '快件到达广州天河区正在派送中', time: '2017-12-23 14:19:00' },
            { content: '快件出货', time: '2017-12-20 08:19:00' },
            { content: '卖家发货', time: '2017-12-19 05:00:00' },
        ]
    };

    render() {
        return (
            <ScrollView>
                <View style={{ marginTop: Constant.sizeMarginDefault, backgroundColor: "white" }}>
                    {/*标题*/}
                    <View style={{ flexDirection: 'row', alignItems: 'center', height: Constant.scale(44) }}>
                        <Text style={{ marginLeft: Constant.sizeMarginDefault, fontSize: 16 }}>物流信息</Text>
                    </View>
                    {/*线*/}
                    <View style={{width: width - Constant.sizeMarginDefault, marginLeft:Constant.sizeMarginDefault, height:0.5, 
                        backgroundColor:Constant.colorDivider}} />
                    {/*物流信息*/}
                    <View style={{ backgroundColor: 'white', marginTop: Constant.scale(20) }}>
                        {
                            this.props.dataArray ? this.props.dataArray.map((item, index) => {
                                let textColorValue = index == 0 ? Constant.colorPrimary : Constant.colorTxtGrayDefault;
                                let viewColorValue = index == 0 ? Constant.colorPrimary : Constant.colorTxtGrayDefault + '50';
                                return (
                                    <View key={index} style={{ flexDirection: 'row', width: width }}>
                                        <View style={{ width: Constant.scale(35), alignItems: 'center', marginLeft: Constant.scale(10), }}>
                                            <View style={{ backgroundColor: viewColorValue, width: Constant.scale(10), height: Constant.scale(10), borderRadius: Constant.scale(10) / 2 }} />
                                            <View style={{ height: Constant.scale(50), width: 2, marginBottom: 3, backgroundColor: Constant.colorDivider }} />
                                        </View>
                                        <View style={{ width: width - Constant.scale(45), marginTop: 0 }}>
                                            <Text style={{ color: textColorValue }}>{item.content}</Text>
                                            <Text style={{ color: textColorValue, marginTop: Constant.scale(10), fontSize: Constant.fontSizeSmall }}>{item.time}</Text>
                                        </View>
                                    </View>
                                )
                            }) : null
                        }
                    </View>
                </View>
            </ScrollView>
        )
    }
}