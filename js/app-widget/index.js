/**
 *
 * Created by lao.jian<PERSON> on 2017/4/11.
 */
import SingleSelectionPanel from './single-selection-panel';
import TagPanel from './tag-panel';
import Header from './header/CommonHeader';
import PrimaryCommonHeader from './primary-header/PrimaryCommonHeader';
import CountTag from './count-tag';
import CommonSearchBar from './search-bar/CommonSearchBar';
import PriceText from './price-text';
import EmText from './em-text';
import AddressSelect from './address-select';
import AddressSelectForCity from './address-select/AddressSelectForCity';
import UploadImagePicker from './upload-image-picker'
import TextInputView from './text-input-view/TextInputView'
import Button from './button'
import Steps from './steps/Steps'
import StarRating from './star-rating/StarRating'
import Marquee from './marquee'
import ScrollVertical from './scroll-vertical'
import DefaultTabBar from './scroll-tab-bar/DefaultTabBar';
import ModalInputView from './modal-input'
import PromotionPop from './promotion-pop/PromotionPop'
import FadeInOutView from './fade-in-out/FadeInOutView'
import Image from './image/Image';
import ZoomModel from './zoom-model';
import NoGoods from './no-goods';
import LabelWithBar from './label-with-bar';
import SelectPop from './select-pop/SelectPop';
import AlertCodeInput from './alert-code-input';
import Stepper from './stepper/Stepper'
import CheckBox from './check-box/CheckBox';
import LogisticsState from './logistics-statement/LogisticsState';
import ConfirmInput from './confirm-input';
import DateView from './date-view';
import WebViewAutoHeight from './webview-auto-height/WebViewAutoHeight';
import ChannelPanel from './channel-panel';
import CommonModal from './common-modal';
import QuantitySelector from './quantity-selector';
import Verifycode from './verifycode';
import ModalCaptchaView from './modal-catchaview';
import ArrowRotate from './arrow-rotate';

class AppWidget {
    static SingleSelectionPanel = SingleSelectionPanel;
    static TagPanel = TagPanel;
    static Header = Header;
    static PrimaryHeader = PrimaryCommonHeader;
    static CountTag = CountTag;
    static CommonSearchBar = CommonSearchBar;
    static PriceText = PriceText;
    static EmText = EmText;
    static AddressSelect = AddressSelect;
    static AddressSelectForCity = AddressSelectForCity;
    static UploadImagePicker = UploadImagePicker;
    static Button = Button;
    static TextInputView = TextInputView;
    static Steps = Steps;
    static StarRating = StarRating;
    static Marquee = Marquee;
    static ScrollVertical = ScrollVertical;
    static DefaultTabBar = DefaultTabBar;
    static ModalInputView = ModalInputView;
    static PromotionPop = PromotionPop;
    static FadeInOutView = FadeInOutView;
    static Image = Image;
    static ZoomModel = ZoomModel;
    static NoGoods = NoGoods;
    static LabelWithBar = LabelWithBar;
    static SelectPop = SelectPop;
    static AlertCodeInput = AlertCodeInput;
    static Stepper = Stepper;
    static CheckBox = CheckBox;
    static LogisticsState = LogisticsState;
    static ConfirmInput = ConfirmInput;
    static DateView = DateView;
    static WebViewAutoHeight = WebViewAutoHeight;
    static ChannelPanel = ChannelPanel;
    static CommonModal = CommonModal;
    static QuantitySelector = QuantitySelector;
    static Verifycode = Verifycode;
    static ModalCaptchaView = ModalCaptchaView;
    static ArrowRotate = ArrowRotate;
}
export default AppWidget;