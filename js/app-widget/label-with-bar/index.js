/**
 * Created by lu.jiarong on 2017/5/2.
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    Text,
    Image
} from 'react-native';
import { Widget } from 'rn-yunxi';
const { width, height } = Dimensions.get('window');
//模块声名并导出
export default class LabelWithBar extends Component {

    //默认属性
    static defaultProps = {
        title: '我是标题',
        rightView: null,
    };
    //构造函数
    constructor(props) {
        super(props);
        this.state = { //状态机变量声明
        }
    }

    //渲染
    render() {
        const { title, rightView, isColumn } = this.props;
        let viewStyle = isColumn ? styles.columnStyle : styles.rowStyle;
        let textWidth = '100%';
        let titleWidth = '55%';
        if (isColumn) {
            titleWidth = '100%';
            textWidth = '90%';
        }
        return (
            <View>
                <View
                    style={viewStyle}
                >
                    <View style={{
                        flexDirection: 'row',
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                        width: titleWidth
                    }}>
                        <View style={styles.bar} />
                        <Text
                            style={{
                                color: Constant.colorTxtTitle,
                                fontSize: Constant.fontSizeNormal,
                                width: textWidth
                            }}
                        >{title}</Text>
                    </View>
                    <View>
                        {rightView}
                    </View>
                </View>
                <View style={styles.lineStyle} />
            </View>

        );
    }

};

const styles = StyleSheet.create({
    columnStyle: {
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'flex-start',
        minHeight: 55
    },
    rowStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: 'white',
        height: 40
    },
    bar: {
        width: 2,
        height: 14,
        backgroundColor: Constant.colorPrimaryTap,
        marginRight: 10
    },
    lineStyle: {
        marginLeft: 12,
        backgroundColor: Constant.colorLightGray,
        width: '100%',
        height: 0.5,
    },

});
