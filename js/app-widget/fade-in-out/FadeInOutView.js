
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    FlatList,
    DeviceEventEmitter,
    Dimensions,
    ScrollView,
    InteractionManager,
    Platform,
    Animated,
    Easing,
    LayoutAnimation,
    UIManager
} from 'react-native';
import { ReactNavComponent, Widget } from 'rn-yunxi';
const { CheckBox, Text, Button } = Widget;


export default class FadeInOutView extends Component {
    constructor(props) {
        super(props);
        this.state = {
            fadeAnim: new Animated.Value(0),          // 透明度初始值设为0
        };
    }

    startAn() {
        Animated.timing(  // Animate value over time
            this.state.fadeAnim,  // The value to drive
            {
                toValue: 1,  // Animate to final value of 1
            }).start()

        setTimeout(() => {
            Animated.timing(  // Animate value over time
                this.state.fadeAnim,  // The value to drive
                {
                    toValue: 0,  // Animate to final value of 1
                }).start()
        }, 2000)
    }

    render() {
        return (
            <Animated.View                            // 可动画化的视图组件
                style={{
                    ...this.props.style,
                    opacity: this.state.fadeAnim,          // 将透明度指定为动画变量值
                }}
            >
                <Text style={{
                    color: Constant.colorTxtPrimary,
                    fontSize: 12
                }}>{this.props.anText}</Text>
            </Animated.View>
        );
    }
}