/**
 * Created by xiaowz on 2017/7/10.
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    Image,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    ListView,
    Platform
} from 'react-native';
import { ReactNavComponent, Widget } from 'rn-yunxi';
import PropTypes from 'prop-types';

const { width, height } = Dimensions.get('window');
const { Text, CommonListView, Button } = Widget;

//模块声名并导出
export default class OrderButton extends ReactNavComponent {

    //属性声名
    static propTypes = {

        onPress: PropTypes.func,
        btnStyle: PropTypes.any,   //默认灰色 0    主题颜色 1
        style: PropTypes.any,  //可以设置btn宽高
        txtStyle: PropTypes.any  //可设置字体颜色


    };
    //默认属性

    static defaultProps = {
        btnStyle: 0,
    };
    //构造函数
    constructor(props) {
        super(props);
        //状态机变量声明
        this.state = {

        };
    }

    //渲染
    render() {
        let textStye = GlobalStyle.styleButtonTxt
        if (this.props.btnStyle) {
            if (this.props.btnStyle == 1) {
                textStye = GlobalStyle.styleButtonPrimaryTxt
            } else if (this.props.btnStyle == 2) {
                textStye = GlobalStyle.styleButtonDisableTxt
            }
        }

        let btnStye = GlobalStyle.styleButtonDefault
        if (this.props.btnStyle) {
            if (this.props.btnStyle == 1) {
                btnStye = GlobalStyle.styleButtonPrimary
            } else if (this.props.btnStyle == 2) {
                btnStye = GlobalStyle.styleButtonDisable
            }
        }

        return (
            <Button
                txtStyle={[textStye, this.props.txtStyle]}
                style={[btnStye, this.props.style]}
                onPress={() => this.btnClick()}
                disabled={this.props.disabled}
            >
                {this.props.children}
            </Button>

        );
    }

    btnClick = () => {
        if (this.props.onPress) {
            this.props.onPress()
        }
    }
};
const styles = StyleSheet.create({
    container: {
        flex: 1,
    }
});
