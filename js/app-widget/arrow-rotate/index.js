'use strict';

import React, { Component, PropTypes } from 'react';
import {
    TouchableOpacity,
    View,
    Image,
    StyleSheet,
    Animated
} from 'react-native';

/**
 * 箭头向下向上旋转，可用于下拉选择
 */
export default class ArrowRotate extends Component {
    constructor(props) {
        super(props);
        this.state = {
            rotateValue: new Animated.Value(0)
        };
    }
    static propTypes = {
        arrow: Image.propTypes.source,
        style: React.PropTypes.object,
    }
    static defaultProps = {
        arrow: require('./img/sanjiaoxiala.png'),
        style: {}
    }

    rorateUp = () => {
        Animated.timing(
            this.state.rotateValue,
            { toValue: 1 }
        ).start();
    }

    rorateDown = () => {
        Animated.timing(
            this.state.rotateValue,
            { toValue: 0 }
        ).start();
    }

    render() {
        const { arrow, style } = this.props;

        return (
            <Animated.Image source={arrow} style={[{
                width: Constant.scale(6),
                height: Constant.scale(4),
                transform: [{ rotate: this.state.rotateValue.interpolate({ inputRange: [0, 1], outputRange: ['0deg', '-180deg'] }) }]
            }, style]} />
        )
    }
}
