/**
 * Created by whw on 2017/6/13.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
} from 'react-native';
import {Widget} from 'rn-yunxi';

const {width, height} = Dimensions.get('window');
const Text = Widget.Text;
import PropTypes from 'prop-types';
import {DatePickerView} from 'antd-mobile';
//模块声名并导出
export default class DateView extends Component {
    //属性声名
    static propTypes = {
        onDismiss: PropTypes.func,
        onConfirm: PropTypes.func,
        initDate:PropTypes.object,
    };
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = { //状态机变量声明
            date:props.initDate?props.initDate: new Date(),

        }
    }

    //渲染
    render() {
        return (
            <View style={[styles.contentStyle, {justifyContent: 'center', alignItems: 'center'}]}>
                <View style={{marginTop: Constant.sizeMarginDefault, width: width, flexDirection: 'row', justifyContent: 'space-between'}}>
                    <TouchableOpacity
                        style={{
                            padding: Constant.sizeMarginDefault
                        }}
                        onPress={() => {
                            this.props.onDismiss && this.props.onDismiss();
                        }}>
                        <Text style={{
                            fontSize: Constant.fontSizeBig,
                        }}>取消</Text>
                    </TouchableOpacity>
                    <Text style={{
                        padding: Constant.sizeMarginDefault,
                        color: Constant.colorTxtTitle,
                        fontSize: Constant.fontSizeCaption,
                    }}>时间选择</Text>
                    <TouchableOpacity
                        style={{
                            padding: Constant.sizeMarginDefault
                        }}
                        onPress={() => {
                            this.props.onConfirm && this.props.onConfirm(this.state.date);
                        }}>
                        <Text style={{
                            fontSize: Constant.fontSizeBig,
                        }}>确定</Text>
                    </TouchableOpacity>
                </View>
                <DatePickerView
                    mode={'date'}
                    style={{
                        marginTop: Constant.sizeMarginDefault,
                        marginBottom: Constant.sizeMarginDefault
                    }}
                    value={this.state.date}
                    onChange={(date) => {
                        this.setState({date})
                    }}
                    {...this.props}
                />
            </View>
        );
    }
};


const styles = StyleSheet.create({
    contentStyle: {
        height: height / 2.5,
        backgroundColor: 'white'


    }
});
