/**
 * Created by lao<PERSON>ji<PERSON><PERSON> on 2017/5/2.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions
} from 'react-native';
import {Widget} from 'rn-yunxi';
const {width, height} = Dimensions.get('window');
const Text=Widget.Text;
import PropTypes from 'prop-types';
//模块声名并导出
export default class TagPanel extends Component {
    //属性声名
    static propTypes = {
        title: PropTypes.string,
        data: PropTypes.array,
        callBack: PropTypes.func,
        actionCallBack:PropTypes.func,
        actionTxt:PropTypes.string,

    };
    //默认属性
    static defaultProps = {
        title: "标题",
        data: ['历史记录1', '历史记录2', '历史记录3', '历史记录4', '历史记录5',],

    };
    //构造函数
    constructor(props) {
        super(props);
        this.state = { //状态机变量声明
        }
    }

    onPressItem(item, index){
        if (this.props.callBack) {
            this.props.callBack(item, index);
        }
    };
    //渲染
    render() {
        let data = this.props.data;
        return (
            <View style={this.props.style||{}}>
                <View style={{flexDirection:'row',justifyContent:'space-between'}}><Text style={styles.title}>{this.props.title}</Text>

                    <Text style={[styles.title,{marginRight:10}]} onPress={this.props.actionCallBack}>{this.props.actionTxt}</Text></View>

                <View style={styles.container}>
                    {
                        !data ? null : data.map((item, index) => {
                            return <Widget.Button onPress={this.onPressItem.bind(this,item,index)}
                                                  key={index}
                                                  style={styles.itemStyle}
                                                  styles={buttonStyles}
                            > {item}</Widget.Button>
                        })
                    }
                </View>
            </View >
        );
    }
};


const styles = StyleSheet.create({
    title: {
        marginLeft: 10,
        marginBottom: 8,
    },
    container: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        marginLeft: 10,
        marginRight: 10,
    },
    itemStyle: {
        marginRight: 10,
        marginBottom: 8,
        borderColor: Constant.colorTransparent,
        backgroundColor: Constant.colorLightGray,
        borderRadius: 16,
        borderWidth: 0.5,
        height: 34,
        padding: 10,

    },
    itemTxtStyle: {
        textAlignVertical: 'center',
        textAlign: 'center',
        color: Constant.colorTxtDefault,
        flex: 0,
        fontSize: 14
    },
});
const buttonStyles = {
    txtStyle: styles.itemTxtStyle,
    underlayColor: Constant.colorPrimaryTap,
    underlayTxtColor: 'white'
};
