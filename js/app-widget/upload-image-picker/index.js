/**
 * Created by lao.jian<PERSON> on 2017/3/24.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Image,
    TouchableOpacity,
    Platform,
    Dimensions,
    Alert
} from 'react-native';
import {Modal, Toast} from 'antd-mobile';
// import  from '../ApiManager';

const alert = Modal.alert;
import {ReactNavComponent,Widget,AliYunOSS} from 'rn-yunxi';
import PropTypes from 'prop-types';
const {width, height} = Dimensions.get('window');
const delImage = require('./image/ic_delete.png');
const addImage = require('./image/img_addimages.png');
const  {ImageChoose,Text} = Widget;
//模块声名并导出
export default class UploadImagePicker extends ReactNavComponent {
    uploadingImages = new Map();//上传中图片
    //属性声名
    static propTypes = {
        images: PropTypes.array,
        enable: PropTypes.bool,//是否可以编辑
        onImageClick: PropTypes.func,//点击callback
        getUploadData: PropTypes.func,//获取上传所需参数
        maxItemLength: PropTypes.number,
        multiple: PropTypes.bool,
        isSaveToPhotos: PropTypes.bool
    };
    //默认属性
    static defaultProps = {
        enable: false,
        images:[],
        maxItemLength: 6,
        multiple: false,
        isSaveToPhotos: true

    }
    //构造函数
    constructor(props) {
        super(props);
        this.state = { //状态机变量声明
            httpImages: this.props.images ? this.props.images : [],
            localImages: [],//本地图片
            // uploadingImages:new Map(),
            enable: this.props.enable,
            uploadValidate:this.props.uploadValidate,//上传的限制
        }
        this.setEnable.bind(this);
        this.getLocalImages.bind(this);
        this.getHttpImages.bind(this);
        this.setHttpImages.bind(this);
        this.hasUploadingFile.bind(this);
        this.getAllUploadPath.bind(this);
        this.reset.bind(this);

    }

    /**
     * 判断是否有图片未上传或失败
     * @returns {boolean}
     */
    hasUploadingFile() {
        console.log('this.uploadingImages ====',this.uploadingImages);
        for (let [key, item] of this.uploadingImages) {
            if (item && item.state == 1 && item.uploadPath) {
            } else {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取组件最后上传到OSS服务的图片路径
     * @returns {Array}
     */
    getAllUploadPath() {
        let resultData = [];
        resultData = resultData.concat(this.state.httpImages);
        // console.log('ersu',resultData)
        for (let [key, item] of this.uploadingImages) {
            if (item && item.state == 1 && item.uploadPath) {
                resultData.push(item.uploadPath);
            }
        }
        // console.log('1111',resultData)
        return resultData;
    }

    getHttpImages() {
        return this.state.httpImages;
    }

    getLocalImages() {
        return this.state.localImages;
    }

    /**
     * 设置是否可编辑
     * @param enable
     */
    setEnable(enable) {
        this.setState({enable: enable});
    }

    /**
     * 设置网络图片数组
     * @param list
     */
    setHttpImages(list) {
        if(list){
            this.setState({httpImages: list},()=> {
                if (this.props.reLoad)
                {
                    this.props.reLoad();
                }
            })
        }

    }

    onDelHttpImage(item, index) {
        let list = this.state.httpImages;
        list.splice(index, 1);
        this.setState({httpImages: list},()=> {
            if (this.props.reLoad)
            {
                this.props.reLoad();
            }
        });
    }

    onDelLocalImage(item, index) {
        let list = this.state.localImages;
        list.splice(index, 1);
        this.setState({localImages: list});
        this.uploadingImages.delete(item);
        if (this.props.reLoad)
        {
            this.props.reLoad();
        }
    }

    /**
     * 删除所有本地图片
     * @param list
     */
    reset(callback) {
        this.uploadingImages.clear();
        this.setState({localImages: [],httpImages: []},()=>{
            callback();
        })
        if (this.props.reLoad)
        {
            this.props.reLoad();
        }
    }
    
    onAddLocalImageClick = (data)=> {
        //做一层过滤
       if(this.state.uploadValidate && this.state.uploadValidate(data))return;
       
        Log('===---=',data);
            if (data) {
                let list = this.state.localImages;
                let findItem = false;
                list.map((item, index) => {
                    if (Array.prototype.isPrototypeOf(data))
                    {
                        data.map((dataItem)=> {
                            if (Platform.OS == 'ios')
                            {
                                if (item.orginFile && item.orginFile === dataItem.orginFile) {
                                    findItem = true;
                                }
                            }else
                            {
                                if (item.path == dataItem.path) {
                                    findItem = true;
                                }
                            }
                        })
                    }else
                    {
                        if (Platform.OS == 'ios')
                        {
                            if (item.orginFile && item.orginFile === data.orginFile) {
                                findItem = true;
                            }
                        }else
                        {
                            if (item.path == data.path) {
                                findItem = true;
                            }
                        }
                    }
                });
                if (!findItem) {
                    if (Array.prototype.isPrototypeOf(data))
                    {
                        data.map((item)=> {
                            list.push(item);
                            this.setState({localImages: list}, () => {
                                this.uploadImage(item.path);
                            });
                        })
                    }else {
                        list.push(data);
                        this.setState({localImages: list}, () => {
                            this.uploadImage(data.path);
                        });
                    }
                } else {
                    setTimeout(()=>{
                        Alert.alert('提示', '不能选择同一张图片');
                    },500);
                }
            }

    }

    //图片点击
    onImageClick(item,index) {
        if (this.props.onImageClick) {
            this.props.onImageClick(item,index);
        }
    }

    //删除图片
    renderDel = (item, index, local) => {
        return (
            <TouchableOpacity style={styles.del} onPress={local ? this.onDelLocalImage.bind(this, item, index) : this.onDelHttpImage.bind(this, item, index)}>
                <View>
                    <Image style={[styles.delImg]}
                           source={delImage}

                    />
                </View>
            </TouchableOpacity>
        );
    };


    //图片选择
    showActionSheet() {
        const BUTTONS = ['拍照', '从相册选择', '取消'];
        let {localImages,httpImages} = this.state;
        let {multiple,maxItemLength,isSaveToPhotos} = this.props;
        Widget.Popup.show(<ImageChoose cropping={false}
                                       multiple={multiple}
                                       isSaveToPhotos={isSaveToPhotos}
                                       maxFiles={maxItemLength - localImages.length - httpImages.length}
                                       callBack={(image) => this.onAddLocalImageClick(image)}
                                       buttons={BUTTONS} title='请选择照片'/>,
            {
                animationType: 'slide-up', backgroundColor: '#00000000',
                onMaskClose: () => {
                    Widget.Popup.hide()
                }
            })
    }

    //图片选择回调和上传
    async uploadImage(file) {
        let THIS = this;
        let uploadingData = this.uploadingImages;
        let tmpLoadingData = uploadingData.get(file);
        if (tmpLoadingData && tmpLoadingData.state != -2) {
            //上传中，
            return;
        }
        let uploadingItem = {
            file: file,
            state: -1,//-2:失败，-1未上传，0上传中，1成功
            progress: 0,
            uploadPath: null,
        };

        if (this.props.getUploadData)
        {
            try {
                let data = await this.props.getUploadData();

                if (data && data.data) {
                    const uploadConf = {
                        AccessKey: data.data.accessKeyId,
                        SecretKey: data.data.accessKeySecret,
                        SecretToken: data.data.securityToken,
                    };

                    AliYunOSS.initWithKey(uploadConf, data.data.endPoint);

                    const uploadConfig = {
                        host: data.data.host,
                        bucketName: data.data.bucketName,  //your bucketName
                        sourceFile: file, // local file path
                        ossFile: data.data.ossFile, // the file path uploaded to oss
                        updateDate: null
                    };

                    // 更新上传状态
                    uploadingItem.state = 0;
                    uploadingData.set(file, uploadingItem);
                    THIS.setState({});

                    AliYunOSS.uploadObjectAsync(uploadConfig).then((resp) => {
                        // alert(JSON.stringify(resp));
                        // let uploadingData=this.state.uploadingImages;
                        if (resp.success) {
                            let uploadFilePath = resp.successFile;
                            uploadingItem.uploadPath = uploadFilePath;
                            uploadingItem.state = 1;
                            uploadingData.set(file, uploadingItem);
                            if (this.props.reLoad)
                            {
                                this.props.reLoad();
                            }
                        } else {
                            uploadingItem.state = -2;
                            uploadingData.set(file, uploadingItem);
                            if (this.props.reLoad)
                            {
                                this.props.reLoad();
                            }
                        }
                        THIS.setState({});
                    });
                }else {
                    uploadingItem.state = -2;
                    uploadingData.set(file, uploadingItem);
                    THIS.setState({});
                    if (this.props.reLoad)
                    {
                        this.props.reLoad();
                    }
                }
            }
            catch (error) {
                uploadingItem.state = -2;
                uploadingData.set(file, uploadingItem);
                THIS.setState({});
                if (this.props.reLoad)
                {
                    this.props.reLoad();
                }
            }
        }


        Log('uploadImage === ',this.uploadingImages);

    }



    //渲染
    render() {
        // console.log(width, height);
        // Log('imagePick=====',this.state)
        let itemCount = this.state.httpImages.length + this.state.localImages.length;

        return (
            <View style={styles.container}>
                {  this.state.httpImages.map((item, index) => {
                    return (

                        <View key={index} style={[styles.itemContainer]}>
                            <TouchableOpacity style={[styles.imageTouch]} onPress={this.onImageClick.bind(this,item,index)}>
                                <Image style={styles.image} source={{uri: item ? item : null}}/>
                            </TouchableOpacity>
                            {this.props.enable ? this.renderDel(item, index, false) : null}

                        </View>

                    );
                })}
                {  this.state.localImages.map((item, index) => {
                    let uploadItem = this.uploadingImages.get(item.path);
                    let state = -1;
                    if (uploadItem) {
                        state = uploadItem.state;
                    }
                    let count = index;
                    if (this.state.httpImages && this.state.httpImages.length)
                    {
                        count = count + this.state.httpImages.length;
                    }
                    return (
                        <View key={index} style={[styles.itemContainer]}>
                            <TouchableOpacity style={[styles.imageTouch]} onPress={this.onImageClick.bind(this,item.path,count)}>
                                <Image style={styles.image} source={{uri: item.path}}/>
                            </TouchableOpacity>
                            {this.props.enable? state==-1&&state==0?null:this.renderDel(item.path, index, true) : null}
                            {state != 1 ? <TouchableOpacity style={styles.uploadState} onPress={() => {
                                    //重新上传
                                    if (state == -2) {
                                        this.uploadImage(item.path);
                                    }
                                }}>
                                    <Text style={styles.uploadStateTxt}>{state == -2 ? "上传失败" : "上传中"}</Text>
                                    {state == -2 ? this.renderDel(item.path, index, true) : null}
                                </TouchableOpacity> : null}

                        </View>
                    )
                })}
                {

                    itemCount < this.props.maxItemLength && this.props.enable ?

                        <TouchableOpacity style={[styles.itemContainer]} onPress={this.showActionSheet.bind(this)}>
                            <Image
                                style={styles.addImage}
                                resizeMode="stretch"
                                source={addImage}/>
                        </TouchableOpacity>
                        : null
                }
            </View>
        );
    }


};
const spaceSize = 10;
const columnCount = 4;
const itemSize = 64;
const styles = StyleSheet.create({
    container: {
        flex: 1,
        // minHeight: itemSize,
        flexDirection: 'row',
        flexWrap: 'wrap',
        // backgroundColor:'red'
        // padding:10,

    },

    itemContainer: {
        alignSelf: 'flex-start',
        height: itemSize,
        width: itemSize,
        marginLeft: spaceSize,
        marginBottom: spaceSize,
        alignItems: 'center',
        justifyContent: 'center',
    },
    imageTouch:{
        height: itemSize,
        width: itemSize,
    },
    image: {
        flex: 1,
        alignSelf: 'stretch'
    },
    del: {
        position: 'absolute',
        right: 2,
        top: 2,
    },
    delImg: {
        width: 25,
        height: 25,
    },
    addImage: {
        height: itemSize,
        width: itemSize,
    },

    uploadState: {
        height: itemSize,
        width: itemSize,
        position: 'absolute',
        alignSelf: 'stretch',
        backgroundColor: 'rgba(0,0,0,0.7)',
        alignItems: 'center',
        justifyContent: 'center',

    },

    uploadStateTxt: {
        textAlign: 'center',
        color: 'white',
        alignItems: 'center',
        justifyContent: 'center',

    }
})