/**
 * Created by z<PERSON><PERSON>.ha<PERSON><PERSON> on 2017.6.8.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 *
 Widget.Popup.show(<SelectPop
 popTitle={'我是标题'}
 selectCallBack={(item) => {
                Widget.Popup.hide()
                //自己添加回调需要搞的事情，item是整个对象
            }}
 listData={SourceOrderType}
 labelName={'展示文字字段，选填'}
 />,
 {
     animationType: 'slide-up', backgroundColor: '#00000000',
     onMaskClose: () => {
         Widget.Popup.hide()
     }
 })


 */
import React, {Component} from 'react';
import {
    StyleSheet,
    StatusBar,
    View,
    Dimensions,
    TouchableOpacity,
    TextInput,
    ScrollView,
    Image, Platform
} from 'react-native';
import PropTypes from 'prop-types';

var {width, height} = Dimensions.get('window');
import {ReactNavComponent, Widget} from 'rn-yun<PERSON>';

const {Text, LabelCell} = Widget;
const IC_CLOSE = require('./ic_popclose.png');
import CheckBox from '../check-box/CheckBox';

//模块声名并导出
export default class SelectPop extends Component {
    //属性声名
    static propTypes = {

    };
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = { //状态机变量声明
        }
    }


    renderScroll = (list) => {
        return (
            <ScrollView style={{maxHeight:height/2,width:'100%'}}>
                {
                    list && list.map((item, index) => {
                        let isSelect = false;
                        if (this.props.selectKey && item[this.props.selectKey] == this.props.selectId) {
                            isSelect = true;
                        } else if (item == this.props.selectId) {
                            isSelect = true;
                        } else if(index == this.props.selectIndex){
                            isSelect = true;
                        }
                        return (
                            <LabelCell
                                titleTextStyle={isSelect ? {color: Constant.colorTxtPrimary} : null}
                                onClick={() => {
                                    this.props.selectCallBack && this.props.selectCallBack(item, index)
                                }}
                                key={index}
                                title={this.props.labelName ? item[this.props.labelName] : item}
                                rightIcon={isSelect ? <CheckBox isChecked={isSelect}/> : <View/>}
                            />
                        );
                    })
                }
            </ScrollView>
        );
    }

    //渲染模版
    render() {
        const {popTitle, listData} = this.props;
        let title = popTitle || '请选择'
        return (
            <View style={styles.container}>
                {
                    Platform.OS=='android'?<StatusBar backgroundColor={'#000000'} translucent={true}  />:null
                }
                {/* <View style={{ padding: 10 }}>
                    <Text style={{
                        color: Constant.colorTxtTitle,
                        fontSize: Constant.fontSizeNormal,
                    }}>{title}</Text>
                </View> */}
                <View style={{width: width, height: Constant.scale(44), justifyContent: 'center', alignItems: 'center', marginTop:Constant.sizeHeaderMarginTop}}>
                    <Text style={{fontSize: Constant.fontSizeCaption, color: Constant.colorTxtTitle}}>{title}</Text>
                    <TouchableOpacity
                        style={{position: 'absolute', right: 0, top: 0, height: Constant.scale(44), width: Constant.scale(44), justifyContent: 'center', alignItems: 'center'}}
                        onPress={() => {
                            Widget.Popup.hide();
                        }}
                    >
                        <Image style={{width: Constant.scale(15), height: Constant.scale(15)}} source={IC_CLOSE}/>
                    </TouchableOpacity>
                </View>
                {this.props.showPleaseChooseBtn?
                    <View style={{width:'100%'}}>
                    <LabelCell
                        style={{width:'100%'}}
                        // titleTextStyle={{color: Constant.colorTxtPrimary}}
                        onClick={() => {
                            this.props.selectCallBack && this.props.selectCallBack('')
                        }}
                        showRightIcon = {false}
                        title={'请选择'}

                    />
                    </View>
                    : null
                }

                {this.renderScroll(listData)}
            </View>
        );
    }


};

const styles = StyleSheet.create({
    container: {
        width: '100%',
        borderTopWidth: Constant.sizeDividerNormal,
        borderColor: Constant.colorDivider,
        backgroundColor: 'white',
        alignItems: 'center',

    },

});
