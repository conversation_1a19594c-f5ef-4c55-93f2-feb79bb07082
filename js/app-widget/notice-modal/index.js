/** 公告列表
 * @Author: 老鸦
 * @Date: 2022-03-28 14:18:30
 * @Last Modified by: duowen
 * @Last Modified time: 2022-04-11 13:54:15
 */

import React, { Component } from "react";
import { ReactNavComponent, Widget, Util } from "rn-yunxi";
import { Button } from "antd-mobile";
import Modal from "rc-dialog/lib/Modal";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  TouchableOpacity,
  WebView,
} from "react-native";
import _ from 'lodash';
const { CommonFlatList, Text } = Widget;
const { CountdownUtil } = Util;

const DEFAULT_TIME = 5;
export default class NoticeModal extends ReactNavComponent {
  webViewRef = null;
  //默认属性
  static defaultProps = {
    visible: false,
    onClose: () => {},
    onConfirm: () => {},
    dataList: [], // 公告内容
    startIndex: 0, // 公告开始下标
    hasNext: false, // 是否下一条
  };
  //构造函数
  constructor(props) {
    super(props);
    this.state = {
      height: 0,
      sec: DEFAULT_TIME,
      // currentItem: {},
      currentIndex: props.startIndex,
    };
  }
  componentWillReceiveProps(nextProps) {
    if (nextProps.startIndex !== this.props.startIndex) {
      this.setState({
        currentIndex: nextProps.startIndex,
      });
    }
    const hasRead =  _.get(this.props, `dataList[${nextProps.startIndex}].isRead`, false);
    // 弹窗被打开，并且是未读公告才开启倒计时
    if (
      nextProps.visible !== this.props.visible &&
      nextProps.visible &&
      !hasRead
    ) {
      this.startTime();
    }
  }

  componentWillUnmount() {
    CountdownUtil.stop();
  }

  startTime = () => {
    let countdownDate = new Date(new Date().getTime() + DEFAULT_TIME * 1000);
    CountdownUtil.settimer(countdownDate, (time) => {
      // console.warn("倒计时：", time.sec);
      this.setState({
        sec: time.sec || 0,
      });
    });
  };

  renderButtonText = () => {
    const { currentIndex } = this.state;
    const dataList = this.props.dataList || [];
    if (dataList.length && dataList[currentIndex].isRead) {
      return "关闭";
    } else if (this.state.sec > 0) {
      // 区分单条、多条公告
      if (
        this.props.dataList.length > 1 &&
        this.state.currentIndex + 1 < this.props.dataList.length
      ) {
        return `下一条（${this.state.sec}秒）`;
      } else {
        return `已读（${this.state.sec}秒）`;
      }
    } else {
      if (
        this.props.dataList.length > 1 &&
        this.state.currentIndex + 1 < this.props.dataList.length
      ) {
        return `下一条`;
      } else {
        return `已读`;
      }
    }
  };
  // 一下条或确认关闭时间
  handleConfirm = () => {
    const { currentIndex } = this.state;
    const dataList = this.props.dataList || [];
    if (this.state.currentIndex < this.props.dataList.length - 1) {
      this.setState(
        {
          currentIndex: this.state.currentIndex + 1,
          sec: DEFAULT_TIME,
        },
        () => {
          this.startTime();
          setTimeout(() => {
            console.warn("");
            this.webViewRef.reload();
          }, 0);
          this.props.onNext && this.props.onNext(dataList[currentIndex]);
        }
      );
    } else {
      this.setState({
        sec: DEFAULT_TIME,
        currentIndex: 0,
      });
      CountdownUtil.stop();
      this.props.onConfirm && this.props.onConfirm(dataList[currentIndex]);
    }
  };

  render() {
    const { currentIndex } = this.state;
    const dataList = this.props.dataList || [];
    const HTML_CONTENT = dataList.length && dataList[currentIndex] ? dataList[currentIndex].content : "";
    const isRead = dataList.length ? dataList[currentIndex].isRead : false;
    const windowHeight = Dimensions.get("window").height * 0.4;
    const webViewHeight =
      +this.state.height > windowHeight
        ? Dimensions.get("window").height * 0.6
        : 300;
    const BaseScript = `
    (function () {
        var height = null;
        function changeHeight() {
          height = document.body.scrollHeight;
          if (window.postMessage) {
            window.postMessage(height)
          }
        }
        setTimeout(changeHeight, 0);
    } ())
    `;

    return (
      <Modal
        transparent
        style={styles.modalViewStyle}
        wrapStyle={{
          flex: 1,
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
        }}
        onClose={() => {
          this.setState({
            sec: DEFAULT_TIME,
          });
          CountdownUtil.stop();
          this.props.onClose && this.props.onClose();
        }}
        visible={this.props.visible}
        closable
      >
        <View style={styles.bgViewStyle}>
          <View style={{ position: "relative" }}>
            <Text
              style={{
                textAlign: "center",
                fontSize: Constant.fontSizeHeader,
              }}
            >
              公告
            </Text>

            <TouchableOpacity
              onPress={() => {
                this.setState({
                  sec: DEFAULT_TIME,
                });
                CountdownUtil.stop();
                this.props.onClose && this.props.onClose();
              }}
              style={{
                position: "absolute",
                right: 10,
                top: 0,
                width: Constant.scale(10),
                height: Constant.scale(10),
              }}
            >
              <Image
                style={{
                  width: Constant.scale(10),
                  height: Constant.scale(10),
                }}
                source={require("./close.png")}
              />
            </TouchableOpacity>
          </View>
          <View
            style={{
              height: webViewHeight,
              width: "100%",
              marginBottom: Constant.scale(20),
            }}
          >
            <WebView
              ref={(ref) => (this.webViewRef = ref)}
              style={{}}
              originWhitelist={["*"]}
              injectedJavaScript={BaseScript}
              automaticallyAdjustContentInsets={true}
              source={{
                html: `<!DOCTYPE html><html> <style type="text/css">
                  #content {
                    font-size: 18px;
                    line-height: 1.4;
                  }
						</style><body><div class='tour_product_explain' id='content'>${HTML_CONTENT}</div></body></html>`,
              }}
              scalesPageToFit={true}
              javaScriptEnabled={true} // 仅限Android平台。iOS平台JavaScript是默认开启的。
              domStorageEnabled={true} // 适用于安卓a
              scrollEnabled={false}
              onMessage={(event) => {
                this.setState({ height: event.nativeEvent.data });
              }}
            ></WebView>
          </View>
          <Button
            type="primary"
            onClick={this.handleConfirm}
            style={{
              backgroundColor: "#e83c4f",
              borderColor: "#e83c4f",
            }}
            activeStyle={{
              backgroundColor: "#f82038",
            }}
            disabled={isRead ? false : this.state.sec > 0}
          >
            {this.renderButtonText()}
          </Button>
        </View>
      </Modal>
    );
  }
}

const styles = StyleSheet.create({
  modalViewStyle: {
    alignItems: "center",
    justifyContent: "center",
    borderRadius: Constant.scale(4),
    overflow: 'hidden'
  },
  bgViewStyle: {
    width: Dimensions.get("window").width * 0.8,
    backgroundColor: "#fff",
    padding: Constant.scale(10),
  },
});
