/**
 * Created by lu.jiarong on 2017/5/2.
 * 
import AppWidget from '../../app-widget';
const {  PriceText } = AppWidget;

    <PriceText 
    price={'123.45'} 
    size={1}
    />

    size={1}    红色大字
        ={2}    红色小字
        ={3}    灰色小字
        ={4}    白色 14号
 * 
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    Text
} from 'react-native';
import { Widget } from 'rn-yunxi';
const { width, height } = Dimensions.get('window');
//模块声名并导出
export default class EmText extends Component {

    //默认属性
    static defaultProps = {
        str: '<em>红酒</em>',
        lines: 1,
    };
    //构造函数
    constructor(props) {
        super(props);
        let normalText = [];
        let specialText = [];


        if (props.str && props.str !== '' && props.str.length > 0) {
            try {
                let tempText = props.str;
                tempText = tempText.replace(/<[^>]*>/g, 'tmd')
                tempText.trim().split('tmd').forEach((obj, index) => {
                    if ((index + 1) % 2 === 0) {
                        normalText.push(obj)
                    } else {
                        specialText.push(obj)
                    }
                })
            } catch (e) { }
        }
        this.state = { //状态机变量声明
            normalText,
            specialText,
        }
    }



    //渲染
    render() {
        const { normalText,
            specialText, } = this.state
        if (normalText && normalText.length > 0) {
            return (
                <Text >
                    {normalText.map((obj, i) => {
                        return (<Text>{obj}
                            <Text style={{ color: 'red' }}>{specialText[i]}</Text>
                        </Text>)
                    })}
                </Text>
            );
        }
        return (
            <Text >{this.props.str}</Text>
        );

    }
};

const styles = StyleSheet.create({

});
