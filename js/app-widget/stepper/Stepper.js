/**
 *
 * Created by xiaowz on 2017/12/12.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */

import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity
} from 'react-native';

import {ReactNavComponent,Widget} from 'rn-yunxi';
const {Text, Button} = Widget;
const { width, height } = Dimensions.get('window');
import PropTypes from 'prop-types';
//模块声名并导出
export default class Stepper extends Component {
    //属性声名
    static propTypes = {
        min: PropTypes.number,
        max: PropTypes.number,
        step: PropTypes.number,
        defaultValue: PropTypes.number,
        onChange: PropTypes.func,
        maxFun: PropTypes.func,
        minFun: PropTypes.func,
    };
    //默认属性
    static defaultProps = {
        min: 1,
        max: 99,
        step: 1,
        defaultValue: 1,
    }
    //构造函数
    constructor(props) {
        super(props);
        this.state = { //状态机变量声明
            currentNumber: this.props.defaultValue,
            interval: 200,//控制增加、减少数量间隔
        }
    }

    //渲染
    render() {
        return (
            <View style={[styles.container, this.props.style]}>

                    <TouchableOpacity
                        underlayColor='#ffffff88'
                        onPress={() => { this.decrease() }}
                        onLongPress={() => this.longDecrease()}
                        onPressOut={() => this.onTouchEnd()}>
                        <View style={styles.buttonStyle}>
                            <Text style={{color:'#FFFFFF',fontSize:14,fontWeight:'bold'}}>一</Text>
                        </View>
                    </TouchableOpacity>

                    <View style={styles.contentStyle}>
                        <Text style={{ textAlign: 'center', fontSize: 16, width: 40 }}>{this.props.defaultValue}</Text>
                    </View>

                    <TouchableOpacity
                        underlayColor='#ffffff88'
                        onPress={() => { this.increase() }}
                        onLongPress={() => this.longIncrease()}
                        onPressOut={() => this.onTouchEnd()}>
                        <View style={styles.buttonStyle}>
                            <Text style={{color:'#FFFFFF',fontSize:14,fontWeight:'bold'}}>十</Text>
                        </View>
                    </TouchableOpacity>

            </View>
        );
    }

    decrease() {
        let newValue = this.props.defaultValue - this.props.step;
        if (newValue < this.props.min) {
            newValue = this.props.min;
            this.props.minFun && this.props.minFun()
            this.setState({
                currentNumber: newValue,
            })
        } else {
            this.setState({
                currentNumber: newValue,
            }, () => {
                this.onChange(newValue)
            })
        }
    }


    increase() {
        let newValue = this.props.defaultValue + this.props.step;
        if (newValue > this.props.max) {
            newValue = this.props.max;
            this.props.maxFun && this.props.maxFun()
            this.setState({
                currentNumber: newValue,
            })
        } else {
            this.setState({
                currentNumber: newValue,
            }, () => {
                this.onChange(newValue)
            })
        }
    }

    onChange(newValue) {
        if (this.props.onChange) {
            this.props.onChange(newValue)
        }
    }

    longDecrease() {
        this.autoInterval = setInterval(this.decrease.bind(this), this.state.interval);
    }

    longIncrease() {
        this.autoInterval = setInterval(this.increase.bind(this), this.state.interval);
    }

    onTouchEnd() {
        if (this.autoInterval) {
            clearInterval(this.autoInterval)
            this.autoInterval = null;
        }
    }
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        width: Constant.scale(108),
        height:Constant.scale(40),
        borderRadius:Constant.scale(20),
        // backgroundColor:Constant.colorPrimary,
        alignItems:'center',
        borderWidth:1,
        borderColor:'#E5E5E5',
        paddingLeft:Constant.scale(10),
        paddingRight:Constant.scale(10)


    },
    buttonStyle: {
        borderRadius: Constant.scale(10),
        width: Constant.scale(20),
        height: Constant.scale(20),
        backgroundColor:Constant.colorTxtDefault,
        justifyContent:'center',
        alignItems:'center'

    },
    contentStyle:{
        flex:1,
        // backgroundColor:'pink',
        justifyContent:'center',
        alignItems:'center'
    }
})
