/**
 * Created by <PERSON> on 2018/4/3.
 */
import {
  observable,
  computed,
  autorun,
  action,
  useStrict,
  runInAction,
  toJS
} from "mobx";
import { Widget } from "rn-yunxi";
import ToastUtil from "../util/ToastUtil";
import ValidateUtil from "../util/ValidatorUtil";
import UserStore from './User'
const { CommonFlatList } = Widget;
const Tag = "ChargeWineStore";

useStrict(true); //这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 费用赠酒
 */
class ChargeWineModel {
  itemIdArray = [];
  accountIDMap = new Map(); //给结算确认使用的数据
  accountListData = []; //费用赠酒，瓶盖赠酒帐务列表
  @observable listParams = {
    //费用赠酒列表
    listState: CommonFlatList.STATE_INIT,
    enableLoadMore: false,
    enableRefresh: true,
    data: [],
    isWholeSend: 0 //是否整版销售(0-否，1-是)
  };
  @observable showListData = []; //展示数组

  constructor() {}

  /**
   * 获取 2费用赠酒，3瓶盖赠酒帐务列表
   */
  @action
  async getAccountTypeList(accountType, orgId, callBack) {
    let params = { accountType: accountType, orgId: orgId };
    let result;
    try {
      result = await Api.getAccountTypeList(params);
    } catch (error) {}
    runInAction(() => {
      if (result && result.data && result.data.list) {
        this.accountListData = [];
        let allItemIds = [];
        for (let item of result.data.list) {
          if (Array.isArray(item.accountIdAnditemId)) {
            let param = {};
            let itemIds = [];
            for (let subItem of item.accountIdAnditemId) {
              this.accountIDMap.set(
                item.description + subItem.itemId,
                subItem.accountId
              );
              itemIds.push(subItem.itemId);
              allItemIds.push(subItem.itemId);
            }
            param.itemIds = itemIds;
            param.orgId = orgId;
            param.description = item.description;
            this.accountListData.push(param);
          }
        }
        callBack && callBack(allItemIds);
        // Log('=====参数===',toJS(this.accountListData))
        this.getChargeWineList()
          .then()
          .catch(err => {});
      }
    });
  }

  /**
   * 获取费用赠酒列表
   */
  @action
  async getChargeWineList() {
    runInAction(() => {
      this.listParams.listState = CommonFlatList.STATE_REFRESH;
    });

    let result = null;
    try {
      result = await Api.getChargeWineList(this.accountListData);
    } catch (error) {}

    runInAction(() => {
      if (result && result.data && result.data.itemList) {
        this.listParams.data = result.data.itemList;
        // 该列表的商品，默认为不带板。设置batchSaleNum、bearing
        if (this.listParams.data.length != 0) {
          for (let obj of this.listParams.data) {
            obj.batchSaleNum = "0";
            obj.bearing = "";
            obj.withPlateFlag = !!obj.withPlateFlag ? 1 : 0;
          }
        }
        this.listParams.isWholeSend = result.data.isWholeSend;
        this.listParams.listState = CommonFlatList.STATE_NO_MORE;
        this.makeRealListData();
      }
    });
  }

  /**
   * 生成渲染列表数据
   * @param array
   */
  @action
  makeRealListData = modifyItem => {
    this.showListData = [];
    let array = this.listParams.data;
    let newArray = [];
    if (array && array.length > 0) {
      for (let item of array) {
        item.isZeroFourGoods = this.handleZeroFourGood(item.itemCode);
        let newItem = {};
        if (modifyItem) {
          if (modifyItem.itemId === item.itemId) {
            //选中的商品
            item = modifyItem;
          }
          newItem = Object.assign({}, item);
          newArray.push(Object.assign({}, toJS(newItem)));
        } else {
          newItem = Object.assign({}, item);
          newItem.itemNum = 0; //商品数目
          newItem.tmpItemNum = 0;
          newItem.awardsName = null; //奖项名
          newItem.awardsId = 0;
          newItem.salesChannelCode = null;
          newItem.subSalesChannelCode = null;
          newItem.withPlateFlag = 0; //0不带板，1带板
          newItem.batchSaleNum = "0"; // 不带板的情况下，默认托板承载规格为 0
          newItem.bearing = ""; // 不带板的情况下，默认托板承载规格文案
          newArray.push(Object.assign({}, toJS(newItem)));
        }
      }

      this.showListData = newArray;
    }
  };

  handleZeroFourGood = code => {
    if (code && code.startsWith("04")) {
      return true;
    }
    return false;
  };

  @action
  modifyItem = async (item, newSku, orgId) => {
    let result = null;
    try {
      let params = Object.assign(
        {},
        {
          itemId: item.itemId,
          orgId: orgId, //组织
          awardsId: newSku.awardsId, //奖项
          salesChannelCode: newSku.channelId.salesChannelCode, //	销售渠道编码
          subSalesChannelCode: newSku.channelId.subSalesChannelCode, //	销售子渠道编码
          withPlateFlag: newSku.withPlateFlag //	是否带板
        }
      );
      if (params.awardsId && params.salesChannelCode) {
        result = await Api.getPrice(params);
      }
    } catch (err) {}

    runInAction(() => {
      item.orgId = orgId;
      item.orgName = newSku.orgName.orgName; //组织名
      item.withPlateFlag = newSku.withPlateFlag; //改变带板
      item.awardsId = newSku.awardsId; //改变奖项
      item.awardsName = newSku.awardsName;
      item.channelId = newSku.channelId;
      item.channelName = newSku.channelName;
      item.salesChannelCode = newSku.channelId.salesChannelCode;
      item.subSalesChannelCode = newSku.channelId.subSalesChannelCode;
      item.batchSaleNum = newSku.batchSaleNum;
      item.bearing = newSku.bearing;
      if (result && result.data) {
        item.advanceDeposit = result.data.advanceDeposit;
        item.discount = result.data.discount;
        item.priceList = result.data.priceList;
      }
      this.makeRealListData(item);
    });
  };

  //修改商品数量
  @action
  modifyItemNum = (itemIndex, text, isTmp, isZeroFourGoods) => {
    text = text + "";
    let verify = false;
    let regEx = /^[0-9][0-9]*$/;
    verify = regEx.test(text);
    if (isZeroFourGoods) {
      verify = true;
    }
    if (isTmp && text.length == 0) {
      verify = true;
    }
    if (verify) {
      if (isTmp) {
        this.listParams.data[itemIndex].tmpItemNum = text;
      } else {
        this.listParams.data[itemIndex].itemNum = text;
      }
    }
  };

  //设置默认awardsId，channelCode
  @action
  setDefaultAwardsIdAndChannelCode = (item, newSku) => {
    item.awardsId = newSku.awardsId; //改变奖项
    item.awardsName = newSku.awardsName;
    item.channelId = newSku.channelId;
    item.channelName = newSku.channelName;
    item.salesChannelCode = newSku.channelId.salesChannelCode;
    item.subSalesChannelCode = newSku.channelId.subSalesChannelCode;
    this.makeRealListData(item);
  };

  //计算商品数目价格等
  selectGoodsCount() {
    let result = {
      totalItem: 0, //总选择商品
      totalCount: 0, //总数量
      totalPrice: 0 //总价格
    };

    for (let item of this.listParams.data) {
      if (parseFloat(item.itemNum) > 0) {
        result.totalItem = result.totalItem + 1;
        result.totalCount = result.totalCount + parseFloat(item.itemNum);

        let itemTotalPrice =
          parseFloat(item.priceList[0].price) * parseFloat(item.itemNum);
        result.totalPrice = result.totalPrice + itemTotalPrice;
      }
    }

    return result;
  }

  //结算订单
  @action
  confirmOrder = async (accountType, orgId) => {
    let array = this.listParams.data;
    let newArray = [];
    let currentItem = null;
    for (let item of array) {
      let accountId = this.accountIDMap.get(item.description + item.itemId);
      if (item.itemNum > 0) {
        let buyItem = {
          itemId: item.itemId,
          itemNum: item.itemNum,
          priceType: item.priceList[0].priceType,
          priceNature: item.priceList[0].priceNature,
          orgId: orgId,
          awardsId: item.awardsId,
          salesChannelCode: item.salesChannelCode,
          subSalesChannelCode: item.subSalesChannelCode || "",
          withPlateFlag: item.withPlateFlag,
          accountId: accountId,
          batchSaleNum: item.batchSaleNum || "0"
        };

        let rule = {
          awardsId: [{ required: true, not: null, msg: "请选择奖项" }],
          salesChannelCode: [{ required: true, not: null, msg: "请选择渠道" }]
        };
        currentItem = UserStore.getChannelName(item.salesChannelCode, item.subSalesChannelCode);
        if (!ValidateUtil.validate(item, rule)) {
          return;
        }

        if(!currentItem.selectParent) {
          ToastUtil.show('请选择渠道')
          return;
        }
        // 有子渠道但是没选
        if(currentItem.selectParent && currentItem.hasChild && !currentItem.selectChild) {
            ToastUtil.show('请选择子渠道')
            return;
        }
        newArray.push(buyItem);
      }
    }

    // console.log('new - 结算时的newArrary = ' + JSON.stringify(newArray));
    // return;

    let json = null;
    try {
      let params = {};
      params.itemBuyDtos = newArray;
      params.zpOrderType = accountType;

      Log("结算订单参数", params);
      if (params.itemBuyDtos && params.itemBuyDtos.length > 0) {
        json = await Api.confirmOftenOrder(params);
      } else {
        ToastUtil.show("请选择商品数量！");
      }
    } catch (e) {}

    return json;
  };
}

export default ChargeWineModel;
