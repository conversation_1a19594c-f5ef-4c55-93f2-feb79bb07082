import {observable, computed, autorun, action, useStrict, runInAction} from 'mobx';
import autobind from 'autobind-decorator';
import {Widget} from 'rn-yunxi';
import {
    Alert,
    InteractionManager
} from 'react-native';

import {toJS} from 'mobx';
const {CommonFlatList} = Widget;


useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action


/**
 * 申请售后
 */
class AfterSaleListModel {

    @observable listParams = {
        pageNum:1,
        listState:CommonFlatList.STATE_INIT,
        enableLoadMore:false,
        enableRefresh:true,
        dataArray:[],
    };
    constructor() {
        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
        }, 10);
    }

    /**
     * 售后订单
     */
    @action
    async getAfterSaleList(loadMore) {
        let listParams = this.listParams;
        let lastListState=listParams.listState;

        runInAction(() => {
            //更新列表状态
            if (loadMore) {
                this.listParams.listState = CommonFlatList.STATE_LOADING_MORE
            } else {
                this.listParams.listState = CommonFlatList.STATE_REFRESH
            }
        });
        let params={afterType: 1};

        if (!loadMore) {
            params.pageNum = 1;
        } else {
            params.pageNum = listParams.pageNum + 1;
        }

        let result;
        try {
            result = await Api.getAfterSaleList(params);
        } catch (error) {

        }

        runInAction(() => {
            if (result && result.data) {
                let hasMore = result.data.pages > result.data.pageNum;
                let oldArray = this.listParams.data;
                let newArray = [];
                if (loadMore) {
                    newArray = oldArray.concat(result.data.list)
                } else {
                    newArray = result.data.list;
                }

                this.listParams.pageNum = result.data.pageNum;
                this.listParams.data = newArray;
                this.listParams.enableLoadMore = hasMore;
                this.listParams.listState = hasMore ? CommonFlatList.STATE_HAS_MORE : newArray.length === 0 ? CommonFlatList.STATE_INIT : CommonFlatList.STATE_NO_MORE;
            }else{
                this.listParams.listState=lastListState;
            }
        })
    }

    cancel(afterId){
        Api.cancelAfterSale(afterId).then((result)=>{
            this.getAfterSaleList(false);
        }).catch()
    }

}


autorun(() => {
});
export default AfterSaleListModel;