import { observable, computed, autorun, action, useStrict, runInAction, toJS, map } from 'mobx';
import ToastUtil from '../util/ToastUtil';
import Api from '../util/Api';
import {Linking,} from 'react-native';
useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 赊销中心
 */
class SubmitPaymentModel {
    @observable canDownload = true;//是否可以下载欠款单
    @observable uploadFileStatus = '';//上传的欠款单文件状态：0：待提交、1：已提交、2：已驳回
    @observable arrearageNo = '';//欠款单号

    constructor() {
        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
        }, 0);
    }

    @observable fileList = [];//上传的附件
    @observable rejectReason = '';//驳回原因
    @action
    setFileList(fileList) {
        this.fileList = fileList;
    }
  
    @action
    setUploadFileStatus(content) {
        this.uploadFileStatus = content;
    }

    /**
     * 下载欠款单
     */
    @action
    getArrearageInfo(orderNo,url) {
        let link = '';
        if(url!=null){
            link = url;
        }else if(orderNo!=null){
            link = Config.HOST_TRADE + `/api/v1/pearlriver/trade/order/export/arrearage/info?orderNo=${orderNo}`;
        }
        Api.checkArrearage(orderNo).then((result)=>{
            Linking.canOpenURL(link).then(supported => {         
                    if (!supported) {            
                        console.warn('Can\'t handle url: ' + link);            
                    } else {            
                        return Linking.openURL(link);            
                    }            
                }).catch(err => console.error('An error occurred',link));  
            }).catch();
    }

    //上传欠款单
    @action
    uploadFile(orderId, arrearageNo, fileList, callback) {

        let fileListParams = [];
        for (let item of fileList) {
            fileListParams.push({
                fileUrl: item.fileUrl,
                fileName: item.fileName,
            })
        }
        let params = {
            orderId: orderId,
            arrearageNo: arrearageNo,
            fileList: fileListParams,
        }
        Api.uploadFile(params).then(result => {
            if (result && result.resultMsg == 'success') {
                runInAction(() => {
                    callback && callback();
                })
            }
        }).catch();
    }

    @observable imagesMap = new Map();
    //查看欠款单
    @action
    chargeSalesInfo(orderId,callback) {
        Api.chargeSalesInfo(orderId).then(result => {
            if (result && result.data) {
                runInAction(() => {
                    this.arrearageNo = result.data.arrearageNo;
                    this.uploadFileStatus = result.data.uploadFileStatus;
                    if(result.data.fileList){
                        this.fileList = result.data.fileList;
                        this.rejectReason = result.data.rejectReason;
                        let images= [];
                        let pdf = [];
                        for(let item of this.fileList){
                            let imagePath = item.fileUrl.toLowerCase();
                            if (imagePath.lastIndexOf(".jpg") > 0 || imagePath.lastIndexOf(".png") > 0) {
                                images.push(imagePath);
                                this.imagesMap.set(imagePath,item.fileName);
                            } else if (imagePath.lastIndexOf(".pdf") > 0) {
                                pdf.push(item);
                            }
                        }
                        callback && callback(images,pdf)
                    }
                })
            }
        }).catch();
    }


}

export default SubmitPaymentModel;