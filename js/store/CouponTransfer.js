/**
 * Created by whw on 2018/1/19.
 */
import { observable, computed, autorun, action, useStrict, runInAction } from 'mobx';
import autobind from 'autobind-decorator';
import { Widget } from 'rn-yunxi';
const { CommonFlatList } = Widget;
import { toJS } from 'mobx';


useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 门店核销列表
 */

class ConponTransferModel {

    //申请转入奖券列表
    @observable couponList = [];

    //转入列表
    tabs = [
        { label: '待审核', status: 0 },
        { label: '已通过', status: 1 },
        { label: '已驳回', status: 2 },
        { label: '已取消', status: 3 },
    ];

    @observable tabIndex = 0;
    @observable listParamsArray = Array(4).fill({
        pageNum: 1,
        listState: CommonFlatList.STATE_INIT,
        enableLoadMore: false,
        enableRefresh: true,
        data: [],
        selectOrgIndex: 0,
        selectTimeIndex: 0,
    });

    @observable transferDetail = {};


    constructor() {
        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
        }, 0);
    }

    /**
     * 获取当前组织下可申请转入的奖券列表
     */
    @action
    async getCouponsByOrgId(orgId) {
        let result;

        try {
            result = await Api.getCouponsByOrgId({ orgId: orgId });
        } catch (err) {
            return Promise.reject(err);
        }

        runInAction(() => {
            if (result && result.data) {
                result.data.map((item) => {
                    item.chooseQuantity = undefined;
                })
                this.couponList = result.data;
            }
        })
    }

    /**
     * 改变选择数量
     */
    @action
    changeChooseQuantity(index, chooseQuantity) {
        this.couponList[index].chooseQuantity = chooseQuantity;
    }

    /**
     * 申请转入
     */
    @action
    async applyTransfer(params) {
        try {
            const result = await Api.applyTransfer(params);
        } catch (err) {
            return Promise.reject(err);
        }
    }



    @computed
    get getTabs() {
        return this.tabs;
    }

    @action
    setTabIndex(index) {
        this.tabIndex = index;
    }

    /**
     * 获取我的奖券转入列表
     */
    @action
    async getTransferList(index, loadMore, params) {
        let listParams = this.listParamsArray[index];
        params.pageSize = 10;
        params.status = this.tabs[index].status;

        runInAction(() => {
            //更新列表状态
            if (loadMore) {
                this.listParamsArray[index].listState = CommonFlatList.STATE_LOADING_MORE
            } else {
                this.listParamsArray[index].listState = CommonFlatList.STATE_REFRESH
            }
        });

        if (!loadMore) {
            params.pageNum = 1;
        } else {
            params.pageNum = listParams.pageNum + 1;
        }

        let result;
        try {
            result = await Api.getTransferList(params);
        } catch (err) {
            return Promise.reject(err);
        }

        runInAction(() => {
            if (result && result.data) {
                let data = result.data;
                let hasMore = data.pages > data.pageNum;
                let oldArray = listParams.data;
                let newArray = [];
                if (!loadMore) {
                    newArray = data.list ? data.list : [];
                } else {
                    newArray = oldArray.concat(data.list ? data.list : []);
                }

                newArray.map((item) => {
                    item.isShowAll = false;
                })

                this.listParamsArray[index].data = newArray;
                this.listParamsArray[index].pageNum = result.data.pageNum;
                this.listParamsArray[index].enableLoadMore = hasMore;
                this.listParamsArray[index].listState = hasMore ? CommonFlatList.STATE_HAS_MORE : newArray.length === 0 ? CommonFlatList.STATE_INIT : CommonFlatList.STATE_NO_MORE;
            }
        })
    }

    /**
     * 组织选择
     * */
    @action
    setSelectOrgIndex(selectOrgIndex) {
        this.listParamsArray[this.tabIndex].selectOrgIndex = selectOrgIndex;
    }

    /**
     * 日期选择
     * */
    @action
    setSelectTimeIndex(selectTimeIndex) {
        this.listParamsArray[this.tabIndex].selectTimeIndex = selectTimeIndex;
    }

    /**
     * 取消转入
     */
    @action
    async cancelTransfer(id) {
        try {
            const result = await Api.cancelTransfer({ id: id });
        } catch (err) {
            return Promise.reject(err);
        }
    }

    /**
     * 获取奖券转入详情
     */
    @action
    async getTransferDetail(id) {
        try {
            const result = await Api.getTransferDetail({ id: id });
            runInAction(() => {
                this.transferDetail = result.data;
            })
        } catch (err) {
            return Promise.reject(err);
        }
    }

}

export default ConponTransferModel;