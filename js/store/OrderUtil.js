/**
 * Created by whw on 2018/1/19.
 */
import {observable, computed, autorun, action, useStrict, runInAction} from 'mobx';
import autobind from 'autobind-decorator';
import {Widget} from 'rn-yunxi';

const {CommonFlatList} = Widget;
import {toJS} from 'mobx';
import UserStore from '../store/User';


useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action

/**
 * 订单工具类
 */
class OrderUtilModel {


    @observable tabIndex = 0;
    @observable type = 1;//1 经销商-我的订单，2 分销商-我的订单 3 经销商-分销商的订单
    constructor() {
        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
        }, 0);
    }

    /**
     * 设置类型
     * @param type
     */
    @action
    setType(type) {
        this.type = type;
    }

    @action
    setTabIndex(index) {
        this.tabIndex = index;
    }


    /**
     * 取消订单
     */
    async cancelOrder(params,callBack) {

        let result;
        try {
            result = await Api.cancelOrder(params);
        } catch (error) {

        }
        if (result && result.resultCode+''== '0' ) {

            callBack && callBack();
        }
        return result;
    }

    /**
     * 再次购买
     */
    @action
    async buyAgain(orderId, callBack) {
        let result;
        try {
            result = await Api.buyAgain(orderId);
        } catch (error) {

        }
        if (result && result.data && result.data.length>0) {

            callBack && callBack();
        }
        return result;
    }

    /**
     * 确认收货
     */
    async confirmReceive(params) {
        let result;
        result = await Api.confirmReceive(params);
        return result;
    }


    /**
     * 转单
     */
    async turnOrder(params, callBack) {

        Log('=================parasm',toJS(params))
        let result = null;
        try {
            result = await Api.getTurnOrder(params)
        } catch (error) {
        }

        if (result && result.data) {
            callBack(result.data)
        }
        return result;
    }

    /**
     * 查询订单合并支付信息
     */
    async getMergerOrderList(orderId) {
        let result = null;
        result = await Api.getMergerOrderList(orderId);
        if (result && result.data) {
            return result.data;
        } else {
            return Promise.reject(null);
        }
    }
}

const OrderUtil=new OrderUtilModel();
autorun(() => {
});
export default OrderUtil;