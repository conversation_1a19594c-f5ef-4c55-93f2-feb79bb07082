/**
 *
 * Created by xiaowz on 2018/11/20.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import { observable,computed,autorun,action,useStrict,runInAction } from 'mobx';
import autobind from 'autobind-decorator';
import { Widget } from 'rn-yunxi';
const {CommonFlatList} = Widget;
useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 地址
 */
class GoodsDetailListStore {
    @observable awardsList = []
    @observable priceList = []
    constructor() {
        setTimeout(()=>{
            //一定要加点延迟，不然Api无法获取
        },10);
    }

    /**
     * 获取奖项
     * */
    @action
     getAwardsListAndPriceList = async(params,callback)=>{
        let result1 = null;
        let result2 = null;
        try {
             result1 = await Api.getAwardList(params)
             result2 = await Api.getPriceList(params)
        }catch (e) {
            Log(e)
        }

        runInAction(()=>{
            if (result1 && result1.data){
                this.awardsList = result1;
            }

            if (result2 && result2.data){
                this.priceList = result2
            }


            callback &&  callback(result1,result2)

        })


    }

    @action
    transferOderClick = async(params)=>{

        // this.props.goodDetailList.transferOderClick(params)
        let result = null;
        try {
            result = await Api.turnOrder(params);
        }catch (e) {
            Log(e)
        }

        runInAction(()=>{

        })

    }


}
const GoodsDetailList =new GoodsDetailListStore();

autorun(()=>{
});
export default GoodsDetailList;