/**
 * Created by whw on 2018/1/17.
 */
import {
  observable,
  computed,
  autorun,
  action,
  useStrict,
  runInAction
} from "mobx";
import { ListView } from "react-native";
import { Widget, Util } from "rn-yunxi";
import autobind from "autobind-decorator";
import _ from "lodash";
import User from "./User";
const { CommonListView } = Widget;
const { StorageUtil } = Util;
useStrict(true); //这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action

const categoryList = ["分类"];

class HomePageModel {
  @observable dataArray = []; //数据源
  @observable pageNum = 1; //当前页
  @observable listState = CommonListView.STATE_INIT;
  @observable enableLoadMore = false;
  ds = new ListView.DataSource({ rowHasChanged: (r1, r2) => r1 !== r2 });

  constructor() {
    setTimeout(() => {
      //一定要加点延迟，不然Api无法获取
    }, 0);
  }

  @computed
  get dataSource() {
    return (this.ds = this.ds.cloneWithRows(
      this.dataArray ? this.dataArray.slice() : []
    ));
  }

  /**
   * 首页数据本地请求
   * */
  @action
  getHomeIndex = async () => {
    runInAction(() => {
      this.listState = CommonListView.STATE_REFRESH;
    });
    let adResult;
    let hotResult;
    let categoryResult;
    try {
      adResult = await Api.getHomeAd();
      categoryResult = await Api.getHomeCategory();
      hotResult = await Api.getHotGoods();
    } catch (error) {
      Log(error);
    }
    // try {
    //     categoryResult = await Api.getHomeCategory();
    // } catch (error) {
    //     // Log(error)
    // }
    // try {
    //     hotResult = await Api.getHotGoods();
    // } catch (error) {
    //     // Log(error)
    // }
    runInAction(() => {
      let banners = { type: 1, data: adResult ? adResult.data : [] };
      let categoryList = {
        type: 2,
        data: categoryResult ? categoryResult.data : null
      };
      let itemList = { type: 3, data: [] };
      if (User.isDealer()) {
        itemList.data = [
          {
            imgUrl: require("../module/img/home-page/ic_home_order.png"),
            name: "我的订单"
          },
          {
            imgUrl: require("../module/img/home-page/ic_home_recyclebottle.png"),
            name: "回瓶管理"
          },
          {
            imgUrl: require("../module/img/home-page/ic_home_money.png"),
            name: "我的资金"
          },
          {
            imgUrl: require("../module/img/home-page/ic_home_greenorder.png"),
            name: "分销商订单"
          },
          {
            imgUrl: require("../module/img/home-page/fenxiaoshang.png"),
            name: "分销商核销"
          },
          {
            imgUrl: require("../module/img/home-page/shenqingzhuanru.png"),
            name: "申请转入"
          }
        ];
      } else {
        itemList.data = [
          {
            imgUrl: require("../module/img/home-page/ic_home_order.png"),
            name: "我的订单"
          },
          {
            imgUrl: require("../module/img/home-page/ic_home_recyclebottle.png"),
            name: "回瓶管理"
          },
          {
            imgUrl: require("../module/img/home-page/mendianhexiao.png"),
            name: "门店核销"
          },
          {
            imgUrl: require("../module/img/home-page/buhuo.png"),
            name: "申请补货"
          }
        ];
      }
      let recommend = { type: 4 };
      let recommendEmpty = { type: 5 };

      let hotResultSize = _.get(hotResult, "data.length", 0);
      let array = [banners, categoryList, itemList, recommend];
      // Log('home result',array);
      this.dataArray = array.concat(
        hotResultSize > 0 ? hotResult.data : recommendEmpty
      );
      if (this.dataArray.length > 0) {
        this.listState = CommonListView.STATE_INIT;
      } else {
        this.listState = CommonListView.STATE_INIT;
      }
    });
  };

  @action
  clearData = () => {
    this.dataArray = [];
  };

  @action
  getUserNoticeReadStatus = async () => {
    let result;
    try {
      result = await Api.getUserNoticeReadStatus();
      // Log("getUserNoticeReadStatus =" + JSON.stringify(result));
      StorageUtil.saveString(
        "USER_NOTICE_READED",
        result.data ? "true" : "false"
      );
      return result.data;
    } catch (error) {
      Log(error);
    }
  };

  @action
  getUserNotice = async () => {
    let result;
    try {
      result = await Api.getUserNotice();
      return result.data;
    } catch (error) {
      Log(error);
      return false;
    }
  };
}

const HomePage = new HomePageModel();
autorun(() => {});

export default HomePage;
