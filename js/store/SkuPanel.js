import {observable, computed, autorun, action, useStrict, runInAction, isObservableArray, toJS} from 'mobx';
import autobind from 'autobind-decorator';
import {Alert} from 'react-native';
import UserStore from './User'
import ItemCountUtil from "../util/ItemCountUtil";

useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * User:lao.jianfeng
 * 商品SKU
 */
class SkuPanelModel {
    // @observable chancelList = [];
    @observable orgList = [];//提货组织列表,显示用
    @observable orgListMap = new Map();//提货组织列表，数据组装用
    @observable awardsMap = new Map();//通过提货组织Id获取奖项
    @observable awardsList = [];//奖项列表
    @observable priceListMap = new Map();//通过提货组织Id获取价格对象，此是缓存对象
    @observable discount = 0;//折价
    @observable plateFlagList =  [{name: '不带板', value: 0}, {name: '带板', value: 1}];
    @observable selectOrgId = null;//提货组织id
    @observable selectAwardId = null;//奖项
    @observable selectPlateId = null;//是否带板
    @observable selectChannelId = {//选择的渠道
        salesChannelCode: null,
        subSalesChannelCode: null,
    };
    @observable selectChannelName = {//选择的渠道名
        salesChannelName: null,
        subSalesChannelName: null,
    };
    @observable itemId = null;//商品id
    @observable suggestNumber = 0;//建意数量
    @observable mandatoryNumber = 0;//强制数量
    @observable purchaseCount = '0';//采购数量
    @observable tmpPurchaseCount = '0';//输入中的采购数量
    @observable isWholeSend = 0;//是否整版销售(0-否，1-是)
    @observable enableZero = true;//是否可以输入0
    @observable purchaseQuantity = {
        multiple: 0,//当前倍数
        isMandatory: false,//是否强制
    };//当前购买倍数

    checkMandatcory = true;

    // 托板承载规格 相关
    @observable palletStruList = []; // 托板承载规格列表
    @observable batchSaleNum = null; // 选择的托板规格
    @observable bearing = ''; // 选择的托板规格文案

    constructor() {
        this.initCallback = null
    }

    setCheckMandatcory(check){
        this.checkMandatcory = check;
    }

    setInitCallback(initCallback) {
        this.initCallback = initCallback;
    }

    //设置折价
    @action
    async obtainDiscount() {
        let params = {
            orgId: this.selectOrgId,
            itemId: this.itemId,
            awardsId: this.selectAwardId,
            salesChannelCode: this.selectChannelId.salesChannelCode,
            subSalesChannelCode: this.selectChannelId.subSalesChannelCode,
        };
        let paramsArray = [];
        paramsArray.push(params)
        let json = null;
        try {
            json = await Api.getDiscountPrice(paramsArray)
        } catch (e) {

        }
        runInAction(() => {
            if (json && json.data && json.data.length > 0) {
                this.discount = json.data[0]
            }
        })

    }


    /**
     *
     * 设置商品Id
     * @param itemId
     */
    @action
    setItemId(itemId, requestOrgList) {
        this.itemId = itemId;
        if (requestOrgList) {
            this.obtainPickOrgList().then().catch();
        }

    }

    /**
     * 费用赠酒和瓶盖赠酒用到
     * @param itemId
     * @param orgItem
     */
    @action
    setItemIdAndOrgItem(itemId, orgItem) {
        this.itemId = itemId;
        this.orgListMap.clear();
        this.orgListMap.set(orgItem.orgId, orgItem);
        this.orgList = [orgItem];
        this.checkPurchaseQuantity();
        this.obtainAwardsList().then().catch();

    }

    @action
    setEnableZero(enable) {
        this.enableZero = enable;
    }


    /**
     * 设置数量
     * @param count
     * @param isTmp
     */
    @action
    setPurchaseCount(count, isTmp,isZeroFourGood) {


        let verify = ItemCountUtil.validatorInputValue(count, isTmp, this.enableZero ,isZeroFourGood);

        count = ItemCountUtil.transformInputValue(count, isTmp, this.enableZero,isZeroFourGood);


        Log('-------count',count)
        if (verify) {

            if (isTmp) {

                this.tmpPurchaseCount = count
            } else {

                let newCount = ItemCountUtil.validatorCountValue(count, this.purchaseQuantity) + '';

                Log('setPurchaseCount', newCount, this.purchaseQuantity.multiple,this.purchaseQuantity.isMandatory,this.checkMandatcory)
                let showAlert = false;
                if (this.purchaseQuantity.multiple > 1 && newCount != count) {
                    showAlert = false;
                }
                if (showAlert) {

                    let btn = [];
                    if (!this.purchaseQuantity.isMandatory) {
                        btn.push({
                            text: '取消', onPress: () => {
                                runInAction(() => {
                                    this.purchaseCount = count;
                                })
                            }
                        });
                        btn.push({
                            text: '自动修改', onPress: () => {
                                runInAction(() => {
                                    this.purchaseCount = newCount;
                                })
                            }
                        })
                    } else {
                        this.purchaseCount = newCount;
                        btn.push({text: '确定'})
                    }
                    Alert.alert(this.purchaseQuantity.isMandatory ? '整板提示' : '整板建议',
                        this.purchaseQuantity.isMandatory ? `商品在当前组织仅支持整板下单,建议修改为${newCount},或${this.purchaseQuantity.multiple}的整数倍` :
                            `便于发货运输建议您整板下单,建议改为${newCount},或${this.purchaseQuantity.multiple}的整数倍`, [...btn])
                } else {
                    Log('不弹框111111111222222222')
                    if(this.checkMandatcory){
                        this.purchaseCount = newCount;
                    }else{
                        this.purchaseCount = count;
                    }
                }


            }
        }else {

        }
    }

    /**
     * 计算当前的倍数
     */
    @action
    checkPurchaseQuantity() {
        let orgItem = this.orgListMap.get(this.selectOrgId);
        // Log('=====================orgItem======',toJS(orgItem),this.selectOrgId)
        let isRibband = 0;
        if (UserStore && UserStore.companyInfo){
            isRibband = UserStore.companyInfo.isRibband;
        }


        if (orgItem) {
            this.isWholeSend = 0;
            try {
                Log('=====this.isWholesend===',orgItem.isWholeSend,isRibband)
                this.isWholeSend = parseInt(orgItem.isWholeSend);
                this.suggestNumber = orgItem.suggestNumber ? orgItem.suggestNumber : 0;
                this.mandatoryNumber = orgItem.mandatoryNumber ? orgItem.mandatoryNumber : 0;
            } catch (err) {
                //处理isWholeSend返回null,默认为0
            }

            if (isRibband == 0){
                this.plateFlagList = [{name: '不带板', value: 0}];
                this.selectPlateId = 0;
            }else {
                this.plateFlagList = [{name: '不带板', value: 0}, {name: '带板', value: 1}];
            }

            if (this.isWholeSend === 0) {
                this.purchaseQuantity.isMandatory = false;
                this.purchaseQuantity.multiple = this.suggestNumber;
            } else {
                this.purchaseQuantity.isMandatory = true;
                if (this.selectPlateId === 1) {
                    //带板 整板倍数
                    this.purchaseQuantity.multiple = this.mandatoryNumber;
                } else {
                    //不带板 整板倍数
                    this.purchaseQuantity.multiple = this.suggestNumber;
                }
            }


        }
        Log('checkPurchaseQuantity', toJS(this.purchaseQuantity));
        this.setPurchaseCount(this.purchaseCount, false);
    }

    /**
     * 设置-建议数量
     */
    @action
    setSuggestNumber(suggestNumber) {
        this.suggestNumber = suggestNumber;
    }

    /**
     * 设置-强制数量
     */
    @action
    setMandatoryNumber(mandatoryNumber) {
        this.mandatoryNumber = mandatoryNumber;
    }

    /**
     * 设置选择带板属性
     * @param value 1 带板 0 不带板
     */
    @action
    setSelectPlateFlag(value) {
        this.selectPlateId = value;
        this.checkPurchaseQuantity();
    }

    /**
     * 设置选中的Tab
     * @param tab
     * @param id
     */
    setTabSelect(tab, id) {
        if (tab === 0) {
            this.setSelectOrgId(id);
        } else if (tab === 1) {
            this.setSelectAwardId(id);
        } else if (tab === 2) {
            this.setSelectChannelId(id.salesChannelCode, id.subSalesChannelCode);
        } else if (tab === 3) {
            this.setSelectPlateFlag(id);
        }
    }

    /**
     * 设置选择提货组织Id
     * @param value
     */
    @action
    setSelectOrgId(value, checkAward = true) {
        this.selectOrgId = value;
        if (checkAward) {
            if (this.selectAwardId && this.selectOrgId) {
                //原来奖项Id是否在新的组织存在，不在就清空
                let find = false;
                let array = this.awardsMap.get(this.selectOrgId);

                if (array) {
                    for (let item of array) {
                        if (item.id == this.selectAwardId) {
                            find = true;
                            this.setSelectAwardId(item.id);
                            break;
                        }
                    }
                }
                if (!find) {
                    this.selectAwardId = array && array.length > 0 ? array[0].id : null
                }
            }
            this.checkPurchaseQuantity()
            this.checkAwardsValid()
        }

    }

    @action
    setPriceListItem(orgId, data) {
        this.priceListMap.set(orgId, data);
    }

    /**
     * 设置选择奖项id
     * @param value
     */
    @action
    setSelectAwardId(value) {
        // this.checkAwardsValid()
        this.selectAwardId = value;
    }

    /**
     * 设置选择渠道
     * @param salesChannelCode
     * @param subSalesChannelCode
     */
    @action
    setSelectChannelId(salesChannelCode, subSalesChannelCode) {
        // Log('=========设置渠道',salesChannelCode,subSalesChannelCode)
        if (UserStore) {
            // Log('=========设置渠道1111111')
            let channelId = {
                salesChannelCode: salesChannelCode,
                subSalesChannelCode: subSalesChannelCode
            }
            let newChannelId = UserStore.getValidChannelId(channelId);
            this.selectChannelId.salesChannelCode = newChannelId.salesChannelCode;
            this.selectChannelId.subSalesChannelCode = newChannelId.subSalesChannelCode;
        } else {
            // Log('=========设置渠道2222222')
            this.selectChannelId.salesChannelCode = salesChannelCode;
            this.selectChannelId.subSalesChannelCode = subSalesChannelCode;
        }

    }


    /**
     *  设置选择渠道名字
     * @param salesChannelName
     * @param subSalesChannelName
     */
    @action
    setSelectChancelName(salesChannelName, subSalesChannelName) {
        this.selectChannelName.salesChannelName = salesChannelName;
        this.selectChannelName.subSalesChannelName = subSalesChannelName;
    }

    /**
     * 设置选择的托板规格
     * @param boxNum
     */
    @action
    setBatchSaleNum(boxNum) {
        this.batchSaleNum = boxNum;
    }

    /**
     * 设置选择的托板规格文案
     * @param text
     */
    @action
    setBearing(text) {
        this.bearing = text;
    }

    @computed
    get getSkuTab() {
        return [
            {label: '提货组织', tab: 0, labelKey: 'orgName', idKey: 'orgId'},
            {label: '奖项', tab: 1, labelKey: 'name', idKey: 'id'},
            {label: '渠道', tab: 2, labelKey: 'salesChannelName', idKey: null},
            {label: '是否带板', tab: 3, labelKey: 'name', idKey: 'value'},
            {label: '托板规格', tab: 4, labelKey: 'bearing', idKey: 'boxnum'},
        ]
    }

    @computed
    get getSkuTabForGiftWine() {
        return [
            {label: '奖项', tab: 1, labelKey: 'name', idKey: 'id'},
            {label: '渠道', tab: 2, labelKey: 'salesChannelName', idKey: null},
            {label: '是否带板', tab: 3, labelKey: 'name', idKey: 'value'},
            {label: '托板规格', tab: 4, labelKey: 'bearing', idKey: 'boxnum'},
        ]
    }

    /**
     * 外部设置提货组织
     * @param orgList
     */
    @action
    setPickOrgList(orgList) {

        if (orgList && orgList.length > 0) {
            this.orgList = orgList;
            if (this.orgList) {
                this.orgList.map((item, index) => {
                    this.orgListMap.set(item.orgId, item);
                })
            }
        }
        this.checkPurchaseQuantity();
    }

    /**
     * 外部设置奖项列表
     * @param awardsMap
     */
    @action
    setAwards(awardsMap) {
        this.awardsMap = awardsMap;
        // Log('初始化奖项map',JSON.stringify(this.awardsMap))
        this.checkAwardsValid()
    }

    /**
     * 获取提货组织
     * @return {Promise.<void>}
     */
    @action
    async obtainPickOrgList() {
        let result = null;
        try {
            result = await Api.getPickOrgList({itemIdList: this.itemId});
        } catch (err) {
        }
        runInAction(() => {
            if (result && result.data && Array.isArray(result.data)) {
                this.orgListMap.clear();
                if (result.data.length > 0 && Array.isArray(result.data[0])) {
                    this.orgList = result.data[0];
                    if (this.orgList) {
                        this.orgList.map((item, index) => {
                            this.orgListMap.set(item.orgId, item);
                        })
                    }
                }
                this.checkPurchaseQuantity();
                this.obtainAwardsList().then().catch();

            }
        })
    }

    /**
     * 获取奖项列表
     * @return {Promise.<void>}
     */
    @action
    async obtainAwardsList() {
        let params = [];
        //通过获取的提货组织，获取全部奖项列表
        if (this.orgList && isObservableArray(this.orgList)) {
            for (let item of this.orgList) {
                params.push({
                    itemId: this.itemId,
                    orgId: item.orgId
                })
            }
        }

        let result = null;
        try {
            result = await Api.getAwardList(params);
        } catch (err) {

        }
        runInAction(() => {

            if (result && result.data && result.data.length > 0) {

                if (params.length != result.data.length) {

                }

                params.map((item, index) => {
                    this.awardsMap.set(item.orgId, index < result.data.length ? result.data[index] : null)
                });
                this.checkAwardsValid();
                this.initCallback && this.initCallback();


            }
        })
    }


    @action
    checkAwardsValid() {
        let firstItem = null
        if (this.selectOrgId && this.awardsMap) {
            let array = this.awardsMap.get(this.selectOrgId);
            // Log('测试奖项数据------',this.selectOrgId,toJS(array))
            if (array && array.length > 0) {
                firstItem = array[0];
                if (this.selectAwardId) {
                    for (let item of array) {
                        if (item.id == this.selectAwardId) {
                            firstItem = item;
                            break;
                        }
                    }
                }

            }
        }
        if (firstItem) {
            this.setSelectAwardId(firstItem.id);
        }


    }

    @computed
    get selectOrgName() {
        let result = '请选择组织'
        if (this.selectOrgId) {
            let item = this.orgListMap.get(this.selectOrgId);
            if (item) {
                result = item.orgName;
            }
        }
        return result
    }

    @computed
    get selectChancelFullName() {
        if (this.selectChannelName && this.selectChannelName.salesChannelName) {
            let result = this.selectChannelName.salesChannelName;
            if (this.selectChannelName.subSalesChannelName) {
                result += '/' + this.selectChannelName.subSalesChannelName;
            }
            return result;
        } else {
            return '请选择渠道'
        }
    }


    @action
    selectAwardName(awardsMap) {

        if (this.selectOrgId) {
            let array = awardsMap.get(this.selectOrgId);  //[{id:xx ,name:xx}]
            // Log('=====购物车奖项',this.selectOrgId,toJS(array),this.selectAwardId)
            // Log('============默认奖项过期0',this.selectAwardId)
            let isFind = false
            if (array && array.length > 0) {
                for (let item of array) {
                    if (item.id == this.selectAwardId) {
                        // Log('============默认奖项过期55555',this.selectAwardId)
                        isFind = true;
                        return item.name;
                    }
                }
                if (!isFind) {
                    // Log('============默认奖项过期1',this.selectAwardId,isFind)
                    this.selectAwardId = array[0].id ;
                    return array[0].name;
                }
                // Log('============默认奖项过期2',this.selectAwardId,isFind)
            }
            else {
                // Log('============默认奖项过期3',this.selectAwardId,isFind)
                return '暂无奖项'
            }
        }
        // Log('============默认奖项过期999',this.selectAwardId)
        return '暂无奖项'
    }

    /**
     * 是否带板
     * @return {boolean}
     */
    @computed
    get selectPlateValue() {
        if (this.selectPlateId && this.selectPlateId == 1) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取
     * @param orgId
     * @return {Promise.<*>}
     */
    @action
    async obtainPriceListByOrgId(orgId) {
        let result = null;
        try {
            result = await Api.getPrice({itemId: this.itemId, orgId: orgId});
        } catch (err) {
            return Promise.reject(err);
        }
        runInAction(() => {
            if (result && result.data) {
                this.setPriceListData(orgId, result.data);
            }
        })
        return result;

    }

    /**
     * 设置价格列表
     * @param orgId
     * @param data 数组
     */
    @action
    setPriceListData(orgId, data) {
        this.priceListMap.set(orgId, data);
    }

    //静态方法 不用加compute
    getChoose() {
        let selectOrgName = this.orgListMap.get(this.selectOrgId);
        return {
            itemId: this.itemId,
            orgId: this.selectOrgId,
            orgName: selectOrgName,
            channelName: UserStore.getChancelFullName(this.selectChannelId.salesChannelCode, this.selectChannelId.subSalesChannelCode),
            channelId: this.selectChannelId,
            salesChannelCode: this.selectChannelId.salesChannelCode,
            subSalesChannelCode: this.selectChannelId.subSalesChannelCode,
            awardsId: this.selectAwardId,
            awardsName: this.selectAwardName,
            withPlateFlag: this.selectPlateId,
            purchaseCount: this.purchaseCount,
            bearing: this.bearing,
            batchSaleNum: this.batchSaleNum,
        }
    }

    /**
     * 外部设置托板承载规格
     * @param arr
     */
    @action
    setPalletStruList(arr) {
        this.palletStruList = arr;
    }

    /**
     * 获取 托板承载规格
     */
    @action
    async getPalletStruList() {
        console.log('new - 开始获取托板承载规格');
        let result = null;
        try {
            result = await Api.getPalletStruList();
        } catch (err) {
            return Promise.reject(err);
        }
        runInAction(() => {
            if (result && result.data) {
                console.log(`new - 托板承载规格 = ${JSON.stringify(result.data)}`);
                this.palletStruList = result.data;
            }
        })
        return result;
    }
}

export default SkuPanelModel;