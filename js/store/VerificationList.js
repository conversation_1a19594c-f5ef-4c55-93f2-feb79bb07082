/**
 * Created by whw on 2018/1/19.
 */
import { observable, computed, autorun, action, useStrict, runInAction } from 'mobx';
import autobind from 'autobind-decorator';
import { Widget } from 'rn-yunxi';
const { CommonFlatList } = Widget;
import { toJS } from 'mobx';


useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 门店核销列表
 */

class VerificationListModel {

    //订单状态：2.未发货，41.已发货，5.已收货，9.取消
    tabs = [
        { label: '未发货', status: 2 },
        { label: '已取消', status: 9 },
        { label: '已发货', status: 41 },
        { label: '已收货', status: 5 },
    ];

    @observable tabIndex = 0;
    @observable listParamsArray = Array(4).fill({
        pageNum: 1,
        listState: CommonFlatList.STATE_INIT,
        enableLoadMore: false,
        enableRefresh: true,
        data: []
    });

    constructor() {
        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
        }, 0);
    }

    /**
     * 获取经销商tabs
     * */
    @computed
    get getTabs() {
        return this.tabs;
    }


    @action
    setTabIndex(index) {
        this.tabIndex = index;
    }

    /**
     * 展开全部
     * */
    @action
    showAll(selectTab, orderIndex) {
        this.listParamsArray[selectTab].data[orderIndex].isShowAll = true;
    }

    /**
     * 获取门店核销列表
     */
    @action
    async getVerificationList(index, loadMore) {
        let listParams = this.listParamsArray[index];
        let params = { pageSize: 10 };
        params.orderType = 2;

        params.status = this.tabs[index].status;

        runInAction(() => {
            //更新列表状态
            if (loadMore) {
                this.listParamsArray[index].listState = CommonFlatList.STATE_LOADING_MORE
            } else {
                this.listParamsArray[index].listState = CommonFlatList.STATE_REFRESH
            }
        });

        if (!loadMore) {
            params.pageNum = 1;
        } else {
            params.pageNum = listParams.pageNum + 1;
        }

        let result;
        try {
            result = await Api.getCouponOrderList(params);
        } catch (err) {
            return Promise.reject(err);
        }

        runInAction(() => {
            if (result && result.data) {
                let data = result.data;
                let hasMore = data.pages > data.pageNum;
                let oldArray = listParams.data;
                let newArray = [];
                if (!loadMore) {
                    newArray = data.list ? data.list : [];
                } else {
                    newArray = oldArray.concat(data.list ? data.list : []);
                }

                newArray.map((item) => {
                    item.isShowAll = false;
                })

                this.listParamsArray[index].data = newArray;
                this.listParamsArray[index].pageNum = result.data.pageNum;
                this.listParamsArray[index].enableLoadMore = hasMore;
                this.listParamsArray[index].listState = hasMore ? CommonFlatList.STATE_HAS_MORE : newArray.length === 0 ? CommonFlatList.STATE_INIT : CommonFlatList.STATE_NO_MORE;
            }
        })
    }

    /**
     * 确认发货
     */
    @action
    async deliverOrder(historyId, orderId) {
        try {
            const result = await Api.deliverOrder({ historyId: historyId, orderId: orderId });
            if (result && result.resultCode === 0) {
                this.getVerificationList(this.tabIndex, false);
            }
        } catch (err) {
            return Promise.reject(err);
        }
    }

    /**
   * 确认送达
   */
    @action
    async comfirmDeliver(historyId, orderId, code) {
        try {
            const result = await Api.comfirmConponOrder({ historyId: historyId, orderId: orderId, receiveGoodsCode: code });
            if (result && result.resultCode === 0) {
                this.getVerificationList(this.tabIndex, false);
            }
        } catch (err) {
            return Promise.reject(err);
        }
    }

    /**
     * 清空数据,恢复成初始状态
     */
    @action
    clearVerificationList() {
        if (this.listParamsArray) {
            this.listParamsArray.map((item) => {
                item.pageNum = 1;
                item.listState = CommonFlatList.STATE_INIT;
                item.enableLoadMore = false;
                item.enableRefresh = true;
                item.data = [];
            });
        }
    }

}
const verificationList = new VerificationListModel();

autorun(() => {
});
export default verificationList;