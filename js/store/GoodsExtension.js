/**
 * Created by whw on 2018/1/23.
 */
import {observable, computed, autorun, action, useStrict, runInAction,toJS} from 'mobx';
import autobind from 'autobind-decorator';

useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 商品扩展信息
 */
class GoodsExtensionModel {

    @observable orgListMap = new Map();//itemId为key,value是提货组织数组
    @observable awardsMap = new Map();//itemId+orgId为key,value是奖项数组)
    @observable orgList = [];

    // 托板承载规格列表
    @observable
    palletStruList = [];

    constructor() {
    }

    /**
     * 初始化列表全部提货组织
     * @param itemIdList  ['itemId1','itemId2','itemId3']
     * @return {Promise.<void>}
     *
     */
    @action
    async obtainAllItemOrgList(itemIdList, callback) {
        // Log('初始化列表全部提货组织',itemIdList)
        let result = null;
        if (itemIdList) {
            let params = {itemIdList: ''};
            params.itemIdList = itemIdList.toString();
            try {
                result = await  Api.getPickOrgList(params);
            } catch (err) {
                Log('初始化列表全部提货组织error',err)
            }

        }
        runInAction(() => {
            // Log('初始化列表全部提货组织出错 == 11111',)
            if (result && result.data) {
                this.orgListMap.clear();
                for (let index in result.data) {
                    let orgList = result.data[index]
                    // Log('=======index=======',index,itemIdList[index],orgList)
                    this.orgListMap.set(itemIdList[index], orgList?orgList:[]);
                }


            }else {
                // Log('初始化列表全部提货组织出错 == null',)
            }
            this.obtainAllItemAwards(callback).then(() => {}).catch(err => {});
        })

    }


    /**
     * 手动设置商品组织
     * @param itemIdList
     * @param orgId
     * @param callback
     */
    @action
    setOrgListMap(itemIdList,orgId,callback){

        runInAction(()=> {
            this.orgList = [];
        })
        for (let itemId of itemIdList){
           this.orgList.push({itemId,orgId});
        }

        this.allItemAwards(callback).then().catch(err => {});


    }

    /**
     * 设置商品组织
     * @param itemIdList
     * @param orgId
     * @param callback
     */
    @action
    setBottleCapWineOrgListMap(itemList,callback){

        runInAction(()=> {
            this.orgList = [];
        })
        itemList && itemList.map((item)=> {
            this.orgList.push({itemId: item.itemId,orgId: item.orgId});
        })
        this.allItemAwards(callback).then().catch(err => {});


    }

    /**
     * 获取全部奖项
     * @return {Promise.<void>}
     */
    @action
    async obtainAllItemAwards(callback) {
        let result = null;
        let params = [];
        if (this.orgListMap) {
            for (let [key, value] of this.orgListMap) {
                for (let orgItem of value) {
                    params.push({
                        itemId: key,
                        orgId: orgItem.orgId
                    })
                }
            }
            Log('org == ',toJS(this.orgListMap));
            try {
                result = await Api.getAwardList(params);
            } catch (err) {

            }

        }
        runInAction(() => {
            if (result && result.data) {
                this.awardsMap.clear();
                for (let index in result.data) {
                    let awards = result.data[index];
                    let key = params[index];
                    // Log('===========key====',key,awards)
                    this.awardsMap.set(JSON.stringify(key), awards);
                }
            }
            callback && callback();
        })

    }


    /**
     * 获取全部奖项
     * @return {Promise.<void>}
     */
    @action
    async allItemAwards(callback) {
        let result = null;
        let params = [];
        if (this.orgList && this.orgList.length) {
            this.orgList.map((item)=> {
                params.push({
                    itemId: item.itemId,
                    orgId: item.orgId
                })
            })
            try {
                result = await Api.getAwardList(params);
            } catch (err) {

            }

        }
        runInAction(() => {
            if (result && result.data) {
                this.awardsMap.clear();
                for (let index in result.data) {
                    let awards = result.data[index];
                    let key = params[index];
                    // Log('===========key====',key,awards)
                    this.awardsMap.set(JSON.stringify(key), awards);
                }
            }
            callback && callback();
        })

    }

    /**
     * 通过itemId,获取全部提货组织的全部奖项
     * @param itemId
     * @return {Map}
     */
    obtainItemAwards(itemId) {
        let result = new Map();
        for (let [key, value] of this.awardsMap) {
            try {
                let item = JSON.parse(key);
                // Log('测试========key，value',key,toJS(value))
                if (item.itemId == itemId) {
                    // Log('===========obtainItemAwards',itemId,JSON.stringify(value))
                    result.set(item.orgId, value);
                }
            } catch (err) {
                // Log('测试========key，value,err',err)
            }
            // Log('选中的item奖项---------',itemId,result.toString())
        }
        // Log('选中的item奖项---------',itemId,toJS(result))
        return result;
    }

    /**
     * 获取 托板承载规格
     */
    @action
    async getPalletStruList() {
        console.log('new - 开始获取托板承载规格');
        let result = null;
        try {
            result = await Api.getPalletStruList();
        } catch (err) {
            return Promise.reject(err);
        }
        runInAction(() => {
            if (result && result.data) {
                console.log(`new - 托板承载规格 = ${JSON.stringify(result.data)}`);
                this.palletStruList = result.data;
            }
        })
        return result;
    }

}

export default GoodsExtensionModel;