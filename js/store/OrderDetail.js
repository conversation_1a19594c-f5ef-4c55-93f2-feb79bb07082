/**
 * Created by whw on 2018/1/23.
 */
import {observable, computed, autorun, action, useStrict, runInAction} from 'mobx';
import autobind from 'autobind-decorator';

useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 商品详情
 */
class OrderDetailModel {

    @observable data = null;
    
    constructor() {
        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
        }, 0);
    }

    @action
    setData(data){
        this.data = data;
    }


    @action
    async getOrderDetail(orderId){

        let result;
        try {
            result = await Api.getOrderDetail({orderId: orderId});
        }catch (error){

        }
        runInAction(()=> {
            if (result && result.data)
            {
                this.data = result.data;
            }
        })

    }

}

export default OrderDetailModel;