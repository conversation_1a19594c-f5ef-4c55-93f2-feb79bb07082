import { observable,computed,autorun,action,useStrict,runInAction } from 'mobx';
import autobind from 'autobind-decorator';
import { Widget } from 'rn-yunxi';
const {CommonFlatList} = Widget;
useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 地址
 */
class AddressModel {
    @observable listParams={
        pageNum: 1,
        listState: CommonFlatList.STATE_INIT,
        enableLoadMore: false,
        enableRefresh: true,
        dataArray: [],
        searchAddress:'' // 地址搜索
    };
    constructor() {
        setTimeout(()=>{
            //一定要加点延迟，不然Api无法获取
        },0);
    }

    @action
    setSearchAddress = (text) => {
        this.searchAddress = text
    }

    @action
    clearSearchAddress = () =>{
        this.searchAddress = ''
        this.obtainAddressList(false)
    }

    /**
     * 获取列表
     * */
    @action
    async obtainAddressList(loadMore,callback){
        let lastListState = this.listParams.listState;
        let address = this.searchAddress;
        runInAction(() => {
            //更新列表状态
            if (loadMore) {
                this.listParams.listState = CommonFlatList.STATE_LOADING_MORE
            } else {
                this.listParams.listState = CommonFlatList.STATE_REFRESH
            }
        });
        let params={

        };

        if(loadMore){
            params.pageNum=this.listParams.pageNum+1;
        }else{
            params.pageNum=1;
        }

        if(address){
            params.address = address;
        }

        let result;
        try {
            result=await Api.obtainAddressList(params);
        }catch (err){

        }

        runInAction(()=>{
            if (result && result.data&&result.data.list) {
                let hasMore = result.data.pages > result.data.pageNum;
                let oldArray = this.listParams.dataArray;
                let newArray = [];
                if (loadMore) {
                    newArray = oldArray.concat(result.data.list)
                } else {
                    newArray = result.data.list;
                }
                this.listParams.dataArray=newArray;
                this.listParams.pageNum = result.data.pageNum;
                this.listParams.enableLoadMore = hasMore;
                this.listParams.listState = hasMore ? CommonFlatList.STATE_HAS_MORE : newArray.length === 0 ? CommonFlatList.STATE_INIT : CommonFlatList.STATE_NO_MORE;
            } else {
                this.listParams.listState = lastListState;
            }
            callback&&callback();
        })



    }

    @action
    clearData(){
        this.listParams={
            pageNum: 1,
            listState: CommonFlatList.STATE_INIT,
            enableLoadMore: false,
            enableRefresh: true,
            dataArray: []
        };
    }
}
const Address =new AddressModel();

autorun(()=>{
});
export default Address;