/**
 * Created by <PERSON> on 2018/4/3.
 */
import {observable, computed, autorun, action, useStrict, runInAction, toJS} from 'mobx';
import { Widget } from 'rn-yunxi';
import ToastUtil from "../util/ToastUtil";
const {CommonFlatList} = Widget;


useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 费用赠酒
 */
class BottleBalanceModel {
    @observable listParams = { //费用赠酒列表
        listState: CommonFlatList.STATE_INIT,
        enableLoadMore: false,
        enableRefresh: true,
        data: [],
        isWholeSend:0, //是否整版销售(0-否，1-是)
    };

    constructor() {
    }

    /**
     * 获取 2费用赠酒，3瓶盖赠酒帐务列表
     */
    @action
    async getAccountTypeList(accountType,orgId){
        let params = {accountType:accountType,orgId:orgId};
        let result;
        try {
            result = await Api.getAccountTypeList(params);
        }catch (error){
        }

        runInAction(()=> {
            if (result && result.data && result.data.list)
            {
                let list = result.data.list;
                this.listParams.data = list;
            }
        })

    }

}

export default BottleBalanceModel;