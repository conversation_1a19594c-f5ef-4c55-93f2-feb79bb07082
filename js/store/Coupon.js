/**
 * Created by whw on 2018/1/19.
 */
import { observable, computed, autorun, action, useStrict, runInAction } from 'mobx';
import autobind from 'autobind-decorator';
import { Widget } from 'rn-yunxi';
const { CommonFlatList } = Widget;
import { toJS } from 'mobx';


useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 门店核销列表
 */

class CouponModel {

    @observable tabIndex = 0;
    @observable newMdList = [];//新的收支记录

    // 奖券列表和收支记录
    @observable listParamsArray = Array(2).fill({
        pageNum: 1,
        listState: CommonFlatList.STATE_INIT,
        enableLoadMore: false,
        enableRefresh: false,
        data: []
    });

    //补货单列表
    @observable myReplenishments = {
        dataArray: [],
        listState: CommonFlatList.STATE_INIT,
        enableLoadMore: false,
        enableRefresh: false,
        pageNum: 1,
    };

    constructor() {
        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
        }, 0);
    }

    @action
    setTabIndex(index) {
        this.tabIndex = index;
    }
    /**
     * 获取奖券列表或者收支记录
     */
    @action
    getListMd(startTime,endTime){
        Api.listMd(startTime,endTime).then((result)=>{
            if(result && result.data){
                runInAction(()=>{
                    this.newMdList = result.data
                })
            }
        })
    }
    /**
     * 获取奖券列表或者收支记录
     */
    @action
    async getCouponList(index, loadMore, queryParams) {
        let listParams = this.listParamsArray[index];
        let params = { pageSize: 10 };
        Object.assign(params, queryParams);

        runInAction(() => {
            //更新列表状态
            if (loadMore) {
                this.listParamsArray[index].listState = CommonFlatList.STATE_LOADING_MORE
            } else {
                this.listParamsArray[index].listState = CommonFlatList.STATE_REFRESH
            }
        });

        if (!loadMore) {
            params.pageNum = 1;
        } else {
            params.pageNum = listParams.pageNum + 1;
        }
        let result;
        try {
            if (index == 0) {
                result = await Api.getCouponList(params);

                result.data.list.map((item) => {
                    item.max = parseInt(Number(item.quantity) / item.normSize);
                    item.chooseQuantity = undefined;
                })
            } else if (index == 1) {
                result = await Api.getRecords(params);
            }
        } catch (err) {
            return Promise.reject(err);
        }

        runInAction(() => {
            if (result && result.data) {
                let data = result.data;
                let hasMore = data.pages > data.pageNum;
                let oldArray = listParams.data;
                let newArray = [];
                if (!loadMore) {
                    newArray = data.list ? data.list : [];
                } else {
                    newArray = oldArray.concat(data.list ? data.list : []);
                }

                newArray.map((item) => {
                    item.isShowAll = false;
                })

                this.listParamsArray[index].data = newArray;
                this.listParamsArray[index].pageNum = result.data.pageNum;
                this.listParamsArray[index].enableLoadMore = hasMore;
                this.listParamsArray[index].listState = hasMore ? CommonFlatList.STATE_HAS_MORE : newArray.length === 0 ? CommonFlatList.STATE_INIT : CommonFlatList.STATE_NO_MORE;
            }
        })
    }

    /**
     * 获取我的补货单列表
     */
    @action
    async getMyReplenishments(loadMore) {
        let listParam = this.myReplenishments;
        let lastListState = listParam.listState;
        runInAction(() => {
            //更新列表状态
            if (loadMore) {
                this.myReplenishments.listState = CommonFlatList.STATE_LOADING_MORE
            } else {
                this.myReplenishments.listState = CommonFlatList.STATE_REFRESH
            }
        });
        let params = {};
        params.orderType = 1;  //订单类型1 补货单 2核销
        if (!loadMore) {
            params.pageNum = 1;
        } else {
            params.pageNum = listParam.pageNum + 1;
        }

        let result;

        try {
            result = await Api.getCouponOrderList(params);
        } catch (err) {
            return Promise.reject(err);
        }

        runInAction(() => {
            if (result && result.data) {
                console.log(result.data.pages, result.data.pageNum)
                let hasMore = result.data.pages > result.data.pageNum;
                let oldArray = this.myReplenishments.dataArray;
                let newArray = [];
                if (loadMore) {
                    newArray = oldArray.concat(result.data.list)
                } else {
                    newArray = result.data.list;
                }

                newArray.map((item) => {
                    item.isShowAll = false;
                })

                this.myReplenishments.dataArray = newArray;
                this.myReplenishments.pageNum = result.data.pageNum;
                this.myReplenishments.enableLoadMore = hasMore;
                this.myReplenishments.listState = hasMore ? CommonFlatList.STATE_HAS_MORE : newArray.length === 0 ? CommonFlatList.STATE_INIT : CommonFlatList.STATE_NO_MORE;
            } else {
                this.myReplenishments.listState = lastListState;
            }
        })
    }

    /**
     * 展开全部
     * */
    @action
    showAll(index) {
        this.myReplenishments.dataArray[index].isShowAll = true;
    }

    /**
     * 改变选择数量
     */
    @action
    changeChooseQuantity(index, chooseQuantity) {
        this.listParamsArray[0].data[index].chooseQuantity = chooseQuantity;
    }

    /**
     * 取消订单
     */
    @action
    async cancelOrder(historyId, orderId) {
        try {
            const result = await Api.cancelCouponOrder({ historyId: historyId, orderId: orderId });
            if (result && result.resultCode === 0) {
                this.getMyReplenishments(false);
            }
        } catch (err) {
            return Promise.reject(err);
        }
    }

    /**
     * 确认收货
     */
    @action
    async comfirmOrder(historyId, orderId) {
        try {
            const result = await Api.comfirmConponOrder({ historyId: historyId, orderId: orderId, receiveGoodsCode: '' });
            if (result && result.resultCode === 0) {
                this.getMyReplenishments(false);
            }
        } catch (err) {
            return Promise.reject(err);
        }
    }

    /**
     * 申请补货
     */
    @action
    async applyReplenish(params) {
        try {
            const result = await Api.applyReplenish(params);
            if (result && result.resultCode === 0) {
                //刷新奖券列表
                this.getCouponList(0, false);
            }
        } catch (err) {
            return Promise.reject(err);
        }
    }

    /**
     * 清空数据,恢复成初始状态
     */
    @action
    clearCoupon() {
        if (this.listParamsArray) {
            this.listParamsArray.map((item) => {
                item.pageNum = 1;
                item.listState = CommonFlatList.STATE_INIT;
                item.enableLoadMore = false;
                item.enableRefresh = true;
                item.data = [];
            });
        }
    }

}

const Coupon = new CouponModel();
autorun(() => {
})

export default Coupon;