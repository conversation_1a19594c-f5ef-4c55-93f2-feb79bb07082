/**
 *
 * Created by xiaowz on 2018/11/8.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */

import { observable,computed,autorun,action,useStrict,runInAction,toJS } from 'mobx';
import autobind from 'autobind-decorator';
import { Widget } from 'rn-yunxi';
const {CommonFlatList} = Widget;
useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 按金余额
 */
class MarginBalanceStore {
    @observable listParams={
        pageNum: 1,
        listState: CommonFlatList.STATE_INIT,
        enableLoadMore: false,
        enableRefresh: true,
        dataArray: []
    };
    constructor() {
        setTimeout(()=>{
            //一定要加点延迟，不然Api无法获取
        },0);
    }

    /**
     * 获取列表
     * */
    @action
    async getMarginBalanceList(loadMore,orgId){
        let lastListState = this.listParams.listState;
        runInAction(() => {
            //更新列表状态
            if (loadMore) {
                this.listParams.listState = CommonFlatList.STATE_LOADING_MORE
            } else {
                this.listParams.listState = CommonFlatList.STATE_REFRESH
            }
        });
        let params={};
       if(orgId){
           params.orgId = orgId;
           params.accountType=1
       }
        if(loadMore){
            params.pageNum=this.listParams.pageNum+1;
        }else{
            params.pageNum=1;
        }
        let result;
        try {
            result=await Api.getMarginBalanceList(params);
        }catch (err){

        }

        runInAction(()=>{
            if (result && result.data&&result.data.list) {
                let hasMore = result.data.pages > result.data.pageNum;
                let oldArray = this.listParams.dataArray;
                let newArray = [];
                if (loadMore) {
                    newArray = oldArray.concat(result.data.list)
                } else {
                    newArray = result.data.list;
                }
                this.listParams.dataArray=newArray;
                this.listParams.pageNum = result.data.pageNum;
                this.listParams.enableLoadMore = hasMore;
                this.listParams.listState = hasMore ? CommonFlatList.STATE_HAS_MORE : newArray.length === 0 ? CommonFlatList.STATE_INIT : CommonFlatList.STATE_NO_MORE;
            } else {
                this.listParams.listState = lastListState;
            }
        })



    }

    @action
    clearData(){
        this.listParams={
            pageNum: 1,
            listState: CommonFlatList.STATE_INIT,
            enableLoadMore: false,
            enableRefresh: true,
            dataArray: []
        };
    }
}
const MarginBalance =new MarginBalanceStore();

autorun(()=>{
});
export default MarginBalance;