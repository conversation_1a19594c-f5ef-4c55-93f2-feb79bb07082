import {observable, computed, autorun, action, useStrict, runInAction, toJS} from 'mobx';
import autobind from 'autobind-decorator';
import {DeviceEventEmitter} from "react-native";
import {Util} from "rn-yunxi";
import _ from "lodash";

useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 用户信息
 */
class UserStateModel {

    @observable init = false;
    @observable token = null;
    @observable.ref userInfo = null;
    @observable.ref userLimits = null;//权限列表
    @observable userId = null;//推送用
    @observable.ref companyInfo = null;//公司信息
    @observable.ref dealerInfo = {currencyType: null, symbol: null};//币种
    @observable allTakeDeliveryOrgList = [];//经销下全部提货组织
    @observable selectTakeDeliveryOrg = null;//选中的提货组织
    @observable takeDeliveryOrgInfoMap = new Map();//选中提货组织下的预存款和营销费用
    @observable channelList = [];//渠道列表数据
    @observable paymentDetailMap = new Map();//费用缴纳详情
    @observable dealerList = [];//经销商dealerList
    
    @observable deaderUserInfo = null;//经销商用户信息

    constructor() {

        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
            this.loadLocalState().then().catch(err => {
            });
        }, 5);

    }

    /**
     * 获取经销商基本信息
     */
         @action
         async getDealerUserInfo(){
     
     
             let result;
             try {
                 result = await Api.getDealerUserInfo();
             }catch (error){
     
             }
     
             runInAction(()=> {
                 if (result && result.data)
                 {
                     this.deaderUserInfo = result.data;
     
                 }
             })
         }

    /**
     * 经销下全部提货组织
     * @return {Promise.<void>}
     */
    @action
    async getAllTakeDeliveryOrgList() {
        let result;
        try {
            result = await Api.loadDealerOrgList();
        } catch (err) {
            //如果有特殊要做catch处理要在这里做状态管理
        }
        runInAction(() => {
            this.allTakeDeliveryOrgList = result.data;
            if (result.data && result.data.length > 0 && !this.selectTakeDeliveryOrg) {
                this.selectTakeDeliveryOrg = result.data[0];
                this.getTakeDeliveryOrgInfo().then().catch(err => {
                });
            }

        })
    }

    /**
     * 获取币种
     * @returns {*}
     */
    getSymbol() {
        //¥
        // Log('getSymbol>>',this.dealerInfo)
        if (!this.dealerInfo || !this.dealerInfo.symbol) {
            return '¥'
        }
        return this.dealerInfo.symbol;

    }

    /**
     * 是否外币
     * @returns {boolean}
     */
    isRMB() {
        if (!this.dealerInfo || (this.dealerInfo != null && (!this.dealerInfo.currencyType || this.dealerInfo.currencyType === 'RMB'))) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 设置选择组织
     */
    @action
    setSelectTakeDeliveryOrg(item) {
        this.selectTakeDeliveryOrg = item;
        this.getTakeDeliveryOrgInfo().then().catch(err => {
        });
    }

    @action
    cleanTakeDeliveryOrg() {
        this.selectTakeDeliveryOrg = null;
        this.allTakeDeliveryOrgList = [];
        this.takeDeliveryOrgInfoMap.clear();
    }


    /**
     * 获取提货组织下的信息
     * @return {Promise.<void>}
     */
    @action
    async getTakeDeliveryOrgInfo() {
        let result;
        if (this.selectTakeDeliveryOrg) {
            try {
                result = await Api.getTakeDeliveryInfo({orgId: this.selectTakeDeliveryOrg.orgId});
            } catch (err) {
                //如果有特殊要做catch处理要在这里做状态管理
            }
        }
        runInAction(() => {
            if (result && result.data) {
                this.takeDeliveryOrgInfoMap.set(this.selectTakeDeliveryOrg.orgId, result.data);
            }
        })
    }

    /**
     * 使用经销商账号相关账号
     * @returns {Promise<void>}
     */
    @action
    async loadDealerList() {
        let result = null;
        try {
            result = await Api.loadDealerList()
        } catch (e) {

        }
        runInAction(() => {
            if (result && result.data && result.data.length > 0) {
                this.dealerList = result.data;
            }
        })


    }


    /**
     * 获取用户资料
     * @return {Promise.<void>}
     */
    @action
    async getUserInfo() {
        let result;
        try {
            result = await Api.getUserInfo();
        } catch (err) {
            //如果有特殊要做catch处理要在这里做状态管理
        }
        runInAction(() => {
            this.userInfo = result.data

        })

    }

    /**
     * 获取权限
     * @returns {Promise<void>}
     */
    @action
    async getUserLimits() {
        let result;
        try {
            result = await Api.obtainUserLimits();
        } catch (err) {
            //如果有特殊要做catch处理要在这里做状态管理
        }
        runInAction(() => {
            this.userLimits = result.data

        })
    }

    /**
     * 子账号权限处理
     * @param routerName
     */
    handleUserLimits(routeName) {
        let resultRouter = routeName;
        if (this.isDealer() && this.isSubAccount()) {
            let filterArray = [
                'ShopCartList',
                'AddressList',
                'InvoiceInfo',
                'OrderList',
                'GoodsDetail',
                'SearchPage',
                'MessageCenter',
                'RecycleBottleList',
                'VerificationList',
                'MyCoupons',
                'ApplyReplenish',
                'MyReplenishments',
                'ApplyTransfer',
                'MyTransfers',
                'ShopCartList',
                'OrgInfoAction',
                'PayAction',
                'PaymentDetails',
                'QuickOrder'];
            let limits = _.get(this.userLimits, 'childKey', []);
            if(!limits){
                limits=[];
            }
            if (filterArray.includes(routeName)) {
                let find = limits.includes(routeName);
                if (!find) {
                    resultRouter = 'NoPermission';
                }
            }
        }
        return resultRouter;
    }

    /**
     * 子账号是否显示提货组织信息
     */
    canPay() {
        let result = false;
        if (this.isDealer()) {
            result = true;
            if (this.isSubAccount()) {
                let limits = _.get(this.userLimits, 'childKey', []);
                if (limits&&limits.includes('PayAction')) {
                    result = true;
                } else {
                    result = false;
                }
            }

        } else {
            result = false;
        }
        return result;

    }

    /**
     * 是否显示杂费
     * @returns {boolean}
     */
    @computed
    get showPaymentDetails(){
        let result = false;
        if (this.isDealer()) {
            result = true;
            if (this.isSubAccount()) {
                let limits = _.get(this.userLimits, 'childKey', []);
                if (limits&&limits.includes('PaymentDetails')) {
                    result = true;
                } else {
                    result = false;
                }
            }

        } else {
            result = false;
        }
        return result;
    }

    /**
     * 子账号是否有支付权限
     */
    @computed
    get showOrgInfo() {
        let result = false;
        if (this.isDealer()) {
            result = true;
            if (this.isSubAccount()) {
                let limits = _.get(this.userLimits, 'childKey', []);
                if (limits&&limits.includes('OrgInfoAction')) {
                    result = true;
                } else {
                    result = false;
                }
            }

        } else {
            result = false;
        }
        return result;
    }

    /**
     * 获取用户id
     * @return {Promise.<void>}
     */
    @action
    async getUserId() {
        let result;
        try {
            result = await Api.getUserId();
        } catch (err) {
            //如果有特殊要做catch处理要在这里做状态管理
        }
        runInAction(() => {
            this.userId = result.data

        })

    }

    /**
     * 通过参数查经销商账号和密码
     * @param param
     * @returns {Promise<*>}
     */
    async getNewUserInfo(param) {
        let result = null;
        try {
            let params = {}
            params.code = param
            result = await Api.getNewUserInfo(params);
        } catch (e) {

        }

        if (result && result.data) {
            return result.data
        }
    }

    @action
    setUserId(data) {
        this.userId = data
    }

    @action
    setUserInfo(data) {
        this.userInfo = data
    }


    @action
    async getCompanyInfo() {
        let result;
        try {
            let params = {};
            let lastAccount = await Api.getLastLoginAccount();
            if (lastAccount) {
                params.type = this.isDealer() ? 1 : 2,
                    params.code = lastAccount
                result = await Api.getCompanyInfo(params);
            }

        } catch (err) {

            //如果有特殊要做catch处理要在这里做状态管理

        }
        runInAction(() => {
            this.setCompanyInfo(result.data);


        })

    }

    @action
    setCompanyInfo(data) {
        this.companyInfo = data;
        // Log('setCompanyInfo',data);
        if (this.companyInfo) {
            this.dealerInfo = this.companyInfo.dealerInfo;
        }
    }


    @action
    setToken(token) {
        this.token = token;
    }


    /**
     * 清空数据
     */
    @action
    clearData() {
        this.token = null;
        this.userInfo = null;
        this.companyInfo = null;
        this.dealerInfo = null;
        this.allTakeDeliveryOrgList = [];
        this.selectTakeDeliveryOrg = null;
        this.userId = null;
        this.userLimits = null;
    }

    /**
     * 获取用户渠道列表
     * @return {Promise.<void>}
     */
    @action
    async obtainChannelList() {
        let result = null;
        try {
            result = await Api.getChannelList();
        } catch (err) {

        }
        runInAction(() => {
            if (result && result.data) {
                this.channelList = Array.isArray(result.data) ? result.data : [];

            } else {
            }
        })
    }

    /**
     * 获取第一个渠道数据
     * @return {*}
     */
    getFirstChannelItem() {
        let result = {
            channelName: {
                salesChannelName: null,
                subSalesChannelName: null,
            },
            channelId: {
                subSalesChannelCode: null,
                salesChannelCode: null,
            }
        };
        let data = toJS(this.channelList)
        if (data && data.length > 0) {
            let pItem = data[0];
            result.channelName.salesChannelName = pItem.label;
            result.channelId.salesChannelCode = pItem.value;

        }
        return result;
    }


    /**
     * 获取渠道名字
     * @param salesChannelCode
     * @param subSalesChannelCode
     * @return {*}
     */
    getChancelFullName(salesChannelCode, subSalesChannelCode) {
        let salesChannelName = '';
        let subSalesChannelName = '';
        let data = toJS(this.channelList)
        //  Log('渠道数据',data)
        if (data && data.length > 0) {
            let isFind = false
            for (let pItem of data) {
                if (pItem.value == salesChannelCode) {
                    isFind = true
                    salesChannelName = pItem.label;
                    if (pItem.children) {
                        for (let subItem of pItem.children) {
                            if (subItem.value == subSalesChannelCode) {
                                subSalesChannelName = subItem.label;
                                return salesChannelName + (subSalesChannelName ? '/' + subSalesChannelName : '');
                            }
                        }
                    }

                    return salesChannelName
                }

            }
            if (!isFind) {
                salesChannelName = data[0].label;
                return salesChannelName;
            }

        } else {
            return '暂无渠道'
        }


    }
    getChannelName(salesChannelCode, subSalesChannelCode) {
        let data = toJS(this.channelList)
        
        let selectParent = false
        let selectChild = false;
        let hasChild = false;
        if (data && data.length > 0) {
            for (let pItem of data) {
                if (pItem.value == salesChannelCode) {
                    selectParent = true;
                    if (pItem.children && pItem.children.length > 0) {
                        hasChild = true;
                        for (let subItem of pItem.children) {
                            if (subItem.value == subSalesChannelCode) {
                                // 选择了子渠道
                                return {
                                    selectParent: true,
                                    selectChild: true,
                                    hasChild: true,
                                }
                            }
                        }
                    }

                    return {
                        selectParent,
                        selectChild,
                        hasChild,
                    }

                }

            }
            if (!selectParent) {
                return {
                    selectParent: false,
                    selectChild: false,
                    hasChild: false,
                }
            }

        } else {
            return {
                selectParent,
                selectChild,
                hasChild,
            }
        }


    }
    /**
     * 获取默认渠道编码
     *
     */
    getValidChannelId(channelId) {
        let newChannelId = {
            salesChannelCode: channelId.salesChannelCode,
            subSalesChannelCode: channelId.subSalesChannelCode
        }
        let data = toJS(this.channelList)
        if (data && data.length > 0) {
            let isFind = false
            for (let pItem of data) {
                if (pItem.value == newChannelId.salesChannelCode) {
                    isFind = true
                    newChannelId.salesChannelCode = pItem.value
                    if (pItem.children) {
                        for (let subItem of pItem.children) {
                            if (subItem.value == newChannelId.subSalesChannelCode) {
                                newChannelId.subSalesChannelCode = subItem.value;
                                return newChannelId;
                            }
                        }
                    }
                    return newChannelId
                }

            }
            if (!isFind) {
                newChannelId.salesChannelCode = data[0].value;
                return newChannelId;
            }

        } else {
            return newChannelId
        }

    }

    /**
     * 加载本地数据
     * @return {Promise.<void>}
     */
    @action
    async loadLocalState() {
        let token = null;
        let userInfo = null;
        let companyInfo = null;
        let userId = null;
        let userLimits = null;
        try {
            token = await Api.getLocalToken();
        } catch (err) {
            console.log('loadLocalState err', err)
        }

        try {
            userLimits = await Api.getLocalUserLimits();
        } catch (err) {
            console.log('loadLocalState err', err)
        }

        // if(__DEV__){
        //     token='eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************************************************************************.ZLO__vjqVAETsV73ljmuFFc_txgwsAtBC4XXCoznc7PwE49IL97r-NU0YIdbU8vuCJ3deBrbQmzCX94JAja9pA'
        //     await Util.StorageUtil.saveString(Util.StorageUtil.KEY_TOKEN,token);
        // }
        // try {
        //     await this.getUserInfo();
        //     await Api.saveChooseOrgItem({id:*********})
        // }catch (err){
        // }
        try {
            userId = await Api.getLocalUserId();
        } catch (err) {
            console.log('loadLocalState err', err)
        }
        try {
            userInfo = await Api.getLocalUserInfo();
        } catch (err) {
            console.log('loadLocalState err', err)
        }
        try {
            companyInfo = await Api.getLocalCompanyInfo();
        } catch (err) {
            console.log('loadLocalState err', err)
        }
        if (token) {
            this.obtainChannelList().then().catch((e) => {
            })
        }
        runInAction(() => {
            Log('loadLocalState end');
            this.userId = userId;
            this.token = token;
            this.userInfo = userInfo;
            this.userLimits = userLimits;
            this.setCompanyInfo(companyInfo);
            this.init = true;
            DeviceEventEmitter.emit("user_store_init");
        })

    }

    /**
     * 是否经销商
     */
    isDealer() {
        if (this.userInfo) {
            return this.userInfo.type == 1
        } else {
            return false;
        }
    }

    /**
     * 是否子账号
     * @returns
     */
    isSubAccount() {
        if (this.userInfo) {
            return this.userInfo.def11 == 1 ? false : true;
        } else {
            return false;
        }
    }

    /**
     * 费用缴纳详情
     * */
    @action
    async getPaymentDetail() {
        let loadingResult;
        let tuobanResult;
        let cartonResult;
        try {
            loadingResult = await Api.getPaymentDetail({paymentType: 1});
            tuobanResult = await Api.getPaymentDetail({paymentType: 2});
            cartonResult = await Api.getPaymentDetail({paymentType: 3});
        } catch (error) {
        }

        runInAction(() => {
            if (loadingResult && loadingResult.data) {
                this.paymentDetailMap.set('loadingFee', loadingResult.data);
            }
            if (tuobanResult && tuobanResult.data) {
                this.paymentDetailMap.set('tuobanFee', tuobanResult.data);
            }
            if (cartonResult && cartonResult.data) {
                this.paymentDetailMap.set('cartonFee', cartonResult.data);
            }
        })
    }


}

const User = new UserStateModel();
// UserState.loadLocalState().then().catch(err=>{});
autorun(() => {
    console.log('user state>>', User.init)
})
export default User;