import {observable, computed, autorun, action, useStrict, runInAction} from 'mobx';
import autobind from 'autobind-decorator';
import ItemCountUtil from '../util/ItemCountUtil';
import ValidatorUtil from '../util/ValidatorUtil';
import {toJS} from 'mobx';
import User from './User'


useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 商品详情
 */
class GoodsDetailModel {

    @observable data = null;
    @observable priceListData = {
        advanceDeposit:0,
        discount:0,
        priceList:[]
    };//价格列表
    @observable priceItem = null;//价格选中列表
    @observable sign = 0;     ////常订标志（0为取消加入常订商品，1为加入常订商品）
    constructor() {
        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
        }, 0);
    }


    /**
     * 设置价格对象
     * @param priceList
     */
    @action
    setPriceListData(priceList) {
        this.priceListData = priceList;
        let tmpPriceList = this.priceListData;
        if(tmpPriceList&&tmpPriceList.priceList&&tmpPriceList.priceList.length>0){
            this.handleCustomTitle(tmpPriceList.priceList);
            // Log('===========----',toJS(tmpPriceList.priceList))
            this.setSelectPriceItem(tmpPriceList.priceList[0])
        }else{
            this.setSelectPriceItem(null);
        }

    }

    handleCustomTitle = (priceList)=>{
        let isRMB = User && User.isRMB();
        let type = 1;
        if (!isRMB){
            for (let tempItem of priceList ){
                type = tempItem.priceNature
                switch (type){
                    case 1:
                        tempItem.priceName = '到岸价';
                        break;
                    case 2:
                        tempItem.priceName = '离岸价';
                        break;
                    case 3:
                        tempItem.priceName = '车皮价';
                        break;
                    case 4:
                        tempItem.priceName = '工厂供货价';
                        break;
                    default:
                        break;
                }
            }

        }else {
            for (let tempItem of priceList ) {
                type = tempItem.priceType
                switch (type) {
                    case 1:
                        tempItem.priceName = '标准价';
                        break;
                    case 2:
                        tempItem.priceName = '特殊价';
                        break;
                    case 3:
                        tempItem.priceName = '底价';
                        break;
                    default:
                        break;
                }
            }
        }

    }

    /**
     * 设置选择的价格
     * @param priceItem
     */
    @action
    setSelectPriceItem(priceItem) {

        this.priceItem = priceItem;
    }

    /**
     * 获取商品详情数据
     * */
    @action
    async getGoodsDetail(prarams) {

        let result;
        try {
            result = await Api.getGoodsDetail(prarams);
        } catch (error) {

        }

        runInAction(() => {
            if (result && result.data) {
                this.data = result.data;
                this.data.isZeroFourGoods = this.handleZeroFourGood(result.data.itemCode)
                this.sign = result.data.sign;
                let tmpPriceListData={
                    advanceDeposit:result.data.advanceDeposit,
                    discount:result.data.discount,
                    priceList:result.data.priceList,
                }
                this.setPriceListData(tmpPriceListData)

            }
        });
        return result;

    }

    handleZeroFourGood = (code)=>{
        if (code && code.startsWith('04')){
            return true;
        }
        return false;
    }

    //商品加入或取消常用订单
    handleOftenGoods = async(params,callback)=>{
        let json = null;
        try {
            let rule = {
                itemId: [
                    {required: true, not: '', msg: '无效商品'},
                ],
                defaultOrgId: [
                    {required: true, not: '', msg: '请选择组织'},
                ],
            };
            if(ValidatorUtil.validate(params,rule)){
                let paramsArray = [];
                paramsArray.push(params);
                json = await Api.handleOftenGoods(paramsArray);
            }
        } catch (error) {

        }

        runInAction(() => {
            if (json && json.resultCode == 0) {
                Log('params.sign',params)
                this.sign=params.sign;

               if (this.sign == 1){
                   if(json && json.data && json.data.total){
                       callback && callback(json.data.total)
                   }
               }

                // this.getGoodsDetail(params.itemId).then().catch(err=>{});
            }
        });
    }


}

export default GoodsDetailModel;