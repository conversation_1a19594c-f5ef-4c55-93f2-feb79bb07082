/**
 * Created by whw on 2018/1/23.
 */
import {observable, computed, autorun, action, useStrict, runInAction} from 'mobx';
import autobind from 'autobind-decorator';
import userStore from './User';

useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 商品详情
 */
class DeliveryOrderDetailModel {

    @observable data = null;
    @observable isUseBottle = false;
    @observable logisticsList = [];
    constructor() {
        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
        }, 0);
    }

    @action
    setData(data){
        this.data = data;
    }

    @action
    setIsUseBottle(isUseBottle){
        this.isUseBottle = isUseBottle;
    }

    /**
     * 获取提货单详情
     * */
    @action
    async getDeliveryOrderDetail(deliveryOrderId, type){

        let result;
        try {
            result = await Api.getDeliveryDetail({
                deliveryOrderId: deliveryOrderId,
                isDistributor: (!userStore.isDealer() || type == 3) ? 1 : ''
            });
        }catch (error){

        }
        runInAction(()=> {
            if (result && result.data)
            {
                this.data = result.data;
            }
        })

    }

    /**
     * 获取物流轨迹
     * */
    @action
    async getLogisticsOrbit(deliveryId){

        let result;
        try {
            result = await Api.getLogisticsOrbit({deliveryId: deliveryId});
        }catch (error){

        }
        runInAction(()=> {
            if (result && result.data)
            {
                let list = result.data.list;
                this.logisticsList = list ? list : [];
            }
        })
    }

    /**
     * 物流评价
     * */
    @action
    async evaluation(params){

        let result;
        try {
            result = await Api.evaluation(params);
        }catch (error){

        }
    }

    /**
     * 确认收货
     * */
    @action
    async confirmReceive(params,callback){

        let result;
        try {
            result = await Api.confirmReceive(params);
        }catch (error){

        }
        runInAction(()=>{
            if (result && result.resultCode == 0){
                callback && callback()
            }
        })
    }


}

export default DeliveryOrderDetailModel;