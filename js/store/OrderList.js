/**
 * Created by whw on 2018/1/19.
 */
import {observable, computed, autorun, action, useStrict, runInAction} from 'mobx';
import autobind from 'autobind-decorator';
import {Util, Widget} from 'rn-yunxi';
import {
    ListView,
    Alert,
    InteractionManager
} from 'react-native';

const {CommonFlatList} = Widget;
import {toJS} from 'mobx';
import UserStore from '../store/User';


useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action

//经销商-我的订单
const dealerOrderTabs = [
    {label: '全部', params: {selectable: true}},
    {label: '待付款', params: {selectable: true}},
    {label: '待审核', params: null},
    {label: '待配货', params: null},
    {label: '待收货', params: null},
    {label: '已完成', params: null},
];
//分销商-我的订单
const distributorOrderTabs = [
    {label: '全部', params: {selectable: true}},
    {label: '待转单', params: {selectable: true}},
    {label: '待收货', params: null},
    {label: '已完成', params: null},
];
//经销商-分销商的订单
const distributorTabs = [
    {label: '全部', params: {selectable: true}},
    {label: '待转单', params: {selectable: true}},
    {label: '待付款', params: null},
    {label: '已完成', params: null},
];
/**
 * 订单列表
 */
class OrderListModel {

    @observable mergerOrderData = null;
    @observable tabIndex = 0;
    @observable listParamsArray = Array(6).fill({
        pageNum: 1,
        listState: CommonFlatList.STATE_INIT,
        enableLoadMore: false,
        enableRefresh: true,
        data: []
    });
    @observable searchParams = {};
    @observable type = 1;//1 经销商-我的订单，2 分销商-我的订单 3 经销商-分销商的订单
    constructor() {
        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
        }, 10);
    }

    /**
     * 设置类型
     * @param type
     */
    @action
    setType(type) {
        this.type = type;
    }

    /**
     * 获取经销商tabs
     * */
    @computed
    get getTabs() {
        if(this.type==1){
            return dealerOrderTabs;
        }else if(this.type==2){
            return distributorOrderTabs;
        }else if(this.type=3){
            return distributorTabs;
        }
        return null;
    }


    @action
    setTabIndex(index) {
        this.tabIndex = index;
    }

    @action
    setSearchParams(params) {
        this.searchParams = params;
    }

    /**
     * checkBox选择
     * */
    @action
    setCheckBox(selectTab, orderIndex, index) {
        let isChecked = this.listParamsArray[selectTab].data[orderIndex].deliverys[index].isChecked;
        this.listParamsArray[selectTab].data[orderIndex].deliverys[index].isChecked = !isChecked;
        Log('dd == ', toJS(this.listParamsArray[selectTab].data[orderIndex].deliverys[index]));
    }


    /**
     * 点击切换
     */
    @action
    changeTabClick = (index,load)=>{
        this.tabIndex = index
        this.initialPage = index

            // this.listParamsArray[index].data = []
            InteractionManager.runAfterInteractions(() => this.getOrderList(index,false))


    }

    /**
     * 获取订单列表 status => 0：待转单  1：待付款  2：待审核  31：待发货  4：待收货  5：已收货  6：已完成
     * 9：已取消(无待配货状态，自主发货及已退单待实现中)【全部：不传递值或传递空】
     */
    @action
    async getOrderList(index, loadMore) {
        let listParams = this.listParamsArray[index];
        let lastListState=listParams.listState;

        runInAction(() => {
            //更新列表状态
            if (loadMore) {
                this.listParamsArray[index].listState = CommonFlatList.STATE_LOADING_MORE
            } else {
                this.listParamsArray[index].listState = CommonFlatList.STATE_REFRESH
            }
        });
        let params={};
        if (this.searchParams.startTimeText)
        {
            params.orderStartDate = this.searchParams.startTimeText;
        }
        if (this.searchParams.endTimeText)
        {
            params.orderEndDate = this.searchParams.endTimeText;
        }
        switch (index) {
            case 1:
                if (this.type==1) {
                    params.status = 1;
                } else {
                    params.status = 0;
                }
                break;
            case 2:
                if (this.type==3) {
                    params.status = 1;
                } else {
                    params.status = 2;
                }
                break;
            case 3:
                params.status = 10;
                break;
            case 4:
                params.status = 4;
                break;
            case 5:
                params.status = 6;
                break;
            default:
                break;
        }



        if (!loadMore) {
            params.pageNum = 1;
        } else {
            params.pageNum = listParams.pageNum + 1;
        }

        let result;
        try {
            if (this.type==1||this.type==2) {
                result = await Api.getOrderList(params);
            }else if(this.type == 3){
                result = await Api.getDistributorOrderList(params);
            }

        } catch (error) {

        }

        runInAction(() => {
            if (result && result.data) {
                let hasMore = result.data.pages > result.data.pageNum;
                let oldArray = this.listParamsArray[index].data;
                let newArray = [];
                if (loadMore) {
                    newArray = oldArray.concat(result.data.list)
                } else {
                    newArray = result.data.list;
                }

                this.listParamsArray[index].pageNum = result.data.pageNum;
                this.listParamsArray[index].data = newArray;
                this.listParamsArray[index].enableLoadMore = hasMore;
                this.listParamsArray[index].listState = hasMore ? CommonFlatList.STATE_HAS_MORE : newArray.length === 0 ? CommonFlatList.STATE_INIT : CommonFlatList.STATE_NO_MORE;
            }else{
                this.listParamsArray[index].listState=lastListState;
            }
        })
    }


    /**
     * 清空数据,恢复成初始状态
     */
    @action
    clearOrderList() {
        if (this.listParamsArray) {
            this.listParamsArray.map((item) => {
                item.pageNum = 1;
                item.listState = CommonFlatList.STATE_INIT;
                item.enableLoadMore = false;
                item.enableRefresh = true;
                item.data = [];
            });
        }
    }

}


autorun(() => {
});
export default OrderListModel;