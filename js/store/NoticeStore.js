/*
 * @Author: <PERSON><PERSON>
 * @Date: 2020-07-16 17:44:28
 * @Last Modified by: duowen
 * @Last Modified time: 2022-04-12 17:16:25
 */

import {
  observable,
  computed,
  autorun,
  action,
  useStrict,
  runInAction,
} from "mobx";

import _ from "lodash";
import { Util } from "rn-yunxi";
import { Alert } from "react-native";
import { Widget } from "rn-yunxi";
const { CommonFlatList } = Widget;
const { StorageUtil } = Util;

useStrict(true); //这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action

const initialState = {
  dataArray: [],
  listState: CommonFlatList.STATE_INIT,
  enableLoadMore: false,
  enableRefresh: true,
  pageNum: 1,
};
class NoticeModel {
  constructor() {
    setTimeout(() => {
      //一定要加点延迟，不然Api无法获取
    }, 0);
  }

  @observable
  html = "";

  // 公告类型 0 未读 1 已读
  @observable
  tabIndex = 0;
  // 公告列表
  @observable listParams = {
    ...initialState,
  }; //列表数据源
  @observable notReadNum = 0; // 未读公告数

  @action
  getUserNotice = async () => {
    let result;
    try {
      result = await Api.getUserNotice();
    } catch (error) {
      Log(error);
    }
    runInAction(() => {
      this.html = (result && result.data && result.data.content) || "";
    });
  };

  
  /**
   * 获取未读公告数量
   */
   @action
   getUserNoticeNum = async () => {
     let result;
     try {
       result = await Api.getUserNoticeNum({
         type: 1
       });
       if (result.resultCode === 0) {
         runInAction(() => {
           this.notReadNum = +result.data;
         })
       }
       // return result.data;
     } catch (error) {
       Log(error);
       Alert.alert('未读数量ERROR：', JSON.stringify(error))
       return false;
     }
   };

  @action
  getUserNoticeList = async (loadMore, hasRead) => {
    const { listParam } = this;
    let result;
    try {
      runInAction(() => {
        //更新列表状态
        if (loadMore) {
          this.listParams.listState = CommonFlatList.STATE_LOADING_MORE;
        } else {
          this.listParams.listState = CommonFlatList.STATE_REFRESH;
        }
      });
      let params = {};

      if (!loadMore) {
        params.pageNum = 1;
      } else {
        params.pageNum = listParam.pageNum + 1;
      }
      params.applicationType = 1;
      result = await Api.getUserNoticeList(params);
      runInAction(() => {
        if (result && result.data) {
          let hasMore = result.data.pages > result.data.pageNum;
          let oldArray = this.listParams.dataArray;
          let newArray = [];
          let list = result.data.list.filter((item) =>
            hasRead ? item.isRead == 1 : item.isRead != 1
          );
          if (loadMore) {
            newArray = oldArray.concat(list);
          } else {
            newArray = list;
          }
          this.listParams.dataArray = newArray;
          this.listParams.pageNum = result.data.pageNum;
          this.listParams.enableLoadMore = hasMore;
          this.listParams.listState = hasMore
            ? CommonFlatList.STATE_HAS_MORE
            : newArray.length === 0
            ? CommonFlatList.STATE_INIT
            : CommonFlatList.STATE_NO_MORE;
        } else {
          this.listParams.listState = lastListState;
        }
      });
      return result;
    } catch (error) {
      Log(error);
    }
  };

  @action
  setTabIndex(index) {
    this.tabIndex = index;
    this.listParams = {
      ...initialState,
    };
    this.getUserNoticeList(false, index === 1)
      .then()
      .catch((e) => {});
  }

  @action
  postNoticeRead = async (param) => {
    let result;
    try {
      result = await Api.postUserNoticeRead(param);
      if (result.resultCode === 0) {
        const targetList = this.listParams.dataArray.filter(item => item.id !== param.id);
        runInAction(() => {
          this.listParams = {
       
              ...this.listParams,
              dataArray: targetList
            
          };
        });
       
      }
    } catch (error) {
      Log(error);
    }
  };

  @action
  postUseNoticeReaded = async () => {
    let result;
    try {
      result = await Api.postUseNoticeReaded();
    } catch (error) {
      Log(error);
    }

    runInAction(() => {
      if (result && result.resultCode == 0) {
        StorageUtil.saveString("USER_NOTICE_READED", "true");
      }
    });
  };
}

const NoticeStore = new NoticeModel();
autorun(() => {});

export default NoticeStore;
