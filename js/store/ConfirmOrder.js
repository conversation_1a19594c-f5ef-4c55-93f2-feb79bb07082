import {observable, computed, autorun, action, useStrict, runInAction,toJS} from 'mobx';
import autobind from 'autobind-decorator';
import ToastUtil from '../util/ToastUtil';
import ValidateUtil from "../util/ValidatorUtil";
import UserStore from './User';
import {Alert} from 'react-native';

useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 确定订单
 */
class ConfirmOrderModel {
    @observable address = null;
    @observable hasMarginBalance = false;  //是否有安押金
    @observable marginBalanceList = [];  //按金列表
    @observable orderInvoice = {
        invoiceId: "",
        invoiceType: 0,
        invoiceTypeName: "",
        orgName: "",
        taxCode: "",
        registerAddress: "",
        registerPhone: ""
    };
    @observable orders = [
        {
            expectedDeliveryDate: new Date(),//发货日期
            remark: '',//备注
            useBottleRemain: false,
            useSaleAmount: false,//使用营销费用
            dealAmount: 0,//应付
            foreignCurrency: {//外币订单
                freight: '0',//运费
                premium: '0',//保费
                incidentals: '',//杂费
                total: 0,//合计
                contractCode: '',//合同编码
            }
        },

    ]

    @observable zpOrderType = 1;

    constructor() {
        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
        }, 0);
    }

    @action
    setZpOrderType(type) {
        Log('set type >>', type)
        this.zpOrderType = type >= 1 ? type : 1;
    }

    @action
    setOrders(orders) {
        // Log('setOrders',toJS(orders))
        if (orders) {
            orders.map((item) => {
                item.useBottleRemain = false;
                item.usePalletRemain = false;
                item.useSaleAmount = true;
                item.expectedDeliveryDate =item.expectedDeliveryDate ;
                item.remark = '';
                item.dealAmount = item.totalAmount;
                item.currentUseMarketingAmount = 0.00;
                item.foreignCurrency = {//外币订单
                    freight: '',//运费
                    premium: '',//保费
                    incidentals: '',//杂费
                    total: 0,//合计
                    contractCode: '',//合同编码
                }
            });
        }
        this.orders = orders;
        this.checkDealAmount();
    }

    /**
     * 外币订单运保杂费
     * @param index
     */
    @action
    setTotal(index){
        let foreignCurrency=this.orders[index].foreignCurrency;
        if(foreignCurrency){
            let freight=0;
            let premium=0;
            let incidentals=0;
            try{
                freight=parseFloat(foreignCurrency.freight);
                if(isNaN(freight)){
                    freight=0;
                }
            }catch (e) {}
            try{
                premium=parseFloat(foreignCurrency.premium);
                if(isNaN(premium)){
                    premium=0;
                }
            }catch (e) {}
            try{
                incidentals=parseFloat(foreignCurrency.incidentals);
                if(isNaN(incidentals)){
                    incidentals=0;
                }
            }catch (e) {}
            // Log('total',freight,premium,incidentals)
            foreignCurrency.total=freight+premium+incidentals;
        }
        this.checkDealAmount();
    }

    /**
     * 设置运费
     * @param index
     * @param value
     */
    @action
    setFreight(index, value) {
        this.orders[index].foreignCurrency.freight = value;
        this.setTotal(index)
    }

    /**
     * 设置保费
     * @param index
     * @param value
     */
    @action
    setPremium(index, value) {
        this.orders[index].foreignCurrency.premium = value;
        this.setTotal(index)
    }

    /**
     * 设置杂费
     * @param index
     * @param value
     */
    @action
    setIncidentals(index, value) {
        this.orders[index].foreignCurrency.incidentals = value;
        this.setTotal(index)
    }

    /**
     * 合同编码
     * @param index
     * @param value
     */
    @action
    setContractCode(index, value) {
        this.orders[index].foreignCurrency.contractCode = value;
    }

    /**
     * 设置发票
     * @param index
     * @param value
     */
    @action
    setOrderInvoice(orderInvoice) {
        this.orderInvoice = orderInvoice;
    }

    /**
     * 设置配送地址
     * @param index
     * @param text
     */
    @action
    setExpectedDeliveryDate(index, value) {
        this.orders[index].expectedDeliveryDate = value;
    }

    /**
     * 设置配送日期
     * @param index
     * @param text
     */
    @action
    setAddress(value) {
        this.address = value
    }

    /**
     * 设置备注
     * @param index
     * @param text
     */
    @action
    setRemark(index, text) {
        this.orders[index].remark = text;
    }

    /**
     * 设置使用瓶箱余额
     * @param index
     * @param isUse
     */
    @action
    useBottleRemain(index, isUse) {
        this.orders[index].useBottleRemain = isUse;
        this.checkDealAmount();
    }

    /**
     * 设置使用托板余额
     * @param index
     * @param isUse
     */
    @action
    usePalletRemain(index, isUse) {
        this.orders[index].usePalletRemain = isUse;
        this.checkDealAmount();
    }

    /**
     * 通过是否 “使用瓶箱余额” 和 “使用托板余额”，算出正确的“瓶箱按金费用小计”
     */
    getRealBottleAmount = (item) => {

        let resultAmount = null;
        const {useBottleRemain, usePalletRemain} = item;
        const {
            bottleAmount,
            bottleCashPayAmount,
            bottlePayAmount,
            palletCashPayAmount,
            palletPayAmount
        } = item;

        if (!useBottleRemain && !usePalletRemain) {
            resultAmount = bottleAmount;
        } else if (useBottleRemain && !usePalletRemain) {
            resultAmount = bottleCashPayAmount + palletCashPayAmount + palletPayAmount;
        } else if (!useBottleRemain && usePalletRemain) {
            resultAmount = bottleCashPayAmount + bottlePayAmount + palletCashPayAmount;
        } else if (useBottleRemain && usePalletRemain) {
            resultAmount = bottleCashPayAmount + palletCashPayAmount;
        }

        return resultAmount;
    };

    /**
     * 设置使用营销费用
     * @param index
     * @param isUse
     */
    @action
    useSaleAmount(index, isUse) {
        this.orders[index].useSaleAmount = isUse;
        this.checkDealAmount();
    }

    @action
    checkDealAmount() {
        this.orders.map((item, index) => {
            if (item) {
                let currentMarketingAmount = 0.0;

                // let bottleAmount = item.useBottleRemain ? item.bottlePayAmount : 0;
                let bottleAmount = item.bottleAmount - this.getRealBottleAmount(item);
                let prepayAmount = item.useBottleRemain ? -item.prepayAmountWithoutDeduct + item.prepayAmountWithDeduct : 0
                if (this.zpOrderType === 1) {
                    Log('----------------1')
                    for (let goodItem of item.itemDtos) {
                        if (goodItem && goodItem.useMarketingAmountFlag == 1) {
                            currentMarketingAmount += goodItem.totalDiscountAmount;
                        }

                    }
                    let marketingAmount = currentMarketingAmount;
                    item.currentUseMarketingAmount = currentMarketingAmount;
                    item.dealAmount = item.totalAmount + item.marketingAmount - marketingAmount - bottleAmount + prepayAmount;
                } else if (this.zpOrderType === 3 || this.zpOrderType === 4) {
                    Log('----------------3 4')
                    item.dealAmount = item.totalAmount - bottleAmount + prepayAmount;
                }
                if(!UserStore.isRMB()){
                    item.dealAmount=item.dealAmount+item.foreignCurrency.total;
                }
                // if(item.useBottleRemain){
                //     item.dealAmount=item.dealAmount-item.prepayAmountWithoutDeduct+item.prepayAmountWithDeduct
                // }else{
                //     item.dealAmount=item.dealAmount-item.prepayAmountWithDeduct+item.prepayAmountWithoutDeduct
                // }
            }
            return item;
        })

    }

    /**
     * 设置商品是否使用营销费用
     * @param orderIndex
     * @param itemIndex
     */
    @action
    setOrderItemUseMarketingAmount(orderIndex, itemIndex) {
        let orderItem = this.orders[orderIndex];
        let item = orderItem.itemDtos[itemIndex];
        let willSetMarketingAmountFlag = item.useMarketingAmountFlag == 1 ? 0 : 1;
        //判断是否可以使用营销费用
        if (willSetMarketingAmountFlag == 1) {
            if (orderItem.marketingRemainAmount - orderItem.maxDiscountAmount > 0) {
                //营销费用足整单使用情况
                if (item) {
                    item.useMarketingAmountFlag = willSetMarketingAmountFlag;
                }
            } else {
                let currentUseMarketingAmount = 0;
                for (let item of orderItem.itemDtos) {
                    if (item && item.useMarketingAmountFlag == 1) {
                        currentUseMarketingAmount += item.totalDiscountAmount;
                    }
                }
                if (orderItem.marketingRemainAmount - currentUseMarketingAmount - item.totalDiscountAmount > 0) {
                    item.useMarketingAmountFlag = willSetMarketingAmountFlag;
                } else {
                    Alert.alert(null, '营销费用不足抵扣！', [{text: '确定'}])
                }

            }
        } else {
            item.useMarketingAmountFlag = willSetMarketingAmountFlag;
        }


        let allUse = true;
        for (let item of orderItem.itemDtos) {
            if (item && item.useMarketingAmountFlag == 0) {
                allUse = false;
                break;
            }
        }
        this.orders[orderIndex].useMarketingAmountFlag = allUse ? 1 : 0;
        this.checkDealAmount();
    }

    /**
     *  全选使用营销费用
     * @param orderIndex
     */
    @action
    setOrderUseMarketingAmount(orderIndex) {
        let orderItem = this.orders[orderIndex];
        let willSetMarketingAmountFlag = orderItem.useMarketingAmountFlag == 1 ? 0 : 1;
        let hasChange = false;
        if (orderItem.marketingRemainAmount - orderItem.maxDiscountAmount > 0) {
            orderItem.useMarketingAmountFlag = willSetMarketingAmountFlag;
            hasChange = true;
        } else {
            if (orderItem.useMarketingAmountFlag == 1) {
                orderItem.useMarketingAmountFlag = 0;
                hasChange = true;
            }
            Alert.alert(null, '营销费用不足抵扣！', [{text: '确定'}])
        }

        if (hasChange) {
            for (let item of orderItem.itemDtos) {
                item.useMarketingAmountFlag = orderItem.useMarketingAmountFlag;
            }
        }
        this.checkDealAmount();

    }

    /**
     * 确认订单
     * @param params
     * @param type 1：立即购买、2：购物车3：转开单
     * @param callBack
     */
    @action
    async submit(params, type, callBack) {

        let result = null;
        if (type == 3){
            try {
                result = await Api.turnOrder(params)
            }catch (e) {
                Log(e)
            }
            runInAction(()=>{
                if (result && result.data && result.resultCode == '0'){
                    callBack && callBack(result.data)

                }
            })
            return;
        }


        Log('=======转开单确认参数',type,toJS(params))
        this.hasMarginBalance = false;

            let rule = {
                receivingAddressId: [
                    {required: true, not: null, msg: '请选择收货地址'},
                ],
            };




        try {
            if (ValidateUtil.validate(params, rule)) {
                if (type == 1) {
                    result = await Api.submitOrder(params)
                } else if (type == 2) {
                    result = await Api.submitShopCartOrder(params)
                } else if (type == 3) {
                    result = await Api.turnOrder(params)
                }

            }
        } catch (data) {
            if (data  && data.resultCode+'' == '1001'){
                this.hasMarginBalance = true
                callBack && callBack(data.data)
            }
        }
        runInAction(() => {

            if (result && result.data && result.resultCode == '0'){
                let resp = null
                let orderIdsArr = result.data || []
                 //判断要不要弹出“超过赠酒比例，继续下单可能财务审核不通过”提示
                if(orderIdsArr&&orderIdsArr[0].auditType+''==='1') {
                    let body = {
                        ...params,
                        //是否继续下单 : 1继续下单 
                        isProceed: 1,
                    }
                    Alert.alert('温馨提示',
                        orderIdsArr[0].orderTips,
                        [
                            { text:'取消' },
                            {
                                text:'继续提交', onPress: async () => {
                                    resp = await Api.submitOrder(body)
                                    callBack && callBack(resp.data)
                                },
                            },
                        ],
                    );
                } else {
                    callBack && callBack(result.data)
                }
            }


        })
    }

    setMarginBalanceData = (data,callback)=>{
        if (data){
            this.marginBalanceList = data;
            callback && callback();
        }
    }



    /**
     * 根据id获取收货地址
     */
    @action
    async getAddress(id) {
        let result;
        try {
            result = await Api.getAddress({id: id})
        } catch (err) {
            Log(err)
        }
        runInAction(() => {
            if (result) {
                this.setAddress(result.data);
            }
        })
    }

}

export default ConfirmOrderModel;