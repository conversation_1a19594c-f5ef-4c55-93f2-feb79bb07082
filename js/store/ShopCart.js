import { observable, extendObservable, computed, autorun, action, useStrict, runInAction, toJS } from 'mobx';
import autobind from 'autobind-decorator';
import ToastUtil from '../util/ToastUtil';
import ValidateUtil from "../util/ValidatorUtil";
import { Alert } from 'react-native'
import UserStore from './User'
useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 购物车
 */
class ShopCartModel {
    @observable resultData = [];
    @observable shopCartData = [];
    @observable isEditStatus = false;
    @observable isSelectAllNormal = false;//非编辑状态是否全选
    @observable isSelectAllEdit = false;//编辑状态是否全选
    @observable isLoading = false;

    @observable palletStruList = []; // 托板承载规格列表

    constructor() {
        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
        }, 0);
    }

    @action
    async obtainShopCartList(callback) {
        this.clearData()
        runInAction(() => {
            this.isLoading = true;
        });
        let result = null;
        try {
            result = await Api.getShopCartList();
        } catch (err) {

        }
        let itemIdArray = [];
        runInAction(() => {
            this.isLoading = false;
            if (result.data) {
                this.resultData = result.data
                // if(this.resultData&&this.resultData.length === 0){
                //     this.merge(result.data) //空数据处理
                // }
                for (let orgItem of result.data) {
                    if (orgItem && orgItem.cartItemDtos && orgItem.cartItemDtos.length > 0) {
                        for (let goodsItem of orgItem.cartItemDtos) {
                            itemIdArray.push(goodsItem.itemId)
                        }
                    }
                }
                callback && callback(itemIdArray)
            }
        })


    }

    /**
     * 合并数据
     * @param array
     */
    @action
    merge = (newShopCartData,itemIds) => {

        // Log('===========处理渲染数据')

        let array = [];
        if (newShopCartData){
             array = newShopCartData;
        }else {
             array = this.resultData;
        }
        console.log('new - merge array = ' + JSON.stringify(array));

        // if (itemIds && itemIds.length>0){
        //     for (let itemId of itemIds){
        //         for (let orgItem of array){
        //             for (let obj of orgItem.cartItemDtos){
        //                 if (itemId == obj.itemId){
        //                     isRebuy = true;
        //                 }
        //             }
        //         }
        //     }
        // }


        // Log('======array====',toJS(array))
        if (array && array.length>0) {
            let orgMap = new Map();
            let goodMap = new Map();
            this.shopCartData.map((item) => {
                //记录之前选择的状态
                if (item) {
                    orgMap.set(item.orgId, item);
                    item.cartItemDtos && item.cartItemDtos.map((child) => {
                        if (child) {
                            goodMap.set(child.shoppingCartNo, child)
                        }
                    });
                }
            });
            array = array.map((orgItem, index) => {
                let orgRebuy = false
                if (itemIds && itemIds.length == 1){
                    orgRebuy = true
                }


                //初始化 缺少mobx的字段
                let oldOrgItem = orgMap.get(orgItem.orgId);

                orgItem.normalSelected =  oldOrgItem ? oldOrgItem.normalSelected : false;//选择状态
                orgItem.editSelected =  oldOrgItem ? oldOrgItem.editSelected : false;
                if (orgItem.cartItemDtos) {
                        orgItem.cartItemDtos = orgItem.cartItemDtos.map((goodItem, index) => {
                            goodItem.isZeroFourGoods = this.handleZeroFourGood(goodItem.itemCode) //默认是否带板
                            let isRebuy = false;
                            if (itemIds && itemIds.length>0){
                                for (let itemId of itemIds){
                                    if (goodItem.itemId == itemId){
                                        isRebuy = true;
                                    }
                                }
                                let oldGoodItem = goodMap.get(goodItem.shoppingCartNo);
                                Log('购物车041')
                                goodItem.normalSelected = isRebuy ? true : oldGoodItem ? oldGoodItem.normalSelected : false;
                                goodItem.editSelected = oldGoodItem ? oldGoodItem.editSelected : false;
                                goodItem.tmpItemNum = null;
                                goodItem.batchSaleNum = this.handleBatchSaleNum(goodItem.withPlateFlag, goodItem.batchSaleNum);
                                goodItem.bearing = this.handleBearingText(goodItem.withPlateFlag, goodItem.batchSaleNum);
                                return goodItem;
                            }
                            let oldGoodItem = goodMap.get(goodItem.shoppingCartNo);
                            goodItem.normalSelected = oldGoodItem ? oldGoodItem.normalSelected : false;
                            goodItem.editSelected = oldGoodItem ? oldGoodItem.editSelected : false;
                            goodItem.tmpItemNum = null;
                            goodItem.batchSaleNum = this.handleBatchSaleNum(goodItem.withPlateFlag, goodItem.batchSaleNum);
                            goodItem.bearing = this.handleBearingText(goodItem.withPlateFlag, goodItem.batchSaleNum);
                            return goodItem;
                        })


                }
                return orgItem;
            });
            Log('merge after',array.length)
            this.shopCartData = array?array:[];

            this.checkCartListSelectAll()
        } else {
            this.shopCartData = [];
            this.isSelectAllNormal = false;
            this.isSelectAllEdit = false;
        }
    }


    handleZeroFourGood = (code)=>{
        Log('购物车04')
        if (code && code.startsWith('04')){
            return true;
        }
        return false;
    }

    // 根据是否“带板”，及“托板承载规格”，设置正确的规格字段
    handleBatchSaleNum = (isFlag, batchSaleNum) => {
        if (!isFlag) {
            return '0';
        }

        let tmpBoxnum = batchSaleNum || '0';
        if (isFlag && (!batchSaleNum || batchSaleNum == '0')) {
            tmpBoxnum = (this.palletStruList[0] && this.palletStruList[0].boxnum) || '';
        }
        console.log('new - handleBatchSaleNum tmpBoxnum = ' + tmpBoxnum);
        return tmpBoxnum;
    };

    // 根据是否“带板”，及“托板承载规格”，显示对应的文案
    handleBearingText = (isFlag, batchSaleNum) => {
        if (!isFlag || !batchSaleNum || batchSaleNum == '0' || (this.palletStruList && this.palletStruList.length == 0)) {
            return '';
        }

        let bearing = '';
        for (let obj of this.palletStruList) {
            if (obj.boxnum == batchSaleNum) {
                bearing = obj.bearing || '';
            }
        }

        // 初始化旧数据，对带板的商品，默认添加上托板承载规格
        if (isFlag && !bearing) {
            bearing = (this.palletStruList[0] && this.palletStruList[0].bearing) || '';
        }
        console.log('new - handleBearingText bearing = ' + bearing);
        return bearing;
    };

    /**
     * 外部设置托板承载规格
     * @param arr
     */
    @action
    setPalletStruList(arr) {
        this.palletStruList = arr;
    }

    @action
    setEditStatus(isEdit) {
        this.isEditStatus = isEdit;
    }

    /**
     * 修改UI数量
     * @param orgListIndex
     * @param itemIndex
     * @param text
     * @param isTmp
     */
    @action
    modifyCartItemNum(orgListIndex, itemIndex, text, isTmp,isZeroFourGoods) {
        Log('modifyCartItemNum', orgListIndex, itemIndex, text, isTmp,isZeroFourGoods);
        text = text + '';
        let verify = false;
        let regEx = /^[1-9][0-9]*$/;
        verify = regEx.test(text);
        if (isZeroFourGoods){
            verify = true;
        }
        if (isTmp && text.length == 0) {
            verify = true
        }
        if (verify) {
            if (isTmp) {
                this.shopCartData[orgListIndex].cartItemDtos[itemIndex].tmpItemNum = text;
            } else {
                this.shopCartData[orgListIndex].cartItemDtos[itemIndex].itemNum = text;
            }
        }
    }

    /**
     * 调起修改商品数量接口
     * @param item
     * @param num
     * @return {Promise.<void>}
     */
    @action
    async callModifyCartItemNumApi(orgListIndex, itemIndex,) {
        let  item  = this.shopCartData[orgListIndex].cartItemDtos[itemIndex];
        let result = null;
        try {
            let params = { shoppingCartNo: item.shoppingCartNo, itemNum: item.itemNum };
            result = await Api.updateShopCartItemNum(params);
        }
        catch (err) {

        }
        runInAction(() => {
            this.isLoading = false;
            if (result.data) {
                this.merge(result.data);
            }
        })

    }

    /**
     * 调起修改购物车规格接口
     * @return {Promise.<void>}
     */
    @action
    async callModifyCartItemApi(orgListIndex, itemIndex, newSku,callback) {
        Log('--------newSku', toJS(newSku));
        let result = null;
        try {
            //TODO 校验result
            let item = this.shopCartData[orgListIndex].cartItemDtos[itemIndex];
            let params = { shoppingCartNo: item.shoppingCartNo };
            params = Object.assign(params, {
                orgId: newSku.orgId,
                awardsId: newSku.awardsId,
                itemNum:item.itemNum,
                subSalesChannelCode: newSku.channelId.subSalesChannelCode,
                salesChannelCode: newSku.channelId.salesChannelCode,
                withPlateFlag: newSku.withPlateFlag,
                batchSaleNum: newSku.batchSaleNum,
                bearing: newSku.bearing,
            });
            result = await Api.updateShopCartItem(params);
        }
        catch (err) {
            Log(err);
        }
        runInAction(() => {
            this.isLoading = false;
            if (result.data) {
                // this.clearData();
                this.merge(result.data);
                callback && callback()
            }
        })

    }

    /**
     * 调起删除购物车接口
     * @return {Promise.<void>}
     */
    @action
    async callDeleteSelectCartItemApi() {
        let result = null;
        let params = [];
        let newShopCartData = toJS(this.shopCartData);
        newShopCartData.map((item, index) => {
            if (item.cartItemDtos) {
                item.cartItemDtos.map((cartItem, cartItemIndex) => {
                    if (cartItem.editSelected) {
                        params.push(cartItem.shoppingCartNo);
                    }
                })
            }
        });
        if(!(params.length>0)){
            ToastUtil.show('请勾选需要移除的商品！');
            return
        }
        try {
            result = await Api.deleteShopCartItems(params);
            if (result && result.resultCode == 0) {
                //删除本地列表数据，通过过滤选中的项目
                newShopCartData = newShopCartData.map((item, index, array) => {
                    if (item && !item.editSelected) {
                        let cartItemDtos = item.cartItemDtos;
                        if (cartItemDtos) {
                            cartItemDtos = cartItemDtos.filter((cartItem, cartItemIndex) => {
                                return !cartItem.editSelected;
                            });
                            item.cartItemDtos = cartItemDtos;
                            return item;
                        } else {
                            return null;
                        }
                    } else {
                        return null;
                    }
                }).filter((item, index) => {
                    return item !== null;
                })
            }
        } catch (err) {

        }

        runInAction(() => {
            this.merge(newShopCartData)
        })


    }


    /**
     * 设置选择状态
     * @param orgListIndex
     * @param itemIndex
     */
    @action
    selectCartItem(orgListIndex, itemIndex, ) {
        // Log('selectCartItemOnNormalStatus', orgListIndex, itemIndex);
         let isOrgListSelectAll = !this.isEditStatus ? this.shopCartData[orgListIndex].normalSelected : this.shopCartData[orgListIndex].editSelected;
        // let isOrgListSelectAll = false
        if (itemIndex == -1) {
            //提货组织全选操作
            if (!this.isEditStatus) {
                this.shopCartData[orgListIndex].normalSelected = !isOrgListSelectAll;
            } else {
                this.shopCartData[orgListIndex].editSelected = !isOrgListSelectAll;
            }
            this.shopCartData[orgListIndex].cartItemDtos.map((item, index) => {
                if (!this.isEditStatus) {
                    item.normalSelected = !isOrgListSelectAll;
                } else {
                    item.editSelected = !isOrgListSelectAll;
                }
                return item;
            })
        } else {
            if (!this.isEditStatus) {
                let isItemSelected = this.shopCartData[orgListIndex].cartItemDtos[itemIndex].normalSelected;
                this.shopCartData[orgListIndex].cartItemDtos[itemIndex].normalSelected = isItemSelected ? false : true;
            } else {
                let isItemSelected = this.shopCartData[orgListIndex].cartItemDtos[itemIndex].editSelected;
                this.shopCartData[orgListIndex].cartItemDtos[itemIndex].editSelected = isItemSelected ? false : true;
            }

            this.checkOrgSelectAll(this.isEditStatus, orgListIndex)
        }
        this.checkCartListSelectAll();
    }

    @action
    checkRebuyItems = (itemIds)=>{

    }

    @action
    checkOrgSelectAll(isEdit, orgListIndex) {
        let orgList = this.shopCartData[orgListIndex];
        let isAllSelect = true;
        // let isAllSelect = false;
        for (let item of orgList.cartItemDtos) {
            if (!isEdit) {
                if (!item.normalSelected) {
                    isAllSelect = false;
                    break;
                }
            } else {
                if (!item.editSelected) {
                    isAllSelect = false;
                    break;
                }
            }
        }
        if (!isEdit) {
            this.shopCartData[orgListIndex].normalSelected = isAllSelect;
        } else {
            this.shopCartData[orgListIndex].editSelected = isAllSelect;
        }
    }

    @action
    checkCartListSelectAll() {
        let isAllNormalSelect = true;
        let isAllEditSelect = true;
        if (this.shopCartData && this.shopCartData.length === 0) {
            isAllNormalSelect = false;
            isAllEditSelect = false;
        }
        for (let item of this.shopCartData) {

            if (!item.normalSelected) {
                isAllNormalSelect = false;
            }
            if (!item.editSelected) {
                isAllEditSelect = false;
            }

        }
        this.isSelectAllNormal = isAllNormalSelect;
        this.isSelectAllEdit = isAllEditSelect;

    }

    /**
     * 全选择
     */
    @action
    selectAll() {
        Log('点击全选')
        let updateCartList = () => {
            for (let orgItem of this.shopCartData) {
                if (!this.isEditStatus) {
                    orgItem.normalSelected = this.isSelectAllNormal;
                    orgItem.cartItemDtos.map((item, index) => {
                        item.normalSelected = this.isSelectAllNormal;
                        return item;
                    })
                } else {
                    orgItem.editSelected = this.isSelectAllEdit;
                    orgItem.cartItemDtos.map((item, index) => {
                        item.editSelected = this.isSelectAllEdit;
                        return item;
                    })
                }

            }
        };
        if (!this.isEditStatus) {
            this.isSelectAllNormal = !this.isSelectAllNormal;
        } else {
            this.isSelectAllEdit = !this.isSelectAllEdit;
        }
        if (this.shopCartData && this.shopCartData.length === 0) {
            this.isSelectAllNormal = false;
            this.isSelectAllEdit = false;
        }
        updateCartList();
    }

   selectGoodCount() {
        let result = {
            totalItem: 0,//总选择商品
            totalCount: 0,//总数量
            totalPrice: 0,//总价格
            totalDiscountAmount: 0,//总折价格
            totalCartItemDtos: [] //选中的商品
        };
        Log('selectGoodCount',this.shopCartData.length);
        // let list=toJS(this.shopCartData);
        for (let orgItem of this.shopCartData) {
            Log('org ',orgItem.orgName);
            if (orgItem.cartItemDtos) {
                for (let item of orgItem.cartItemDtos) {
                    let isSelect = false;
                    if (this.isEditStatus && item.editSelected) {
                        isSelect = true;
                    } else if (!this.isEditStatus && item.normalSelected) {
                        isSelect = true;
                    }
                    Log('shopcart total', isSelect, item.shoppingCartNo)
                    if (isSelect) {
                        result.totalItem = result.totalItem + 1;
                        let itemTotalPrice = parseFloat(item.price) * parseFloat(item.itemNum);
                        let itemTotalDiscountAmount = parseFloat(item.discountAmount) * parseFloat(item.itemNum);
                        result.totalCount = result.totalCount + parseFloat(item.itemNum);
                        result.totalPrice = result.totalPrice + itemTotalPrice;
                        result.totalDiscountAmount = result.totalDiscountAmount + itemTotalDiscountAmount;
                        let selectItem = {
                            itemId: item.itemId,   //商品Id
                            defaultOrgId: item.orgId, //提货组织ID
                            defaultAwards: item.awards,   //奖项
                            defaultSalesChannelCode: item.salesChannelCode,  //销售渠道编码
                            defaultSubSalesChannelCode: item.subSalesChannelCode,  //销售子渠道编码
                            isTakePlate: item.withPlateFlag, //是否带板(0-否，1-是)
                            sign: 1,  //常订标志（0为取消加入常订商品，1为加入常订商品）
                        }
                        result.totalCartItemDtos.push(selectItem)


                    }
                }
            }

        }
        return result;
    }

    /**
     * 加入购物车
     * @param itemId 商品ID
     * @param itemNum 数量
     * @param priceType
     * @param orgId 供货商id
     * @param awardsId 奖项id
     * @param salesChannelCode 销售渠道id
     * @param subSalesChannelCode 销售渠道id
     * @param withPlateFlag 是否带板
     * @param batchSaleNum 托板承载规格（原批量销售单位数量）
     */
    @action
    addShopCart(itemId, itemNum, priceType, orgId, awardsId, salesChannelCode, subSalesChannelCode, withPlateFlag, batchSaleNum,awardsList) {

        let data = {
            itemId: itemId,
            itemNum: itemNum,
            priceType: priceType,
            orgId: orgId,
            awardsId: awardsList && awardsList.length>0 ? awardsId: null,
            salesChannelCode: salesChannelCode,
            subSalesChannelCode: subSalesChannelCode,
            withPlateFlag: withPlateFlag,
            batchSaleNum: batchSaleNum || '0',
        };
        let rule = {
            itemNum: [
                { required: true, not: '', msg: '请输入数量' },
            ],
            priceType: [
                { required: true, not: null, msg: '请选择价格' },
            ],
            orgId: [
                { required: true, not: null, msg: '请选择组织' },
            ],
            awardsId: [
                { required: true, not: null, msg: '请选择奖项' },
            ],
            salesChannelCode: [
                { required: true, not: null, msg: '请选择渠道' },
            ],


        };
        let currentItem = null;
        currentItem = UserStore.getChannelName(data.salesChannelCode, data.subSalesChannelCode);
        if(!currentItem.selectParent) {
            ToastUtil.show('请选择渠道')
            return;
        }
        // 有子渠道但是没选
        if(currentItem.selectParent && currentItem.hasChild && !currentItem.selectChild) {
            ToastUtil.show('请选择子渠道')
            return;
        }
        if (ValidateUtil.validate(data, rule)) {
            let params = [];
            params.push(data);
            Api.addShopCart(params).then(() => {
                ToastUtil.show('加入购物车成功！')
            }).catch(() => {
                ToastUtil.show('加入购物车错误！')
            })
        }


    }

    /**
     * 立即购买
     * @param params
     * @param callBack
     * @return {Promise.<void>}
     */
    @action
    async quickBuy(params, type, callBack) {
        let rule = {
            itemNum: [
                { required: true, not: '', msg: '请输入数量' },
            ],
            priceType: [
                { required: true, not: null, msg: '请选择价格' },
            ],
            orgId: [
                { required: true, not: null, msg: '请选择组织' },
            ],
            awardsId: [
                { required: true, not: null, msg: '请选择奖项' },
            ],
            salesChannelCode: [
                { required: true, not: null, msg: '请选择渠道' },
            ],

        };
        let currentItem = null;
        currentItem = UserStore.getChannelName(params.salesChannelCode, params.subSalesChannelCode);
        if(!currentItem.selectParent) {
            ToastUtil.show('请选择渠道')
            return;
        }
        // 有子渠道但是没选
        if(currentItem.selectParent && currentItem.hasChild && !currentItem.selectChild) {
            ToastUtil.show('请选择子渠道')
            return;
        }
        let result = null;
        try {
            if (ValidateUtil.validate(params, rule)) {
                let obj = {
                    itemBuyDtos: [params],
                    zpOrderType: type
                };
                result = await Api.quickBuy(obj)
            }
        } catch (err) {

        }
        runInAction(() => {
            if (result && result.data && callBack) {
                callBack(result.data);
            }
        })

    }


    /**
     * 确认订单
     * @param params
     * @param callBack
     * @return {Promise.<void>}
     */
    @action
    async confirmOrder() {
        let rule = {
            shoppingCartNos: [
                { required: true, not: null, msg: '请选择商品' },
                {
                    run: (array) => {
                        return array.length !== 0;
                    }, msg: '请选择商品'
                },
            ],
        };
        let params = {
            shoppingCartNos: [],
        };

        let result = null;
        let currentItem = null;
        try {
            for (let item of this.shopCartData) {
                if (item && item.cartItemDtos) {
                    for (let cartItem of item.cartItemDtos) {
                        if (cartItem.normalSelected) {
                            params.shoppingCartNos.push(cartItem.shoppingCartNo)
                            currentItem = UserStore.getChannelName(cartItem.salesChannelCode, cartItem.subSalesChannelCode);
                            if(!currentItem.selectParent) {
                                ToastUtil.show('请选择渠道')
                                return;
                            }
                            // 有子渠道但是没选
                            if(currentItem.selectParent && currentItem.hasChild && !currentItem.selectChild) {
                                ToastUtil.show('请选择子渠道')
                                return;
                            }
                        }
                    }
                }

            }
            if (ValidateUtil.validate(params, rule)) {
                result = await Api.confirmOrder(params.shoppingCartNos)
            }
        } catch (err) {
            return Promise.reject(err)
        }
        return result;

    }

    @action
    addToOftenBuyList = async (params) => {
        if ( ! params.length>0){
            ToastUtil.show('请勾选需要加入常订的商品');
            return
        }
        let json = null;
        try {

            json = await Api.handleOftenGoods(params);

        } catch (error) {

        }

        if (json && json.data && json.data.list && json.data.list.length>0){
           ToastUtil.show('加入常订成功');
        }



    }


    /**
     * 查询是否已有相同商品
     * data": "0：不存在   1：存在相同商品"
     */
    @action
    checkIsSameItem = async (orgListIndex, cartIndex, newSku, callback) => {
        let json = null;
        let item = this.shopCartData[orgListIndex].cartItemDtos[cartIndex];
        let params = { shoppingCartNo: item.shoppingCartNo };
        params = Object.assign(params, {
            orgId: newSku.orgId,
            awardsId: newSku.awardsId,
            salesChannelCode: newSku.channelId.salesChannelCode,
            subSalesChannelCode: newSku.channelId.subSalesChannelCode,
            itemNum:item.itemNum,
            withPlateFlag: newSku.withPlateFlag,
            chooseFlag: 1   //是否选中
        });
        json = await Api.checkIsSameItem(params);
        runInAction(() => {
            if (json && json.data) {
                if (json.data.data == 1) { //存在相同商品  需要弹框提示
                    Alert.alert(
                        '温馨提示',
                        '购物车已有相同商品，是否合并？',
                        [
                            {
                                text: '取消', onPress: () => {

                                }
                            },
                            {
                                text: '确定', onPress: () => {
                                    callback && callback()
                                }
                            }
                        ])
                } else {
                    callback && callback()
                }
            }
        })
    }


    @action
    clearData() {
        this.resultData = [];
        this.shopCartData = []
    }



}

const ShopCart = new ShopCartModel();

autorun(() => {
});
export default ShopCart;