import {observable, computed, autorun, action, useStrict, runInAction} from 'mobx';
import autobind from 'autobind-decorator';
import {Widget} from 'rn-yunxi';
import {
    Alert,
    InteractionManager
} from 'react-native';

import {toJS} from 'mobx';


useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action


/**
 * 申请售后
 */
class ApplyAfterSaleModel {

    @observable memo = null;
    @observable data = null;
    constructor() {
        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
        }, 10);
    }

    /**
     *
     * @param reason
     */
    @action
    setMemo(memo) {
        this.memo = memo;
    }


    /**
     * 申请售后
     * */
    submit(params,callback){

        Api.applyAfterSale(params).then(()=>{
            Alert.alert(null,'提交成功',[{text: '确定',onPress: ()=> {
                    callback && callback();
                }}])
        }).catch()
    }

    /**
     * 修改售后单
     * */
    update(callback){
        let params = Object.assign({},this.data);
        params.memo = this.memo;
        params.afterOrderId = this.data.id;
        Api.updateAfterSale(params).then(()=> {
            Alert.alert(null,'修改成功',[{text: '确定',onPress: ()=> {
                    callback && callback();
                }}])
        }).catch()

    }

    getDetail(afterId){
        Api.getAfterSaleDetail(afterId).then((result)=>{
            if (result && result.data)
            {
                runInAction(()=> {
                    this.data = result.data;
                    this.memo = result.data.memo;
                })
            }
        }).catch()
    }


}


autorun(() => {
});
export default ApplyAfterSaleModel;