/**
 * Created by whw on 2018/1/26.
 */
import { observable,computed,autorun,action,useStrict,runInAction } from 'mobx';
import autobind from 'autobind-decorator';
import { Widget } from 'rn-yunxi';
const {CommonFlatList} = Widget;


useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 发票
 */


class InvoiceModel {

    @observable data = null;

    constructor() {
        setTimeout(()=>{
            //一定要加点延迟，不然Api无法获取
        },0);
    }



    /**
     * 获取发票
     */
    @action
    async getInvoice(){


        let result;
        try {
            result = await Api.getInvoice();
        }catch (error){

        }

        runInAction(()=> {
            if (result && result.data)
            {
                this.data = result.data;

            }
        })
    }

}
const Invoice =new InvoiceModel();

autorun(()=>{
});
export default Invoice;