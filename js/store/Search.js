/**
 *
 * Created by xiaowz on 2018/2/1.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */

import { observable,computed,autorun,action,useStrict,runInAction,toJS } from 'mobx';
import autobind from 'autobind-decorator';
import { Widget,Util } from 'rn-yunxi';
const { CommonListView } = Widget;
const StorageUtil = Util.StorageUtil;
import {
    ListView,
    Alert,
    InteractionManager

} from 'react-native';

useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 搜索内容
 */
class SearchStore {
    @observable searchValue = null; //搜索框值
    @observable searchPageType = 1;  //1.默认状态（显示最近搜索结果） 2.联想状态(输入关键字联想) 3.结果页状态（搜索结果产品列表展示）
    @observable searchResultData = null; //搜索结果
    @observable lastestSearchArray = []; //最近搜索历史
    @observable defaultSort = true; //0默认
    @observable priceSort = false; //
    @observable priceImgSort = null; //
    @observable priceUpSort = 0; //1价格正序
    @observable priceDownSort = 0 //2价格倒序
    @observable vipPriceSort = false; //3特殊价
    @observable nextIndex = 0;
    @observable paramsArray = [];  //参数数组
    @observable params = {};  //参数
    @observable catalogId = null;  //参数

    @observable goodsPropertyData = null;  //商品属性总数据
    @observable seriesIdList = []; //系列
    @observable capacityIdList = []; //容量
    @observable technologyIdList = []; //工艺
    @observable concentrationIdList = []; //浓度
    @observable packingIdList = []; //储运包装
    @observable specificationsIdList = []; //规格
    @observable containerIdList = []; //容器

    @observable paramSeriesIdList = []; //系列
    @observable paramCapacityIdList = []; //容量
    @observable paramTechnologyIdList = []; //工艺
    @observable paramConcentrationIdList = []; //浓度
    @observable paramPackingIdList = []; //储运包装
    @observable paramSpecificationsIdList = []; //规格
    @observable paramContainerIdList = []; //容器


    @observable goodsListData = [] ;
    @observable listState = CommonListView.STATE_INIT;
    @observable enableLoadMore = false;
    @observable pageNum = 1;
    ds = new ListView.DataSource({ rowHasChanged: (r1, r2) => r1 !== r2 });
    @computed
    get dataSource() {
        return this.ds.cloneWithRows(this.goodsListData.slice());
    }

    constructor() {

        setTimeout(()=>{
            //一定要加点延迟，不然Api无法获取
        },10);

    }


    @action
    loadgoodsListData = async(params,loadMore)=>{
        Log('测试点击顺序3')
        let json = null;
        runInAction(() => {
            //更新列表状态
            if (loadMore && (this.goodsListData.length == 0)) {
                this.listState = CommonListView.STATE_LOADING_MORE
            } else {
                this.listState = CommonListView.STATE_REFRESH
                Log('测试点击顺序刷新')
            }
        });
        try {

            let pageNum = 1;
            if (loadMore) {
                pageNum = this.pageNum + 1
            }
            params.pageNum = pageNum;
            Log('===============',params)
            json = await Api.loadGoodsListData(params)

        } catch (error) {

        }
        runInAction(() => {
            // Log('测试数据',result)
            if (json && json.data && json.data.itemList) {  //如果有列表数据的情况
                // this.orderListData = json.data.list
                let hasMore = json.data.pages > json.data.pageNum;
                let oldArray = this.goodsListData;
                let newArray = [];
                if (loadMore) {
                    newArray = oldArray.concat(json.data.itemList)
                } else {
                    newArray = json.data.itemList;
                }
                this.goodsListData = newArray;
                this.enableLoadMore = hasMore;
                this.pageNum = json.data.pageNum;
                this.listState = hasMore ? CommonListView.STATE_HAS_MORE : newArray.length == 0 ? CommonListView.STATE_INIT : CommonListView.STATE_NO_MORE;

            } else {  //请求结果返回没有数据或者返回错误的情况
                this.goodsListData = [];
                this.enableLoadMore = false;
                this.pageNum = 1;
                this.listState = CommonListView.STATE_INIT;
            }
        });


    }

    //下拉刷新
    @action
    onRefresh = (param) => {
      this.handleFilteroperation(param)
    }

    //上拉加载更多
    @action
    onLoadMore = (param) => {

        this.listState = CommonListView.STATE_LOADING_MORE
        this.handleFilteroperation(param,true)


    }


    @action
    getSearchText = (text)=>{
        // Log('测试11122222',text)
        this.searchValue = text
         Log('测试1111111111',this.searchValue)
        // this.loadSearchData(text)
        if (text){
            this.searchPageType = 2
        }else {
            this.searchPageType = 1

        }
    }

    //加载搜索结果数据
    @action
    loadSearchData =  (text)=>{

        if (text){
            this.searchValue = text
        }else{
            this.searchValue='';
        }
        this.searchPageType = 3
        this.sortPrducts(0)

    }

    //本地保存搜索记录
    @action
    saveHistoryKeyword = async(text)=>{
            let old = this.lastestSearchArray ;
            //判断是否已有关键字
            let hasKeyword = old.indexOf(text) > -1;
            let array = old;
            if (!hasKeyword) {
                array.unshift(text)
            } else {
                return false;
            }
            //超过8个清除第一个
            if (array.length > 10) {
                array.pop();
            }
            await Api.saveHistorySearch(Config.LOCAL_SEARCH_KEYWORD, array);
            return true;


    }

    @action
    getLocalhistorySearchData = async ()=>{
        let result = null
        try{
            result = await Api.getHistorySearch(Config.LOCAL_SEARCH_KEYWORD, [])
        }catch (e){

        }
        runInAction(()=>{
            if (result){
                this.lastestSearchArray = result
            }
        })

    }

    //清空搜索记录
    @action
    clearSearchRecord = ()=>{

        StorageUtil.remove(Config.LOCAL_SEARCH_KEYWORD).then().catch()

        this.lastestSearchArray = [];
    }

    //筛选搜索结果
    @action
    sortPrducts = (sortType,param)=>{
        this.searchPageType = 3
        switch (sortType){
            case 0:
                this.handleDefaultSort(param)
                break;
            case 1:
            case 2:
                this.handlePriceSort(param)
                break;
            case 3:
                this.handleVipPriceSort(param)
                break;
            default:
                break;

        }



    }

    //点击默认
    @action
    handleDefaultSort = (param)=>{
        this.goodsListData = [];
        this.defaultSort = true
        this.priceSort = false
        this.priceImgSort = null
        Log('测试点击顺序1')
        this.handleFilteroperation(param)
    }

    //点击价格排序按钮
    @action
    handlePriceSort = (param)=>{
        this.goodsListData = [];
        this.priceImgSort = null
        let array = [1,2];
        for (let i = 0;i<array.length;i++){
            if (i == this.nextIndex) {
                this.priceSort = true
                this.defaultSort = false
                if (this.priceSort == true){ //如果点击了价格
                    this.priceImgSort = array[this.nextIndex]
                    // Log('点击了Img',this.priceImgSort)
                    this.handleFilteroperation(param)
                }

                this.nextIndex = i + 1;
                if (this.nextIndex>=array.length){
                    this.nextIndex = 0
                }
                break
            }
        }

    }

    //点击特殊价排序
    @action
    handleVipPriceSort = (param)=> {
        this.goodsListData = [];
        this.vipPriceSort = !this.vipPriceSort
        this.handleFilteroperation(param)
    }

    //=========筛选商品属性=======
    @action
    loadGoodsPropertyData = async()=>{
        let json = null;
        try {
            json = await Api.loadGoodsPropertyData();
        } catch (err) {

        }
        runInAction(() => {
            if (json && json.data) {
                this.goodsPropertyData = json.data;
                for (let item of json.data.seriesList){
                    item.isSelected = false
                }
                for (let item of json.data.capacityList){
                    item.isSelected = false
                }
                for (let item of json.data.technologyList){
                    item.isSelected = false
                }
                for (let item of json.data.concentrationList){
                    item.isSelected = false
                }
                for (let item of json.data.packingList){
                    item.isSelected = false
                }
                for (let item of json.data.specificationsList){
                    item.isSelected = false
                }
                for (let item of json.data.containerList){
                    item.isSelected = false
                }

                this.seriesIdList = json.data.seriesList                   //系列
                this.capacityIdList = json.data.capacityList               //容量
                this.technologyIdList = json.data.technologyList           //工艺
                this.concentrationIdList = json.data.concentrationList     //浓度
                this.packingIdList = json.data.packingList                 //储运包装
                this.specificationsIdList = json.data.specificationsList   //规格
                this.containerIdList = json.data.containerList             //容器
            }
        })

    }


    //处理选中的属性
    @action
    handleChooseProperty = (seriesIdList,capacityIdList,technologyIdList,concentrationIdList,packingIdList,specificationsIdList,containerIdList)=>{
        this.seriesIdList = seriesIdList;
        this.capacityIdList = capacityIdList;
        this.technologyIdList = technologyIdList
        this.concentrationIdList = concentrationIdList
        this.packingIdList = packingIdList
        this.specificationsIdList = specificationsIdList
        this.containerIdList = containerIdList


        this.paramSeriesIdList = [];
        this.paramCapacityIdList = [];
        this.paramTechnologyIdList = [];
        this.paramConcentrationIdList = [];
        this.paramPackingIdList = [];
        this.paramSpecificationsIdList = [];
        this.paramContainerIdList = [];
        if (seriesIdList.length>0){
            seriesIdList.map((obj,i)=>{
                if (obj.isSelected == true){
                    this.paramSeriesIdList.push(obj.propValueId)
                }
            })
        }

        if (capacityIdList.length>0){
            capacityIdList.map((obj,i)=>{
                if (obj.isSelected == true){
                    this.paramCapacityIdList.push(obj.propValueId)
                }
            })
        }

        if (technologyIdList.length>0){
            technologyIdList.map((obj,i)=>{
                if (obj.isSelected == true){
                    this.paramTechnologyIdList.push(obj.propValueId)
                }
            })
        }
        if (concentrationIdList.length>0){
            concentrationIdList.map((obj,i)=>{
                if (obj.isSelected == true){
                    this.paramConcentrationIdList.push(obj.propValueId)
                }
            })
        }
        if (packingIdList.length>0){
            packingIdList.map((obj,i)=>{
                if (obj.isSelected == true){
                    this.paramPackingIdList.push(obj.propValueId)
                }
            })
        }
        if (specificationsIdList.length>0){
            specificationsIdList.map((obj,i)=>{
                if (obj.isSelected == true){
                    this.paramSpecificationsIdList.push(obj.propValueId)
                }
            })
        }
        if (containerIdList.length>0){
            containerIdList.map((obj,i)=>{
                if (obj.isSelected == true){
                    this.paramContainerIdList.push(obj.propValueId)
                }
            })
        }

        this.handleFilteroperation({catalogId:this.catalogId})

    }

    //筛选数据操作
    @action
    handleFilteroperation = (param,loadMore)=>{
        if (param && param.catalogId){
            this.searchPageType = 3
            this.catalogId = param.catalogId

        }
        Log('筛选操作')
        Log('测试点击顺序2')
        let params = {}

        //关键字
        if (this.searchValue){
            params.keyword = this.searchValue;
        }

        //排序类型 （0-默认排序，1-价格正序，2-价格倒序，3特殊价,不填则为0）

            params.catalogIdList =this.catalogId? [this.catalogId]:[];//类目ID

         //系列
            params.seriesList = this.paramSeriesIdList;
        // Log('参数1----',toJS( params.seriesIdList))

        //容量
            params.capacityList = this.paramCapacityIdList;
        // Log('参数2----',toJS( params.capacityIdList))

        //工艺
            params.technologyList = this.paramTechnologyIdList;
        // Log('参数3----',toJS( params.technologyIdList))

        //浓度
            params.concentrationList = this.paramConcentrationIdList;
        // Log('参数4----',toJS( params.concentrationIdList))

        //储运包装
            params.packingList = this.paramPackingIdList;
        // Log('参数5----',toJS( params.packingIdList))

        //规格
            params.specificationsList = this.paramSpecificationsIdList;
        // Log('参数6----',toJS( params.specificationsIdList))

        //容器
            params.containerList = this.paramContainerIdList;
        // Log('参数7----',toJS( params.containerIdList))



        if (this.defaultSort) { //默认排序
            params.sortType = 0
        }

        if (this.priceSort) {  //价格排序 1正序 2倒序
            params.sortType = this.priceImgSort
        }

        if (this.vipPriceSort) {
            params.isSpecialPrice = 1  //是否特殊价（0否，1是）
        } else {
            params.isSpecialPrice = 0
        }

        Log('参数----',toJS(params))
      this.loadgoodsListData(params,loadMore).then().catch()


    }

    //清空所有数据 还原默认状态
    @action
    clearAllData = () => {
           this.catalogId = null,
            this.searchPageType = 1,
            this.nextIndex = 0,
            this.searchValue = null,
            this.priceImgSort = null,
            this.defaultSort = true,
            this.priceSort = false,
            this.vipPriceSort = false,
            this.paramsArray = [],
            this.seriesIdList = [];
            this.capacityIdList = [];
            this.technologyIdList = []
            this.concentrationIdList = []
            this.packingIdList = []
            this.specificationsIdList = []
            this.containerIdList = []

    }



}
export default SearchStore;