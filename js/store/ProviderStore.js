import {
  observable,
  computed,
  autorun,
  action,
  useStrict,
  runInAction
} from "mobx";
import autobind from "autobind-decorator";
import user from "./User";
import homePage from "./HomePage";
import quickOrder from "./QuickOrder";
import shopCart from "./ShopCart";
import address from "./Address";
import messageCenter from "./MessageCenter";
import invoice from "./Invoice";
import recycleBottle from "./RecycleBottle";
import pay from "./Pay";
import verificationList from "./VerificationList";
import coupon from "./Coupon";
import orderUtil from "./OrderUtil";
import marginBalance from "./MarginBalance";
import goodDetailList from "./GoodsDetailList";
import userNotice from "./NoticeStore";

export default {
  user,
  homePage,
  orderUtil,
  quickOrder,
  shopCart,
  address,
  messageCenter,
  invoice,
  recycleBottle,
  pay,
  verificationList,
  coupon,
  marginBalance,
  goodDetailList,
  userNotice
};
