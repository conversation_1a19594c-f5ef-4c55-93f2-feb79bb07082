import {observable, computed, autorun, action, useStrict, runInAction, toJS} from 'mobx';
import autobind from 'autobind-decorator';
import ToastUtil from '../util/ToastUtil';
import ValidateUtil from "../util/ValidatorUtil";
import GoodsExtension from './GoodsExtension'
import { Widget } from 'rn-yunxi';
const {CommonFlatList} = Widget;
import UserStore from './User'
useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 快捷下单
 */
class QuickOrderModel {

    @observable oftenListData = []; //展示常用列表数据 （展示数组）
    @observable isEditStatus = false;  //是否便捷状态
    @observable isSelectAll = false;//编辑状态是否全选
    @observable removeArray = []; //准备移除的商品数组
    @observable normalResultData = []; //源数据数组
    @observable isLoading = true;




    constructor() {

        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
        }, 1);
    }


    //获取快捷下单列表
    @action
    loadQuickData = async (callback) => {
       runInAction(()=>{
           this.isLoading = true;
       })
        this.oftenListData = [];
        this.normalResultData =[];
        let json = null;
        let params = {
            pageNum: 1,
            pageSize: 1000
        }

        try {
            json = await Api.loadQuickData(params)

        } catch (e) {

        }

        runInAction(() => {

            if (json && json.data && json.data.list && json.data.list.length>0) {
                let newArray = [];

               if (json.data.list.length>30){
                    newArray=json.data.list.slice(0, 30)
               }else {
                   newArray = json.data.list
               }

                this.isLoading = false;
                this.normalResultData = newArray;
                let itemIdArray = [];
                for (let item of newArray) {
                    itemIdArray.push(item.itemId)
                }
                callback && callback(itemIdArray)
                // this.isLoading = true;
            }else {
                this.isLoading = false;
            }
        })

    }



    /**
     * 生成渲染列表数据
     * @param array
     */
    @action
    makeOftenBuyDataList = (modifyItem, palletStruList) => {

        this.oftenListData = [];  //展示数组
        let array = this.normalResultData //源数组
        let newArray = [];    //临时展示数组
        if (array && array.length > 0) {
            for (let item of array) {
                //
                // if (modifyItem) { //如果有修改
                //     if (modifyItem.itemId == item.itemId) { //选中的商品
                //         item = modifyItem;
                //     }
                // } else { //默认展示组织
                    item.orgName = item.defaultOrgName;  //默认组织名
                    item.orgId = item.defaultOrgId;//默认组织Id
                    item.awardsName =null; //默认奖项名字
                    item.awardsId = item.defaultAwardsId; //默认奖项Id
                    item.salesChannelCode = item.defaultSalesChannelCode; //默认渠道码
                    item.subSalesChannelCode = item.defaultSubSalesChannelCode; //默认渠道子码
                    item.channelId = {salesChannelCode: item.defaultSalesChannelCode, subSalesChannelCode: item.defaultSubSalesChannelCode} //默认创建渠道对象
                    item.channelName = null;
                    item.withPlateFlag = item.isTakePlate //默认是否带板
                    item.isZeroFourGoods = this.handleZeroFourGood(item.itemCode) //04商品
                    item.bearing = (item.isTakePlate && palletStruList[0] && palletStruList[0].bearing) || ''; // 托板承载规格文案
                    item.batchSaleNum = (item.isTakePlate && palletStruList[0] && palletStruList[0].boxnum) || '0'; // 默认的托板承载规格
                // }

                if (item && item.priceList) {
                    let newItem = Object.assign({}, toJS(item));
                    newItem.editSelected = false; //是否选中
                    newItem.itemNum = 0;  //商品数目
                    newItem.tmpItemNum = null;
                    for (let priceItem of newItem.priceList) {
                        newItem.price = priceItem.price;   //商品价格
                        newItem.priceName = priceItem.priceTypeName;   //商品类型名字
                        newItem.priceType = priceItem.priceType;  //商品类型
                        newItem.stock = priceItem.stock;   //商品库存
                        newArray.push(Object.assign({}, toJS(newItem)))


                    }
                }


            }

            // 测试，只显示少数商品，减少渲染
            // const testArr = [];
            // testArr.push(newArray[0]);
            // testArr.push(newArray[1]);
            // testArr.push(newArray[2]);
            // this.oftenListData = testArr;

            this.oftenListData = newArray;
            // let itemIdArray = [];
            // for (let item of this.oftenListData){
            //     itemIdArray.push(item.itemId)
            // }
            Log('展示数组--1', toJS(this.oftenListData))

            this.checkOftenListSelectAll()
        }
    }

    handleZeroFourGood = (code)=>{
        if (code && code.startsWith('04')){
            return true;
        }
        return false;
    }



    //清空所有商品数量
    @action
    clearItemNum = () => {
        if (this.oftenListData && this.oftenListData.length > 0) {
            this.oftenListData.map((obj, i) => {
                obj.itemNum = 0;
                return obj
            })
        }
    }


    //切换编辑和完成状态
    @action
    setEditStatus = (isEdit) => {
        this.isEditStatus = !isEdit;
    }

    // 设置选择状态
    @action
    selectItem = (itemIndex) => {
        this.oftenListData[itemIndex].editSelected = !this.oftenListData[itemIndex].editSelected;
        Log('点击后', this.oftenListData[itemIndex].editSelected)
        this.checkOftenListSelectAll();
    }

    //检查是否全选
    @action
    checkOftenListSelectAll = () => {
        let isAllSelect = true;
        for (let item of this.oftenListData) {
            if (!item.editSelected) {
                isAllSelect = false;
                break;
            }
        }
        this.isSelectAll = isAllSelect;

    }

    //点击全选
    @action
    allSelectClick = (isAllSelect) => {
        this.isSelectAll = !isAllSelect
        for (let item of this.oftenListData) {
            item.editSelected = this.isSelectAll;
        }

    }


    //修改商品数量
    @action
    modifyItemNum = (itemIndex, text, isTmp,isZeroFourGoods) => {
        Log('modifyCartItemNum', itemIndex, text, isTmp);
        text = text + '';
        let verify = false;
        let regEx = /^[0-9][0-9]*$/;

        verify = regEx.test(text);

        if (isZeroFourGoods){
            verify = true;
        }
        if (isTmp && text.length == 0) {
            verify = true
        }
        if (verify) {
            if (isTmp) {
                this.oftenListData[itemIndex].tmpItemNum = text;
            } else {
                this.oftenListData[itemIndex].itemNum = text;
                Log('修改后的itemNum==========',toJS(this.oftenListData[itemIndex].itemNum))
            }
        }
    }

    //移除商品
    @action
    removeItem = (callback) => {
        this.removeArray = [];
        let newOftenList = [];
        if (this.oftenListData && this.oftenListData.length > 0) {
            this.oftenListData.map((obj, i) => {
                if (obj.editSelected) {
                    this.removeArray.push(obj.itemId)
                }else {
                    newOftenList.push(obj)
                }
            })

            this.removeGoods(newOftenList,callback).then().catch(e=>{})
        }


    }

    //移除商品网络请求
    removeGoods = async (newOftenList,callback) => {
        let json = null;
        try {
            let params = {}
            params = this.removeArray
            if (params.length>0){
                json = await Api.removeGoods(params)
            }else {
                ToastUtil.show('请选择想要移除的商品')
            }

        } catch (e) {

        }
        runInAction(() => {
            if (json && json.resultCode == 0) {
                this.oftenListData = newOftenList;
                callback&&callback()
            }
        })
    }




    //设置默认awardsId，channelCode
    @action
    setDefaultAwardsIdAndChannelCode = (item,newSku,callback)=>{
        // Log('展示newSku--3', toJS(newSku))
        item.awardsId = newSku.awardsId;  //改变奖项
        item.awardsName = newSku.awardsName;
        item.channelId = newSku.channelId;
        item.channelName = newSku.channelName
        item.salesChannelCode = newSku.channelId.salesChannelCode;
        item.subSalesChannelCode = newSku.channelId.subSalesChannelCode;
        // this.makeOftenBuyDataList(item);
        Log('展示数组--3', toJS(this.oftenListData))
        callback && callback()

    }


    //修改pannel
    @action
    callModifyItemApi = async (item, newSku,callback) => {
        Log('------newSku', toJS(newSku))
        item.orgId = newSku.orgId;
        item.orgName = newSku.orgName.orgName;//组织名
        item.withPlateFlag = newSku.withPlateFlag; //改变带板
        item.awardsId = newSku.awardsId;  //改变奖项
        item.awardsName = newSku.awardsName;
        item.channelId = newSku.channelId;
        item.channelName = newSku.channelName
        item.salesChannelCode = newSku.channelId.salesChannelCode;
        item.subSalesChannelCode = newSku.channelId.subSalesChannelCode;
        // item.withPlateFlag = newSku.withPlateFlag  //sku板 是否带板
        // item.chancelName = newSku.chancelName  //sku板 渠道名`
        // let newItem = toJS(item)
        item.bearing = newSku.bearing;
        item.batchSaleNum = newSku.batchSaleNum;
        let result = null;
        try {
            //TODO 校验result

            let params = {itemId: item.itemId};
            params = Object.assign(params, {
                orgId: newSku.orgId,  //组织
                awardsId: newSku.awardsId,  //奖项
                salesChannelCode: newSku.channelId.salesChannelCode,  //	销售渠道编码
                subSalesChannelCode: newSku.channelId.subSalesChannelCode, //	销售子渠道编码
                withPlateFlag: newSku.withPlateFlag,  //	是否带板
            });
            // Log('参数------',params)
            if(params.awardsId && params.salesChannelCode){
                result = await Api.getPrice(params);
            }
        }
        catch (err) {

        }
        runInAction(() => {

            if (result && result.data) {
                item.advanceDeposit = result.data.advanceDeposit;
                item.discount = result.data.discount;
                item.priceList = result.data.priceList;
                // this.makeOftenBuyDataList(item);
                // Log('展示数组--2', toJS(this.oftenListData))
                // }else {
                //
                // }
            }
            callback && callback()
        })

    }


    //结算订单
    @action
    confirmOrder = async () => {

        Log('=====结算商品======',toJS(this.oftenListData))
        let currentItem = null;
        let array = this.oftenListData;
        let newArray = [];
        for (let item of array) {
            if (item.itemNum > 0) {
                let buyItem = {
                    itemId: item.itemId,
                    itemNum: item.itemNum,
                    priceType: item.priceType,
                    orgId: item.orgId,
                    awardsId: item.awardsId,
                    salesChannelCode: item.salesChannelCode,
                    subSalesChannelCode: item.subSalesChannelCode,
                    withPlateFlag: item.withPlateFlag,
                    batchSaleNum: item.batchSaleNum || '0',
                }
                let rule = {

                    priceType: [
                        {required: true, not: null, msg: '请选择价格'},
                    ],
                    orgId: [
                        {required: true, not: null, msg: '请选择组织'},
                    ],
                    awardsId: [
                        {required: true, not: null, msg: '请选择奖项'},
                    ],
                    salesChannelCode: [
                        {required: true, not: null, msg: '请选择渠道'},
                    ],


                };
                currentItem = UserStore.getChannelName(item.salesChannelCode, item.subSalesChannelCode);
                if ( !ValidateUtil.validate(item, rule)) {
                    return ;
                }

                if(!currentItem.selectParent) {
                    ToastUtil.show('请选择渠道')
                    return;
                }
                // 有子渠道但是没选
                if(currentItem.selectParent && currentItem.hasChild && !currentItem.selectChild) {
                    ToastUtil.show('请选择子渠道')
                    return;
                }

                newArray.push(buyItem)
            }

        }


        let json = null;
        try {
            let params = {}
            params.itemBuyDtos = newArray
            params.zpOrderType = 1  // 订单类型 : 1标准订单 2海外订单 3费用赠酒 4瓶盖赠酒 (默认是标准订单)

            // Log('结算订单参数', params)
            if (params.itemBuyDtos && params.itemBuyDtos.length > 0) {
                json = await Api.confirmOftenOrder(params)
            } else {
                ToastUtil.show('请选择商品数量！')
            }

        } catch (e) {

        }

        return json


    }


    //计算商品数目价格等
    @computed
    get selectGoodsCount() {
        let result = {
            totalItem: 0,//总选择商品
            totalCount: 0,//总数量
            totalPrice: 0,//总价格
            totalDiscountAmount: 0,//总折价格
        };

        for (let item of this.oftenListData) {
            // let isSelect = false;
            // if (item.editSelected) {
            //     Log('选中的item',item)
            //     isSelect = true;
            // }
            // if (isSelect) {
            if (parseFloat(item.itemNum) > 0) {
                result.totalItem = result.totalItem + 1;
                let itemTotalPrice = parseFloat(item.price) * parseFloat(item.itemNum);
                let itemTotalDiscountAmount = parseFloat(item.discountAmount) * parseFloat(item.itemNum);
                result.totalCount = result.totalCount + parseFloat(item.itemNum);
                result.totalPrice = result.totalPrice + itemTotalPrice;
                result.totalDiscountAmount = result.totalDiscountAmount + itemTotalDiscountAmount;
            }
            // }
        }

        return result;
    }


    @action
    clearData(){
        this.oftenListData=[];
        this.normalResultData=null;
        this.removeArray=[];
        this.isSelectAll=false;
        this.isEditStatus=false;
    }



}

const QuickOrder = new QuickOrderModel();

autorun(() => {
});
export default QuickOrder;