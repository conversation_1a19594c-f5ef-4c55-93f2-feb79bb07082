/**
 * Created by whw on 2018/1/26.
 */
import {observable, computed, autorun, action, useStrict, runInAction} from 'mobx';
import autobind from 'autobind-decorator';
import {Widget} from 'rn-yunxi';

const {CommonFlatList} = Widget;


useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 消息列表
 */


class MessageCenterModel {

    @observable listParams =  Array(2).fill({
        pageNum: 1,
        listState: CommonFlatList.STATE_INIT,
        enableLoadMore: false,
        enableRefresh: true,
        dataArray: []
    });
    @observable unReadCount = 0;
    @observable status = 0;   //0未读  1已读

    constructor() {
        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
        }, 0);
    }


    /**
     * 获取消息列表
     */
    @action
    async getMessageList(loadMore,status) {
        let lastListState = this.listParams[status].listState;
        let listParams = this.listParams[status];


        runInAction(() => {
            //更新列表状态
            if (loadMore) {
                this.listParams.listState = CommonFlatList.STATE_LOADING_MORE
            } else {
                this.listParams.listState = CommonFlatList.STATE_REFRESH
            }
        });
        let params = {};
        params.status = status;
        if (!loadMore) {
            params.pageNum = 1;
        } else {
            params.pageNum = listParams.pageNum + 1;
        }

        let result;
        try {
            result = await Api.getMessageList(params);
        } catch (error) {

        }

        runInAction(() => {
            if (result && result.data) {
                let hasMore = result.data.pages > result.data.pageNum;
                let oldArray = this.listParams[status].dataArray;
                let newArray = [];
                if (loadMore) {
                    newArray = oldArray.concat(result.data.list)
                } else {
                    newArray = result.data.list;
                }
                this.listParams[status].dataArray = newArray;
                this.listParams[status].pageNum = result.data.pageNum;
                this.listParams[status].enableLoadMore = hasMore;
                this.listParams[status].listState = hasMore ? CommonFlatList.STATE_HAS_MORE : newArray.length === 0 ? CommonFlatList.STATE_INIT : CommonFlatList.STATE_NO_MORE;
            } else {
                this.listParams[status].listState = lastListState;
                this.listParams[status].dataArray = [];
            }
        })
    }

    @action
    changeTabClick = (index)=>{
        this.status = index;
        this.getMessageList(false,this.status).then().catch(e=>{})
    }

    @action
    readMessage = async(messageId) => {
        let ids = []
        ids.push(messageId)
        let params={ids:ids}
        let result = null;
        try {
            result = await Api.readMessage(params)
        }catch (e) {
            Log(e)
        }
        runInAction(()=>{
            if (result && result.resultCode+'' == '0'){
               this.getMessageList(false,this.status).then().catch(e=>{})
            }
        })
    }

    @action
    getUnreadMessageCount = async()=>{
        this.unReadCount = 0;
        let result = null;
        try {
            result = await Api.getUnreadMessageCount();
        }catch (e) {
            Log(e)
        }
        runInAction(()=>{
            if (result && result.data>0){
                this.unReadCount = result.data
            }
        })
    }

    @action
    clearData() {
        this.listParams = Array(2).fill({
            pageNum: 1,
            listState: CommonFlatList.STATE_INIT,
            enableLoadMore: false,
            enableRefresh: true,
            dataArray: []
        });
    }

}

const MessageCenter = new MessageCenterModel();

autorun(() => {
});
export default MessageCenter;