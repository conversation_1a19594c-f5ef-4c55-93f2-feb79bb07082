import {observable, computed, autorun, action, useStrict, runInAction, toJS} from 'mobx';
import autobind from 'autobind-decorator';
import {InteractionManager, Alert} from 'react-native';
import {Widget, AliPay, WeChat, APPayAssistEx, Util} from 'rn-yunxi';
import ToastUtil from "../util/ToastUtil";
import UserStore from './User';
const {CommonFlatList} = Widget;
import _ from 'lodash';

useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
const PAY_LIST = [
    {label: '银联', icon: require('../module/img/pay/unionpay.png'), type: 1,},
    {label: '支付宝', icon: require('../module/img/pay/alipay.png'), type: 2},
    {label: '微信', icon: require('../module/img/pay/weixinpay.png'), type: 3},
    {label: '传统支付', icon: require('../module/img/pay/dae.png'), type: 4,},
    {label: '赊销支付', icon: require('../module/img/pay/shexiao.png'), type: 5,},
];

/**
 * 支付
 */
class PayModel {
    @observable order;//支付的订单数组 [{"parentOrderId": "父订单id","orderIds": ["订单id"]}]
    @observable orderIdDtos = [];
    @observable lastestDataArray = []; //汇款人历史记录
    @observable totalAmount = 0; //订单费用
    @observable payTransactionAmount = 0; //手续费
    @observable businessType = null; //5：商品结算类型 6：费用缴纳类型
    @observable payList = [];//筛选后的支付渠道
    @observable payInfo;//支付信息渠道等
    @observable lastAppPayParams = {payNo: null};//最后一次调后App支付的参数信息
    @observable lastOrderPayStatus = null;//最后一次订单支付状态
    @observable lastPayCode = null;//最后一次选择的支付方式
    @observable singleAmount = 0;//最后一次选择的支付方式
    @observable payRate = 0;//最后一次选择的支付方式
    @observable paymentDetailIds = [];//未缴纳费用的id
    @observable payType = 0;  // 0：没有未缴纳费用，1：当前组织有未缴纳费用，2：其他组织有未缴纳费用
    @observable.ref unPayAmountMap = new Map();//欠款数据
    constructor() {
        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
        }, 0);
    }

    /**
     * 获取支付列表
     * */
    @action
    async cashier(order) {
        runInAction(() => {
            this.order = order;

        });

        let result;
        try {
            result = await Api.cashier(order);
        } catch (err) {
        }

        runInAction(() => {
            if (result && result.data) {
                this.handlePayInfo(result.data)
            } else {
                this.payInfo = null;
                this.payList = [];
            }
        })

    }

    @action
    handlePayInfo(payInfoData) {
        this.businessType = null;
        let array = [];
        // 传统支付
        array.push(PAY_LIST[3]);
        if (payInfoData.zpOrderType && payInfoData.zpOrderType == 1) {
            // 赊销支付
            array.push(Object.assign({payCode: 13}, PAY_LIST[4]));
        }
        if (UserStore.isRMB() && payInfoData.payMethodDtos) {
            for (let item of payInfoData.payMethodDtos) {
                let payItem = null;
                if (item.payCode + '' == '8') {
                    // payItem = Object.assign({}, PAY_LIST[1], item);
                } else if (item.payMethod == '102') {
                    // payItem = Object.assign({}, PAY_LIST[2], item);

                } else if (item.payCode + '' == '10') {
                    // 银联
                    payItem = Object.assign({}, PAY_LIST[0], item);

                }
                if (payItem) {
                    array.push(payItem)
                }

            }
        }

        if (payInfoData.orderIdDtos.length > 0) {
            this.orderIdDtos = payInfoData.orderIdDtos
            this.businessType = payInfoData.orderIdDtos[0].businessType
        }
        this.payInfo = payInfoData;
        this.payList = array;
        this.totalAmount = _.get(payInfoData, 'orderCashierDto.totalAmount', 0);
    }

    /**
     * 清空数据
     */
    @action
    cleanPayInfo() {
        this.payList = [];
        this.order = null;
        this.payInfo = null;
        this.lastAppPayParams = null;
        this.lastOrderPayStatus = null;
        this.lastPayCode = null;
        this.payRate = 0;
        this.singleAmount = 0;

    }

    /**
     * 获取订单支付状态
     */
    @action
    async checkOrderPayStatus() {
        let result = null;
        if (this.lastAppPayParams && this.lastAppPayParams.payNo) {
            try {
                result = await Api.checkPayStatus(toJS(this.lastAppPayParams.payNo));
            } catch (err) {
                // Log(err);
                return Promise.reject(err);
            }
            runInAction(() => {
                if (result && result.data) {
                    this.lastOrderPayStatus = result.data;

                }
                return Promise.resolve();
            })
        }


    }


    /**
     * 保存汇款人和汇款银行
     * @param name
     * @param bankName
     */
    saveHistoryNameAndBankName = async(item)=>{

        this.lastestDataArray = [];

        this.lastestDataArray.push(item);

        await Api.saveHistoryNameAndBankName(Config.LOCAL_NAME_BANKNAME, this.lastestDataArray);

    }

    /**
     * 获取汇款人和汇款银行历史
     * @param name
     * @param bankName
     */
    getHistoryNameAndBankName = async()=>{
        let result = null
        try{
            result = await Api.getHistoryNameAndBankName(Config.LOCAL_NAME_BANKNAME, [])
        }catch (e){

        }
        runInAction(()=>{
            if (result){
                this.lastestDataArray = result
            }
        })
        return result;
    }

    /**
     * 判断是否传统支付
     * @return {boolean}
     */
    @action
    isTraditionalPay = () => {
        if (this.lastPayCode == 5 ||
            this.lastPayCode == 6 ||
            this.lastPayCode == 7 ||
            this.lastPayCode == 13) {
            return true;
        }
        return false;
    }

    @action
    setLastPayCode = (code)=>{
        this.lastPayCode = code;
    }


    /**
     * 通过选择的方式支付
     * @param index
     * @param callBack
     */
    @action
    payOrder(transactionData, callBack) {
        Log('payOrder', this.lastPayCode, transactionData);
        if (this.lastPayCode != null) {

            if (this.isTraditionalPay()) {
                Log('is Traditional Pay')
                let params = {
                    orderIdDtos: this.orderIdDtos,
                    payCode: this.lastPayCode,
                };
                if (this.lastPayCode == 5 || this.lastPayCode == 6 || this.lastPayCode == 7) {
                    if (transactionData) {
                        params.paymentMoney = transactionData.cheque,
                            params.paymentDate = Util.DateUtil.parserStandardTime(transactionData.date),
                            params.remitPerson = transactionData.name,
                            params.remittingBank = transactionData.bankName
                    }

                }
                if (this.payType == 1 && params.orderIdDtos && params.orderIdDtos.length == 1) {
                    params.orderIdDtos[0].paymentDetailIds = this.paymentDetailIds;
                }
                // Log('before Traditional Pay ')
                Api.traditionalPay(params).then(data => {
                    if (data && data.resultCode == 0) {
                        callBack && callBack(true);
                        const resp = JSON.parse(data.data)
                        if (
                          this.orderIdDtos &&
                          this.orderIdDtos[0].auditType !== 1 &&
                          ((resp && resp.auditType !== 1) || !resp)
                        ) {
                          this.ajaxAutoOrder();
                        }
                    }
                }).catch(err => {
                })
            } else {

                let params = {
                    orderIdDtos: this.orderIdDtos,
                    payCode: this.lastPayCode,
                };

                //0元单支付
                if (this.lastPayCode+'' == '11'){
                    Api.traditionalPay(params).then(data => {
                        if (data && data.resultCode == 0) {
                            callBack && callBack(true);
                            const resp = JSON.parse(data.data)
                            if (
                              this.orderIdDtos &&
                              this.orderIdDtos[0].auditType !== 1 &&
                              ((resp && resp.auditType !== 1) || !resp)
                            ) {
                              this.ajaxAutoOrder();
                            }
                        } else {

                            Alert.alert('提示' ,data.resultMsg)
                        }
                    }).catch(err => {
                    })
                    return;
                }

                if (this.payType == 1 && params.orderIdDtos && params.orderIdDtos.length == 1) {
                    params.orderIdDtos[0].paymentDetailIds = this.paymentDetailIds;
                }
                this.lastAppPayParams = null;
                // Log('==========log参数=====',params)
                Api.payOrder(params).then(data => {
                    this.lastAppPayParams = data.data;
                    if (data && data.data) {
                        const resp = JSON.parse(data.data)
                        if (
                          this.orderIdDtos &&
                          this.orderIdDtos[0].auditType !== 1 &&
                          ((resp && resp.auditType !== 1) || !resp)
                        ) {
                          this.ajaxAutoOrder();
                        }
                        if (this.lastPayCode + '' == '8') {
                            this.aliPayCallBack(data.data, callBack)
                        } else if (this.lastPayCode + '' == '10') {
                            this.apPayAssistExCallBack(data.data, callBack)
                        }

                    }
                })
            }
        }

    }


    @action
    handlePayCode = (index) => {
        if (index !== -1) {
            if (index < 0) {
                //传统支付
                let payCode = null;
                if (index === -2) {
                    payCode = 5;
                } else if (index === -3) {
                    payCode = 7;
                } else if (index === -4) {
                    payCode = 6;
                }
                this.payTransactionAmount = 0
                this.lastPayCode = payCode;
            } else {
                let item = this.payList[index];
                this.lastPayCode = item.payCode;
                this.payTransactionAmount = this.payList[index].payTransactionAmount ? this.payList[index].payTransactionAmount : 0;
                this.payRate = this.payList[index].payRate ? this.payList[index].payRate : 0;
                this.singleAmount = this.payList[index].singleAmount ? this.payList[index].singleAmount : 0;
            }
        }
    }

    /**
     * 通联支付
     */
    apPayAssistExCallBack(payParams, callback) {
        if (payParams.data && payParams.data.mode && payParams.data.payData) {
            APPayAssistEx.pay(payParams.data.payData, '00').then(data => {
                callback && callback();
            }).catch(err => {
                Log('APPayAssistEx pay err>>', err);
            })
        } else {
            ToastUtil.show('无效支付信息');
        }

    }


    /**
     * 调起微信支付
     * @param payParams
     */
    weChatPayCallBack = (payParams, callback) => {
        if (payParams.data) {
            WeChat.pay(payParams.data).then((data) => {
                InteractionManager.runAfterInteractions(() => {
                    callback && callback();
                })
            }).catch((err) => {
                Log('WeChat pay err>>', err)
            })
        } else {
            ToastUtil.show('无效支付信息');
        }

    };


    /**
     * 调起支付宝支付
     * @param payParams
     */
    aliPayCallBack = (payParams, callback) => {
        Log('aliPayCallBack', payParams)
        if (payParams.url) {
            AliPay.payWithUrl(payParams.url).then((data) => {
                callback && callback();

            }).catch((err) => {
                Log('AliPay err>>', err)
            })
        } else {
            ToastUtil.show('无效支付信息');
        }

    };

    /**
     * 检查是否有未缴纳费用
     * @param params
     * @return {Promise<void>}
     */
    @action
    checkUnPay = async (selectIndex, callback) => {
        this.handlePayCode(selectIndex);
        let result = null;
        let params = {}
        params.orgIds = this.payInfo.orderCashierDto.orgId

        try {
            result = await  Api.checkUnPay(params)
        } catch (e) {
            Log(e)
        }
        runInAction(() => {
            if (result && result.data) {
                if (result.data.list && result.data.list.length > 0) {
                    this.unPayAmountMap.clear();
                    let itemMap = new Map();
                    this.paymentDetailIds = [];
                    for (let item of result.data.list) {
                        this.paymentDetailIds.push(item.id)
                        let key = item.orgId + ',' + item.opreateTime;
                        let tempValue = itemMap.get(key);
                        if (tempValue != null) {
                            tempValue.array.push(item)
                        } else {

                            let tempArray = [];
                            tempArray.push(item);
                            let newItem = {
                                orgName: item.orgName,
                                orgId: item.orgId,
                                opreateTime: item.opreateTime,
                                array: tempArray
                            }
                            itemMap.set(key, newItem)
                        }

                    }

                    this.unPayAmountMap = itemMap;
                }
                this.payType = result.data.type;
                callback && callback(result.data.type)

            }


        })

    }

    
    /**
     * 检查是否有未缴纳费用
     * @param params
     * @return {Promise<void>}
     */
    @action
    ajaxAutoOrder = () => {

        let orderNos = this.payInfo.orderCashierDto.orderNos
        if(!orderNos && orderNos.length == 0) return;
        Api.ajaxAutoOrder(orderNos).then(data => {
            if (data && data.resultCode == 0) {
                callBack && callBack(true);
            } else {
                ToastUtil.show(data.resultMsg);
            }
        }).catch(err => {
        })
        runInAction(() => {

        })

    }
}

const Pay = new PayModel();

autorun(() => {
});
export default Pay;