import { observable,computed,autorun,action,useStrict,runInAction } from 'mobx';
import autobind from 'autobind-decorator';
import {DeviceEventEmitter, InteractionManager} from "react-native";
import { Widget } from 'rn-yunxi';
const {CommonFlatList} = Widget;
import {toJS} from 'mobx'

useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 用户信息
 */
class PaymentDetailModel {
    @observable page = 0; //顶部Tab初始页
    @observable initialPage = 0; //顶部Tab初始页
    @observable listParams = Array(2).fill({
        pageNum: 1,
        listState: CommonFlatList.STATE_INIT,
        enableLoadMore: false,
        enableRefresh: true,
        dataArray: [],
        load:false
    });
    @observable unPaymentListMap = new Map(); //未缴纳费用Map
    constructor() {

        setTimeout(()=>{
            //一定要加点延迟，不然Api无法获取
        },5);

    }

    @action
    changeTabClick = (index,load,paymentType)=>{
        this.page = index;
        this.initialPage = index;
        this.listParams[index].load = load;
        if (this.listParams[index].dataArray && this.listParams[index].dataArray.length > 0 && !this.listParams[index].load) {
            Log('已有数据')
        } else {
            InteractionManager.runAfterInteractions(() => this.getPaymentDetailListData(index, false, paymentType))
        }
    }

    /**
     * 费用缴纳详情列表
     */
    @action
    getPaymentDetailListData = async(page,loadMore, paymentType) => {
        let json = null;
        runInAction(() => {
            //更新列表状态
            if (loadMore) {
                this.listParams[page].listState = CommonFlatList.STATE_LOADING_MORE
            } else {
                this.listParams[page].listState  = CommonFlatList.STATE_REFRESH
            }
        });
        try {
            //状态(0-未支付，1-已支付)
            let param = {};
            switch (page) {
                case 0:
                    param.status = 0; //未支付
                    break;
                case 1:
                    param.status = 1;  //已支付
                    break;
                default:
                    break;
            }
            let pageNum = 1;
            if (loadMore) {
                pageNum = this.listParams[this.page].pageNum + 1
            }
            param.pageNum = pageNum;
            param.pageSize = 10;
            param.paymentType = paymentType;
            json = await Api.getPaymentDetailList(param)

        } catch (error) {
        }

        runInAction(() => {
            // Log('测试数据',result)
            if (json && json.data && json.data.list) {  //如果有列表数据的情况‘’
               let tmp = [];
               if(page == 0){
                   for(let item of json.data.list){
                       tmp.push(item)
                       // if (item && item.canPayFlag == true){
                       //     tmp.push(item)
                       // }
                   }
               }else {
                   tmp = json.data.list;
               }


                let hasMore = json.data.pages > json.data.pageNum;
                let oldArray = this.listParams[this.page].dataArray;
                let newArray = [];
                if (loadMore) {
                    newArray = oldArray.concat(tmp)
                } else {
                    newArray = tmp;
                }
                this.listParams[this.page].dataArray = newArray;
                this.listParams[this.page].enableLoadMore = hasMore;
                this.listParams[this.page].pageNum = json.data.pageNum;
                this.listParams[this.page].listState = hasMore ? CommonFlatList.STATE_HAS_MORE : newArray.length === 0 ? CommonFlatList.STATE_INIT : CommonFlatList.STATE_NO_MORE;

            } else {  //请求结果返回没有数据或者返回错误的情况
                this.listParams[this.page].dataArray = [];
                this.listParams[this.page].enableLoadMore = false;
                this.listParams[this.page].pageNum = 1;
                this.listParams[this.page].listState = CommonFlatList.STATE_INIT;
            }
        });
    }


    /**
     * 通过orgId和支付状态获取未缴费列表数据
     * @param orgId
     * @return {Promise<void>}
     */
    @action
    getUnpayList = async(orgId,callback)=>{
        let params={}
        params.orgId = orgId;
        params.status = 0 ; //状态：0未支付、1已支付
        params.pageSize = 1000;
        let result = null;
        try {
            result = await Api.getUnpayList(params)
        }catch (e) {
            Log(e)
        }
        runInAction(()=>{
            if(result && result.data && result.data.list && result.data.list.length>0){
                this.unPaymentListMap.clear();
                let itemMap = new Map();
                for (let item of result.data.list){
                    let key = item.opreateTime+','+item.orgId;
                    let tempValue = itemMap.get(key);
                    if (tempValue != null){
                        tempValue.array.push(item);
                    }else {

                        let tempArray = [];
                        tempArray.push(item);
                        let newItem={
                            orgId : item.orgId,
                            orgName : item.orgName,
                            opreateTime : item.opreateTime,
                            array : tempArray
                        }
                        itemMap.set(key,newItem);
                    }
                }
                this.unPaymentListMap = itemMap;
                callback && callback(this.unPaymentListMap)
            }
        })


    }

    //费用单支付
    @action
    costSinglePayment = async(paymentDetailId)=>{
        let json = null;
        try {
            let param = paymentDetailId
            json = await Api.costSinglePayment(param)
        } catch (e) {

        }
        runInAction(()=>{

        })

        return json

    }

    /**
     * 计算应缴费用
     * @param itemIds
     * @param dataArray
     */
    @action
    computeResult = (itemIds,dataArray)=>{
        Log('==============',toJS(itemIds),toJS(dataArray))
        let tempArray = [];
        if (dataArray && dataArray.length>0){
            for (let obj of dataArray){
                for (let obj2 of obj.array){
                  for (let itemId of itemIds){
                      if (obj2.id == itemId){
                          tempArray.push(obj2.amount)
                      }
                  }
                }
            }
        }

        let result  = 0  //欠费总额
        for (let item of tempArray){
            result = result + item;
        }
        return result;
    }


}
export default PaymentDetailModel;