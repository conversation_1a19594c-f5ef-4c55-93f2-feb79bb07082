/**
 *
 * Created by xiaowz on 2018/4/22.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */

import { observable, computed, autorun, action, useStrict, runInAction, toJS } from 'mobx';
import autobind from 'autobind-decorator';
import { DeviceEventEmitter } from 'react-native';
import { Util, Widget } from 'rn-yunxi';
import ValidateUtil from '../util/ValidatorUtil';
import ToastUtil from '../../js/util/ToastUtil';
import UserStore from './User'
const { CommonFlatList } = Widget;

useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 用户信息
 */
class BottleCapForWine {

    @observable listParams = {
        pageNum:1,
        listState:CommonFlatList.STATE_INIT,
        enableLoadMore:false,
        enableRefresh:true,
        dataArray:[],
    };
    @observable showListData = []; //展示数组


    constructor() {

        setTimeout(() => {
            //一定要加点延迟，不然Api无法获取
        }, 5);

    }

    @action
    getCapWineListData = async (param) => {
        let result = null;
        let params = {};
        // params.orgId = param.orgId;
        params.accountType = param.accountType;
        try {
            result = await Api.getAccountTypeList(params);
        } catch (e) {
            Log(e);
        }
        let itemIdArray = [];
        runInAction(() => {
            if (result && result.data && result.data.list) {
                this.listParams.dataArray = result.data.list;
                // 该列表的商品，默认为不带板。设置batchSaleNum、bearing
                if (this.listParams.dataArray.length != 0) {
                    for (let obj of this.listParams.dataArray) {
                        obj.batchSaleNum = '0';
                        obj.bearing = '';
                    }
                }
            }
        });

        if (result && result.data && result.data.list) {
            for (let item of result.data.list) {
                itemIdArray.push(item);
            }
        }

        Log('itemIdArray', itemIdArray);
        return itemIdArray;

    };

    @action
    handleCapForWineData = (modifyItem) => {
        this.showListData = [];
        let array = this.listParams.dataArray;
        if (array && array.length > 0) {

            for (let item of array) {
                item.itemNum = 0;  //商品数目
                item.tmpItemNum = null;
                // if (modifyItem) { //如果有修改
                //     if (modifyItem.itemId == item.itemId) { //选中的商品
                //         item = modifyItem;
                //     }
                // } else { //默认展示组织
                item.awardsId = null; //默认奖项Id
                item.salesChannelCode = null; //默认渠道码
                item.subSalesChannelCode = null; //默认渠道子码
                item.withPlateFlag = 0; //默认是否带板
                item.batchSaleNum = '0'; // 不带板的情况下，默认托板承载规格为 0
                item.bearing = ''; // 不带板的情况下，默认托板承载规格文案
                item.isZeroFourGoods = this.handleZeroFourGood(item.itemCode); //默认是否带板

                // }

            }

            let filterArray = [];
            filterArray = array.filter((item) => {
                if (Math.floor(item.balance - item.occupiedAmount) > 0) {
                    return true;
                } else {
                    return false;
                }
            });
            this.showListData = filterArray;
        }
    };

    handleZeroFourGood = (code)=>{
        if (code && code.startsWith('04')){
            return true;
        }
        return false;
    }

    //修改pannel
    @action
    modifyItemClick = async (item, newSku) => {
        Log('------newSku', toJS(newSku));
        item.withPlateFlag = newSku.withPlateFlag; //改变带板
        item.awardsId = newSku.awardsId;  //改变奖项
        item.awardsName = newSku.awardsName;
        item.channelId = newSku.channelId;
        item.channelName = newSku.channelName;
        item.salesChannelCode = newSku.channelId.salesChannelCode;
        item.subSalesChannelCode = newSku.channelId.subSalesChannelCode;
        item.batchSaleNum = newSku.batchSaleNum;
        item.bearing = newSku.bearing;
        // this.handleCapForWineData(item);
        // Log('修改后的item',toJS(item));
        // Log('展示数组--2', toJS(this.showListData))

    };

    //修改商品数量
    @action
    modifyItemNum = (itemIndex, text, isTmp,isZeroFourGoods, callback) => {
        Log('modifyCartItemNum', itemIndex, text, isTmp);
        text = text + '';
        let verify = false;
        let regEx = /^[0-9][0-9]*$/;
        verify = regEx.test(text);
        if (isZeroFourGoods){
            verify = true;
        }
        if (isTmp && text.length == 0) {
            verify = true;
        }
        if (verify) {
            if (isTmp) {
                this.showListData[ itemIndex ].tmpItemNum = text;
            } else {
                this.showListData[ itemIndex ].itemNum = text;
                Log('计算数量111===', toJS(this.showListData[ itemIndex ]));
            }
        }
        callback && callback();
    };

    //设置默认奖项和渠道
    setDefaultAwardsIdAndChannelCode = (item, newSku) => {
        item.awardsId = newSku.awardsId;  //改变奖项
        item.awardsName = newSku.awardsName;
        item.channelId = newSku.channelId;
        item.channelName = newSku.channelName;
        item.salesChannelCode = newSku.channelId.salesChannelCode;
        item.subSalesChannelCode = newSku.channelId.subSalesChannelCode;
        // this.handleCapForWineData(item);

    };

    //计算商品数目价格等

    selectGoodsCount() {
        let result = {
            totalItem:0,//有多少种商品
            totalCount:0,//总数量
        };
        for (let item of this.showListData) {
            if (parseInt(item.itemNum) > 0) {
                result.totalItem = result.totalItem + 1;
                result.totalCount = result.totalCount + parseInt(item.itemNum);
            }

        }

        return result;
    }

    //底部结算按钮
    @action
    settlementClick = async () => {
        let array = this.showListData;
        let currentItem = null;
        let newArray = [];
        for (let item of array) {
            if (item.itemNum > 0) {
                let buyItem = {
                    itemId:item.itemId,
                    itemNum:item.itemNum,
                    priceType:1,
                    orgId:item.orgId,
                    awardsId:item.awardsId,
                    salesChannelCode:item.salesChannelCode,
                    subSalesChannelCode:item.subSalesChannelCode,
                    withPlateFlag:item.withPlateFlag,
                    accountId:item.id,
                    batchSaleNum: item.batchSaleNum || '0',
                };
                let rule = {


                    orgId:[
                        { required:true, not:null, msg:'请选择组织' },
                    ],
                    awardsId:[
                        { required:true, not:null, msg:'请选择奖项' },
                    ],
                    salesChannelCode:[
                        { required:true, not:null, msg:'请选择渠道' },
                    ],


                };
                currentItem = UserStore.getChannelName(item.salesChannelCode, item.subSalesChannelCode);
                if (!ValidateUtil.validate(item, rule)) {
                    return;
                }

                if(!currentItem.selectParent) {
                    ToastUtil.show('请选择渠道')
                    return;
                }
                // 有子渠道但是没选
                if(currentItem.selectParent && currentItem.hasChild && !currentItem.selectChild) {
                    ToastUtil.show('请选择子渠道')
                    return;
                }

                newArray.push(buyItem);
            }


        }

        // console.log('new - 结算时的newArrary = ' + JSON.stringify(newArray));
        // return;

        let json = null;
        try {
            let params = {};
            params.itemBuyDtos = newArray;
            params.zpOrderType = 4;  // 订单类型 : 1标准订单 2海外订单 3费用赠酒 4瓶盖赠酒 (默认是标准订单)

            Log('结算订单参数', params);
            if (params.itemBuyDtos && params.itemBuyDtos.length > 0) {
                json = await Api.confirmOftenOrder(params);
            } else {
                ToastUtil.show('请选择商品数量！');
            }

        } catch (e) {

        }

        return json;


    };


}

export default BottleCapForWine;