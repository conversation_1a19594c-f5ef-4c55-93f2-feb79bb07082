/**
 *
 * Created by xiaowz on 2018/2/6.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import {
    ListView,
    Alert,
    InteractionManager

} from 'react-native';
import {observable, computed, autorun, action, useStrict, runInAction,toJS} from 'mobx';
import autobind from 'autobind-decorator';
import { Widget } from 'rn-yunxi';
import ValidatorUtil from '.././util/ValidatorUtil';
import ItemCountUtil from ".././util/ItemCountUtil";
import ToastUtil from ".././util/ToastUtil";
const {CommonFlatList} = Widget;

useStrict(true);//这里用到了严格模式，在修改类的成员属性的时候函数前面需要加上 @action
/**
 * 回瓶申请
 */
class RecycleBottleStore {
    @observable recycleDetailId = null; //id
    @observable deliveryOrderNo = ''; //提货单单号
    @observable itemType = 0; // 1为白酒商品 0为其他商品
    @observable billOfLadingInfo = null; //提货单信息
    @observable orgListOfLadingBill = null; //提货单组织列表
    @observable selectOrgId = null; //提货单组织Id
    @observable normalSelectOrgId = null; //提货单组织Id
    @observable adressListData = null; //提货单地址
    @observable selectAdressId = null; //提货单起始地址Id
    @observable address = ''; //提货单起始地址
    @observable bottleTypeData = null; //瓶种类
    @observable bottleCategory = ''; //瓶种类 名称
    @observable bottleCategoryCode = ''; //瓶种类Code
    @observable specsOfBottlePackingData = null; //散瓶包装规格
    @observable specsOfBottlePackingId = null; //散瓶包装规格Id
    @observable specsOfBottleNum= 0; //散瓶包装规格数量
    @observable dispersePackSpec= ''; //散瓶包装规格描述
    @observable subSpecsOfBottlePackingData = null; //散瓶包装规格 子规格
    @observable subSpecsOfBottlePackingDataMap = new Map();//散瓶包装规格 子规格Map

    @observable subSpecsOfBottlePackingId = null; //散瓶包装规格Id 子规格Id
    @observable subSpecsOfBottleNum = 0; //散瓶包装规格shulaign  子规格数量
    @observable subSpec = ''; //散瓶包装规格 子规格描述
    @observable takeBottleType = ''; //瓶承载形式
    @observable takeBottleTypeData = null; //瓶承载形式数据
    @observable boxTypeData = null; //箱种类
    @observable boxCategory = null; //箱种类 描述
    @observable boxCategoryCode = ''; //箱种类Code
    @observable boxNum= ''; //箱种类 数量
    @observable boxUnit= ''; //箱种类 数量单位
    @observable palletTypeData = null; //托板类
    @observable pallet = ''; //托板 描述
    @observable palletCategoryCode = ''; //托板Code
    @observable palletNum = ''; //托板总数量
    @observable nullPalletNum = ''; //其中空托板数量
    @observable itemNum = ''; //数量
    @observable bottleNum = ''; //瓶数量
    @observable dealerCode = ''; //经销商Code
    @observable distributorCode = ''; //分销商Code
    @observable unit = ''; //单位
    @observable bottleUnitNum = ''; //瓶规格单位数量
    @observable recycleOrderNo = null; //申请单号
    @observable wineRate = ''; //回瓶兑酒有标率
    @observable plateSpecification = ''; //托板承载规格



    @observable page = 0; //顶部Tab初始页
    @observable initialPage = 0; //顶部Tab初始页
    @observable listParams = Array(3).fill({
        pageNum: 1,
        listState: CommonFlatList.STATE_INIT,
        enableLoadMore: false,
        enableRefresh: true,
        dataArray: [],
        load:false
    });

    @observable bottleDetailData = null; //回瓶验收单详情数据

    constructor() {
        setTimeout(()=>{
            //一定要加点延迟，不然Api无法获取
        },10);
    }

    @computed
    get renderTabName(){
        let tabName = ['全  部','待验收','已验收'];
        return tabName
    }


    /**
     * 设置默认值（当点击编辑回瓶单）
     * @param data
     */
    @action
    setDefaultRecycleDetailData = (data)=>{
        let {recycleOrderNo,bottleCategory,boxCategory,bottleCategoryCode,supplierId,addressId,dispersePackSpec,subSpec,loadWay,boxCategoryCode,boxNum,pallet,palletCode,palletNum,nullPalletNum,actualBoxNum,bottleNum,dealerCode,distributorCode,wineRate} = data.recycleOrderDto;
        this.recycleOrderNo = recycleOrderNo?recycleOrderNo:null; //申请单号
        // this.deliveryOrderNo = recycleOrderDto.deliveryOrderNo ? recycleOrderDto.deliveryOrderNo : ''; //提货单单号
        this.selectOrgId = supplierId ? supplierId: null; //提货单组织Id
        this.selectAdressId = addressId ? addressId : null; //提货单起始地址Id
        // this.address = recycleOrderDto.address ? recycleOrderDto.address : ''; //提货单起始地址
        this.bottleCategory = bottleCategory ? bottleCategory : ''; //瓶种类 名称
        this.bottleCategoryCode = bottleCategoryCode  ? bottleCategoryCode : ''; //瓶种类Code
        // // this.specsOfBottlePackingId = null; //散瓶包装规格Id
        // // this.specsOfBottleNum = 0; //散瓶包装规格数量
        this.dispersePackSpec = dispersePackSpec ? dispersePackSpec : ''; //散瓶包装规格描述
        // this.subSpecsOfBottlePackingId = null; //散瓶包装规格Id 子规格Id
        // this.subSpecsOfBottleNum = 0; //散瓶包装规格shulaign  子规格数量
        this.subSpec = subSpec ? subSpec :''; //散瓶包装规格 子规格描述
        this.takeBottleType = loadWay ? loadWay :''; //瓶承载形式
        this.boxCategory = boxCategory ? boxCategory : ''; //箱种类 描述
        this.boxCategoryCode = boxCategoryCode ? boxCategoryCode : null; //箱种类Code
        this.boxNum = boxNum ? boxNum+'' :''; //箱种类 数量
        // // this.boxUnit = ''; //箱种类 数量单位
        // // this.palletTypeData = null; //托板类
        this.pallet = pallet ? pallet : ''; //托板 描述
        this.palletCategoryCode = palletCode ? palletCode : null; //托板Code
        this.palletNum = palletNum ? palletNum+'' :''; //托板总数量
        this.nullPalletNum = nullPalletNum ? nullPalletNum +'' : ''; //其中空托板数量
        this.itemNum = actualBoxNum ? actualBoxNum +'' :''; //数量(实际箱数量)
        this.bottleNum = bottleNum ? bottleNum +'' :''; //瓶数量
        this.dealerCode = dealerCode ? dealerCode :''; //经销商Code
        this.distributorCode = distributorCode ? distributorCode : ''; //分销商Code
        this.wineRate = wineRate ? wineRate : ''; //回瓶兑奖有标率
    }

    @action
    changeTabClick = (index,load)=>{

        this.page = index
        this.initialPage = index
        this.listParams[index].load = load
        if (this.listParams[index].dataArray && this.listParams[index].dataArray.length > 0 && !this.listParams[index].load) {
            Log('已有数据')
        } else {
            // this.listParams[index].dataArray = []
            InteractionManager.runAfterInteractions(() => this.getRecycleBottleListData(index))
        }
    }

    @action
    getRecycleBottleListData = async(page,loadMore)=>{
        let json = null;
        runInAction(() => {
            //更新列表状态
            if (loadMore) {
                this.listParams[page].listState = CommonFlatList.STATE_LOADING_MORE
            } else {
                this.listParams[page].listState  = CommonFlatList.STATE_REFRESH
            }
        });
        try {
            //状态(0-未验收，1-已验收)
            let param = {}
            switch (page) {
                case 0:
                    // param.status = 0 //全部
                    break;
                case 1:
                    param.status = 1  //未验收
                    break;
                case 2:
                    param.status = 2  //已验收
                    break;
                default:
                    break;
            }
            let pageNum = 1;
            if (loadMore) {
                pageNum = this.listParams[this.page].pageNum + 1
            }
            param.pageNum = pageNum;
            // Log('--------',param)
            json = await Api.getRecycleBottleListData(param)

        } catch (error) {

        }
        runInAction(() => {
            // Log('测试数据',result)
            if (json && json.data && json.data.list) {  //如果有列表数据的情况
                // this.orderListData = json.data.list
                let hasMore = json.data.pages > json.data.pageNum;
                let oldArray = this.listParams[this.page].dataArray;
                let newArray = [];
                if (loadMore) {
                    newArray = oldArray.concat(json.data.list)
                } else {
                    newArray = json.data.list;
                }
                this.listParams[this.page].load = false
                this.listParams[this.page].dataArray = newArray;
                this.listParams[this.page].enableLoadMore = hasMore;
                this.listParams[this.page].pageNum = json.data.pageNum;
                this.listParams[this.page].listState = hasMore ? CommonFlatList.STATE_HAS_MORE : newArray.length == 0 ? CommonFlatList.STATE_INIT : CommonFlatList.STATE_NO_MORE;

            } else {  //请求结果返回没有数据或者返回错误的情况
                this.listParams[this.page].load = false
                this.listParams[this.page].dataArray = [];
                this.listParams[this.page].enableLoadMore = false;
                this.listParams[this.page].pageNum = 1;
                this.listParams[this.page].listState = CommonFlatList.STATE_INIT;
            }
        });
    }

    //回瓶验收单详情
    @action
     getBottleDetail = async(params)=> {
        let jsonData = null;
         this.bottleDetailData = null;

        try{
             jsonData = await Api.getBottleDetail(params);
        }catch (error){
        }

        runInAction(() => {
            if (jsonData && jsonData.data){
                this.bottleDetailData = jsonData.data;
                this.recycleDetailId = jsonData.data.recycleOrderDto.id
                if(jsonData.data.recycleOrderDto && jsonData.data.recycleOrderDto.plateSpecification){
                    this.plateSpecification = jsonData.data.recycleOrderDto.plateSpecification.toString()
                }

            }
        });

        if (jsonData && jsonData.data){
            return jsonData.data;
        }
    }

    //提货单单号赋值
    @action
    setDeliveryOrderNo = (text,callback)=>{
        if (ItemCountUtil.validatorInputValue(text, true)){
            this.deliveryOrderNo = text
            callback && callback();
        }
    }

    //设置orgId addressId bottleCategoryCode
    @action
    setSelectId(index,selectData) {
        // this.specsOfBottleNum = 0;

        if (index == 0){
            this.selectOrgId = selectData.id;  //提货组织
        }
        else if (index == 1){
            Log('选中的地址Id=======',selectData.id)
            this.selectAdressId = selectData.id;  //起始地址
            this.address = selectData.address;
            Log('address === ',toJS(this.address),toJS(this.selectAdressId))
        }
        else if (index == 2){
           if (selectData){
               this.bottleCategoryCode = selectData.code;  //瓶种类编码
               this.bottleCategory = selectData.name //瓶品种名称描述
           }else {
               this.bottleCategoryCode = '';  //瓶种类编码
               this.bottleCategory = '' //瓶品种名称描述
               this.bottleNum = '' //瓶数量清空
           }
        }
        else if (index == 3){
            runInAction(() => {
                this.subSpecsOfBottlePackingData = null;
                this.setSelectId(4);
            });
            if (selectData){
                this.specsOfBottlePackingId = selectData.id //散瓶包装规格
                this.specsOfBottleNum = selectData.num || 0 //数量
                this.dispersePackSpec = selectData.name //名字描述`
                this.boxCategory = null;
                this.boxCategoryCode = '';
                this.boxNum = '';
                this.unit = selectData.unit;
                this.bottleUnitNum = selectData.num;
                // if(selectData.name != '麻包瓶'){
                //     this.subSpec=''
                //     this.subSpecsOfBottlePackingId = null
                // }

                //选择了散瓶包装规格，请求对应的子规格列表
                this.getSubPackSpecListChild(this.specsOfBottlePackingId);

            }else {
                this.specsOfBottlePackingId = null //散瓶包装规格
                this.specsOfBottleNum = selectData.num || 0 //数量
                this.dispersePackSpec = '' //名字描述`
                this.subSpecsOfBottlePackingId = null
                this.subSpec=''
                this.itemNum = ''
                this.loadWay=''
                this.pallet=''
                this.palletCategoryCode=''
                this.palletNum=''
                this.nullPalletNum=''
                this.unit = ''
                this.bottleUnitNum = ''

            }

            if(this.unit != '箱' || this.unit != '瓶'){
                this.wineRate = '';
            }
        }
        else if (index == 4){
            if (selectData){
                this.subSpecsOfBottlePackingId = selectData.id //散瓶包装子规格
                this.subSpecsOfBottleNum = selectData.num ||0 //数量
                this.subSpec = selectData.name //子规格描述
                this.unit = selectData.unit
                this.bottleUnitNum = selectData.num;
            }else {
                this.subSpecsOfBottlePackingId = null //散瓶包装子规格
                this.subSpecsOfBottleNum = selectData&&selectData.num ||0 //数量
                this.subSpec = '' //子规格描述
                // this.itemNum= ''
                this.unit = ''
                this.bottleUnitNum = '';
            }
            if (this.subSpec == '托板') {
                this.bottleNum = '',
                    this.itemNum = '',
                    this.boxNum = '',
                    this.bottleCategory = '',
                    this.bottleCategoryCode = '',
                    this.boxCategoryCode = '',
                    this.boxCategory = '',
                    this.unit = '',
                    this.bottleUnitNum = ''
            }


        }
        else if (index == 5){
            this.takeBottleType = selectData.name //瓶承载形式
        }
        else if (index == 6){
            if (selectData) {
                this.boxCategoryCode = selectData.code;  //箱种类code
                this.boxCategory = selectData.name  //箱种类描述
                this.boxUnit = selectData.unitBtlQty //箱单位
            }else {
                this.pallet = ''; //托板 描述
                this.palletCategoryCode = ''; //托板Code
                this.palletNum = ''; //总托板数量
                this.nullPalletNum = ''
                this.boxNum = '';
                this.boxCategoryCode = '';
                this.boxUnit = '';
            }

        }
        else if (index == 7){
            if(selectData){
                this.boxCategory = '';
                this.boxCategoryCode = null;
                this.boxNum = '';
                this.pallet = selectData.name ; //托板描述名称
                this.palletCategoryCode = selectData.code ;//托板种类code
            }else {
                this.palletNum = ''
                this.palletCategoryCode = ''
                this.pallet = ''
                this.nullPalletNum= ''
            }
        }



    }

    //获取提货组织
    @action
    getOrgInfo = async()=>{
        let json = null
        try{
            json = await Api.getOrgInfo()
        }catch (e){

        }
        runInAction(()=>{
            if(json && json.data&& json.data.length>0){
                this.orgListOfLadingBill = json.data
                this.normalSelectOrgId = json.data[0].id
            }
        })
    }

    //提货组织展示名字
    @computed
    get selectOrgName (){
        if (this.selectOrgId) {
            for (let item of this.orgListOfLadingBill) {
                if (item.id == this.selectOrgId) {
                    return item.orgName;
                }
            }
        } else {
            return ''
        }
    }

    //获取起始地址
    @action
    getAdressListData = async()=>{
        let json = null
        try{
            json = await Api.getAdressListData()
        }catch (e){

        }
        runInAction(()=>{
            if(json && json.data && json.data.list){
                this.adressListData = json.data.list
            }
        })
    }


    //展示起始地址
    @computed
    get selectAdressName(){
        if (this.selectAdressId && this.adressListData && this.adressListData.length) {
            Log('000000222222')
            for (let item of this.adressListData) {
                if (item.id == this.selectAdressId) {
                    return item.address;
                }
            }
        } else if(this.billOfLadingInfo && this.billOfLadingInfo.address && this.deliveryOrderNo) {
            console.log('this.billOfLadingInfo === ', toJS(this.billOfLadingInfo));

            return this.billOfLadingInfo.address
        }else {
            return '请选择'
        }
    }

    //展示经销商名字
    @computed
    get getdealerName(){
        if (this.selectAdressId) {
            for (let item of this.adressListData) {
                if (item.id == this.selectAdressId) {
                    this.dealerCode = ''
                    this.dealerCode = item.dealerCode||''
                    return item.orgName;
                }
            }
        }
        return '暂无'
    }
    //展示分销商名字
    @computed
    get getDistributorName(){
        if (this.selectAdressId) {
            Log('========this.selectAdressId',this.selectAdressId,toJS(this.adressListData))
            for (let item of this.adressListData) {
                if (item.id == this.selectAdressId && (item.distributorCode.indexOf('-')<0)) {
                    Log('=======1111===',this.selectAdressId,)
                    this.distributorCode = ''
                    this.distributorCode = item.distributorCode
                    return item.abbreviation;
                }
            }
        }
        return '暂无'
    }

    //根据提货单单号获取提货单信息
    @action
    getBillOfLadingInfo =async(callback)=>{
        if (this.deliveryOrderNo.length>0){
            let json = null
            try{
                let params ={}
                params.deliveryOrderNo = this.deliveryOrderNo
                json = await Api.getBillOfLadingInfo(params)
            }catch (e){

            }
            runInAction(()=>{
                if (json && json.data){
                    this.billOfLadingInfo  = json.data
                    this.selectOrgId = json.data.orgId ?json.data.orgId :  this.normalSelectOrgId
                    this.address = json.data.address
                    this.selectAdressId = json.data.addressId

                    let code = json.data.itemCode ? json.data.itemCode.substr(0,4) : '';	
                    if (code == '05-8') {//05-8是白酒产品  如提货单为白酒产品，需设置默认值，且不可修改。散瓶包装规格：麻包瓶，子规格：托板，瓶承载形式：托板，托板：默认品种，托板承载规格：0。
                        this.itemType = 1;

                        this.specsOfBottlePackingId = '1180017770047228937'; // 散瓶包装规格
                        this.dispersePackSpec = "麻包瓶";
                        this.subSpecsOfBottlePackingId = '1180017770143697928'; // 散瓶包装子规格
                        this.subSpec = '托板';
                
                        this.takeBottleType = '托板'; // 瓶承载形式

                        if(this.palletTypeData && this.palletTypeData.length>0){
                            this.palletCategoryCode =this.palletTypeData[0].code ; // 托板品种
                            this.pallet =this.palletTypeData[0].name ; // 托板品种
                        }
                        this.plateSpecification = '0';
                    } else {//非白酒产品
                        this.itemType = 0;
                    }

                    //     "deliveryOrderId": "1166964940418873344",//提货单ID
                    //     "deliveryOrderNo":8564251254,//提货单号
                    //     "dealerOrgCode": "jxs211111",//经销商编码
                    //     "addressId": 333,//地址ID
                    //     "address": "西安市高陵区耿镇东赢物流园食品母婴仓B1库（新)",//地址名称
                    //     "bottleCategoryCode": "34324",// 瓶品种编码
                    //     "bottleCategory": "640ml成品在用饮料瓶",// 瓶品种名称
                    //     "itemCode": "223432",// 商品编码
                    //     "itemName": "10°P珠江纯生啤酒330ml*24罐托箱装"//商品名称
                    callback && callback()

                }else {
                    this.deliveryOrderNo = null
                }
            })
        }

    }


    //获取瓶种类
    getBottleTypeData = async()=>{
        let json = null
        try{
            let params = {}
            //类型：1瓶，2箱/桶，3托板，默认1
            params.type = 1
            json = await Api.getBoxTypeData(params)
        }catch (e){

        }
        runInAction(()=>{
            if(json && json.data && json.data){
                this.bottleTypeData = json.data
            }
        })
    }


    //展示瓶种类
    @computed
    get selectBottleTypeName(){
        if (this.bottleCategoryCode) {
            for (let item of this.bottleTypeData) {
                if (item.code == this.bottleCategoryCode) {
                    return item.name;
                }
            }
        } else {
            return '请选择'
        }
    }

    //获取散瓶包装规格
    getSpecsOfBottlePackingData = async(param)=>{
        let json = null
        try{
            //	层级：默认0，0父规格，1子规格
            json = await Api.getSpecsOfBottlePackingData(param)
        }catch (e){

        }
        runInAction(()=>{
            if(json && json.data&&json.data.length>0){
                if (param && param.leval == 0){
                    this.specsOfBottlePackingData = json.data
                    //获取对应的子规格列表
                    if(this.specsOfBottlePackingData && this.specsOfBottlePackingData.length>0){
                        for(let item of this.specsOfBottlePackingData){
                            if(item.name == this.dispersePackSpec){
                                this.specsOfBottlePackingId = item.id;
                                this.getSubPackSpecListChild(this.specsOfBottlePackingId);
                                break
                            }
                        }
                    }
                }else if(param && param.leval == 1){
                    this.subSpecsOfBottlePackingData = json.data
                }

            }
        })
    }

    /**
    * 获取包装子规格列表
    * @param id 父规格的ID
    */
    @action
    getSubPackSpecListChild(id) {
        if (this.subSpecsOfBottlePackingDataMap.has(id)) {
            runInAction(() => {
                this.subSpecsOfBottlePackingData = this.subSpecsOfBottlePackingDataMap.get(id)
            });
        } else {
            Api.getSubPackSpecListChild(id).then(result=>{
                runInAction(() => {
                    if (result && result.data) {
                        let ll = result.data;
                        this.subSpecsOfBottlePackingData = ll;
                        this.addSubPackSpecList(id,this.subSpecsOfBottlePackingData)
                    }
                });
            });
        }
    }
    /**
        * 包装子规格列表 添加到Map
        * @param id 父规格的ID
        */
     @action
     addSubPackSpecList(id,list){
         this.subSpecsOfBottlePackingDataMap.set(id,list);
     }
     
    //展示散瓶包装规格名字
    @computed
    get selectSpecsBottlePackName(){
        if (this.dispersePackSpec) {

            return this.dispersePackSpec;

        } else {
            return '请选择'
        }
    }

    //展示散瓶包装子规格名字
    @computed
    get selectSubSpecsBottlePackName(){
        if (this.subSpec) {
          return this.subSpec;
        }else {
            return '请选择';
        }
    }

    //瓶承载形式
    @action
    getTakeBottleTypeData = ()=>{
        this.takeBottleTypeData = [{name:"托板"},{name:"落地"}]
    }

    //展示散承载形式名字
    @computed
    get selectTakeBottleTypeName(){
        if (this.takeBottleType) {
            for (let item of this.takeBottleTypeData) {
                if (item.name == this.takeBottleType) {
                    return item.name;
                }
            }
        }else {
            return '请选择'
        }
    }

    //获取箱种类
    getBoxTypeData = async()=>{
        let json = null
        try{
            let params = {}
            //类型：1瓶，2箱/桶，3托板，默认1
            params.type = 2
            json = await Api.getBoxTypeData(params)
        }catch (e){

        }
        runInAction(()=>{
            if(json && json.data && json.data.length>0){
                this.boxTypeData = json.data
            }
        })
    }


    //展示箱种类名字
    @computed
    get selectBoxTypeName(){
        if (this.boxCategoryCode) {
            for (let item of this.boxTypeData) {
                if (item.code == this.boxCategoryCode) {
                    return item.name;
                }
            }

        }else {
            return '请选择'
        }
    }

    //获取托板种类
    getPalletTypeData = async()=>{
        let json = null
        try{
            let params = {}
            //类型：1瓶，2箱/桶，3托板，默认1
            params.type = 3
            json = await Api.getBoxTypeData(params)
        }catch (e){

        }
        runInAction(()=>{
            if(json && json.data && json.data.length>0){
                this.palletTypeData = json.data
            }
        })
    }


    //展示托板名字
    @computed
    get selectPalletTypeName(){
        if (this.itemType) return '(1200X1000)双面内置钢管塑料托板';
        if (this.palletCategoryCode) {
            for (let item of this.palletTypeData) {
                if (item.code == this.palletCategoryCode) {
                    return item.name;
                }
            }

        }else {
            return '请选择'
        }
    }

    //获取瓶数量
    @action
     getBottleNum = ()=>{
        let result = '0';
        if (this.boxCategoryCode && this.boxNum && this.boxUnit) {
            Log('---11111111',this.boxUnit,this.boxNum)
            result = this.boxUnit * this.boxNum
        }
        else if (this.bottleUnitNum){
            Log('---2222222')
            result = this.bottleUnitNum * this.itemNum ;
        }
        else if (this.bottleNum){
            Log('---3333333')
            result = this.bottleNum;
        }
        else {
            Log('---44444444')
            result = this.itemNum
        }
        this.bottleNum = result
        return result + '支'
    }


    //设置选择规格数量
    @action
    setItemNum = (value)=>{
        // this.itemNum = 0
        if (ItemCountUtil.validatorInputValue(value, true)){
            if (value.length<10){
                this.itemNum = value
                Log('-----', this.itemNum)
            }else {
                ToastUtil.show('瓶数量过大，请输入合适的瓶数量');
            }

        }
    }

    //设置回瓶兑酒有标率
    @action
    setWineRate = (value)=>{
        // let regEx = ;
       if(value){
           if(parseFloat(value)<=100){
               if (value){
                   let regEx =/^\d+\.?\d{0,3}$/;
                   if ( regEx.test(value)){
                       this.wineRate = value
                   }
               }else {
                   this.wineRate = '';
               }
           }else {
               ToastUtil.show('请输入正确的百分比');
           }
       }else {
           this.wineRate = '';
       }
    }

    //箱品种数量赋值
    @action
    setBoxNum = (value)=>{
        // this.boxNum = 0
        if (ItemCountUtil.validatorInputValue(value, true)){
            if (value.length<10){
                if (this.boxUnit * this.boxNum<2147483640){
                    this.boxNum =value
                }else {
                    this.boxNum =value
                    ToastUtil.show('箱数量过大，请输入合适的箱数量')
                }

            }else {
                ToastUtil.show('箱数量过大，请输入合适的箱数量')
            }

        }
    }

    //托板总数量赋值
    @action
    setPalletNum = (value)=>{
        // this.palletNum = 0
        if (ItemCountUtil.validatorInputValue(value, true)){
            if (value.length<10){
                this.palletNum =value
                Log('-----',this.palletNum)
            }else {
                ToastUtil.show('托板数量过大，请输入合适的托板数量')
            }


        }
    }
    //空托板数量赋值
    @action
    setNullPalletNum = (value)=>{
        // this.nullPalletNum =value
       if (this.palletNum){
           if (ItemCountUtil.validatorInputValue(value, true,true)){
               if ((parseInt(value)<=parseInt(this.palletNum)) || (value == '')){
                   this.nullPalletNum =value
               }else {
                   ToastUtil.show('请输入正确的空托板数量')
               }
           }

       }else {
           ToastUtil.show('请先输入托板总数量')
       }
    }

    /**
     * 设置托板承载规格 数字  可输入零  三位数
     */
    @action
    setPlateSpecification = (value)=>{
        if(value){
            if(parseFloat(value)<=999){
                if (value){
                    let regEx =/^\d+\d{0,3}$/;
                    if ( regEx.test(value)){
                        this.plateSpecification = value
                    }
                }else {
                    this.plateSpecification = '';
                }
            }else {
                ToastUtil.show('请输入正确的托板承载规格');
            }
        }else {
            this.plateSpecification = '';
        }
    }

    //提交申请
    @action
    submitApplyClick =async(isUpdate,callback)=>{
        let json = null

        try {
            let params = {}
            params.deliveryOrderNo = this.deliveryOrderNo  //提货单号  必填
            params.supplierId = this.selectOrgId  //提货组织Id 必填
            params.dealerCode = this.dealerCode;
            params.address = this.address;  //提货地址 必填
            params.addressId = this.selectAdressId  //提货地址Id 必填

            console.log('selectaddress  == ',toJS(this.selectAdressName));
            if(this.selectAdressName && this.selectAdressName != '请选择' && (!this.selectAdressId || !this.address))
            {
                Log(233232)
                params.address = this.billOfLadingInfo.address;
                params.addressId = this.billOfLadingInfo.addressId;
            }

            console.log('dddd == ',toJS(params),toJS(this.address),toJS(this.addressId));

            if(this.wineRate || (this.wineRate+'' == '0')){
                params.wineRate = parseFloat(this.wineRate).toFixed(3);
            }

            if(this.plateSpecification || (this.plateSpecification+'' == '0')){
                params.plateSpecification = this.plateSpecification;
            }

            //经销商Code
            params.dealerCode = this.dealerCode || (this.billOfLadingInfo && this.billOfLadingInfo.dealerOrgCode)
            

            if (this.distributorCode){ //分销商Code
                params.distributorCode = this.distributorCode
            }

            if(this.dispersePackSpec){  //散瓶包装规格
                params.dispersePackSpec = this.dispersePackSpec
            }

            if (this.subSpec){         //散瓶包装规格子规格
                params.subSpec = this.subSpec
            }

            if (this.takeBottleType){    //	瓶承载形式
                params.loadWay = this.takeBottleType
            }

            if (this.bottleCategory){    //瓶品种编码
                params.bottleCategory = this.bottleCategory
            }

            if (this.bottleCategoryCode){  //瓶品种
                params.bottleCategoryCode = this.bottleCategoryCode
            }

            if (this.bottleNum){   //瓶数量
                params.bottleNum =   parseInt(this.bottleNum)
            }

            if (this.boxCategory){
                params.boxCategory = this.boxCategory  //箱品种名称
            }

            if (this.boxCategoryCode){
                params.boxCategoryCode = this.boxCategoryCode  //箱品种 编码
            }

            if (this.boxNum){      //箱数量
                params.boxNum =   parseInt(this.boxNum)
            }

            if(this.itemNum){   //实际箱数量
                params.actualBoxNum =  parseInt(this.itemNum)
            }

            if(this.pallet){       //托板名称
                params.pallet = this.pallet
            }

            if(this.palletCategoryCode){       //托板code
                params.palletCode = this.palletCategoryCode
            }

            if (this.palletNum){      //托板总数量
                params.palletNum =  parseInt(this.palletNum)
            }

            if (this.nullPalletNum){      //其中空托板托板数量
                params.nullPalletNum =  parseInt(this.nullPalletNum)
            }

            if(isUpdate){
                params.recycleOrderNo = this.recycleOrderNo;
            }

            console.log('params ==== ',toJS(params));
            let rule = {
                deliveryOrderNo: [
                    {required: true, not: '', msg: '请输入提货单单号'},
                ],
                supplierId: [
                    {required: true, not: '', msg: '请输入提货组织'},
                ],
                address: [
                    {required: true, not: '', msg: '请输入起始地址'},
                ],



            };

            // Log('===========参数',toJS(params))

            if (ValidatorUtil.validate(params,rule)){

                if (this.dispersePackSpec && !this.bottleCategoryCode && !this.palletCategoryCode &&  (this.itemNum+'' == '') && this.handleHasSubSpecsOfBottlePackingData()){
                    ToastUtil.show('请输入数量')
                    return
                } else if (this.dispersePackSpec && (this.itemNum+'' == '') && this.handleHasSubSpecsOfBottlePackingData() ){
                    ToastUtil.show('请输入数量')
                    return
                }
                if (this.hasSubSpecsOfBottlePackingData() && !this.subSpec){
                    ToastUtil.show('请选择子规格')
                    return
                }else if (this.hasSubSpecsOfBottlePackingData() && (this.itemNum == '') && this.subSpec!='托板'){
                    ToastUtil.show('请输入数量')
                    return
                }else if (this.hasSubSpecsOfBottlePackingData()  && (this.itemNum == '') && this.subSpec=='托板' && !this.palletCategoryCode){
                    ToastUtil.show('请选择托板')
                    return
                }else if(this.hasSubSpecsOfBottlePackingData() && this.subSpec!='托板' && !this.bottleCategoryCode){
                    ToastUtil.show('请选择瓶品种')
                    return
                }
                if (this.dispersePackSpec && this.handleHasSubSpecsOfBottlePackingData() && !this.bottleCategoryCode){
                    ToastUtil.show('请选择瓶品种')
                    return
                }

                if (this.dispersePackSpec && this.subSpec &&this.subSpec!='托板' && !this.bottleCategoryCode){
                    ToastUtil.show('请选择瓶品种')
                    return
                }


                if(!this.boxCategoryCode && !this.dispersePackSpec){
                    ToastUtil.show('请选择散瓶包装规格或者箱品种')
                    return
                }

                if(this.boxCategoryCode && (this.boxNum == '')){
                    ToastUtil.show('请输入箱数量')
                    return
                }

                if(this.palletCategoryCode  && (this.palletNum == '')){
                    ToastUtil.show('请输入托板数量')
                    return
                }
                if(this.palletCategoryCode  && (!this.plateSpecification)){
                    ToastUtil.show('请输入托板承载规格')
                    return
                }

                if(parseInt(params.bottleNum)>2147483640){

                    ToastUtil.show('箱数量过大，请输入合适的箱数量')
                    return
                }

                if(parseInt(params.palletNum)<parseInt(params.nullPalletNum)){
                    ToastUtil.show('空托板数量不能比总托板数量大，请输入正确的空托板数量')
                    return
                }

                // Log('===========参数',toJS(params))
                if (isUpdate){
                    params.id = this.recycleDetailId;
                    json = await Api.upDateApplyClick(params)
                }else {
                    json = await Api.submitApplyClick(params)
                }




            }
        }catch (e){
            Log('eeee',e);
        }
        runInAction(()=>{
            if(json && json.resultCode ==0 ){
                this.clearData();
                callback && callback()
            }
        })



    }
    @action
    hasSubSpecsOfBottlePackingData(){
        return this.subSpecsOfBottlePackingData && this.subSpecsOfBottlePackingData.length>0;
    }
    //白酒的话，是写死数据的，要这样处理一下才行
    @action
    handleHasSubSpecsOfBottlePackingData(){
        return !this.hasSubSpecsOfBottlePackingData() && this.dispersePackSpec != '麻包瓶'
    }

    @action
    clearData = ()=>{
        this.deliveryOrderNo = ''; //提货单单号this.billOfLadingInfo = null; //提货单信息
        // this.orgListOfLadingBill = null; //提货单组织列表
        this.selectOrgId = null; //提货单组织Id
        this.normalSelectOrgId = null; //提货单组织Id
        // this.adressListData = null; //提货单地址
        this.selectAdressId = null; //提货单起始地址Id
        this.address = ''; //提货单起始地址
        // this.bottleTypeData = null; //瓶种类
        this.bottleCategory = ''; //瓶种类 名称
        this.bottleCategoryCode = ''; //瓶种类Code
        // this.specsOfBottlePackingData = null; //散瓶包装规格
        this.specsOfBottlePackingId = null; //散瓶包装规格Id
        this.specsOfBottleNum = 0; //散瓶包装规格数量
        this.dispersePackSpec = ''; //散瓶包装规格描述
        // this.subSpecsOfBottlePackingData = null; //散瓶包装规格 子规格
        this.subSpecsOfBottlePackingId = null; //散瓶包装规格Id 子规格Id
        this.subSpecsOfBottleNum = 0; //散瓶包装规格shulaign  子规格数量
        this.subSpec = ''; //散瓶包装规格 子规格描述
        this.takeBottleType = ''; //瓶承载形式
        // this.takeBottleTypeData = null; //瓶承载形式数据
        // this.boxTypeData = null; //箱种类
        this.boxCategory = null; //箱种类 描述
        this.boxCategoryCode = null; //箱种类Code
        this.boxNum = ''; //箱种类 数量
        this.boxUnit = ''; //箱种类 数量
        // this.palletTypeData = null; //托板类
        this.pallet = ''; //托板 描述
        this.palletCategoryCode = null; //托板Code
        this.palletNum = ''; //托板总数量
        this.nullPalletNum = '';//其中空托板数量
        this.itemNum = ''; //数量
        this.bottleNum = ''; //瓶数量
        this.dealerCode = ''; //经销商Code
        this.distributorCode = ''; //分销商Code
        this.unit = ''            //单位
        this.bottleUnitNum = ''            //单位
        this.wineRate = ''            //回瓶兑奖有标率
        this.plateSpecification = ''            //回瓶兑奖有标率
    }






}



const RecycleBottle = new RecycleBottleStore();

autorun(() => {
});
export default RecycleBottle;