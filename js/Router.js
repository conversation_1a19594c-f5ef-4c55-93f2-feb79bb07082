/**
 * Created by lao.jian<PERSON> on 2017/5/3.
 */
import React, { Component } from "react";
import { Image } from "react-native";
import _ from "lodash";
import {
  StackNavigator,
  TabNavigator,
  DrawerNavigator,
  NavigationActions
} from "react-navigation";
import Test from "./module/test/Test";
import Splash from "./module/welcome/Splash";

import CommonWebView from "./module/webview/CommonWebView";
import HomePage from "./module/home/<USER>";
import AddressList from "./module/address/AddressList";
import DeliveryComment from "./module/comment/DeliveryComment";
import MineCompany from "./module/company/MineCompany";
import InvoiceInfo from "./module/invoice/InvoiceInfo";
import UserInfo from "./module/user/UserInfo";
import Login from "./module/login/Login";
import Setting from "./module/login/Setting";
import ActivateAccount from "./module/login/ActivateAccount";
import ResetPassword from "./module/login/ResetPassword";
import DeliveryOrderDetail from "./module/order/DeliveryOrderDetail";
import OrderList from "./module/order/OrderList";
import OrderFilter from "./module/order/OrderFilter";
import GoodsDetailList from "./module/order/GoodsDetailList";
import ConfirmOrder from "./module/order/ConfirmOrder";
import QuickOrder from "./module/order/QuickOrder";
import PayOrder from "./module/pay/PayOrder";
import InPayment from "./module/pay/InPayment";
import ShopCartList from "./module/shopcart/ShopCartList";
import GoodsDetail from "./module/goods/GoodsDetail";
import SearchPage from "./module/search/SearchPage";
import BottleBalance from "./module/company/BottleBalance";
//import CapForWine from './module/company/CapForWine';
import ChargeWine from "./module/charge-wine/ChargeWine";
import OrderDetail from "./module/order/OrderDetail";
import PaySuccess from "./module/pay/PaySuccess";
import MessageCenter from "./module/message/MessageCenter";
import ApplyRecycleBottle from "./module/recycle-bottle/ApplyRecycleBottle";
import RecycleBottleList from "./module/recycle-bottle/RecycleBottleList";
import RecycleBottleDetail from "./module/recycle-bottle/RecycleBottleDetail";
import RecycleBottleInspected from "./module/recycle-bottle/RecycleBottleInspected";
import PaymentDetails from "./module/company/PaymentDetails";
import BottleCapWine from "./module/cap-wine/BottleCapWine";
import MergePay from "./module/pay/MergePay";
import MergePayment from "./module/company/MergePayment";
import UserStore from "./store/User";
import MarginBalance from "./module/margin-balance/MarginBalance";

//数字兑奖
import VerificationList from "./module/convert-award/VerificationList";
import MyCoupons from "./module/convert-award/MyCoupons";
import ApplyReplenish from "./module/convert-award/ApplyReplenish";
import ApplyTransfer from "./module/convert-award/ApplyTransfer";
import MyReplenishments from "./module/convert-award/MyReplenishments";
import MyTransfers from "./module/convert-award/MyTransfers";
import TransferDetail from "./module/convert-award/TransferDetail";
import NoPermission from "./module/no-permission/NoPermission";

//申请售后
import ApplyAfterSale from "./module/after-sale/ApplyAfterSale";
import AfterSaleList from "./module/after-sale/AfterSaleList";
import AfterSaleDetail from "./module/after-sale/AfterSaleDetail";

// 用户须知
import UserNotice from "./module/user-notice/UserNotice";


//赊销支付
import CreditPaymentSuccess from "./module/credit-payment/CreditPaymentSuccess";
import SubmitPayment from "./module/credit-payment/SubmitPayment";
import UserNoticeList from "./module/notice-list/NoticeList";
const renderTabImage = image => {
  return (
    <Image
      style={[{ width: Constant.scale(21), height: Constant.scale(21) }]}
      resizeMode="contain"
      source={image}
    />
  );
};

const TabStack = TabNavigator(
  {
    HomePage: {
      title: "首页",
      screen: HomePage,
      navigationOptions: {
        header: null,
        tabBarLabel: "首页",
        // tabBarLabel: '热更噫',
        tabBarIcon: ({ focused, tintColor }) =>
          renderTabImage(
            !focused
              ? require("./module/img/tab/ic_home_normal.png")
              : require("./module/img/tab/ic_home_hover.png")
          )
      }
    },
    QuickOrder: {
      title: "快捷下单",
      screen: QuickOrder,
      navigationOptions: {
        header: null,
        tabBarLabel: "快捷下单",
        tabBarIcon: ({ focused, tintColor }) =>
          renderTabImage(
            !focused
              ? require("./module/img/tab/ic_quick_normal.png")
              : require("./module/img/tab/ic_quick_hover.png")
          )
      }
    },
    ShopCartList: {
      title: "购物车",
      screen: ShopCartList,
      navigationOptions: {
        header: null,
        tabBarLabel: "购物车",
        tabBarIcon: ({ focused, tintColor }) =>
          renderTabImage(
            !focused
              ? require("./module/img/tab/ic_shopcart_normal.png")
              : require("./module/img/tab/ic_shopcart_hover.png")
          )
      }
    },
    MineCompany: {
      title: "企业中心",
      screen: MineCompany,
      navigationOptions: {
        header: null,
        tabBarLabel: "企业中心",
        tabBarIcon: ({ focused, tintColor }) =>
          renderTabImage(
            !focused
              ? require("./module/img/tab/ic_company_normal.png")
              : require("./module/img/tab/ic_company_hover.png")
          )
      }
    }
  },
  {
    tabBarVisible: false,
    tabBarPosition: "bottom",
    backBehavior: "none",
    swipeEnabled: false,
    animationEnabled: false,
    lazy: true,
    tabBarOptions: {
      showIcon: true,
      scrollEnabled: false,
      activeTintColor: Constant.colorPrimary,
      inactiveTintColor: Constant.colorTxtDefault,
      style: {
        margin: 0,
        backgroundColor: "white",
        paddingTop: 0,
        height: Constant.scale(50)
      },
      tabStyle: {
        borderTopWidth: Constant.sizeDividerNormal,
        borderTopColor: Constant.colorDivider,
        margin: 0,
        padding: 0
      },
      indicatorStyle: {
        height: 0
      },
      labelStyle: {
        padding: 0,
        margin: 0,
        marginTop: 1,
        fontSize: Constant.scale(11)
      }
    }
  }
);

const AppRouterInfo = {
  Tab: {
    screen: TabStack
  },
  Splash: {
    title: "欢迎页",
    screen: Splash
  },
  Test: {
    title: "测试页",
    screen: Test
  },
  ShopCartList: {
    title: "购物车",
    screen: ShopCartList
  },
  CommonWebView: {
    title: "通用WebView",
    screen: CommonWebView
  },
  AddressList: {
    title: "地址管理",
    screen: AddressList
  },
  DeliveryComment: {
    title: "评价物流",
    screen: DeliveryComment
  },
  BottleBalance: {
    title: "瓶箱余额",
    screen: BottleBalance
  },
  ChargeWine: {
    title: "费用赠酒",
    screen: ChargeWine
  },
  InvoiceInfo: {
    title: "发票详情",
    screen: InvoiceInfo
  },
  UserInfo: {
    title: "用户须知",
    screen: UserInfo
  },
  Login: {
    title: "登陆",
    screen: Login
  },
  Setting: {
    title: "设置页",
    screen: Setting
  },
  ActivateAccount: {
    title: "激活账号",
    screen: ActivateAccount
  },
  ResetPassword: {
    title: "重置密码",
    screen: ResetPassword
  },
  DeliveryOrderDetail: {
    title: "提货单详情",
    screen: DeliveryOrderDetail
  },
  OrderList: {
    title: "订单列表",
    screen: OrderList
  },
  PayOrder: {
    title: "支付订单",
    screen: PayOrder
  },
  ConfirmOrder: {
    title: "确定订单",
    screen: ConfirmOrder
  },
  GoodsDetail: {
    title: "商品详情",
    screen: GoodsDetail
  },
  SearchPage: {
    title: "搜索页",
    screen: SearchPage
  },
  OrderDetail: {
    title: "订单详情",
    screen: OrderDetail
  },
  PaySuccess: {
    title: "支付成功",
    screen: PaySuccess
  },
  MessageCenter: {
    title: "全部消息",
    screen: MessageCenter
  },
  ApplyRecycleBottle: {
    title: "回瓶申请",
    screen: ApplyRecycleBottle
  },
  RecycleBottleList: {
    title: "回瓶申请列表",
    screen: RecycleBottleList
  },
  RecycleBottleDetail: {
    title: "回瓶申请详情",
    screen: RecycleBottleDetail
  },
  RecycleBottleInspected: {
    title: "回瓶验收单",
    screen: RecycleBottleInspected
  },
  GoodsDetailList: {
    title: "商品列表",
    screen: GoodsDetailList
  },
  InPayment: {
    title: "支付中",
    screen: InPayment
  },
  PaymentDetails: {
    title: "费用缴纳",
    screen: PaymentDetails
  },

  //数字兑奖
  VerificationList: {
    title: "核销列表",
    screen: VerificationList
  },
  MyCoupons: {
    title: "我的奖券",
    screen: MyCoupons
  },
  ApplyReplenish: {
    title: "补货申请",
    screen: ApplyReplenish
  },
  MyReplenishments: {
    title: "我的补货单",
    screen: MyReplenishments
  },
  ApplyTransfer: {
    title: "申请转入",
    screen: ApplyTransfer
  },
  MyTransfers: {
    title: "我的奖券转入",
    screen: MyTransfers
  },
  TransferDetail: {
    title: "申请转入详情",
    screen: TransferDetail
  },

  ShopCartPage: {
    title: "购物车",
    screen: ShopCartList
  },
  BottleCapWine: {
    title: "瓶盖赠酒",
    screen: BottleCapWine
  },

  MergePay: {
    title: "合并支付",
    screen: MergePay
  },
  MergePayment: {
    title: "费用缴纳合并支付",
    screen: MergePayment
  },
  NoPermission: {
    title: "无权限页",
    screen: NoPermission
  },
  MarginBalance: {
    title: "按金余额",
    screen: MarginBalance
  },
  ApplyAfterSale: {
    title: "申请售后",
    screen: ApplyAfterSale
  },
  AfterSaleList: {
    title: "售后列表",
    screen: AfterSaleList
  },
  AfterSaleDetail: {
    title: "售后列表",
    screen: AfterSaleDetail
  },
  OrderFilter: {
    title: "订单筛选",
    screen: OrderFilter
  },
  UserNotice: {
    title: "用户须知",
    screen: UserNotice
  },

  CreditPaymentSuccess: {
    title: "赊销支付成功",
    screen: CreditPaymentSuccess
  },
  SubmitPayment: {
    title: "赊销查看",
    screen: SubmitPayment
  },

  UserNoticeList: {
    title: "公告列表",
    screen: UserNoticeList
  },
};
const AppStack = StackNavigator(AppRouterInfo, {
  headerMode: "none",
  initialRouteName: !__DEV__ ? "Splash" : "Login"
});
const defaultGetStateForAction = AppStack.router.getStateForAction;
AppStack.router.getStateForAction = (action, state) => {
  // Log('getStateForAction', action, state);
  let lastRouterName = null;
  if (state && state.routes) {
    //获取最后的路由名
    lastRouterName = state.routes[state.index].routeName;
  }

  if (
    state &&
    action.type === NavigationActions.NAVIGATE &&
    lastRouterName === action.routeName &&
    action.routeName != "GoodsDetail"
  ) {
    //TODO 防止跳转同一个页面
    return null;
  } else if (state && action && action.type === NavigationActions.NAVIGATE) {
    let { routeName } = action;
    //TODO 判断路由是否声明要登陆才跳转
    if (
      AppRouterInfo[routeName] &&
      AppRouterInfo[routeName].login &&
      UserStore.token == null
    ) {
      action.routeName = "Login";
    }
  }
  if (
    action &&
    (action.type === NavigationActions.NAVIGATE ||
      action.type === NavigationActions.INIT)
  ) {
    //TODO 埋点设置路由组件开始时间
    if (!action.params) {
      action.params = {};
    }
    action.params.startPageTime = new Date().getTime();
  }

  action.routeName = UserStore.handleUserLimits(action.routeName);
  return defaultGetStateForAction(action, state);
};

// const defaultGetComponentForState = AppStack.router.getComponentForState;
// AppStack.router.getComponentForState = (state) => {
//     Log('getComponentForState', state);
//     return defaultGetComponentForState(state);
// };
// const defaultGetComponentForRouteName=AppStack.router.getComponentForRouteName;
// AppStack.router.getComponentForRouteName=(routeName) => {
//     Log('getComponentForRouteName', routeName);
//     return defaultGetComponentForRouteName(routeName);
// };

// const defaultGetActionForPathAndParams=AppStack.router.getActionForPathAndParams;
// AppStack.router.getActionForPathAndParams=(path, params) => {
//         Log('getActionForPathAndParams', path,params);
//     return defaultGetActionForPathAndParams(path,params);
// };

export { AppStack };
