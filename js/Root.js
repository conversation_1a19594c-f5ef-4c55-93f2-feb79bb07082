/**
 * Created by lao.jian<PERSON> on 2017/3/16.
 */
import './Global';
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Text,
    DeviceEventEmitter,
    Image,
    Alert,
    Platform,
    Dimensions,
    NativeEventEmitter,
    NativeAppEventEmitter,
    Linking,
    NativeModules
} from 'react-native';
import {NavigationActions} from 'react-navigation';
import {WeChat, AndroidPermission, GlobalLoading, SensorsData, UmengShare, MobilePush,AppTool,DeviceInfo} from 'rn-yunxi';
import {AppStack} from './Router'
import ProviderStore from './store/ProviderStore';
import { Provider, inject } from 'mobx-react/native';
//
const Push = NativeModules.MobilePush;
const PushEmitter = new NativeEventEmitter(Push);
import QueryString from 'querystring'
let {width, height, scale} = Dimensions.get('window');

function getCurrentRouteName(navigationState) {
    if (!navigationState) {
        return null;
    }
    let route = navigationState.routes[navigationState.index];
    // dive into nested navigators
    if (route.routes) {
        return getCurrentRouteName(route);
    }
    return route.routeName;
}
function getPreRouteName(navigationState) {
    if (!navigationState) {
        return null;
    }
    if(navigationState.index-1<0){
        return null;
    }
    let route = navigationState.routes[navigationState.index-1];
    // dive into nested navigators
    if (route.routes) {
        return getCurrentRouteName(route);
    }
    return route.routeName;
}

function getPreRouteParam(navigationState) {
    if (!navigationState) {
        return null;
    }
    let route = navigationState.routes[navigationState.index-1];
    // dive into nested navigators
    if (route.routes) {
        return getCurrentRouteParam(route);
    }
    return route.params;
}

function getCurrentRouteParam(navigationState) {
    if (!navigationState) {
        return null;
    }
    let route = navigationState.routes[navigationState.index];
    // dive into nested navigators
    if (route.routes) {
        return getCurrentRouteParam(route);
    }
    return route.params;
}


//模块声名并导出
export default class Root extends Component {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};
    navigator = null;

    //构造函数
    constructor(props) {
        super(props);
        Log('root',props,'hostKey' in props);
        if('hostKey' in props&&'isCanChangeEvn' in props&&'isShowEvn' in props){
            Config.HOST_KEY=props.hostKey;
            Config.isCanChangeEvn=props.isCanChangeEvn;
            Config.isShowEvn=props.isShowEvn;
            Config.setHostWithHostKey(Config.HOST_KEY)
            if(Config.isCanChangeEvn){
                Config.setHost().then().catch();
            }

        }else{
            Config.setHost().then().catch();
        }
        GlobalLoading.hide();
    }

    onNavigationStateChange(prevState, currentState, action) {
        // console.log('onNavigationStateChange','preState>>',prevState,'currentState>>',currentState,'action>>',action);
        const realCurrentScreen=getCurrentRouteName(currentState);
        const realCurrentScreenPreRouterName=getPreRouteName(currentState);
        const realPreScreen=getCurrentRouteName(prevState);

        DeviceEventEmitter.emit(Config.EVENT_SCREEN_CHANGE, {currentScreen: realCurrentScreen, prevScreen: realPreScreen});
        global.preScreenName=realCurrentScreenPreRouterName;

        if(action.type===NavigationActions.SET_PARAMS||action.type===NavigationActions.INIT||action.type===NavigationActions.URI){
            //过滤Action
            return;
        }
        //TODO 页面埋点统一处理
        let currentScreen=getCurrentRouteName(prevState);
        let currentScreenParams=getCurrentRouteParam(prevState);
        let preScreen=getPreRouteName(prevState);
        preScreen=preScreen?preScreen:'';
        // console.log('onNavigationStateChange','preScreen>>',preScreen,'currentScreen>>',currentScreen,"currentScreenParams>>",currentScreenParams,"action>>",action);
        let trackViewScreen={};
        if(currentScreenParams&&currentScreenParams.trackViewScreen){
            trackViewScreen=Object.assign(trackViewScreen,{args:JSON.stringify(currentScreenParams.trackViewScreen)})
        }
        let currentTime=new Date().getTime();
        trackViewScreen.view_dur=currentTime-((currentScreenParams&&currentScreenParams.startPageTime)?currentScreenParams.startPageTime:currentTime);
        SensorsData.trackViewScreen(currentScreen,preScreen,trackViewScreen);


    }

    componentWillMount() {
        if (!__DEV__) {
            this.onError();
        }

        // SensorsData.init(Config.SENSORS_SA, Config.SENSORS_CONFIG, true);
        if (Platform.OS === 'android') {
            AndroidPermission.requestPermission(
                [
                    'android.permission.CALL_PHONE',
                    'android.permission.CAMERA',
                    'android.permission.RECORD_AUDIO',
                    'android.permission.ACCESS_FINE_LOCATION',
                    'android.permission.READ_EXTERNAL_STORAGE',
                    'android.permission.WRITE_EXTERNAL_STORAGE',
                ]);
        }


    }

    checkVersion(data){
        let bulidNumber = DeviceInfo.getBuildNumber();
        Log('checkVersion', data,bulidNumber);
        let {versionCode,forceUpdate,downloadUrl,remark,versionName,status}=data;

        if (versionCode > bulidNumber) {
            //是否强制更新
            let alertTxt = 'app有新版本:' + versionName + '\n';
            if (forceUpdate) {
                alertTxt = alertTxt + '必须更新才能正常使用\n' + (remark ? '更新内容:\n' + remark : '');
            }
            else {
                alertTxt = alertTxt + (remark ? '更新内容:\n' + remark : '');
            }

            let btnArray = [];
            if (downloadUrl.indexOf('http') == -1 && downloadUrl.indexOf('https') == -1) {
                downloadUrl = 'https://' + downloadUrl;
            }
            if (forceUpdate) {
                btnArray = [{
                    text: '立即更新', onPress: () => {
                        if (Platform.OS === 'ios') {
                            Linking.openURL(downloadUrl).catch(err => console.error('An error occurred', err));
                        } else {
                            AppTool.installApkFromUrl(downloadUrl,'transportation',"珠啤车管理更新")
                        }

                    }
                }];
            }
            else {
                btnArray = [{
                    text: '稍后更新',
                }, {
                    text: '立即更新', onPress: () => {
                        if (Platform.OS === 'ios') {
                            Linking.openURL(downloadUrl).catch(err => console.error('An error occurred', err));
                        } else {
                            AppTool.installApkFromUrl(downloadUrl,'transportation',"珠啤车管理更新")
                        }
                    }
                }];
            }
            if(status=='1') {
                Alert.alert("发现新版本", alertTxt, btnArray, {cancelable: false});
            }
        }
    }

    componentDidMount() {
        WeChat.registerApp(Config.WECHAT_APP_KEY).then().catch((e)=>{});
        this.userStoreInit = DeviceEventEmitter.addListener('user_store_init', () => {
            //TODO 初始化UserStore才显示页面
            MobilePush.onReactLoaded();
            this.setState({}, () => {
                setTimeout(() => {
                    Api.checkVersion().then(data => {
                        if(data!=null){
                            // this.checkVersion(data.data)
                        }

                    }).catch(err => {});
                })

            })

        });
        this.authRequireListener = DeviceEventEmitter.addListener('auth_require', () => {
            //TODO 服务器返回状态码401统一处理
            // Log(this.navigator);
            this.navigator && this.navigator.dispatch({type: NavigationActions.NAVIGATE, routeName: 'Login', params: {auth_require: true}});
        });
        if (Platform.OS == 'android') {
            this.onNotificationOpened = DeviceEventEmitter.addListener('onNotificationOpened', (data) => {
                Log('onNotificationOpened', data)
                this.handPushData(data)
            });

            this.onNotification = DeviceEventEmitter.addListener('onNotification', (data) => {
                Log('onNotification', data)
            });
        } else {
            this.onNotificationOpened = PushEmitter.addListener('onNotificationOpened', (data) => {
                Log('onNotificationOpened', data)
                this.handPushData(data)

            });

            this.onNotification = PushEmitter.addListener('onNotification', (data) => {
                Log('onNotification', data)
            });
        }
    }

    /**
     * 处理打开的推送消息
     * @param data
     */
    handPushData(data) {
        // Log('push>>>>>>>>>>>>>>>>>>>>>>>>>', data);

        if (data && data.extra) {
            let reg = /^(app:\/\/)(.*?)(\?)(.*?)$/g;
            let result = null;
            if (!data.extra.action) {
                return;
            }
            if (data.extra.action.indexOf('http') != -1) {
                this.navigate('CommonWebView', {url: data.extra.action});
                return;
            }
            result = data.extra.action.replace(reg, (text, p1, p2, p3, p4) => {
                let router = p2;
                let params = QueryString.parse(p4);

                if (!p2 || p2 == '') {
                    return '';
                }

                return JSON.stringify({
                    router,
                    params
                });
            })
            try {
                let data = JSON.parse(result);
                if (data && data.router) {
                    this.navigator && this.navigator.dispatch({
                        type: NavigationActions.NAVIGATE,
                        routeName: data.router,
                        params: data.params
                    });
                }
            } catch (err) {
                Log(err)
            }
        }
    }

    componentWillUnmount() {
        this.userStoreInit&&this.userStoreInit.remove();
        this.authRequireListener&&this.authRequireListener.remove();
        this.onNotificationOpened&&this.onNotificationOpened.remove();
        this.onNotification&&this.onNotification.remove();
    }

    onError() {
        //TODO 全局Error处理
        ErrorUtils.setGlobalHandler(error => {
            try {
                Alert.alert("出错了", error.toString(), [{
                    text: 'OK', onPress: () => {
                        GlobalLoading.hide();
                        let resetAction = NavigationActions.reset({
                            index: 0,
                            actions: [
                                NavigationActions.navigate({routeName: 'Splash'}),
                            ]
                        });
                        this.navigator && this.navigator.dispatch(resetAction);
                    }
                }])
            }
            catch (err) {

            }
        })
    }

    render() {
        return (
            <Provider {...ProviderStore}>
                {ProviderStore.user.init? <AppStack
                    ref={nav => {
                        this.navigator = nav;
                    }}
                    onNavigationStateChange={this.onNavigationStateChange}/>:<View/>
                }
            </Provider>
        );
    }
};