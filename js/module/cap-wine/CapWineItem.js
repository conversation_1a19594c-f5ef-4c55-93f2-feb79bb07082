/**
 *
 * Created by xiaowz on 2018/4/22.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager,
    AsyncStorage
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'
const {width, height} = Dimensions.get('window');
const {Image, CheckBox, PriceText, ConfirmInput} = AppWidget;
const {Text, CommonListView,CommonFlatList} = Widget;
import {toJS} from 'mobx';
import _ from 'lodash';
import SkuPanelStore from '../../store/SkuPanel';
import {inject, observer} from 'mobx-react/native';
import SkuPanel from '../shopcart/SkuPanel';
import ToastUtil from "../../util/ToastUtil";

//模块声名并导出
@inject(stores => ({
    user: stores.user,
}))
@observer
export default class CapWineItem extends ReactNavComponent {
//属性声名
    static propTypes = {
        onItemNumConfirm: PropType.any,
        chargeWineStore: PropType.any,
        data: PropType.any,
        goodsExtensionStore:PropType.any,
        capForWineStore:PropType.any

    };
    //默认属性
    static defaultProps = {

    };

//     orgList ={this.goodsExtensionStore.orgListMap.get(item.itemId)}

    //构造函数
    constructor(props) {
        super(props);
        this.skuPanelStore = new SkuPanelStore();
        //状态机变量声明
        this.state = {
            isFocus: false,
        };
    }


    componentWillMount() {

    }

    componentDidMount() {
        this.initSkuPanel(this.props.data)
    }

    initSkuPanel = (item) => {
        // Log('初始化商品数据------',toJS(item.itemId) )
        let itemAwards = this.props.goodsExtensionStore.obtainItemAwards(item.itemId);
        this.skuPanelStore.setSelectOrgId(item.orgId);
        this.skuPanelStore.setSelectChannelId(item.salesChannelCode, item.subSalesChannelCode);
        this.skuPanelStore.setAwards(itemAwards);
        // this.skuPanelStore.setSelectAwardId(item.awardsId);
        this.skuPanelStore.setSelectPlateFlag(0);
        this.skuPanelStore.setPurchaseCount(0, false, item.isZeroFourGoods);
        this.skuPanelStore.setItemId(item.itemId,false);
        this.skuPanelStore.setItemIdAndOrgItem(item.itemId,
            {
                orgId: item.orgId,
                orgName: item.orgName
            }
        );
        let tmp = this.skuPanelStore.getChoose();
        this.props.capForWineStore.setDefaultAwardsIdAndChannelCode(item,tmp)

        // 设置可选择的托板承载规格列表
        this.skuPanelStore.setPalletStruList(this.props.goodsExtensionStore.palletStruList || []);
        this.skuPanelStore.setBatchSaleNum(item.batchSaleNum || '0');
        this.skuPanelStore.setBearing(item.bearing || '');
    }

    //渲染
    render() {
        // let isEditStatus = this.props.isEditStatus;  //是否编辑状态
        let item = this.props.data;   //单个商品的数据
        // let awardsName = _.get(item, 'awardsName');
        // Log('=====测试item渠道码====',item.salesChannelCode,item.subSalesChannelCode)
        let awardsName = this.skuPanelStore.selectAwardName(this.props.goodsExtensionStore.obtainItemAwards(item.itemId));
        let channelName = this.props.user.getChancelFullName(item.salesChannelCode,item.subSalesChannelCode);
        let convertibleNum = Math.floor(item.balance-item.occupiedAmount);  //可兑换数量

        let itemNum = '';
        if (this.state.isFocus) {
            itemNum = this.skuPanelStore.tmpPurchaseCount + '';
        } else {
            itemNum = this.skuPanelStore.purchaseCount + '';
        }
        let purchaseQuantity = this.skuPanelStore.purchaseQuantity;

        return (
            <View>
            <View style={{paddingLeft:Constant.sizeMarginDefault,flexDirection:'row',alignItems:'center',width:width,height:Constant.scale(44),borderBottomWidth:Constant.sizeDividerNormal,borderColor:Constant.colorDividerDefault,backgroundColor:'white'}}>
                <Text style={{color:Constant.colorTxtContent}}>赠酒类型：</Text>
                <Text>{item.description||'暂无'}</Text>
            </View>
            <View style={{
                padding: Constant.sizeMarginDefault,
                paddingRight: 0,
                borderBottomWidth: Constant.sizeDividerNormal,
                borderColor: Constant.colorDividerDefault,
                backgroundColor: 'white',
                marginBottom:Constant.scale(10)
            }}>
                <View style={{flexDirection: 'row', justifyContent: 'center'}}>

                    <View style={{flex: 1, paddingRight: Constant.sizeMarginDefault}}>
                        <View style={{flexDirection: 'row', marginBottom: Constant.sizeMarginDefault}}>
                            <Image
                                resizeMode={'cover'}
                                source={{uri:item.itemPic}}
                                // defaultBackgroundColor={'#fff'}
                                style={{
                                    borderWidth: Constant.sizeDividerNormal,
                                    borderColor: Constant.colorDividerDefault,
                                    marginRight: Constant.sizeMarginDefault,
                                    width: Constant.scale(75),
                                    height: Constant.scale(75)
                                }}/>
                            <View style={{flex: 1, paddingLeft: Constant.scale(10)}}>
                                <Text style={{color:Constant.colorTxtContent,fontSize:Constant.fontSizeSmall}} numberOfLines={1}>{item.itemName}</Text>
                                <View style={{flexDirection: 'row', alignItems: 'flex-end',marginTop:Constant.sizeMarginDefault}}>
                                   <Text>可兑换：{convertibleNum}</Text>
                                </View>
                                <View style={{flex: 1}}/>

                                <ConfirmInput
                                    isFocus={(isFocus) => {
                                        if (isFocus) {
                                            this.skuPanelStore.setPurchaseCount(this.skuPanelStore.purchaseCount, true,item.isZeroFourGoods);
                                        } else {
                                            this.skuPanelStore.setPurchaseCount(this.skuPanelStore.tmpPurchaseCount, false,item.isZeroFourGoods);
                                        }
                                        this.setState({isFocus: isFocus}, () => {
                                            if (!this.state.isFocus) {
                                                this.props.onItemNumConfirm && this.props.onItemNumConfirm(this.skuPanelStore.purchaseCount);
                                            }
                                        })

                                    }}
                                    value={itemNum}
                                    onChangeText={(text) => {
                                        if (text > convertibleNum){
                                            ToastUtil.show('输入商品数量大于可兑换数量');
                                            this.skuPanelStore.setPurchaseCount(0, true,item.isZeroFourGoods)
                                            return;
                                        }
                                        this.skuPanelStore.setPurchaseCount(text, true,item.isZeroFourGoods)
                                        // this.props.onItemNumChange&&this.props.onItemNumChange(text);
                                    }}/>
                            </View>
                            {
                                purchaseQuantity.multiple > 0 ? <View style={{marginLeft: Constant.sizeMarginDefault}}>
                                    <Text style={{

                                        borderRadius: Constant.scale(2),
                                        paddingLeft: Constant.scale(2),
                                        paddingRight: Constant.scale(2),
                                        backgroundColor: '#e5e5e5',
                                        color: Constant.colorTxtAlert
                                    }}>{purchaseQuantity.isMandatory?'整板':'建议'}1*{purchaseQuantity.multiple}</Text>
                                </View> : null
                            }
                        </View>

                    </View>


                </View>
                <TouchableOpacity
                    onPress={() => this.showSkuPanel(item)}
                    style={{
                        alignItems: 'center',
                        flexDirection: 'row',
                        padding: Constant.sizeMarginDefault,
                        marginLeft:Constant.scale(0),
                        minHeight: Constant.scale(30),
                        width:'100%',
                        backgroundColor: Constant.colorBackgroundDefault
                    }}>
                    <View style={{flex:1,paddingRight:5}}>
                        <View style={{flexDirection: 'row',justifyContent:'space-between',width:'100%',}}>
                            <Text numberOfLines={1} style={[styles.attrTxt]}>
                                {awardsName}
                            </Text>

                            <Text style={[styles.attrTxt,{marginRight:10}]}>
                                {item.withPlateFlag == '1' ? '带板' : '不带板'}
                            </Text>
                        </View>

                        <View style={{flexDirection: 'row',justifyContent:'space-between',width:'100%'}}>
                            <Text numberOfLines={1} style={[styles.attrTxt]}>
                                {item.orgName}  {item.bearing}
                            </Text>

                            <Text style={[styles.attrTxt,{marginRight:10}]}>
                                {channelName}
                            </Text>
                        </View>


                    </View>

                    <Image style={{width: Constant.scale(7.5), height: Constant.scale(4)}}
                           source={require('../img/arrow/ic_content_open.png')}
                    />
                </TouchableOpacity>

            </View>
            </View>
        )
    }

    // showPanel 选择不同属性

    showSkuPanel(itemData) {
        // return;
        Widget.Popup.show(
            <SkuPanel
                skuPanelStore={this.skuPanelStore}
                awardsMap = {this.props.goodsExtensionStore.obtainItemAwards(itemData.itemId)}
                orgId={this.skuPanelStore.selectOrgId}
                channelListData={toJS(this.props.user.channelList)}
                salesChannelCode = { this.skuPanelStore.selectChannelId.salesChannelCode}
                subSalesChannelCode = {this.skuPanelStore.selectChannelId.subSalesChannelCode}
                salesChannelName= {this.skuPanelStore.selectChannelName.salesChannelName}
                subSalesChannelName= {this.skuPanelStore.selectChannelName.subSalesChannelName}
                plateFlag={this.skuPanelStore.selectPlateId}
                awardId={this.skuPanelStore.selectAwardId}
                palletStruList = {this.skuPanelStore.palletStruList}
                batchSaleNum = {this.skuPanelStore.batchSaleNum}
                bearing = {this.skuPanelStore.bearing}
                isGiftWinePanel={true}
                onConfirm={(result) => {
                    //更新规格，刷新列表
                    let newResult = toJS(result)
                    Widget.Popup.hide();
                    InteractionManager.runAfterInteractions(() => {
                        this.props.capForWineStore.modifyItemClick(itemData, newResult).then(()=>this.setState({})).catch(e=>{})
                    })
                }}
                onCancel={() => {
                    Widget.Popup.hide()
                }
                }/>,
            {
                animationType: 'slide-up', backgroundColor: '#00000000',
                onMaskClose: () => {
                    Widget.Popup.hide()
                }
            })
    }
};
const styles = StyleSheet.create({
   container: {
        flex: 1,
        backgroundColor:Constant.colorBackgroundDefault
    },
    attrTxt: {
        fontSize: Constant.fontSizeSmall,
        color: Constant.colorTxtContent,
        // marginRight: Constant.scale(5),
    }
});
