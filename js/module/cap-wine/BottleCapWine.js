/**
 *
 * Created by xiaowz on 2018/4/22.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager,
    AsyncStorage
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'
const {width, height} = Dimensions.get('window');
const {Text, CommonListView,CommonFlatList} = Widget;
const {Header,PrimaryHeader,Button,Image} = AppWidget;
import {toJS} from 'mobx';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
import {inject} from "mobx-react/native";
import {observer} from "mobx-react/native";
import CapWineItem from './CapWineItem';
import GoodsExtension from '../../store/GoodsExtension';
import BottleCapForWine from  '../../store/BottleCapForWine';
//模块声名并导出
@inject(stores => ({
    user: stores.user,
}))
@observer
export default class BottleCapWine extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {
    };
    //默认属性
    static defaultProps = {
    };
    //构造函数
    constructor(props) {
        super(props);
         //状态机变量声明
        this.goodsExtensionStore = new GoodsExtension()
        this.capForWineStore = new BottleCapForWine()
         this.state={
         };

    }


    componentWillMount() {

    }

    componentDidMount() {

        let {params} = this.getNavState();
        this.props.user.obtainChannelList().then().catch();

        // 加载托板承载规格列表
        this.goodsExtensionStore.getPalletStruList();

        if (params.accountType) {
            this.capForWineStore.getCapWineListData({
                accountType: params.accountType
            }).then((itemList) => {
                this.goodsExtensionStore.setBottleCapWineOrgListMap(itemList,()=>{
                    this.capForWineStore.handleCapForWineData()
                }).then().catch(e => {})
            }).catch(e => {
            })
        }
    }

    //渲染
    render() {

        return (
            <Header showBackAction={true}
                    title={'瓶盖赠酒'}>
                <View style={styles.container}>
                    {this.renderCapWinList()}
                    {this.renderBottomView()}

                </View>
            </Header>
        );
    }

    //标题View
    renderheaderView = ()=>{
        return(
            <View style={{width:width,height:Constant.scale(44),flexDirection:'row',alignItems:'center',paddingLeft:Constant.sizeMarginDefault,backgroundColor:'white'}}>
                <Text style={{color:Constant.colorTxtContent}}>赠酒类型: </Text>
                <Text style={{color:Constant.colorTxtTitle}}>消化客户费用</Text>
            </View>
        )
    }

    //瓶盖赠酒列表（FlatList）
    renderCapWinList = ()=>{
        return(
            <CommonFlatList
                listViewRef={ref => this.listView = ref}
                style={{flex:1,marginBottom:Constant.scale(65),marginTop:Constant.scale(10)}}
                data={ this.capForWineStore.showListData}
                listState={this.capForWineStore.listParams.listState}
                enableLoadMore={this.capForWineStore.listParams.enableLoadMore}
                enableRefresh={this.capForWineStore.listParams.enableRefresh}
                keyExtractor={(item, index) => {
                    return  'bottleCapWine' + index;
                }}
                renderItem={this.renderItem}
                extraData={this.props}
                // renderHeader={this.renderheaderView()}

            >
                <View style={[{flex: 1, justifyContent: 'center', alignItems: 'center',}]}>

                    <Text style={{fontSize: Constant.fontSizeBig, color: Constant.colorTxtContent}}>
                        暂无数据
                    </Text>
                </View>
            </CommonFlatList>
        )


    }

    //瓶盖赠酒item
    renderItem = ({item,index})=>{
        return(
          <CapWineItem
              data={item}
              goodsExtensionStore = {this.goodsExtensionStore}
              capForWineStore = {this.capForWineStore}
              onItemNumConfirm={(text) => {
                  this.capForWineStore.modifyItemNum(index, text, false,item.isZeroFourGoods,()=>{this.setState({})})
              }}
          />
        )
    }

    //底部结算
    renderBottomView = ()=>{
        let {totalItem, totalCount} = this.capForWineStore.selectGoodsCount();
        Log('=====计算结果==',this.capForWineStore.selectGoodsCount())
        return(
            <View style={{width:width,height:Constant.scale(64),position:'absolute',left:0,bottom:0,backgroundColor:'white',flexDirection:'row',borderTopWidth:Constant.sizeDividerNormal,borderColor:Constant.colorDividerDefault}}>
                <View style={{flex:1,justifyContent:'center',alignItems:'flex-end',marginRight:Constant.sizeMarginDefault}}>
                    <Text style={{color:Constant.colorTxtContent}}>共{totalItem}种商品 {totalCount}件</Text>
                </View>
                <TouchableOpacity onPress={()=>this.settlementClick()}>
                <View style={{width:Constant.scale(110),height:'100%',backgroundColor:Constant.colorPrimary,justifyContent:'center', alignItems:'center'}}>
                    <Text style={{fontSize:Constant.fontSizeCaption,color:'white'}}>结算</Text>
                </View>
                </TouchableOpacity>

            </View>
        )
    }

    //结算订单
    settlementClick = ()=>{
        this.capForWineStore.settlementClick().then(
            (json)=>{
                if (json && json.data){
                    this.navigate('ConfirmOrder', {data:json.data,type:1})
                }
            }
        ).catch(e=>{})
    }



};
const styles = StyleSheet.create({
   container: {
        flex: 1,
        backgroundColor:Constant.colorBackgroundDefault
    }
});
