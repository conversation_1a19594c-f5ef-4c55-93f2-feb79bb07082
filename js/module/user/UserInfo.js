import React, { Component } from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import PropType from 'prop-types';
import { ReactNavComponent, Widget } from 'rn-yunxi';
import AppWidget from '../../app-widget';

const { width, height } = Dimensions.get('window');
import { inject, observer } from 'mobx-react/native';

const { Header } = AppWidget;
const { LabelCell, Text } = Widget;

/**
 * 基本信息
 */
@inject((stores) => ({
  user: stores.user,
}))
@observer
export default class InvoiceInfo extends ReactNavComponent {
  //属性声名
  static propTypes = {};
  //默认属性
  static defaultProps = {};

  //构造函数
  constructor(props) {
    super(props);
    this.state = {
      //状态机变量声明
    };
  }

  componentWillMount() {
    this.props.user
      .getDealerUserInfo()
      .then()
      .catch((err) => {});
  }

  componentDidMount() {}

  componentWillUnmount() {}

  renderItem = (key, value, type) => {
    if (type){
      return <View style={{ marginTop: Constant.sizeMarginDefault, flexDirection: 'row', flexWrap: 'wrap', backgroundColor: '#FFFFFF'}}>
        {/* <LabelCell
          titleTextStyle={{ color: Constant.colorTxtContent }}
          title={key}
          underLine={0}
          rightIcon={<View />}
          extra={''}
        /> */}
        {/* <View style={{width: '100%', flexDirection: 'row', justifyContent: 'center'}} >
        <Text
              style={{
              color: Constant.colorTxtTitle,
              fontSize: Constant.fontSizeCaption,
              lineHeight: Constant.scale(30)
            }}>银企直连账号</Text>
        </View> */}
        <View style={{width: '100%', flexDirection: 'row', justifyContent: 'space-between'}} >
          <View style={{flex:1, flexDirection: 'row', justifyContent: 'center'}}>
            <Text
              style={{
              color: Constant.colorTxtTitle,
              fontSize: Constant.fontSizeNormal,
              lineHeight: Constant.scale(25)
            }}>可开单组织</Text>
          </View>
          <View style={{flex:1, flexDirection: 'row', justifyContent: 'center'}}>
            <Text
              style={{
              color: Constant.colorTxtTitle,
              fontSize: Constant.fontSizeNormal,
              lineHeight: Constant.scale(25)
            }}>银企直连账号</Text>
          </View>
        </View>
        {
          value.map(item => {
            return (
                <View key={item.id} style={{width: '100%', flexDirection: 'row', justifyContent: 'space-between', paddingLeft: Constant.scale(15), paddingRight: Constant.scale(15)}}>
                  <View style={{ flexDirection: 'row', justifyContent: 'flex-start'}}>
                    <Text
                      style={{
                        color: Constant.colorTxtTitle,
                        fontSize: Constant.fontSizeSmall,
                        lineHeight: Constant.scale(20)
                      }}>
                      {item.orgName}
                    </Text>
                  </View>
                  <View style={{ flexDirection: 'row', justifyContent: 'flex-end'}}>
                    <Text
                      style={{
                        color: Constant.colorTxtTitle,
                        fontSize: Constant.fontSizeSmall,
                        lineHeight: Constant.scale(20)
                      }}>
                      {item.cbsAccount}
                    </Text>
                  </View>
                </View>
            )
          })
        }
      </View>
    }
    return (
      <LabelCell
        titleTextStyle={{ color: Constant.colorTxtContent }}
        title={key}
        underLine={1}
        rightIcon={<View />}
        extra={
          <Text
            style={{
              color: Constant.colorTxtTitle,
              fontSize: Constant.fontSizeNormal,
            }}>
            {value || '暂无'}
          </Text>
        }
      />
    );
  };

  //渲染
  render() {
    let data = this.props.user.deaderUserInfo;
    console.log('经销商信息', data);
    return (
      <Header style={styles.container} title={'基本信息'}>
        {data ? (
          <View style={{ marginTop: Constant.sizeMarginDefault }}>
            {this.renderItem('客户编号:', data.customerCode)}
            {this.renderItem('客户名称:', data.customerName)}
            {this.renderItem('用戶名:', data.userName)}
            {this.renderItem('预留手机号码:', data.reservePhone)}
            {this.renderItem('银企直连账号:', data.supplierVos, 1)} 
            {/* {this.renderItem('可开单组织:', data.supplierVos, 1)} */}
          </View>
        ) : null}
      </Header>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
