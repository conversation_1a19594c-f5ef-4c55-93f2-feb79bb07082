/**
 * Created by <PERSON> on 2018/3/23.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    Dimensions,
    Text,
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget';
import DeliveryOrderDetailModel from "../../store/DeliveryOrderDetail";
import ToastUtil from "../../util/ToastUtil";
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
import { inject } from 'mobx-react/native';
import { observer } from 'mobx-react/native';

const {LabelCell} = Widget;
const {width, height} = Dimensions.get('window');
const {Header, PriceText, StarRating, TextInputView} = AppWidget;
const IC_SHOP_ICON = require('../img/order/ic_shop_icon.png');
const DEFAULT_ICON = require('../img/img_default.png');
const SELECTED_ICON = require('../img/ic_star_selected.png');
const UNSELECTED_ICON = require('../img/ic_star_defualt.png');
const IC_SPECIAL = require('../img/home-page/ic_special.png')

/**
 * 提货单物流评论
 */
@inject(stores => ({
    user:stores.user,
}))
@observer
export default class DeliveryComment extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        let {params} = this.getNavState();
        this.state = {//状态机变量声明
            text: '',
            starCount: -1,
            isEvaluated: params && params.isEvaluated,
            deliveryOrderId: params && params.deliveryOrderId,
            data: null
        };
    }

    componentWillMount() {
    }

    componentDidMount() {
        if (this.state.isEvaluated) {
            Api.getEvaluationInfo({deliveryOrderId: this.state.deliveryOrderId}).then((result) => {
                if (result && result.data) {
                    this.setState({data: result.data});
                }
            }).catch(() => {

            })
        }
    }

    componentWillUnmount() {
    }

    order = (orderNo, supplierOrgName) => {
        if (!orderNo || !supplierOrgName) {
            return;
        }
        return (

                <View
                    style={{
                        width: width,
                        minHeight: Constant.scale(63),
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        backgroundColor:'white'
                    }}>
                    <View style={{flex: 1, flexDirection: 'row', alignItems: 'center', justifyContent: 'center'}}>
                        <Image
                            style={{
                                width: Constant.scale(17),
                                height: Constant.scale(15),
                                marginLeft: Constant.sizeMarginDefault
                            }}
                            source={IC_SHOP_ICON}/>
                        <View style={{flex: 1}}>
                            <Text
                                style={{
                                    color: Constant.colorTxtTitle,
                                    fontSize: Constant.fontSizeNormal,
                                    marginLeft: Constant.sizeMarginDefault
                                }}>
                                {supplierOrgName}
                            </Text>
                            <Text
                                style={{
                                    color: Constant.colorTxtTitle,
                                    fontSize: Constant.fontSizeNormal,
                                    marginLeft: Constant.sizeMarginDefault,
                                    marginTop: Constant.scale(5)
                                }}>
                                订单号：{orderNo}
                            </Text>
                        </View>
                    </View>

                </View>

        )
    }

    delivery = (itemData) => {
        if (!itemData) {
            return;
        }

        return (
            <View>
                <View
                    style={{
                        width: width,
                        height: Constant.scale(40),
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: Constant.colorDefault
                    }}>
                    <Text
                        style={{
                            flex: 1,
                            color: Constant.colorTxtTitle,
                            fontSize: Constant.fontSizeNormal,
                            marginLeft: Constant.sizeMarginDefault
                        }}>
                        提货单号：{itemData.deliveryOrderNo}
                    </Text>
                </View>
                {this.item(itemData.items[0])}
            </View>
        )
    }

    item = (itemData) => {

        let isDealer = this.props.user.isDealer();
        let {imgUrl, itemName, awards, salesChannel, itemPrice, itemNum, priceType} = itemData;

        return (
            <View style={{
                width: width,
                height: Constant.scale(104),
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: 'white',
                paddingRight:Constant.scale(10)
            }}>
                {
                    imgUrl === '' ?
                        <Image
                            style={{
                                width: Constant.scale(84),
                                height: Constant.scale(84),
                                borderWidth: Constant.sizeDividerNormal,
                                borderColor: Constant.colorDividerDefault,
                                marginLeft: Constant.sizeMarginDefault,
                                backgroundColor: Constant.colorBackgroundDefault
                            }}
                            source={DEFAULT_ICON}/> :
                        <Image
                            style={{
                                width: Constant.scale(84),
                                height: Constant.scale(84),
                                borderWidth: Constant.sizeDividerNormal,
                                borderColor: Constant.colorDividerDefault,
                                marginLeft: Constant.sizeMarginDefault,
                                backgroundColor: Constant.colorBackgroundDefault
                            }}
                            source={{uri: imgUrl}}/>
                }
                <View style={{marginLeft: Constant.sizeMarginDefault,backgroundColor:'white',flex:1}}>
                    <Text numberOfLines={2} style={{fontSize: Constant.fontSizeNormal, color: '#21202F',width:'100%'}}>
                        {itemName}
                    </Text>
                    <Text style={{
                        fontSize: Constant.fontSizeSmall,
                        color: Constant.colorTxtContent,
                        marginTop: Constant.scale(5)
                    }}>
                        {awards} {salesChannel}
                    </Text>

                    <View style={{
                        flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start',
                        marginTop: Constant.scale(10)
                    }}>
                        {
                            isDealer ? <PriceText price={itemPrice} /> : <View style = {{flex:1}}></View>

                        }

                        {
                            priceType === 2  && isDealer?
                                <Image
                                    style={{
                                        marginLeft: Constant.scale(3),
                                        width: Constant.scale(17),
                                        height: Constant.scale(14)
                                    }}
                                    source={IC_SPECIAL}/> : null
                        }

                    </View>

                </View>
                <Text style={{
                    fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert,
                    position:'absolute',bottom:Constant.scale(10),right:Constant.scale(10)
                }}>
                    x{itemNum}
                </Text>
            </View>
        )
    }

    star = () => {
        let starCount = this.state.data && this.state.data.evaluate ? this.state.data.evaluate/2-1 : 0;
        return (
            <LabelCell title="评价等级" rightIcon={<View/>}
                       underLine={2}
                       extra={<View style={{flexDirection: 'row', alignItems: 'center'}}>
                           <StarRating
                               maxStars={5}
                               rating={this.state.isEvaluated ? starCount : this.state.starCount}
                               starSize={Constant.scale(15)}
                               interItemSpacing={Constant.scale(20)}
                               editAble={this.state.isEvaluated ? false : true}
                               selectStar={SELECTED_ICON}
                               unSelectStar={UNSELECTED_ICON}
                               valueChanged={(e) => this.setState({starCount: e})}/>
                       </View>}/>
        )
    };

    commentContent = () => {
        return (
            <View style={{marginTop: Constant.scale(10), width: width, minHeight: Constant.scale(190)}}>
                {this.star()}
                {this.state.isEvaluated ?
                    <View style={{backgroundColor: Constant.colorDefault}}>
                        <Text style={{ color: Constant.colorTxtContent, fontSize: Constant.fontSizeNormal,margin:Constant.sizeMarginDefault}}>
                            评价内容: {this.state.data &&  this.state.data.evaluateDetail ? this.state.data.evaluateDetail : '暂无'}
                        </Text>
                    </View> :
                    <TextInputView style={{width: width, height: Constant.scale(150), flex: 1}}
                                   placeholder={'请输入您要评价的内容'}
                                   remarkText={this.state.text}
                                   minHeight={150}
                                   onChangeText={(text) => {
                                       this.setState({text: text})
                                   }}/>
                }
            </View>
        )
    };

    commentBtn = (itemData) => {
        let deliveryOrderId = itemData.deliveryOrderId;
        return (
            <View style={{
                width: width,
                height: Constant.scale(40),
                marginTop: Constant.scale(15),
                justifyContent: 'center',
                alignItems: 'center'
            }}>
                <TouchableOpacity style={{
                    width: Constant.scale(351),
                    height: Constant.scale(40),
                    backgroundColor: '#df0522',
                    borderRadius: Constant.scale(4),
                    justifyContent: 'center',
                    alignItems: 'center'
                }}
                                  onPress={() => {
                                      let evaluate = 0;
                                      switch (this.state.starCount) {
                                          case 0:
                                              evaluate = 2;
                                              break;
                                          case 1:
                                              evaluate = 4;
                                              break;
                                          case 2:
                                              evaluate = 6;
                                              break;
                                          case 3:
                                              evaluate = 8;
                                              break;
                                          case 4:
                                              evaluate = 10;
                                              break;
                                          default:
                                              break;
                                      }

                                      let params = {};
                                      params.deliveryOrderId = deliveryOrderId;
                                      params.evaluate = evaluate;
                                      params.evaluateDetail = this.state.text;

                                      if (evaluate === 0) {
                                          ToastUtil.show('请选择评价等级！');
                                          return;
                                      }

                                      let commentDelivery = new DeliveryOrderDetailModel();
                                      commentDelivery.evaluation(params).then(() => {
                                          ToastUtil.show('评价成功!')
                                          this.navigate('OrderList', {type: 1})
                                      }).catch(err => {
                                      })
                                  }}>
                    <Text style={{fontSize: Constant.fontSizeBig, color: 'white'}}>发表评价</Text>
                </TouchableOpacity>
            </View>
        )
    }

    //渲染UI
    render() {
        let {params} = this.getNavState();
        let orderNo = params.orderNo;
        let supplierOrgName = params.supplierOrgName;

        if (!(params && params.itemData)) {
            return;
        }

        return (
            <Header style={styles.container} title={'评价物流'}>
                <KeyboardAwareScrollView>
                {this.order(orderNo, supplierOrgName)}
                <View style={{width: width, height: Constant.sizeDividerNormal, backgroundColor: Constant.colorDivider}}/>
                {this.delivery(params.itemData)}
                {this.commentContent()}
                {this.state.isEvaluated ? null : this.commentBtn(params.itemData)}
                </KeyboardAwareScrollView>
            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        color:Constant.colorBackgroundDefault,
    }
});
