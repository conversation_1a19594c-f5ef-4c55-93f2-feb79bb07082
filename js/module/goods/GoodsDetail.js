import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    TouchableWithoutFeedback,
    Dimensions,
    StatusBar,
    ScrollView,
    Platform,
    InteractionManager,
    Switch,
    TextInput,
} from 'react-native';
import {SafeAreaView} from'react-navigation';
import PropType from 'prop-types';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget';
import {toJS} from 'mobx';
import {inject, observer} from 'mobx-react/native';
import GoodsDetailModel from '../../store/GoodsDetail';
import SkuPanelModel from '../../store/SkuPanel';
import Carousel from 'react-native-looped-carousel';
import ValidatorUtil from '../../util/ValidatorUtil';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import HtmlReplaceUtil from '../../util/HtmlReplaceUtil'
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
import ToastUtil from "../../util/ToastUtil";

const {width, height} = Dimensions.get('window');
const {PriceText, Image, SelectPop, Header, ChannelPanel} = AppWidget;
const {Text, LabelCell, AutoHeightWebView} = Widget;
const IC_GROW = require('../img/good-detail/ic_grow.png');
const IC_RED_GROW = require('../img/good-detail/ic_red_grow.png')
const IC_BACK = require('../img/good-detail/ic_back.png');
const IC_SHOP_CART = require('../img/good-detail/ic_shop_cart.png');
const IC_NEXT = require('../img/next.png');
/**
 * 商品详情
 */
@inject(stores => ({
    shopCart: stores.shopCart,
    user: stores.user,
}))
@observer
export default class GoodsDetail extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
            isInputFocus: false,//数量输入框是否焦点
            tab: 0,
        };
        this.goodsDetail = new GoodsDetailModel();
        this.skuPanel = new SkuPanelModel();
    }

    componentWillMount() {
        // this.setParams({itemId: '100000771'})
    }

    componentDidMount() {
        this.loadData();
    }

    componentWillUnmount() {
    }

    /**
     * 初始化数据
     */
    loadData = () => {
        this.props.user.obtainChannelList().then(data => {

        }).catch();
        let {params} = this.getNavState();
        let param = {};
        if (params.itemId) {
            param.itemId = params.itemId
        }
        if (params.defaultOrgId) {
            param.orgId = params.defaultOrgId
        }
        // params.itemId = '100000771';
        this.goodsDetail.getGoodsDetail(param).then(data => {

            if (this.goodsDetail.data) {
                //缓存 默认的priceList对象
                this.skuPanel.setPriceListData(this.goodsDetail.data.defaultOrgId, {
                    advanceDeposit: this.goodsDetail.data.advanceDeposit,
                    discount: this.goodsDetail.data.discount,
                    priceList: this.goodsDetail.data.priceList,
                });
                //设置其它选择项
                this.skuPanel.setEnableZero(false);
                this.skuPanel.setSelectOrgId(this.goodsDetail.data.defaultOrgId);
                this.skuPanel.setSelectChannelId(this.goodsDetail.data.defaultSalesChannelCode, this.goodsDetail.data.defaultSubSalesChannelCode);
                this.skuPanel.setSelectAwardId(this.goodsDetail.data.defaultAwardsId);
                this.skuPanel.setSelectPlateFlag(this.goodsDetail.data.isTakePlate ? 1 : 0);
                this.skuPanel.setSuggestNumber(this.goodsDetail.data.suggestNumber);
                this.skuPanel.setMandatoryNumber(this.goodsDetail.data.mandatoryNumber);
                this.skuPanel.setInitCallback(() => {
                    // Log('setInitCallback')
                    this.skuPanel.setPurchaseCount(this.skuPanel.purchaseQuantity.multiple == 0 ? 1 : this.skuPanel.purchaseQuantity.multiple, false,this.goodsDetail.data.isZeroFourGoods);
                    this.skuPanel.obtainDiscount().then().catch(e => {
                    })

                })
                this.skuPanel.setItemId(params.itemId, true);

            }

        }).catch();

        // 获取 托板承载规格
        this.skuPanel.getPalletStruList();

    };

    /**
     * 轮播图
     * */
    renderBanner = (data) => {
        return (
            data && data.length<1 ?
                <Carousel
                delay={2000}
                style={styles.bannerImage}
                autoplay
                bullets
                chosenBulletStyle={{margin: 0, marginRight: 5, width: 5, height: 5,}}
                bulletStyle={{margin: 0, marginRight: 5, width: 5, height: 5,}}>
                {
                    data.map((item, index) => {
                        // Log(Constant.ossImgUrl(item.imgUrl,width));
                        Log('======item.imgUrl',item.imgUrl)
                        return (
                            <TouchableWithoutFeedback key={index}>
                                <Image
                                    style={{flex:1}}
                                    resizeMode={'contain'}
                                    source={{ uri: item.imgUrl ? Constant.ossImgUrl(item.imgUrl, width) : '' }}

                                />
                            </TouchableWithoutFeedback>
                        );
                    })
                }
            </Carousel> : <Image
                style={styles.bannerImage}
                resizeMode={'contain'}
                source={{uri:data && data.length>0 && data.length<2 ?data[0].imgUrl : '' }}
            />
        );
    }

    /**
     * 打开渠道面板
     */
    selectChannel() {

        let data = toJS(this.props.user.channelList);
        Widget.Popup.show(<ChannelPanel
                onConfirm={(data) => {
                    Log('onConfirm', data);
                    let {channelId, channelName} = data;
                    this.skuPanel.setSelectChannelId(channelId.salesChannelCode, channelId.subSalesChannelCode);
                    this.skuPanel.setSelectChancelName(channelName.salesChannelName, channelName.subSalesChannelName);
                    this.skuPanel.obtainDiscount().then().catch(e => {})
                }}
                data={data}
                showHeader={true}
                salesChannelCode={this.skuPanel.selectChannelId.salesChannelCode}
                subSalesChannelCode={this.skuPanel.selectChannelId.subSalesChannelCode}/>,
            {
                animationType: 'slide-up', backgroundColor: '#00000000',
                onMaskClose: () => {
                    Widget.Popup.hide()
                }
            })
    }

    /**
     * 选择提货组织,奖项
     * */
    selectItem = (index) => {
        // 0 提货组织  1 商品奖项  2采购渠道 3托板承载规格
        let title = '';
        let data = [];
        let selectId;
        let selectKey = '';
        let labelName = '';
        if (index == 0) {
            title = '选择提货组织';
            data = this.skuPanel.orgList;
            selectId = this.skuPanel.selectOrgId;
            selectKey = 'orgId';
            labelName = 'orgName';
        } else if (index == 1) {
            title = '选择奖项';
            data = this.skuPanel.awardsMap.get(this.skuPanel.selectOrgId);
            selectId = this.skuPanel.selectAwardId;
            selectKey = 'id';
            labelName = 'name';
        } else if (index == 3){
            title = '选择托板承载规格';
            data = this.skuPanel.palletStruList;
            selectId = this.skuPanel.batchSaleNum;
            selectKey = 'boxnum';
            labelName = 'bearing'; // 取哪个字段的值进行显示文案
        } else {
        }
        // Log(selectId);
        Widget.Popup.show(<SelectPop
                popTitle={title}
                listData={data}
                labelName={labelName}
                selectId={selectId}
                selectKey={selectKey}
                selectCallBack={(selectData) => {
                    //TODO
                    Widget.Popup.hide();
                    InteractionManager.runAfterInteractions(() => {
                        if (index == 0) {
                            Log('sel == ', selectData);
                            this.skuPanel.setSelectOrgId(selectData.orgId);
                            this.skuPanel.obtainPriceListByOrgId(selectData.orgId).then((data) => {
                                this.goodsDetail.setPriceListData(this.skuPanel.priceListMap.get(selectData.orgId))
                                this.skuPanel.obtainDiscount().then().catch(e => {})
                            }).catch();

                        } else if (index == 1) {
                            // Log('测试奖项',toJS(selectData))
                            this.skuPanel.setSelectAwardId(selectData.id);
                            this.skuPanel.obtainDiscount().then().catch(e => {});
                            setTimeout(()=>{this.setState({})},10);
                        } else if (index == 3) {
                            this.skuPanel.setBatchSaleNum(selectData.boxnum);

                            // 设置文案
                            this.skuPanel.setBearing(selectData.bearing);
                        } else {
                            this.skuPanel.setSelectChannelId(selectData.salesChannelCode, selectData.subSalesChannelCode);

                        }
                    })
                }}

            />,
            {
                animationType: 'slide-up', backgroundColor: '#00000000',
                onMaskClose: () => {
                    Widget.Popup.hide()
                }
            })
    }
    /**
     * 加入购物车
     */
    addShopCart = () => {

        let awardsList = this.skuPanel.awardsMap.get(this.skuPanel.selectOrgId)

        const {selectPlateId, batchSaleNum} = this.skuPanel;
        if (selectPlateId == 1 && (!batchSaleNum || batchSaleNum == '0')) {
            ToastUtil.show('请选择托板承载规格');
            return;
        }

        if ( this.skuPanel.purchaseCount && parseFloat(this.skuPanel.purchaseCount)>0 ){
            this.props.shopCart.addShopCart(
                this.skuPanel.itemId,
                this.skuPanel.purchaseCount,
                this.goodsDetail.priceItem ? this.goodsDetail.priceItem.priceType : null,
                this.skuPanel.selectOrgId,
                this.skuPanel.selectAwardId,
                this.skuPanel.selectChannelId.salesChannelCode,
                this.skuPanel.selectChannelId.subSalesChannelCode,
                this.skuPanel.selectPlateId,
                batchSaleNum || '0', // 该字段表示“托板承载规格”，不再是“批量销售单位数量”
                awardsList
            );
        }else {
            ToastUtil.show('请输入正确商品数量');
        }



    }

    /**
     * 立即购买
     * */
    quickBuy() {
        //this.skuPanel.purchaseCount
        let awardsList = this.skuPanel.awardsMap.get(this.skuPanel.selectOrgId)

        if (!(awardsList && awardsList.length>0)){
            return ToastUtil.show('请选择奖项');
        }

        if (this.goodsDetail.data.priceList.length !== 0) {
            let priceData = this.goodsDetail.data.priceList[0];
            if (priceData.priceType === 2) {
                if (priceData.stock === 0) {
                    ToastUtil.show('该商品库存不足!');
                    return;
                } else if (this.skuPanel.purchaseCount > priceData.stock && priceData.stock !=null) {
                    ToastUtil.show('商品库存不足，请修改采购数量!');
                    return;
                }
            }
        }

        const {selectPlateId, batchSaleNum} = this.skuPanel;
        if (selectPlateId == 1 && (!batchSaleNum || batchSaleNum == '0')) {
            ToastUtil.show('请选择托板承载规格');
            return;
        }

        let tmp = this.skuPanel.getChoose();
        let params = {
            itemId: tmp.itemId,
            orgId: tmp.orgId,
            awardsId: tmp.awardsId,
            salesChannelCode: tmp.channelId.salesChannelCode,
            subSalesChannelCode: tmp.channelId.subSalesChannelCode,
            withPlateFlag: tmp.withPlateFlag,
            itemNum: tmp.purchaseCount,
            priceType: this.goodsDetail.priceItem ? this.goodsDetail.priceItem.priceType : null,
            batchSaleNum: tmp.batchSaleNum || '0', // 托板承载规格 入参字段
        };


        if(tmp.purchaseCount && parseFloat(tmp.purchaseCount)>0){
            this.props.shopCart.quickBuy(params, 1, (data) => {

                if (data) {
                    this.navigate('ConfirmOrder', {data: data, type: 1});
                }
            }).then().catch((err => {
                Log('err = ', err);
            }))
        }else {
            ToastUtil.show('商品数量要大于0');
        }



    }

    /**
     * 显示价格
     * */
    showPriceList = () => {
        let isRMB = this.props.user.isRMB();
        let priceType = this.goodsDetail.priceItem && this.goodsDetail.priceItem.priceType;
        let priceNature = this.goodsDetail.priceItem && this.goodsDetail.priceItem.priceNature;
        let selectId = isRMB ? priceType : priceNature;
        let selectKey = isRMB ? 'priceType':'priceNature';

        Widget.Popup.show(<SelectPop
                listData={this.goodsDetail.priceListData.priceList}
                selectId={selectId}
                popTitle={'选择价格列表'}
                labelName={'priceName'}
                selectKey={selectKey}
                selectCallBack={(selectData) => {
                    //TODO
                    Widget.Popup.hide();
                    InteractionManager.runAfterInteractions(() => {
                        this.goodsDetail.setSelectPriceItem(selectData)

                    })
                }}

            />,
            {
                animationType: 'slide-up', backgroundColor: '#00000000',
                onMaskClose: () => {
                    Widget.Popup.hide()
                }
            })
    }

    //加入或者取消常购
    handleOftenGoods = () => {
        let tmp = this.skuPanel.getChoose();
        let params = {
            itemId: tmp.itemId,   //商品Id
            defaultOrgId: tmp.orgId, //提货组织ID
            awardsId: tmp.awardsId,   //奖项
            defaultSalesChannelCode: tmp.channelId.salesChannelCode,  //销售渠道编码
            defaultSubSalesChannelCode: tmp.channelId.subSalesChannelCode,  //销售子渠道编码
            isTakePlate: tmp.withPlateFlag, //是否带板(0-否，1-是)
            sign: (this.goodsDetail.sign == 1 ? 0 : 1),  //常订标志（0为取消加入常订商品，1为加入常订商品）

        }
        // Log('=====================加入常订参数',toJS(params))

        if (this.goodsDetail.data) {
            this.goodsDetail.handleOftenGoods(params,(oftenGoodsCount)=>{
                Log('常订列表个数',toJS(oftenGoodsCount))
                if (oftenGoodsCount>30){
                    ToastUtil.show('常订列表最多显示30个商品，超过30个商品，旧的将会被替换');
                }

            }).then().catch()
        }

    }

    renderWebView(str) {
        let htmlStr = HtmlReplaceUtil.addHead(str ? str : '');
        return (
            <AutoHeightWebView
                // style={{flex:1}}
                source={{html: htmlStr}}

            />
        )
    }

    //渲染
    render() {
        let data = this.goodsDetail.data;
        let sign = this.goodsDetail.sign;
        let purchaseQuantity = this.skuPanel.purchaseQuantity;
        let isDealer = this.props.user && this.props.user.isDealer()
        Log('render>>isDealer', isDealer)

        let priceListData = this.goodsDetail.priceListData;
        let price = this.goodsDetail.priceItem ? this.goodsDetail.priceItem.price : 0;
        let specialPrice = this.goodsDetail.priceItem ? this.goodsDetail.priceItem.priceType == 2 ? this.goodsDetail.priceItem.price : 0 : 0
        let stock = '暂无库存';
        if (this.goodsDetail.priceItem  && this.goodsDetail.priceItem.stock || (this.goodsDetail.priceItem && this.goodsDetail.priceItem.stock+'' == '0')){
                stock =  this.goodsDetail.priceItem && this.goodsDetail.priceItem.stock+'';
        }else {
            stock = '库存充足';

        }
        let priceText = this.goodsDetail.priceItem ? this.goodsDetail.priceItem.priceTypeName : 0;

        let itemCount = this.state.isInputFocus ? this.skuPanel.tmpPurchaseCount + '' : this.skuPanel.purchaseCount + '';
        let purchaseQuantityTitle = "采购数量";
        if (purchaseQuantity.multiple > 0) {
            purchaseQuantityTitle = "采购数量" + '(' + purchaseQuantity.multiple + '倍数)'
        }
        Log('render>>purchaseCount', itemCount)
        return (
            data ?
                <SafeAreaView style={{flex:1,backgroundColor:'white'}}>
                <KeyboardAwareScrollView
                    contentContainerStyle={{flex: 1}}
                    scrollEnabled={false}
                    keyboardShouldPersistTaps={'always'}
                    extraHeight={height < 667 ? Constant.scale(18) : Constant.scale(60)}
                >

                    <ScrollView
                        style={{flex: 1}}
                        overScrollEffect={true}
                        scrollEventThrottle={1}
                    >
                        {this.renderBanner(data.imgList || [])}
                        <View style={styles.bgViewStyle}>
                            <View style={{backgroundColor: 'white'}}>
                                <Text style={{
                                    margin: Constant.sizeMarginDefault,
                                    fontSize: Constant.fontSizeBig,
                                    color: Constant.colorTxtTitle
                                }}>
                                    {data.name}
                                </Text>
                            </View>
                            <TouchableOpacity onPress={() => {
                                this.showPriceList();
                            }} style={{flexDirection: 'row', alignItems: 'center', backgroundColor: 'white'}}>
                                <View style={{flex: 1, backgroundColor: 'white', padding: Constant.sizeMarginDefault}}>

                                    <View  style={{flexDirection: 'row', alignItems: 'flex-end',marginBottom:Constant.scale(5)}}>


                                        <PriceText price={price}/>


                                        {
                                            this.goodsDetail.priceItem && this.goodsDetail.priceItem.priceType == 2 ?
                                                <Text style={{color: Constant.colorTxtContent, fontSize: Constant.fontSizeNormal,marginLeft:Constant.scale(10)}}>
                                                    库存:{stock}
                                                </Text>
                                                : null
                                        }

                                        {(!isDealer)||(this.goodsDetail.priceItem && (this.goodsDetail.priceItem.priceType == 3 ||this.goodsDetail.priceItem.priceType == 2))?
                                            null :
                                            <Text style={{
                                                color: Constant.colorTxtAlert,
                                                fontSize: Constant.fontSizeSmall,
                                                marginLeft: 2 * Constant.sizeMarginDefault
                                            }}>
                                                折 <PriceText size={-1} price={this.skuPanel.discount}/>
                                            </Text>
                                        }


                                        {
                                            (!isDealer)? null:
                                                <Text style={{
                                                    color: Constant.colorTxtAlert,
                                                    fontSize: Constant.fontSizeSmall,
                                                    marginLeft: 2 * Constant.sizeMarginDefault
                                                }}>
                                                    预付 <PriceText size={-1} price={data.advanceDeposit}/>
                                                </Text>
                                        }

                                    </View>


                                    {this.goodsDetail.data && this.goodsDetail.data.priceList && this.goodsDetail.data.priceList.length > 1 ?
                                            this.renderPriceView(this.goodsDetail.data.priceList)
                                        : null}

                                </View>
                                <Image source={IC_NEXT} style={{marginRight: Constant.sizeMarginDefault}}/>
                            </TouchableOpacity>

                            {/*参数选择*/}
                            <View style={[styles.bgViewStyle, {marginTop: Constant.sizeMarginDefault}]}>
                                <LabelCell
                                    title="提货组织"
                                    extra={<Text style={{
                                        color: Constant.colorTxtAlert,
                                        fontSize: Constant.fontSizeSmall
                                    }}>{this.skuPanel.selectOrgName}</Text>}
                                    underLine={1}
                                    onClick={() => {
                                        this.selectItem(0);
                                    }}
                                />

                                <LabelCell
                                    title="商品奖项"
                                    extra={<Text style={{
                                        color: Constant.colorTxtAlert,
                                        fontSize: Constant.fontSizeSmall
                                    }}>{this.skuPanel.selectAwardName(this.skuPanel.awardsMap)}</Text>}
                                    underLine={1}
                                    onClick={() => {
                                        this.selectItem(1);
                                    }}
                                />

                                <LabelCell
                                    title="采购渠道"
                                    extra={<Text
                                        style={{
                                            color: Constant.colorTxtAlert,
                                            fontSize: Constant.fontSizeSmall
                                        }}>
                                        {this.props.user.getChancelFullName(this.skuPanel.selectChannelId.salesChannelCode, this.skuPanel.selectChannelId.subSalesChannelCode)}
                                    </Text>}
                                    underLine={1}
                                    onClick={() => {
                                        this.selectChannel();
                                        // this.selectItem(2);
                                    }}
                                />

                                <LabelCell
                                    title={purchaseQuantityTitle}
                                    extra={<TextInput
                                        onFocus={() => {
                                            this.setState({isInputFocus: true}, () => {
                                                this.skuPanel.setPurchaseCount(this.skuPanel.purchaseCount, true,this.goodsDetail.data.isZeroFourGoods)
                                            });
                                        }}
                                        onBlur={() => {
                                            this.setState({isInputFocus: false}, () => {
                                                this.skuPanel.setPurchaseCount(this.skuPanel.tmpPurchaseCount, false,this.goodsDetail.data.isZeroFourGoods)
                                            });
                                        }}
                                        style={styles.textInputStyle}
                                        underlineColorAndroid={'transparent'}
                                        maxLength={8}
                                        selectionColor={Constant.colorPrimary}
                                        value={itemCount}
                                        onChangeText={
                                            (text) => {
                                                  this.skuPanel.setPurchaseCount(text, true,this.goodsDetail.data.isZeroFourGoods);
                                            }
                                        }
                                    />}
                                    rightIcon={<View/>}
                                    underLine={1}
                                    onClick={() => {

                                    }}
                                />
                                <LabelCell
                                    title="是否带板"
                                    extra={
                                        <Switch
                                            disabled={( this.props.user && this.props.user.companyInfo && this.props.user.companyInfo.isRibband == 1) ?false:true }
                                            value={this.skuPanel.selectPlateValue}
                                            onValueChange={(value) => {
                                                this.skuPanel.setSelectPlateFlag(value ? 1 : 0);
                                                if (!value) {
                                                    // 如果切换为不带板的话，则重置托板字段
                                                    this.skuPanel.setBearing('');
                                                    this.skuPanel.setBatchSaleNum('0');
                                                }
                                            }}
                                        />
                                    }
                                    underLine={2}
                                    rightIcon={<View/>}
                                    onClick={() => {
                                        ( this.props.user && this.props.user.companyInfo && this.props.user.companyInfo.isRibband == 1)?null:ToastUtil.show('该经销商暂不支持带板')
                                    }}
                                />

                                {/* 选择了带板后，才显示托板选择 */}
                                {this.skuPanel.selectPlateValue == 1 && (
                                    <LabelCell
                                        title="托板承载规格"
                                        extra={<Text
                                            style={{
                                                color: Constant.colorTxtAlert,
                                                fontSize: Constant.fontSizeSmall
                                            }}>
                                            {this.skuPanel.bearing || '请选择'}
                                        </Text>}
                                        underLine={1}
                                        onClick={() => {
                                            this.selectItem(3);
                                        }}
                                    />
                                )}
                            </View>
                        </View>
                        {this.renderWebView(data.appStatement)}
                    </ScrollView>

                    {/*</ScrollableTabView>*/}
                    <View
                        style={[styles.headerView, {
                            paddingTop: Platform.OS === 'ios' ? Constant.ISIPHONEX ? 45 : 30 : 20+StatusBar.currentHeight,
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between'
                        }]}>
                        <TouchableWithoutFeedback
                            onPress={() => {
                                this.goBack();
                            }}>
                            <View style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                width: Constant.scale(40) + Constant.sizeMarginDefault,
                                height: Constant.scale(40)
                            }}>
                                <Image resizeMode={'contain'} style={{
                                    width: Constant.scale(30),
                                    height: Constant.scale(30),
                                    marginLeft: Constant.sizeMarginDefault
                                }} source={IC_BACK}/>
                            </View>

                        </TouchableWithoutFeedback>

                        <TouchableWithoutFeedback
                            onPress={() => {
                                this.navigate('ShopCartPage', {showBack: true})
                            }}>
                            <View
                                style={{
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    width: Constant.scale(40) + Constant.sizeMarginDefault,
                                    height: Constant.scale(40)
                                }}>
                                <Image
                                    resizeMode={'contain'}
                                    style={{
                                        width: Constant.scale(30),
                                        height: Constant.scale(30),
                                        marginRight: Constant.sizeMarginDefault
                                    }}
                                    source={IC_SHOP_CART}/>
                            </View>

                        </TouchableWithoutFeedback>
                    </View>

                    <View
                        style={{
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignItems: 'center',
                            height: Constant.scale(49),
                            width: width
                        }}>
                        <TouchableOpacity onPress={() => this.handleOftenGoods()} style={{flex: 1}}>
                            <View
                                style={{
                                    flex: 1,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    backgroundColor: Constant.colorDefault
                                }}>
                                <Image style={{width: Constant.scale(16), height: Constant.scale(16)}}
                                       source={sign == 1 ? IC_RED_GROW : IC_GROW}/>
                                <Text
                                    style={{
                                        fontSize: Constant.fontSizeXSmall,
                                        color: Constant.colorTxtContent,
                                        marginTop: Constant.scale(3)
                                    }}>{sign == 1 ? '已加入常订' : '加入常订'}</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => {
                                this.addShopCart();
                            }}
                            style={{flex: 2}}
                        >
                            <View
                                style={{
                                    flex: 1,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    backgroundColor: '#FFE9EC'
                                }}>
                                <Text style={{
                                    fontSize: Constant.fontSizeBig,
                                    color: Constant.colorTxtPrimary
                                }}>加入购物车</Text>
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => {
                                this.quickBuy();
                            }}
                            style={{flex: 2}}
                        >
                            <View
                                style={{
                                    flex: 1,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    backgroundColor: '#DF0522'
                                }}>
                                <Text style={{fontSize: Constant.fontSizeBig, color: Constant.colorDefault}}>立即购买</Text>
                            </View>
                        </TouchableOpacity>
                    </View>
                    {
                        Platform.OS=='android'?<StatusBar backgroundColor={'#00000000'} translucent={true}  />:null
                    }
                </KeyboardAwareScrollView>
                </SafeAreaView>
                : <Header showBackAction={true}>
                    <View style={{ marginTop: Constant.scale(250), alignItems: 'center' }}>
                        <Text style={{ textAlign: 'center', fontSize: Constant.fontSizeNormal, color: Constant.colorTxtContent }}>数据加载中...</Text>
                    </View>
                </Header>
        );
    }

    //价格UI
    renderPriceView = (priceList)=>{
        let data = this.goodsDetail.data;
        let sign = this.goodsDetail.sign;
        let purchaseQuantity = this.skuPanel.purchaseQuantity;
        let priceListData = this.goodsDetail.priceListData;
        let price = this.goodsDetail.priceItem ? this.goodsDetail.priceItem.price : 0;
        let specialPrice = this.goodsDetail.priceItem ? this.goodsDetail.priceItem.priceType == 2 ? this.goodsDetail.priceItem.price : 0 : 0
        let stock = this.goodsDetail.priceItem ? this.goodsDetail.priceItem.stock+'' : '货源充足';
        let priceText = this.goodsDetail.priceItem ? this.goodsDetail.priceItem.priceTypeName : '暂无';

        for(let item of priceList) {
            if (item.priceType != this.goodsDetail.priceItem.priceType) {
                if (item.priceType == 1) {
                    return (
                        <View style={{flexDirection: 'row', alignItems: 'flex-end',marginBottom:Constant.scale(5)}}>
                            <Text>标准价:<PriceText size={5} price={item.price}/></Text>
                            <Text style={{color: Constant.colorTxtAlert, fontSize: Constant.fontSizeSmall, marginLeft: 2 * Constant.sizeMarginDefault}}>
                                折 <PriceText size={-1} price={this.skuPanel.discount}/>
                            </Text>
                            <Text style={{color: Constant.colorTxtAlert, fontSize: Constant.fontSizeSmall, marginLeft: 2 * Constant.sizeMarginDefault}}>
                                预付 <PriceText size={-1} price={data.advanceDeposit}/>
                            </Text>
                        </View>
                    );
                } else if (item.priceType == 2) {
                    return (
                        <View style={{flexDirection: 'row', alignItems: 'center',marginBottom:Constant.scale(5)}}>
                            <Text>特殊价:<PriceText size={5} price={item.price}/></Text>
                            <Text style={{color: Constant.colorTxtContent, fontSize: Constant.fontSizeNormal,marginLeft:Constant.scale(10)}}>
                                库存:{item.stock||0}
                            </Text>

                            <Text style={{color: Constant.colorTxtAlert, fontSize: Constant.fontSizeSmall, marginLeft: 2 * Constant.sizeMarginDefault}}>
                                预付 <PriceText size={-1} price={data.advanceDeposit}/>
                            </Text>
                        </View>
                    )
                } else if (item.priceType == 3) {
                    return (
                        <View style={{flexDirection: 'row', alignItems: 'center',marginBottom:Constant.scale(5)}}>
                            <Text>底价:<PriceText size={5} price={item.price}/></Text>
                            <Text style={{color: Constant.colorTxtAlert, fontSize: Constant.fontSizeSmall, marginLeft: 2 * Constant.sizeMarginDefault}}>
                                预付 <PriceText size={-1} price={data.advanceDeposit}/>
                            </Text>
                        </View>
                    )

                }
            }
        }
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    bannerImage: {
        // flex: 1,
        width: width,
        height: Constant.scale(275),
    },
    bgViewStyle: {
        width: width,
        backgroundColor: Constant.colorBackgroundDefault
    },
    textInputStyle: {
        paddingTop: 0,
        paddingBottom: 0,
        paddingLeft: Constant.scale(10),
        borderWidth: Constant.sizeDividerNormal,
        borderColor: Constant.colorDivider,
        width: Constant.scale(80),
        height: 28,
        fontSize: Constant.fontSizeSmall
    },
    headerView: {
        width: width,
        paddingBottom: 10,
        height: Constant.sizeHeader,
        position: 'absolute',
    },
});
