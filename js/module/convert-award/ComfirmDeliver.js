import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Platform,
    TextInput,
    Alert,
    StatusBar
} from 'react-native';
import PropType from 'prop-types';
import { ReactNavComponent, Widget } from 'rn-yunxi';
import AppWidget from '../../app-widget';
const { CommonModal, Verifycode } = AppWidget;
const { Text } = Widget;
import ToastUtil from '../../util/ToastUtil';

/**
 * 确认送达
 */
export default class ComfirmDeliver extends Component {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {
        };

    }

    show(data) {
        this.data = data;
        this.modal.show();
    }

    onInputCompleted = (value) => {
        this.comfirm(value);
    }

    comfirm = (value) => {
        const data = this.data;
        console.log('>>>>>>', value);
        if (value == '' || value == undefined || value == null) {
            Alert.alert('', '验证码不能为空');
            return;
        }
        this.props.verificationList.comfirmDeliver(data.couponTransferId, data.orderId, value).then(() => {
            setTimeout(() => {
                this.modal.hide();
                ToastUtil.show('确认送达成功');
            }, 400);
        }).catch();
        setTimeout(() => {
            this.verifycode && this.verifycode.reset();
        }, 300);
    }

    //渲染
    render() {

        return (
            <CommonModal ref={(ref) => this.modal = ref}
                containerStyle={{ justifyContent: 'center', alignItems: 'center' }}>
                <View style={styles.container}>
                    <Text style={{
                        marginTop: Constant.scale(15),
                        fontSize: Constant.fontSizeBig,
                        color: Constant.colorTxtDefault
                    }}>请输入验证码</Text>

                    <Text style={{
                        marginTop: Constant.scale(20),
                        fontSize: Constant.fontSizeNormal,
                        color: Constant.colorTxtContent
                    }}>请跟客户索取收货验证码,</Text>
                    <Text style={{
                        fontSize: Constant.fontSizeNormal,
                        color: Constant.colorTxtContent
                    }}>客户操作确认收货同样可以确认送达.</Text>

                    <View style={{ flex: 1, width: '100%', justifyContent: 'center', alignItems: 'center' }}>
                        <Verifycode
                            codeViewWidth={Constant.scale(240 / 6)}
                            verifyCodeLength={6}
                            ref={ref => (this.verifycode = ref)}
                            onInputCompleted={this.onInputCompleted}
                        />
                    </View>

                    <View style={{
                        width: '100%',
                        height: Constant.sizeDividerNormal,
                        marginTop: Constant.scale(20),
                        backgroundColor: 'rgba(65,65,65,0.4)'
                    }} />

                    <View style={{ width: '100%', height: Constant.scale(40), flexDirection: 'row', justifyContent: 'space-between' }}>
                        <TouchableOpacity style={{
                            width: '49.99%',
                            height: Constant.scale(45),
                            justifyContent: 'center',
                            alignItems: 'center'
                        }} onPress={() => {
                            this.modal.hide();
                        }}>
                            <Text style={{ fontSize: 18, color: '#333333' }}>取消</Text>
                        </TouchableOpacity>

                        <View style={{
                            width: Constant.sizeDividerNormal,
                            height: '100%',
                            backgroundColor: 'rgba(65,65,65,0.4)'
                        }}></View>

                        <TouchableOpacity style={{
                            width: '49.5%',
                            height: Constant.scale(45),
                            justifyContent: 'center',
                            alignItems: 'center'
                        }} onPress={() => { this.comfirm(this.verifycode.state.text); }}>
                            <Text style={{ fontSize: 18, color: '#FA3D4F' }}>确定</Text>
                        </TouchableOpacity>
                    </View>

                </View>
                {
                    Platform.OS=='android'?<StatusBar backgroundColor={'#000000'} translucent={true}  />:null
                }
            </CommonModal>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        width: Constant.scale(280),
        height: Constant.scale(211.5),
        backgroundColor: Constant.colorDefault,
        alignItems: 'center',
        borderRadius: Constant.scale(5)
    }
});