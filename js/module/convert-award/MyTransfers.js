import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    ScrollView,
    InteractionManager,
    Alert,
    Platform
} from 'react-native';
import PropType from 'prop-types';
import { ReactNavComponent, Widget } from 'rn-yunxi';
const { width, height } = Dimensions.get('window');
import AppWidget from '../../app-widget';
const { Header, QuantitySelector, Image, SelectPop, DefaultTabBar, Button } = AppWidget;
const { Text, CommonFlatList } = Widget;
import { inject, observer } from 'mobx-react/native';
import ToastUtil from '../../util/ToastUtil'
import { toJS } from 'mobx';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import CouponTransfer from '../../store/CouponTransfer';
import clone from 'lodash/cloneDeep';

function getDay(day) {
    var today = new Date();

    var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;

    today.setTime(targetday_milliseconds); //注意，这行是关键代码  

    var tYear = today.getFullYear();
    var tMonth = today.getMonth();
    var tDate = today.getDate();
    tMonth = doHandleMonth(tMonth + 1);
    tDate = doHandleMonth(tDate);
    return tYear + "-" + tMonth + "-" + tDate;
}

function doHandleMonth(month) {
    var m = month;
    if (month.toString().length == 1) {
        m = "0" + month;
    }
    return m;
}

/**
 * 我的奖券转入
 */
@inject(stores => ({
    user: stores.user,
}))
@observer
export default class MyTransfers extends Component {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.couponTransferStore = new CouponTransfer();
        this.orgList = clone(toJS(this.props.user.allTakeDeliveryOrgList));
        this.orgList.unshift({
            orgName: '全部组织',
            orgId: ''
        });

        this.timeList = [
            { title: '全部日期', beginTime: '', endTime: '' },
            { title: '查看近一周', beginTime: getDay(-7), endTime: getDay(0) },
            { title: '查看近一月', beginTime: getDay(-30), endTime: getDay(0) },
            { title: '查看近三月', beginTime: getDay(-90), endTime: getDay(0) }
        ];
    }

    componentWillMount() {
        Platform.OS === 'ios' && this.getTransferList(0, false);
    }

    getTransferList = (index, loadMore) => {
        let params = {};
        params.orgId = this.orgList[this.couponTransferStore.listParamsArray[this.couponTransferStore.tabIndex].selectOrgIndex].orgId;
        params.requestTimeBegin = this.timeList[this.couponTransferStore.listParamsArray[this.couponTransferStore.tabIndex].selectTimeIndex].beginTime;
        params.requestTimeEnd = this.timeList[this.couponTransferStore.listParamsArray[this.couponTransferStore.tabIndex].selectTimeIndex].endTime;

        this.couponTransferStore.getTransferList(index, loadMore, params).then().catch(err => { });
    }

    /**
     * 选择组织
     */
    selectOrganization = () => {
        Widget.Popup.show(<SelectPop
            listData={this.orgList}
            popTitle={'转入组织选择'}
            labelName={'orgName'}
            selectIndex={this.couponTransferStore.listParamsArray[this.couponTransferStore.tabIndex].selectOrgIndex}
            selectCallBack={(selectData, index) => {
                Widget.Popup.hide();
                InteractionManager.runAfterInteractions(() => {
                    this.couponTransferStore.setSelectOrgIndex(index);
                    this.getTransferList(this.couponTransferStore.tabIndex, false);
                })
            }}

        />,
            {
                animationType: 'slide-up', backgroundColor: '#00000000',
                onMaskClose: () => {
                    Widget.Popup.hide()
                }
            })
    };

    /**
    * 选择时间
    */
    selectTime = () => {
        Widget.Popup.show(<SelectPop
            listData={this.timeList}
            popTitle={'时间选择'}
            labelName={'title'}
            selectIndex={this.couponTransferStore.listParamsArray[this.couponTransferStore.tabIndex].selectTimeIndex}
            selectCallBack={(selectData, index) => {
                Widget.Popup.hide();
                InteractionManager.runAfterInteractions(() => {
                    this.couponTransferStore.setSelectTimeIndex(index);
                    this.getTransferList(this.couponTransferStore.tabIndex, false);
                })
            }}

        />,
            {
                animationType: 'slide-up', backgroundColor: '#00000000',
                onMaskClose: () => {
                    Widget.Popup.hide()
                }
            })
    };

    cancelTransfer = (item) => {
        Alert.alert('取消申请', '是否取消奖券转入申请？',
            [
                { text: '否', onPress: () => { } },
                {
                    text: '是', onPress: () => {
                        this.couponTransferStore.cancelTransfer(item.id).then(() => {
                            ToastUtil.show('取消成功');
                            this.getTransferList(this.couponTransferStore.tabIndex, false);
                        }).catch(err => { });
                    }
                },
            ]
        );
    }

    renderRow = ({ item, index }) => {
        const tabIndex = this.couponTransferStore.tabIndex;
        let requestTime = '';
        const arr = item.requestTime.split(' ');
        if (arr && arr.length > 0) {
            requestTime = arr[0];
        }
        return (
            <View style={{
                backgroundColor: Constant.colorDefault,
                marginBottom: tabIndex == 0 ? Constant.scale(10) : 0
            }}>
                <TouchableOpacity onPress={() => { this.props.navigation.navigate('TransferDetail', { id: item.id }) }} style={[styles.item, { height: Constant.scale(63) }]}>
                    <Text style={{
                        fontSize: Constant.fontSizeSmall,
                        color: Constant.colorTxtContent,
                        flex: 2
                    }} numberOfLines={2}>{item.orgName}</Text>
                    <Text style={{
                        fontSize: Constant.scale(15),
                        color: Constant.colorTxtDefault,
                        flex: 1, textAlign: 'center'
                    }} numberOfLines={2}>{item.requestNo}</Text>
                    <Text style={{
                        fontSize: Constant.scale(15),
                        color: Constant.colorTxtDefault,
                        flex: 1,
                        textAlign: 'center'
                    }} numberOfLines={2}>{requestTime}</Text>
                </TouchableOpacity>
                {tabIndex == 0 && <View style={{
                    height: Constant.scale(44),
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'flex-end'
                }}>
                    <Button
                        style={{
                            borderRadius: 5,
                            borderColor: Constant.colorTxtPrimary,
                        }}
                        txtStyle={{
                            fontSize: Constant.fontSizeSmall,
                            color: Constant.colorTxtPrimary
                        }}
                        onPress={this.cancelTransfer.bind(this, item)}
                    >取消申请</Button>
                </View>}
            </View>
        );
    }

    renderList = () => {
        const list = this.couponTransferStore.getTabs;

        return (
            list.map((item, tabIndex) => {
                const orgTitle = this.orgList && this.orgList[this.couponTransferStore.listParamsArray[tabIndex].selectOrgIndex] ? this.orgList[this.couponTransferStore.listParamsArray[tabIndex].selectOrgIndex].orgName : '';

                return (
                    <View tabLabel={item.label} key={`transfer_list${tabIndex}`} style={{ flex: 1 }}>
                        <View style={{
                            width: width,
                            height: Constant.scale(43),
                            flexDirection: 'row',
                            alignItems: 'center',
                            paddingHorizontal: Constant.scale(15),
                        }}>
                            <TouchableOpacity onPress={this.selectOrganization} style={{ flexDirection: 'row', alignItems: 'center' }}>
                                <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent }}>{orgTitle}</Text>
                                <Image source={require('../img/verification/sanjiaoxiala.png')} style={{ width: Constant.scale(6), height: Constant.scale(4), marginLeft: Constant.scale(4) }} />
                            </TouchableOpacity>

                            <TouchableOpacity onPress={this.selectTime} style={{ flexDirection: 'row', alignItems: 'center', marginLeft: Constant.scale(38) }}>
                                <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent }}>{this.timeList[this.couponTransferStore.listParamsArray[tabIndex].selectTimeIndex].title}</Text>
                                <Image source={require('../img/verification/sanjiaoxiala.png')} style={{ width: Constant.scale(6), height: Constant.scale(4), marginLeft: Constant.scale(4) }} />
                            </TouchableOpacity>
                        </View>
                        <View style={styles.item}>
                            <Text style={{ fontSize: Constant.fontSizeNormal, color: Constant.colorTxtAlert, flex: 2 }}>转入组织</Text>
                            <Text style={{ fontSize: Constant.fontSizeNormal, color: Constant.colorTxtAlert, flex: 1, textAlign: 'center' }}>申请单号</Text>
                            <Text style={{ fontSize: Constant.fontSizeNormal, color: Constant.colorTxtAlert, flex: 1, textAlign: 'center' }}>申请日期</Text>
                        </View>

                        <CommonFlatList
                            style={{ flex: 1 }}
                            data={this.couponTransferStore.listParamsArray[tabIndex].data}
                            listState={this.couponTransferStore.listParamsArray[tabIndex].listState}
                            enableLoadMore={this.couponTransferStore.listParamsArray[tabIndex].enableLoadMore}
                            onLoadMore={() => {
                                this.getTransferList(tabIndex, true);
                            }}
                            keyExtractor={(item, index) => {
                                return 'tab' + tabIndex + 'transfers' + index;
                            }}
                            renderItem={this.renderRow}
                            enableRefresh={false}
                        >
                            <View style={[{ flex: 1, justifyContent: 'center', alignItems: 'center', }]}>

                                <Text style={{ fontSize: Constant.fontSizeBig, color: Constant.colorTxtContent, marginTop: Constant.scale(25) }}>暂无数据</Text>
                            </View>
                        </CommonFlatList>
                    </View>
                )
            })
        );
    }

    //渲染
    render() {

        return (
            <Header containerStyles={styles.container} title="我的奖券转入">
                <ScrollableTabView
                    onChangeTab={(obj) => {
                        this.couponTransferStore.setTabIndex(obj.i);
                        this.setState({}, () => {
                            this.getTransferList(this.couponTransferStore.tabIndex, false);
                        });
                    }}
                    style={{ paddingBottom: Constant.sizeDividerNormal, backgroundColor: Constant.colorBackgroundDefault, }}
                    scrollWithoutAnimation={true}
                    renderTabBar={() => {
                        return (
                            <DefaultTabBar
                                activeTextColor={Constant.colorTxtPrimary}
                            />
                        )
                    }}>

                    {this.renderList()}

                </ScrollableTabView>
            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center'
    },
    item: {
        height: Constant.scale(40),
        borderBottomWidth: Constant.sizeDividerNormal,
        borderColor: Constant.colorDividerDefault,
        paddingHorizontal: Constant.scale(15),
        backgroundColor: Constant.colorDefault,
        flexDirection: 'row',
        alignItems: 'center'
    }
});