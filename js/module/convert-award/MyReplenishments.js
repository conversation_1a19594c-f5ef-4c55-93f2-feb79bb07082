import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    ScrollView,
    Alert,
    TouchableWithoutFeedback
} from 'react-native';
import PropType from 'prop-types';
import { ReactNavComponent, Widget } from 'rn-yunxi';
const { width, height } = Dimensions.get('window');
import AppWidget from '../../app-widget';
const { Header, Image, Button } = AppWidget;
const { Text, CommonFlatList, Popup } = Widget;
import { inject, observer } from 'mobx-react/native';
import ToastUtil from '../../util/ToastUtil';

//订单状态：2.未发货，41.已发货，5.已收货，9.取消
const STATUS = {
    5: '已收货',
    9: '已取消',
    2: '待发货',
    41: '待收货',
};

/**
 * 我的补货单
 */
@inject(stores => ({
    myReplenishments: stores.coupon.myReplenishments,
    couponStore: stores.coupon
}))
@observer
export default class MyReplenishments extends Component {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {

        };
    }

    componentDidMount() {
        this.props.couponStore.getMyReplenishments().then().catch(err => { });
    }

    onClick(item) {
        if (item.status == 2) {
            // '取消申请';
            Alert.alert('取消申请', '是否取消补货申请？',
                [
                    { text: '否', onPress: () => { } },
                    {
                        text: '是', onPress: () => {
                            this.props.couponStore.cancelOrder(item.couponTransferId, item.orderId).then(() => {
                                ToastUtil.show('取消成功');
                            }).catch(err => { });
                        }
                    },
                ]
            );
        } else if (item.status == 41) {
            // '确认收货';
            Alert.alert('确认收货', '是否确认收货？',
                [
                    { text: '否', onPress: () => { } },
                    {
                        text: '是', onPress: () => {
                            this.props.couponStore.comfirmOrder(item.couponTransferId, item.orderId).then(() => {
                                ToastUtil.show('确认收货成功');
                            }).catch(err => { });
                        }
                    },
                ]
            );
        }
    }

    showNorm = (item)=> {
        Popup.show(
            <TouchableWithoutFeedback onPress={() => {
                Widget.Popup.hide();
            }}>
                <View style={{width: '100%', height: '100%', alignItems: 'center', justifyContent: 'center'}}>
                    <View style={{backgroundColor: '#fff', width: '80%', borderRadius: Constant.scale(5)}}>

                        <Text style={{
                            fontSize: Constant.fontSizeNormal,
                            margin: Constant.sizeMarginDefault,
                            color: Constant.colorTxtContent
                        }}>兑换比例：{item.exchangeProportion}</Text>
                        <Text style={{
                            fontSize: Constant.fontSizeNormal,
                            margin: Constant.sizeMarginDefault,
                            marginTop: 0,
                            color: Constant.colorTxtContent
                        }}>余券可兑：{item.exchangeStatus == 1 ? '是' : '否'}</Text>
                    </View>
                </View>
            </TouchableWithoutFeedback>
            ,
            {
                onMaskClose: () => {
                    Widget.Popup.hide();
                }
            }
        )
    }

    renderGoods(goods, isShowAll) {
        let list = [];
        if (goods.length > 2 && !isShowAll) {
            list = [goods[0], goods[1]];
        } else {
            list = goods;
        }

        return (
            list.map((item, index) => {
                return (
                    <View tabLabel={item.title} key={`goods${index}`} style={{ flex: 1, paddingVertical: Constant.scale(10), paddingHorizontal: Constant.scale(30), justifyContent: 'center' }}>
                        <Text style={{ fontSize: Constant.fontSizeNormal, color: Constant.colorTxtDefault }}>{item.itemName + (item.prizeContent ? ('（' + item.prizeContent + '）') : '')}</Text>
                        <View style={{flexDirection:'row', marginTop: Constant.scale(5),width: '100%'}}>
                            <View style={{ flexDirection: 'row', alignItems: 'center',flex: 1 }}>
                                <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert }}>{item.itemUnitSpec}</Text>
                                <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert, marginLeft: Constant.scale(15) }}>x{item.itemNum}{item.itemUnit}</Text>
                                <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert, marginLeft: Constant.scale(15) }} zz>合{item.itemNum * item.itemUnitNum}个</Text>
                            </View>
                            {/*<TouchableOpacity style={{width: Constant.scale(80), alignItems: 'flex-end'}} onPress={()=> {*/}
                                {/*this.showNorm(item);*/}
                            {/*}}><Text style={{fontSize: Constant.fontSizeSmall,color: Constant.colorTxtPrimary}}>兑换规则</Text></TouchableOpacity>*/}
                        </View>
                    </View>
                )
            })
        );
    }

    renderItem = ({ item, index }) => {
        let btnTitle = '';
        if (item.status == 2) {
            btnTitle = '取消申请';
        } else if (item.status == 41) {
            btnTitle = '确认收货';
        }

        return (
            <View style={{ marginTop: Constant.scale(10), backgroundColor: Constant.colorDefault }}>
                <View style={{
                    height: Constant.scale(40),
                    backgroundColor: Constant.colorDefault,
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingHorizontal: Constant.scale(15)
                }}>
                    <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert, flex: 1 }}>申请时间：{item.orderTime}</Text>
                    <Text style={{ fontSize: Constant.fontSizeNormal, color: '#FF7E00' }}>{STATUS[item.status]}</Text>
                </View>

                {item.orderItemResList && item.orderItemResList.length > 0 && this.renderGoods(item.orderItemResList, item.isShowAll)}
                {item.orderItemResList && item.orderItemResList.length > 2 && item.isShowAll != true &&
                    <TouchableOpacity onPress={() => { this.props.couponStore.showAll(index); this.setState({}); }} style={{ width: width, height: Constant.scale(40), flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                        <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent }}>展开全部</Text>
                        <Image source={require('../img/verification/sanjiaoxianxiala.png')} style={{ width: Constant.scale(7.2), height: Constant.scale(3.6), marginLeft: Constant.scale(5) }} />
                    </TouchableOpacity>}

                {btnTitle != '' && <View style={{
                    width: width, borderTopWidth: Constant.sizeDividerNormal, borderColor: Constant.colorDividerDefault, height: Constant.scale(44), flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end'
                }}>
                    <Button style={{ borderRadius: 5, borderColor: Constant.colorTxtPrimary }}
                        txtStyle={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtPrimary }}
                        onPress={this.onClick.bind(this, item)}
                    >{btnTitle}</Button>
                </View>}

            </View>
        );
    }

    //渲染
    render() {

        return (
            <Header containerStyles={styles.container} title="我的补货单">
                <CommonFlatList
                    style={{ flex: 1, width: width, marginTop: Constant.scale(10) }}
                    data={this.props.myReplenishments.dataArray}
                    listState={this.props.myReplenishments.listState}
                    enableLoadMore={this.props.myReplenishments.enableLoadMore}
                    keyExtractor={(item, index) => {
                        return 'myReplenishment' + index;
                    }}
                    onLoadMore={() => {
                        this.props.couponStore.getMyReplenishments(true).then().catch(e => { })
                    }}
                    renderItem={this.renderItem}
                    enableRefresh={false}
                >
                    <View style={[{ flex: 1, width: width, justifyContent: 'center', alignItems: 'center' }]}>
                        <Text style={{ fontSize: 16, color: Constant.colorTxtContent, marginTop: Constant.scale(25) }}>
                            暂无数据
                        </Text>
                    </View>
                </CommonFlatList>
            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center'
    }
});