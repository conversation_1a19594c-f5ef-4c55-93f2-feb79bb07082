import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    ScrollView,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import { ReactNavComponent, Widget } from 'rn-yunxi';
const { width, height } = Dimensions.get('window');
import AppWidget from '../../app-widget';
const { Header, Image, Button } = AppWidget;
const { Text } = Widget;
import { inject, observer } from 'mobx-react/native';
import ToastUtil from '../../util/ToastUtil'
import CouponTransfer from '../../store/CouponTransfer';

/**
 * 申请转入详情
 */
@observer
export default class TransferDetail extends Component {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.couponTransferStore = new CouponTransfer();
    }

    componentWillMount() {
        this.getTransferDetail();
    }

    getTransferDetail = () => {
        this.couponTransferStore.getTransferDetail(this.props.navigation.state.params.id).then().catch(e => { });
    }

    renderList = () => {
        const transferDetail = this.couponTransferStore.transferDetail;
        const list = transferDetail && transferDetail.detail ? transferDetail.detail : [];

        return list.map((item, index) => {
            return <View key={index} style={styles.wrap}>
                <View style={styles.item}>
                    <Text style={styles.itemLeftText}>
                        产品编码：<Text style={styles.itemRightText}>{item.code}</Text>
                    </Text>
                </View>
                <View style={styles.item}>
                    <Text style={styles.itemLeftText}>
                        奖券描述：<Text style={styles.itemRightText}>{item.description}</Text>
                    </Text>
                </View>
                <View style={styles.item}>
                    <Text style={styles.itemLeftText}>
                        奖券总数(个)：<Text style={[styles.itemRightText, { color: '#DA3746' }]}>{item.amount}</Text>
                    </Text>
                </View>
                <View style={styles.item}>
                    <Text style={styles.itemLeftText}>
                        占用(个)：<Text style={[styles.itemRightText, { color: '#DA3746' }]}>{item.occupation}</Text>
                    </Text>
                </View>
                <View style={styles.item}>
                    <Text style={styles.itemLeftText}>
                        可申请数(个)：<Text style={[styles.itemRightText, { color: '#DA3746' }]}>{item.mayApply}</Text>
                    </Text>
                </View>
                <View style={styles.item}>
                    <Text style={[styles.itemLeftText, { color: Constant.colorTxtAlert }]}>
                        申请数值(个)：<Text style={[styles.itemRightText, { color: '#DA3746' }]}>{item.outNumber}</Text>
                    </Text>
                </View>
            </View>
        });
    }

    //渲染
    render() {
        const blank = '　'; //全角空格
        const transferDetail = this.couponTransferStore.transferDetail;
        let status = '';
        const tabs = this.couponTransferStore.getTabs;
        for (i = 0; i < tabs.length; i++) {
            if (tabs[i].status == transferDetail.status) {
                status = tabs[i].label;
                break;
            }
        }

        return (
            <Header containerStyles={styles.container} title="申请转入详情">
                <ScrollView
                    alwaysBounceVertical={false}
                    showsVerticalScrollIndicator={false}>
                    <View style={[styles.wrap, { paddingVertical: Constant.scale(8) }]}>
                        <Text style={styles.leftText}>
                            申请单号：<Text style={styles.rightText}>{transferDetail.requestNo}</Text>
                        </Text>
                        <Text style={styles.leftText}>
                            转入组织：<Text style={styles.rightText}>{transferDetail.orgName}</Text>
                        </Text>
                        <Text style={styles.leftText}>
                            {`${blank}${blank}状态：`}<Text style={[styles.rightText, { color: '#DA3746' }]}>{status}</Text>
                        </Text>
                        <Text style={styles.leftText}>
                            申请时间：<Text style={styles.rightText}>{transferDetail.requestTime}</Text>
                        </Text>
                        {(transferDetail.status == 1 || transferDetail.status == 2) && <Text style={styles.leftText}>
                            审核时间：<Text style={styles.rightText}>{transferDetail.checkTime}</Text>
                        </Text>}
                        {(transferDetail.status == 1 || transferDetail.status == 2) && <Text style={styles.leftText}>
                            {`${blank}${blank}备注：`}<Text style={styles.rightText}>{transferDetail.remark ? transferDetail.remark : '--'}</Text>
                        </Text>}
                    </View>

                    {this.renderList()}

                </ScrollView>

                {status == '待审核' && <TouchableOpacity style={[{
                    marginVertical: Constant.scale(10),
                    borderRadius: Constant.scale(4),
                    height: Constant.scale(44), width: '94%',
                    backgroundColor: Constant.colorPrimary,
                    alignItems: 'center',
                    justifyContent: 'center',
                }]} onPress={this.apply}>
                    <Text style={{ color: 'white', fontSize: Constant.fontSizeBig }}>取消申请</Text>
                </TouchableOpacity>}

            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center'
    },
    wrap: {
        width: width,
        backgroundColor: Constant.colorDefault,
        marginTop: Constant.scale(10),
        paddingHorizontal: Constant.scale(15),
    },
    leftText: {
        fontSize: Constant.fontSizeNormal,
        color: Constant.colorTxtContent,
        marginVertical: Constant.scale(4)
    },
    rightText: {
        fontSize: Constant.fontSizeNormal,
        color: Constant.colorTxtTitle
    },
    item: {
        height: Constant.scale(40),
        borderBottomWidth: Constant.sizeDividerNormal,
        borderColor: Constant.colorDividerDefault,
        justifyContent: 'center'
    },
    itemLeftText: {
        fontSize: Constant.fontSizeNormal,
        color: Constant.colorTxtTitle
    },
    itemRightText: {
        fontSize: Constant.fontSizeNormal,
        color: Constant.colorTxtContent
    }
});