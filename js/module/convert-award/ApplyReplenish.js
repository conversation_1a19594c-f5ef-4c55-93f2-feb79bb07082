import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    ScrollView,
    TouchableWithoutFeedback
} from 'react-native';
import PropType from 'prop-types';
import { ReactNavComponent, Widget } from 'rn-yunxi';
const { width, height } = Dimensions.get('window');
import AppWidget from '../../app-widget';
const { Header, QuantitySelector } = AppWidget;
const { Text, Popup } = Widget;
import { inject, observer } from 'mobx-react/native';
import ToastUtil from '../../util/ToastUtil'
import { toJS } from 'mobx';

/**
 * 补货申请
 */

@inject(stores => ({
    list: stores.coupon.listParamsArray[0].data,
    couponStore: stores.coupon
}))
@observer
export default class ApplyReplenish extends Component {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {
            btnTitle: '全部申请',
        };
        this.isTab = this.props.navigation.state.routeName.indexOf('Tab') != -1;
    }

    componentWillMount() {
        this.getCouponList(0, false);
    }

    getCouponList = (index, loadMore, queryParams) => {
        this.props.couponStore.getCouponList(index, loadMore, queryParams).then().catch(e => { });
    }

    quantityChange = (index, value) => {
        this.props.couponStore.changeChooseQuantity(index, value);
        if (this.state.btnTitle == '全部申请') {
            this.setState({ btnTitle: '立即申请' });
        }
    }

    apply = () => {
        const list = toJS(this.props.list);
        if (list && list.length > 0) {
            let applyList = [];
            for (const item of list) {
                if (item.chooseQuantity > 0 || (item.max > 0 && this.state.btnTitle == '全部申请')) {
                    applyList.push(item);
                }
            }
            if (applyList.length <= 0) {
                ToastUtil.show('申请补货数量不能为0，请检查');
                return;
            }
            let replenishQuantity = [];
            let norm = [];
            let itemCode = [];
            let scanCode = [];
            let couponType = [];
            let couponList = [];
            for (const item of applyList) {
                replenishQuantity.push(item.chooseQuantity > 0 ? item.chooseQuantity : item.max);
                norm.push(item.normSize);
                itemCode.push(item.itemCode);
                scanCode.push(item.scanCode);
                couponType.push(item.couponType);
                let obj = {itemCode: item.itemCode, scanCode: item.scanCode, couponType: item.couponType,convertQuantity: item.chooseQuantity > 0 ? item.chooseQuantity : item.max,norm: item.convertNum};
                couponList.push(obj);
            }
            const params = {
                replenishQuantity: replenishQuantity,
                norm: norm,
                itemCode: itemCode,
                scanCode: scanCode,
                couponType: couponType,
                couponList
            };
            console.log('>>>params', params)
            this.props.couponStore.applyReplenish(params).then(() => {
                ToastUtil.show('申请成功')
                if (this.isTab) {
                    this.props.navigation.navigate('MyReplenishments');
                } else {
                    this.props.navigation.replace('MyReplenishments');
                }
            }).catch(err => { })
        }
    }

    showNorm = (item)=> {
        Popup.show(
            <TouchableWithoutFeedback onPress={() => {
                Widget.Popup.hide();
            }}>
                <View style={{width: '100%', height: '100%', alignItems: 'center', justifyContent: 'center'}}>
                    <View style={{backgroundColor: '#fff', width: '80%', borderRadius: Constant.scale(5)}}>
                        <Text style={{
                            fontSize: Constant.fontSizeNormal,
                            margin: Constant.sizeMarginDefault,
                            color: Constant.colorTxtContent
                        }}>兑换比例：{item.exchangeProportion}</Text>
                        <Text style={{
                            fontSize: Constant.fontSizeNormal,
                            margin: Constant.sizeMarginDefault,
                            marginTop: 0,
                            color: Constant.colorTxtContent
                        }}>余券可兑：{item.exchangeStatus == 1 ? '是' : '否'}</Text>
                    </View>
                </View>
            </TouchableWithoutFeedback>
            ,
            {
                onMaskClose: () => {
                    Widget.Popup.hide();
                }
            }
        )
    }

    renderList() {
        const { list } = this.props;

        return (
            list && list.map((item, index) => {
                return <View key={index} style={{ marginTop: Constant.scale(10) }}>
                    <View style={{
                        height: Constant.scale(70.5),
                        backgroundColor: Constant.colorDefault,
                        borderBottomWidth: Constant.sizeDividerNormal, borderColor: Constant.colorDividerDefault,
                        justifyContent: 'center',
                        paddingHorizontal: Constant.scale(15)
                    }}>
                        <Text style={{ fontSize: Constant.fontSizeNormal, color: Constant.colorTxtContent }} numberOfLines={1}>{item.itemName + (item.prizeContent ? ('（' + item.prizeContent + '）') : '')}</Text>
                        <View style={{ flexDirection: 'row', marginTop: Constant.scale(10) }}>
                            <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert }}>规格：{item.norm}</Text>
                            <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert, marginLeft: Constant.scale(30) }}>可用：{Number(item.quantity)}个</Text>
                            {/*<TouchableOpacity style={{flex: 1, alignItems: 'flex-end'}} onPress={()=> {*/}
                                {/*this.showNorm(item);*/}
                            {/*}}><Text style={{fontSize: Constant.fontSizeSmall,color: Constant.colorTxtPrimary}}>兑换规则</Text></TouchableOpacity>*/}
                        </View>
                    </View>
                    <View style={{
                        height: Constant.scale(44),
                        backgroundColor: Constant.colorDefault,
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingHorizontal: Constant.scale(15)
                    }}>
                        <Text style={{ fontSize: Constant.fontSizeNormal, color: Constant.colorTxtAlert, flex: 1 }}>
                            可补货：<Text style={{ fontSize: Constant.fontSizeNormal, color: Constant.colorTxtTitle }}>{item.max}箱</Text>
                        </Text>

                        <QuantitySelector value={item.chooseQuantity} min={1} max={item.max} onChange={(value) => { this.quantityChange(index, value); }} />
                    </View>
                </View>
            })
        );
    }

    //渲染
    render() {
        const { btnTitle } = this.state;
        const list = this.props.list;

        return (
            <Header showBackAction={!this.isTab} containerStyles={styles.container} title="补货申请">
                {list && list.length > 0 ?
                    <ScrollView style={{ flex: 1, width: '100%' }}>
                        {
                            this.renderList()
                        }
                    </ScrollView> :
                    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                        <Text style={{ fontSize: 16, color: Constant.colorTxtContent }}>暂无数据</Text>
                    </View>}

                {list && list.length > 0 && <TouchableOpacity style={[{
                    marginTop: Constant.sizeMarginDefault,
                    borderRadius: Constant.scale(4),
                    height: Constant.scale(44), width: '94%',
                    backgroundColor: Constant.colorPrimary,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: this.isTab ? Constant.scale(41.5) : Constant.scale(15)
                }]} onPress={this.apply}>
                    <Text style={{ color: 'white', fontSize: Constant.fontSizeBig }}>{btnTitle}</Text>
                </TouchableOpacity>}
            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center'
    }
});