/**
 * Created by whw on 2018/1/19.
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    Alert,
    TouchableWithoutFeedback
} from 'react-native';
import PropType from 'prop-types';
import { ReactNavComponent, Widget } from 'rn-yunxi';
import AppWidget from '../../app-widget';

const { width, height } = Dimensions.get('window');
const { Image, Button } = AppWidget;
const { Text, Popup } = Widget;
import { inject, observer } from 'mobx-react/native';
import { toJS } from 'mobx';
import ToastUtil from '../../util/ToastUtil';

/**
 * 门店核销列表item
 */

@inject(stores => ({
    verificationList: stores.verificationList,
    user:stores.user,
}))
@observer
export default class VerificationListItem extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {
    };

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
            showCheckBox: this.props.showCheckBox,
        };
    }

    showAll = () => {
        this.props.showAll && this.props.showAll();
    }


    onClick = () => {
        const tabIndex = this.props.verificationList.tabIndex;
        const data = this.props.data;

        if (data.status == 2) {
            //发货
            Alert.alert('发货', '是否确认发货？',
                [
                    { text: '否', onPress: () => { } },
                    {
                        text: '是', onPress: () => {
                            this.props.verificationList.deliverOrder(data.couponTransferId, data.orderId).then(() => {
                                ToastUtil.show('发货成功');
                            }).catch(err => { });
                        }
                    },
                ]
            );
        } else if (data.status == 41) {
            //确认送达
            this.props.showComfirmDeliver && this.props.showComfirmDeliver(data);
        }
    }

    showNorm = (item)=> {
        Popup.show(
            <TouchableWithoutFeedback onPress={() => {
                Widget.Popup.hide();
            }}>
                <View style={{width: '100%', height: '100%', alignItems: 'center', justifyContent: 'center'}}>
                    <View style={{backgroundColor: '#fff', width: '80%', borderRadius: Constant.scale(5)}}>

                        <Text style={{
                            fontSize: Constant.fontSizeNormal,
                            margin: Constant.sizeMarginDefault,
                            color: Constant.colorTxtContent
                        }}>兑换比例：{item.exchangeProportion}</Text>
                        <Text style={{
                            fontSize: Constant.fontSizeNormal,
                            margin: Constant.sizeMarginDefault,
                            marginTop: 0,
                            color: Constant.colorTxtContent
                        }}>余券可兑：{item.exchangeStatus == 1 ? '是' : '否'}</Text>
                    </View>
                </View>
            </TouchableWithoutFeedback>
            ,
            {
                onMaskClose: () => {
                    Widget.Popup.hide();
                }
            }
        )
    }

    renderGoods() {
        let goods = this.props.data.orderItemResList;
        const isShowAll = this.props.data.isShowAll;
        let list = [];
        if (goods.length > 2 && !isShowAll) {
            list = [goods[1], goods[2]];
        } else {
            list = goods;
        }

        return (
            list.map((item, index) => {
                return (
                    <View tabLabel={item.title} key={`goods${index}`} style={{ flex: 1, paddingVertical: Constant.scale(10), marginLeft: Constant.scale(30), justifyContent: 'center' }}>
                        <Text style={{ fontSize: Constant.fontSizeNormal, color: Constant.colorTxtDefault }}>{item.itemName + (item.prizeContent ? ('（' + item.prizeContent + '）') : '')}</Text>
                        <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: Constant.scale(5) }}>
                            {
                                this.props.user.isDealer()  ?
                                    <View style={{ flexDirection: 'row', alignItems: 'center',flex: 1 }}>
                                        <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert }}>{item.itemUnitSpec}</Text>
                                        <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert, marginLeft: Constant.scale(15) }}>x{item.itemNum}{item.itemUnit}</Text>
                                        <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert, marginLeft: Constant.scale(15) }} zz>合{item.itemNum * item.itemUnitNum}个</Text>
                                    </View>
                                    : <View style={{ flexDirection: 'row', alignItems: 'center',flex: 1 }}>
                                    <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert }}>{item.itemUnitSpec}</Text>
                                    <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert, marginLeft: Constant.scale(15) }}>使用{item.itemNum}个奖券兑换{item.convertQuantity ? item.convertQuantity : 0}支</Text>
                                    {/*<Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert, marginLeft: Constant.scale(15) }}>合{item.itemNum}个</Text>*/}
                                </View>
                            }
                            {this.props.user.isDealer() ? null : <TouchableOpacity style={{width: Constant.scale(80), alignItems: 'flex-end',marginRight: Constant.sizeMarginDefault}} onPress={()=> {
                                this.showNorm(item);
                            }}><Text style={{fontSize: Constant.fontSizeSmall,color: Constant.colorTxtPrimary}}>兑换规则</Text></TouchableOpacity>}
                        </View>
                    </View>
                )
            })
        );
    }


    //渲染
    render() {
        const data = this.props.data;
        const tabIndex = this.props.verificationList.tabIndex;
        console.log('table=== ',tabIndex,toJS(data));
        let btnTitle;
        if (data.status == 2) {
            btnTitle = '确认发货';
        } else if (data.status == 41) {
            btnTitle = '确认送达';
        }

        let orderTime = data.orderTime;
        let timeText = '申请';
        if (data.status == 9) {
            orderTime = data.cancelTime;
            timeText = '取消';
        } else if (data.status == 41) {
            orderTime = data.shippingTime;
            timeText = '发货';
        } else if (data.status == 5) {
            orderTime = data.collectTime;
            timeText = '收货';
        }
        orderTime = orderTime ? orderTime : '无';

        return (
            <View style={{ width: width, backgroundColor: Constant.colorDefault, marginTop: Constant.sizeMarginDefault }}>

                <View style={{ width: width, height: Constant.scale(40), flexDirection: 'row', alignItems: 'center', paddingHorizontal: Constant.scale(15) }}>
                    <Image source={require('../img/verification/userIcon.png')} style={{ width: Constant.scale(11), height: Constant.scale(13) }} />
                    <Text style={{ fontSize: Constant.fontSizeNormal, color: Constant.colorTxtDefault, marginLeft: Constant.scale(5), flex: 1 }} numberOfLines={1}>{data.memberName ? data.memberName : '未设置'}</Text>
                    <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert }}>{timeText}时间：{orderTime}</Text>
                </View>

                {this.renderGoods()}

                {data.list && data.list.length > 2 && data.isShowAll != true && <TouchableOpacity onPress={this.showAll} style={{ width: width, height: Constant.scale(40), flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                    <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent }}>展开全部</Text>
                    <Image source={require('../img/verification/sanjiaoxianxiala.png')} style={{ width: Constant.scale(7.2), height: Constant.scale(3.6), marginLeft: Constant.scale(10) }} />
                </TouchableOpacity>}

                <View style={{
                    width: width, borderTopWidth: Constant.sizeDividerNormal, borderColor: Constant.colorDividerDefault, paddingVertical: Constant.scale(10), flexDirection: 'row', alignItems: 'center', paddingHorizontal: Constant.scale(15)
                }}>
                    <Text style={{ width:'100%',fontSize: Constant.fontSizeNormal, color: Constant.colorTxtContent, flex: 1, marginRight: Constant.scale(5) }} >{`${data.fullAddress} ${data.receiver} ${data.receiverPhone}`}</Text>
                    {btnTitle && <Button style={{ borderRadius: 5, borderColor: Constant.colorTxtPrimary, marginRight: 0 }}
                        txtStyle={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtPrimary }}
                        onPress={this.onClick}
                    >{btnTitle}</Button>}
                </View>
            </View>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
});