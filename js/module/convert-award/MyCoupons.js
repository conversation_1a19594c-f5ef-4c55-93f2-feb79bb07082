import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    ScrollView
} from 'react-native';
import PropType from 'prop-types';
import { ReactNavComponent, Util, Widget } from 'rn-yunxi';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import { inject, observer } from 'mobx-react/native';
import AppWidget from '../../app-widget';
const { width, height } = Dimensions.get('window');
const { Header, Image, DefaultTabBar, SelectPop, ArrowRotate } = AppWidget;
const { CommonFlatList, Text } = Widget;
import { toJS } from 'mobx';

function getDay(day) {
    var today = new Date();

    var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;

    today.setTime(targetday_milliseconds); //注意，这行是关键代码  

    var tYear = today.getFullYear();
    var tMonth = today.getMonth();
    var tDate = today.getDate();
    tMonth = doHandleMonth(tMonth + 1);
    tDate = doHandleMonth(tDate);
    return tYear + "-" + tMonth + "-" + tDate;
}

function doHandleMonth(month) {
    var m = month;
    if (month.toString().length == 1) {
        m = "0" + month;
    }
    return m;
}

const REASON = {
    //分销商
    7: { title: '下游核销', isPlus: true },
    8: { title: '核销转出', isPlus: false },
    9: { title: '选择原因' },
    //经销商
    10: { title: '奖券转入', isPlus: true },
    11: { title: '核销转出', isPlus: false },
    12: { title: '选择原因' },
    15: { title: '扫码转入', isPlus: true },
};

/**
 * 我的奖券
 */
@inject(stores => ({
    listParamsArray: stores.coupon.listParamsArray,
    couponStore: stores.coupon,
    isDealer: stores.user.isDealer()
}))
@observer
export default class MyCoupons extends Component {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};
    
    //构造函数
    constructor(props) {
        super(props);

        this.timeList = [
            { title: '全部日期', beginTime: '', endTime: '' },
            { title: '查看近一周', beginTime: getDay(-7), endTime: getDay(0) },
            { title: '查看近一月', beginTime: getDay(-30), endTime: getDay(0) },
            { title: '查看近三月', beginTime: getDay(-90), endTime: getDay(0) }
        ];

        if (!this.props.isDealer) {
            this.reasonList = [
                { title: '选择原因', reason: 9 },
                { title: '扫码转入', reason: 15 },
                { title: '下游核销', reason: 7 },
                { title: '核销转出', reason: 8 },
            ];
        } else {
            this.reasonList = [
                { title: '选择原因', reason: 12 },
                { title: '扫码转入', reason: 15 },
                { title: '奖券转入', reason: 10 },
                { title: '核销转出', reason: 11 },
            ];
        }
        let yesterday = Util.DateUtil.formatDate(new Date().getTime()-24*60*60*1000, 'yyyy-MM-dd');
        let yesterdayDate =  Util.DateUtil.parserDateString(yesterday);
        this.state = {
            timeTitle: this.timeList[0].title,
            reasonTitle: this.reasonList[0].title,

            startTime:yesterdayDate,
            endTime:yesterdayDate,
            startTimeText:yesterday,
            endTimeText:yesterday,
        };

        //收支记录查询参数
        this.queryParams = {
            beginTime: '',
            endTime: '',
            reason: !this.props.isDealer ? 9 : 12,
        }
    }

    componentWillMount() {
        this.props.couponStore.getListMd(this.state.startTimeText, this.state.endTimeText);
        this.getCouponList(0, false);
    }

    getCouponList = (index, loadMore) => {
        this.props.couponStore.getCouponList(index, loadMore, this.queryParams).then().catch(e => { });
    }

    selectTime1 = () => {
        this.props.navigation.navigate("OrderFilter", {
            callback: obj => {
                this.setState(
                    {
                        startTime:obj.startTime,
                        endTime:obj.endTime,
                        startTimeText:obj.startTimeText,
                        endTimeText:obj.endTimeText}
                    )
                this.props.couponStore.getListMd(obj.startTimeText, obj.endTimeText);
            },
            searchParams: {
                title:'日期选择',
                startTime:this.state.startTime,
                endTime:this.state.endTime,
                startTimeText:this.state.startTimeText,
                endTimeText:this.state.endTimeText
            }
          });
    }
    selectTime = () => {
        this.timeArrow.rorateUp();
        Widget.Popup.show(
            <SelectPop
                popTitle={'选择时间'}
                labelName={'title'}
                selectKey={'title'}
                selectId={this.state.timeTitle}
                listData={this.timeList}
                showPleaseChooseBtn={false}
                selectCallBack={(data) => {
                    Widget.Popup.hide();
                    this.timeArrow.rorateDown();

                    this.setState({ timeTitle: data.title });
                    this.queryParams.beginTime = data.beginTime;
                    this.queryParams.endTime = data.endTime;
                    this.getCouponList(1, false);
                }} />,
            {
                animationType: 'slide-up', backgroundColor: '#00000000',
                onMaskClose: () => {
                    Widget.Popup.hide();
                    this.timeArrow.rorateDown();
                }
            }
        )
    }

    selectReason = () => {
        this.reasonArrow.rorateUp();
        Widget.Popup.show(
            <SelectPop
                popTitle={'选择原因'}
                labelName={'title'}
                selectKey={'title'}
                selectId={this.state.reasonTitle}
                listData={this.reasonList}
                showPleaseChooseBtn={false}
                selectCallBack={(data) => {
                    this.reasonArrow.rorateDown();
                    Widget.Popup.hide();

                    this.setState({ reasonTitle: data.title });
                    this.queryParams.reason = data.reason;
                    this.getCouponList(1, false);
                }} />,
            {
                animationType: 'slide-up', backgroundColor: '#00000000',
                onMaskClose: () => {
                    this.reasonArrow.rorateDown();
                    Widget.Popup.hide();
                }
            }
        )
    }

    // 奖券列表
    renderCoupons = () => {
        const list = [1, 2, 3, 4];
        const applyText = this.props.isDealer ? '申请转入' : '申请补货';
        const applyRoute = this.props.isDealer ? 'ApplyTransfer' : 'ApplyReplenish';

        return (
            <View tabLabel={'我的奖券'} style={{ flex: 1, alignItems: 'center' }}>
                <View style={{ width: '100%', height: Constant.scale(40), backgroundColor: Constant.colorDefault, borderBottomWidth: Constant.sizeDividerNormal, borderColor: Constant.colorDividerDefault, flexDirection: 'row', alignItems: 'center', marginTop: Constant.scale(10) }}>
                    <Text style={{ fontSize: Constant.fontSizeNormal, color: Constant.colorTxtAlert, marginHorizontal: Constant.scale(15), flex: 3.5 }}>奖券</Text>
                    <Text style={{ fontSize: Constant.fontSizeNormal, color: Constant.colorTxtAlert, textAlign: 'center', flex: 2 }}>冻结</Text>
                    <Text style={{ fontSize: Constant.fontSizeNormal, color: Constant.colorTxtAlert, textAlign: 'center', flex: 2 }}>数量</Text>
                </View>

                <CommonFlatList
                    style={{ flex: 1, width: width }}
                    data={this.props.listParamsArray[0].data}
                    listState={this.props.listParamsArray[0].listState}
                    enableLoadMore={this.props.listParamsArray[0].enableLoadMore}
                    onLoadMore={() => {
                        this.getCouponList(0, true);
                    }}
                    keyExtractor={(item, index) => {
                        return 'tab' + 0 + 'coupon' + index;
                    }}
                    renderItem={this.renderCouponItem}
                    enableRefresh={false}
                >
                    <View style={[{ flex: 1, justifyContent: 'center', alignItems: 'center', }]}>
                        <Text style={{ fontSize: 16, color: Constant.colorTxtContent, marginTop: Constant.scale(25) }}>暂无数据</Text>
                    </View>
                </CommonFlatList>

                <TouchableOpacity style={[{
                    marginTop: Constant.sizeMarginDefault,
                    borderRadius: Constant.scale(4),
                    height: Constant.scale(44), width: '94%',
                    backgroundColor: Constant.colorPrimary,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: Constant.scale(15)
                }]} onPress={() => { this.props.navigation.navigate(applyRoute) }}>
                    <Text style={{ color: 'white', fontSize: Constant.fontSizeBig }}>{applyText}</Text>
                </TouchableOpacity>
            </View>
        );
    }

    renderCouponItem = ({ item, index }) => {
        return (
            <View style={{
                width: '100%', height: Constant.scale(63),
                backgroundColor: Constant.colorDefault,
                borderBottomWidth: index == this.props.listParamsArray[0].data.length - 1 ? 0 : Constant.sizeDividerNormal, borderColor: Constant.colorDividerDefault,
                flexDirection: 'row', alignItems: 'center'
            }}>
                <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent, marginHorizontal: Constant.scale(15), flex: 3.5, lineHeight: Constant.scale(20) }} numberOfLines={3}>{item.itemName + (item.prizeContent ? ('（' + item.prizeContent + '）') : '')}</Text>
                <Text style={{ fontSize: Constant.scale(15), color: Constant.colorTxtTitle, textAlign: 'center', flex: 2 }}>{item.frozenQuantity}个</Text>
                <Text style={{ fontSize: Constant.scale(15), color: Constant.colorTxtTitle, textAlign: 'center', flex: 2 }}>{item.quantity}个</Text>
            </View>
        );
    }

    // 收支记录
    renderRecords = () => {
        const list = [1, 2, 3, 4];
        const { timeTitle, reasonTitle } = this.state;

        return (
            <View tabLabel={'奖券明细'} style={{ flex: 1 }}>

                <View style={{ height: Constant.scale(44), flexDirection: 'row', alignItems: 'center' }}>
                    <TouchableOpacity onPress={this.selectTime} style={{ flexDirection: 'row', alignItems: 'center', marginLeft: Constant.scale(15) }}>
                        <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent }}>{timeTitle}</Text>
                        <ArrowRotate ref={(ref) => { this.timeArrow = ref; }} style={{ marginLeft: Constant.scale(4) }} />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={this.selectReason} style={{ flexDirection: 'row', alignItems: 'center', marginLeft: Constant.scale(120) }}>
                        <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent }}>{reasonTitle}</Text>
                        <ArrowRotate ref={(ref) => { this.reasonArrow = ref; }} style={{ marginLeft: Constant.scale(4) }} />
                    </TouchableOpacity>
                </View>

                <View style={{ width: '100%', height: Constant.scale(40), backgroundColor: Constant.colorDefault, borderBottomWidth: Constant.sizeDividerNormal, borderColor: Constant.colorDividerDefault, flexDirection: 'row', alignItems: 'center' }}>
                    <Text style={{ fontSize: Constant.fontSizeNormal, color: Constant.colorTxtAlert, marginHorizontal: Constant.scale(15), flex: 3.5 }}>奖券</Text>
                    <Text style={{ fontSize: Constant.fontSizeNormal, color: Constant.colorTxtAlert, textAlign: 'center', flex: 2 }}>原因</Text>
                    <Text style={{ fontSize: Constant.fontSizeNormal, color: Constant.colorTxtAlert, marginRight: Constant.scale(15), flex: 2.5, textAlign: 'right' }}>数量及时间</Text>
                </View>

                <CommonFlatList
                    style={{ flex: 1, width: '100%' }}
                    data={this.props.listParamsArray[1].data}
                    listState={this.props.listParamsArray[1].listState}
                    enableLoadMore={true}
                    onLoadMore={() => {
                        this.getCouponList(1, true);
                    }}
                    keyExtractor={(item, index) => {
                        return 'tab' + 1 + 'coupon' + index;
                    }}
                    renderItem={this.renderRecordItem}
                    enableRefresh={false}
                >
                    <View style={[{ flex: 1, justifyContent: 'center', alignItems: 'center', }]}>
                        <Text style={{ fontSize: 16, color: Constant.colorTxtContent, marginTop: Constant.scale(25) }}>暂无数据</Text>
                    </View>
                </CommonFlatList>

            </View>
        );
    }
    newRenderItem(text1,text2,text3,text4){
        return <View style={{ width: '100%', minHeight: Constant.scale(40), backgroundColor: Constant.colorDefault, borderBottomWidth: Constant.sizeDividerNormal, borderColor: Constant.colorDividerDefault, flexDirection: 'row', alignItems: 'center' }}>
        <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent, marginHorizontal: Constant.scale(15), flex:5, lineHeight: Constant.scale(20) }} numberOfLines={3}>{text1}</Text>
        <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent, textAlign: 'center', flex: 2 }}>{text2}</Text>
        <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent, textAlign: 'center', flex: 2 }}>{text3}</Text>
        <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent, textAlign: 'center', flex: 2 }}>{text4}</Text>
        </View>
    }
    // 收支记录 新的,干脆直接新做
    newRenderRecords = () => {
        const list = [1, 2, 3, 4];
        const { startTimeText, endTimeText } = this.state;
        let mdList =this.props.couponStore.newMdList;
        return (
            <View tabLabel={'收支统计'} style={{ flex: 1 }}>

                <View style={{ height: Constant.scale(44), flexDirection: 'row', alignItems: 'center' }}>
                    <TouchableOpacity onPress={this.selectTime1} style={{ flexDirection: 'row', alignItems: 'center', marginLeft: Constant.scale(15) }}>
                        <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent }}>日期:{startTimeText} —— {endTimeText}</Text>
                        <ArrowRotate ref={(ref) => { this.timeArrow = ref; }} style={{ marginLeft: Constant.scale(4) }} />
                    </TouchableOpacity>
                </View>

                {this.newRenderItem('奖项','收入','支出','库存')}

                <CommonFlatList
                    style={{ flex: 1, width: '100%' }}
                    data={mdList}
                    enableLoadMore={true}
                    enableLoadMore={false}
                    keyExtractor={(item, index) => {
                        return 'tab' + 1 + 'coupon' + index;
                    }}
                    renderItem={({item,index})=>{
                     return this.newRenderItem(item.scanName,item.couponEnterNum,item.couponOutNum,item.couponAvailableNum);
                    }}
                    enableRefresh={false}
                >
                    <View style={[{ flex: 1, justifyContent: 'center', alignItems: 'center', }]}>
                        <Text style={{ fontSize: 16, color: Constant.colorTxtContent, marginTop: Constant.scale(25) }}>暂无数据</Text>
                    </View>
                </CommonFlatList>

            </View>
        );
    }

    
    renderRecordItem = ({ item, index }) => {
        return (
            <View style={{
                width: '100%', height: Constant.scale(63),
                backgroundColor: Constant.colorDefault,
                borderBottomWidth: index == this.props.listParamsArray[1].data.length - 1 ? 0 : Constant.sizeDividerNormal, borderColor: Constant.colorDividerDefault,
                flexDirection: 'row', alignItems: 'center'
            }}>
                <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent, marginHorizontal: Constant.scale(15), flex: 3.5, lineHeight: Constant.scale(20) }} numberOfLines={3}>{item.couponName + (item.prizeContent ? ('（' + item.prizeContent + '）') : '')}</Text>
                <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent, textAlign: 'center', flex: 2 }}>{REASON[item.reason] && REASON[item.reason].title}</Text>
                <View style={{ alignItems: 'flex-end', justifyContent: 'center', marginRight: Constant.scale(15), flex: 2.5 }}>
                    <Text style={{ fontSize: Constant.fontSizeBig, color: Constant.colorTxtTitle }}>{(REASON[item.reason] && REASON[item.reason].isPlus ? '+' : '-') + item.quantity}个</Text>
                    <Text style={{ fontSize: Constant.fontSizeXSmall, color: Constant.colorTxtAlert }}>{item.createTime}</Text>
                </View>
            </View>
        );
    }
    

    //渲染
    render() {

        return (
            <Header style={styles.container} title="我的奖券">
                <ScrollableTabView
                    onChangeTab={(obj) => {
                        this.props.couponStore.setTabIndex(obj.i);
                        //自动加载一次
                        if (obj.i == 1 && !this.hasLoadRecords) {
                            this.getCouponList(1, false);
                            this.hasLoadRecords = true;
                        }
                    }}
                    style={{ paddingBottom: Constant.sizeDividerNormal, backgroundColor: Constant.colorBackgroundDefault, }}
                    scrollWithoutAnimation={true}
                    renderTabBar={() => {
                        return (
                            <DefaultTabBar
                                activeTextColor={Constant.colorTxtPrimary}
                            />
                        )
                    }}>

                    {this.renderCoupons()}
                    {this.renderRecords()}
                    {this.newRenderRecords()}

                </ScrollableTabView>

            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    }
});