import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    Alert,
    Platform
} from 'react-native';
import PropType from 'prop-types';
import { ReactNavComponent, Widget } from 'rn-yunxi';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import { inject, observer } from 'mobx-react/native';
import AppWidget from '../../app-widget';
const { width, height } = Dimensions.get('window');
const { Header, Image, DefaultTabBar } = AppWidget;
const { CommonFlatList, Text } = Widget;
import VerificationListItem from './VerificationListItem';
import { toJS } from 'mobx';
import ComfirmDeliver from './ComfirmDeliver';


/**
 * 门店核销
 */

@inject(stores => ({
    verificationList: stores.verificationList,
    isDealer: stores.user.isDealer()
}))
@observer
export default class VerificationList extends Component {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {
        };

    }

    componentWillMount() {
        Platform.OS === 'ios' && this.props.verificationList.getVerificationList(0, false);
    }

    componentWillUnmount() {
        this.props.verificationList.clearVerificationList();
    }

    renderRow = ({ item, index }) => {
        return (
            <VerificationListItem
                showAll={() => {
                    this.props.verificationList.showAll(this.props.verificationList.tabIndex, index);
                }}
                showComfirmDeliver={(data) => { this.comfirmDeliver.show(data); }}
                data={item} />
        );
    }

    renderList = () => {
        const list = this.props.verificationList.getTabs;
        return (
            list.map((item, tabIndex) => {

                return (
                    <View tabLabel={item.label} key={`verfication_list${tabIndex}`} style={{ flex: 1 }}>
                        <CommonFlatList
                            style={{ flex: 1 }}
                            data={this.props.verificationList.listParamsArray[tabIndex].data}
                            listState={this.props.verificationList.listParamsArray[tabIndex].listState}
                            enableLoadMore={this.props.verificationList.listParamsArray[tabIndex].enableLoadMore}
                            onLoadMore={() => {
                                this.props.verificationList.getVerificationList(tabIndex, true);
                            }}
                            keyExtractor={(item, index) => {
                                return 'tab' + tabIndex + 'goods' + index;
                            }}
                            renderItem={this.renderRow}
                            enableRefresh={false}
                        >
                            <View style={[{ flex: 1, justifyContent: 'center', alignItems: 'center', }]}>

                                <Text style={{ fontSize: Constant.fontSizeBig, color: Constant.colorTxtContent, marginTop: Constant.scale(25) }}>暂无数据</Text>
                            </View>
                        </CommonFlatList>
                    </View>
                )
            })
        );
    }

    //渲染
    render() {

        return (
            <Header style={styles.container} title={this.props.isDealer ? '分销商核销' : "门店核销"}>
                <ScrollableTabView
                    onChangeTab={(obj) => {
                        this.props.verificationList.setTabIndex(obj.i)
                        this.props.verificationList.getVerificationList(obj.i, false);
                    }}
                    style={{ paddingBottom: Constant.sizeDividerNormal, backgroundColor: Constant.colorBackgroundDefault, }}
                    scrollWithoutAnimation={true}
                    renderTabBar={() => {
                        return (
                            <DefaultTabBar
                                activeTextColor={Constant.colorTxtPrimary}
                            />
                        )
                    }}>

                    {this.renderList()}

                </ScrollableTabView>

                <ComfirmDeliver ref={(ref) => this.comfirmDeliver = ref} verificationList={this.props.verificationList} />

            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    }
});