import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    ScrollView,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import { ReactNavComponent, Widget } from 'rn-yunxi';
const { width, height } = Dimensions.get('window');
import AppWidget from '../../app-widget';
const { Header, QuantitySelector, Image, SelectPop, ArrowRotate } = AppWidget;
const { Text } = Widget;
import { inject, observer } from 'mobx-react/native';
import ToastUtil from '../../util/ToastUtil'
import { toJS } from 'mobx';
import CouponTransfer from '../../store/CouponTransfer';

/**
 * 申请转入
 */

@inject(stores => ({
    user: stores.user,
}))
@observer
export default class ApplyTransfer extends Component {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.orgList = this.props.user.allTakeDeliveryOrgList;
        this.orgId = this.orgList && this.orgList.length > 0 ? this.orgList[0].orgId : '';
        this.state = {
            organizationTitle: this.orgList && this.orgList.length > 0 ? this.orgList[0].orgName : '',
            btnTitle: '全部申请',
        };

        this.couponTransferStore = new CouponTransfer();
    }

    componentWillMount() {
        this.getCouponsByOrgId();
    }

    getCouponsByOrgId = () => {
        if (this.orgId) {
            this.couponTransferStore.getCouponsByOrgId(this.orgId).then().catch(err => { });
        }
    }

    quantityChange = (index, value) => {
        this.couponTransferStore.changeChooseQuantity(index, value);
        if (this.state.btnTitle == '全部申请') {
            this.setState({ btnTitle: '立即申请' });
        }
    }

    /**
     * 选择组织
     */
    selectOrganization = () => {
        this.arrowRotate.rorateUp();
        Widget.Popup.show(<SelectPop
            listData={this.orgList}
            selectId={this.orgId}
            popTitle={'转入组织选择'}
            labelName={'orgName'}
            selectKey={'orgId'}
            selectCallBack={(selectData) => {
                Widget.Popup.hide();
                this.arrowRotate.rorateDown();

                this.orgId = selectData.orgId;
                InteractionManager.runAfterInteractions(() => {
                    this.setState({ organizationTitle: selectData.orgName }, () => {
                        this.getCouponsByOrgId();
                    });
                })
            }}

        />,
            {
                animationType: 'slide-up', backgroundColor: '#00000000',
                onMaskClose: () => {
                    Widget.Popup.hide();
                    this.arrowRotate.rorateDown();
                }
            })
    };

    //提交申请
    apply = () => {
        let list = toJS(this.couponTransferStore.couponList);

        if (list && list.length > 0) {
            let applyList = [];
            for (let item of list) {
                if (item.chooseQuantity > 0 || (item.mayApply > 0 && this.state.btnTitle == '全部申请')) {
                    item.outNumber = item.chooseQuantity > 0 ? item.chooseQuantity : item.mayApply;
                    applyList.push(item);
                }
            }
            if (applyList.length <= 0) {
                ToastUtil.show('申请数量不能为0，请检查');
                return;
            }
            const params = {
                orgId: this.orgId,
                detail: applyList
            };
            this.couponTransferStore.applyTransfer(params).then(() => {
                ToastUtil.show('申请成功')
                this.props.navigation.replace('MyTransfers');
            }).catch(err => { })
        }
    }

    renderList() {
        const list = this.couponTransferStore.couponList;

        return (
            list && list.map((item, index) => {
                return <View key={index} style={{ marginBottom: Constant.scale(10), backgroundColor: Constant.colorDefault }}>
                    <View style={styles.item}>
                        <Text style={{ color: Constant.colorTxtDefault, fontSize: Constant.fontSizeNormal }}>
                            产品编码：<Text style={{ color: Constant.colorTxtContent, fontSize: Constant.fontSizeNormal }}>{item.code}</Text>
                        </Text>
                    </View>
                    <View style={styles.item}>
                        <Text style={{ color: Constant.colorTxtDefault, fontSize: Constant.fontSizeNormal }}>
                            奖券描述：<Text style={{ color: Constant.colorTxtContent, fontSize: Constant.fontSizeNormal }}>{item.description}</Text>
                        </Text>
                    </View>
                    <View style={[styles.item, { height: Constant.scale(80) }]}>
                        <Text style={{ color: Constant.colorTxtDefault, fontSize: Constant.fontSizeNormal }}>
                            奖券总数(个)：<Text style={{ color: '#DA3746', fontSize: Constant.fontSizeSmall }}>{item.amount}</Text>
                        </Text>
                        <Text style={{ color: Constant.colorTxtDefault, fontSize: Constant.fontSizeNormal, marginTop: Constant.scale(12) }}>
                            占用(个)：<Text style={{ color: '#DA3746', fontSize: Constant.fontSizeSmall }}>{item.occupation}</Text>
                        </Text>
                    </View>
                    <View style={styles.item}>
                        <Text style={{ color: Constant.colorTxtDefault, fontSize: Constant.fontSizeNormal }}>
                            可申请数(个)：<Text style={{ color: '#DA3746', fontSize: Constant.fontSizeSmall }}>{item.mayApply}</Text>
                        </Text>
                    </View>
                    <View style={{
                        height: Constant.scale(44),
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        paddingHorizontal: Constant.scale(15)
                    }}>
                        <Text style={{ color: Constant.colorTxtAlert, fontSize: Constant.fontSizeNormal }}>申请数值(个)</Text>
                        <QuantitySelector value={item.chooseQuantity} min={0} max={item.mayApply} onChange={(value) => { this.quantityChange(index, value); }} />
                    </View>
                </View>
            })
        );
    }

    //渲染
    render() {
        const list = this.couponTransferStore.couponList;
        const { organizationTitle, btnTitle } = this.state;

        return (
            <Header containerStyles={styles.container} title="申请转入">

                <View style={{
                    width: width,
                    height: Constant.scale(43),
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingHorizontal: Constant.scale(15),
                    justifyContent: 'space-between'
                }}>
                    <Text style={{ color: Constant.colorTxtContent, fontSize: Constant.fontSizeSmall }}>转入组织</Text>

                    <TouchableOpacity onPress={this.selectOrganization} style={{ flexDirection: 'row', alignItems: 'center', marginLeft: Constant.scale(120) }}>
                        <Text style={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent }}>{organizationTitle}</Text>
                        <ArrowRotate ref={(ref) => { this.arrowRotate = ref; }} style={{ marginLeft: Constant.scale(4) }} />
                    </TouchableOpacity>
                </View>
                {list && list.length > 0 ?
                    <ScrollView style={{ flex: 1, width: '100%' }}>
                        {
                            this.renderList()
                        }
                    </ScrollView> :
                    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                        <Text style={{ fontSize: 16, color: Constant.colorTxtContent }}>暂无数据</Text>
                    </View>}

                {list && list.length > 0 && <TouchableOpacity style={[{
                    marginTop: Constant.sizeMarginDefault,
                    borderRadius: Constant.scale(4),
                    height: Constant.scale(44), width: '94%',
                    backgroundColor: Constant.colorPrimary,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: this.isTab ? Constant.scale(41.5) : Constant.scale(15)
                }]} onPress={this.apply}>
                    <Text style={{ color: 'white', fontSize: Constant.fontSizeBig }}>{btnTitle}</Text>
                </TouchableOpacity>}
            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center'
    },
    item: {
        height: Constant.scale(40),
        marginLeft: Constant.scale(15),
        borderBottomWidth: Constant.sizeDividerNormal,
        borderColor: Constant.colorDividerDefault,
        justifyContent: 'center',
    }
});