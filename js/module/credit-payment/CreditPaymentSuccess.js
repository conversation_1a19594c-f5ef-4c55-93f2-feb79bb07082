/**
 * Created by lao.jian<PERSON> on 2017/6/13.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Platform,
    BackHandler,
    ScrollView,
    Image,
    Dimensions,
    Linking,
    InteractionManager
} from 'react-native';
import {NavigationActions} from 'react-navigation';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget';
import {inject, observer} from 'mobx-react/native';
import _ from 'lodash';
import PropTypes from 'prop-types';
import ToastUtil from '../../util/ToastUtil';
import { action, runInAction } from 'mobx';
import Api from '../../util/Api';

const {width, height} = Dimensions.get('window');
const {Text, LabelCell, Button} = Widget;
const {Header} = AppWidget;
//模块声名并导出
const backHandlerFunc = function () {
    return true;
};
//赊销支付
@inject(stores => ({
    pay: stores.pay,
    user:stores.user,
}))
@observer
export default class CreditPaymentSuccess extends ReactNavComponent {
    //禁止侧滑
    static navigationOptions = ({navigation}) => ({
        gesturesEnabled: false
    });
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        let {params}=this.getNavState();
        this.state = {//状态机变量声明
            orderNo: params && params.orderNo,
        };
        this.canDownload =  this.state.orderNo !=null && this.state.orderNo !='';
        
    }

    componentWillMount() {
        if (Platform.OS === 'android') {
            BackHandler.addEventListener('hardwareBackPress', backHandlerFunc)
        }
    }

    componentDidMount() {

    }


    componentWillUnmount() {
        if (Platform.OS === 'android') {
            BackHandler.removeEventListener('hardwareBackPress', backHandlerFunc)
        }
    }


    //渲染
    render() {
        return (
            <Header title={"提交成功"} showBackAction={false}>
                <ScrollView
                    style={styles.scrollView}
                >
                    <View style={styles.container}>
                        <View style={styles.topContent}>
                            <Text style={{
                                marginLeft: Constant.sizeMarginDefault,
                                marginRight: Constant.sizeMarginDefault,
                                fontSize:Constant.scale(17),
                                color:'black'
                            }}>提交成功，请下载欠款单，签名盖章后，去订单列表上传</Text>
                    </View>
                    {this.canDownload ?
                            <TouchableOpacity style={[styles.checkOrderBtn, { backgroundColor: Constant.colorPrimary }]} onPress={() => {
                             this.download(this.state.orderNo);
                            }}><Text style={{ color: 'white' }}>下载欠款单</Text>
                            </TouchableOpacity> :
                            <View style={[styles.checkOrderBtn, { backgroundColor: 'gray' }]} onPress={() => {
                            }}><Text style={{ color: 'white' }}>下载欠款单</Text>
                            </View>
                        }
                        
                    <TouchableOpacity style={styles.checkOrderBtn1} onPress={() => {
                        let resetAction = NavigationActions.reset({
                            index: 1,
                            actions: [
                                NavigationActions.navigate({routeName: 'Tab'}),
                                NavigationActions.navigate({
                                    routeName: 'OrderList',
                                    params: {type:1}
                                }),
                            ]
                        });
                        this.dispatch(resetAction)
                    }}><Text style={{color: 'black'}}>查看订单</Text></TouchableOpacity>
                </View>


            </ScrollView>
    </Header>
    )
        ;
    }
    @action
    download(orderNo){
        Api.checkArrearage(orderNo).then((result)=>{
        let link = Config.HOST_TRADE + `/api/v1/pearlriver/trade/order/export/arrearage/info?orderNo=${orderNo}`;
        Linking.canOpenURL(link).then(supported => {         
                if (!supported) {            
                    console.warn('Can\'t handle url: ' + link);            
                } else {            
                    return Linking.openURL(link);            
                }            
            }).catch(err => console.error('An error occurred',link));  
        }).catch();
    }
};

const styles = StyleSheet.create({
    scrollView: {
        height: height,
        backgroundColor: 'white',
    },
    container: {
        flex: 1,
        alignItems: 'center',
        backgroundColor: 'white',
    },
    topContent: {
        marginTop: 60,
        alignItems: 'center',
    },
    orderOK: {
        width: 70,
        height: 70,
        marginBottom: 20,
    },
    highLightTxt: {
        color: Constant.colorTxtPrimary,
    },
    payTxt: {
        color: Constant.colorTxtContent,
    },
    payContent: {
        marginBottom: 30,
        flexDirection: 'row',
        height: 40,
        marginTop: 30,
        justifyContent: 'center',
        alignItems: 'center'
    },
    payContentItem: {
        flexDirection: 'row',
        height: 40,
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center'
    },
    verticalDivider: {
        height: 20,
        backgroundColor: Constant.colorDivider,
        width: 1,
    },
    checkOrderBtn: {
        marginTop: Constant.scale(40),
        height: Constant.scale(40),
        width: Constant.scale(180),
        borderRadius: 5,
        justifyContent: 'center',
        alignItems: 'center',
    },
    checkOrderBtn1: {
        marginTop: Constant.scale(20),
        height: Constant.scale(40),
        width: Constant.scale(180),
        borderRadius: 5,
        borderWidth: Constant.sizeDividerNormal,
        borderColor: '#E1E1E1',
        justifyContent: 'center',
        alignItems: 'center',
    },
    goHomeBtn: {
        marginTop: 10,
        height: 35,
        width: 86,
    },
    goHomeImg: {
        width: 14,
        height: 14,
    },
    tipView: {
        padding: 20,
        height: 160,
    },
    tipTitle: {
        flexDirection: 'row',
        marginBottom: Constant.sizeMarginDefault
    },
    tipImage: {
        marginRight: 5,
        height: 16,
        width: 16
    }
});
