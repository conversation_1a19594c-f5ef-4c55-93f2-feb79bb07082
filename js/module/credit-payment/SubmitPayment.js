/**
 * Created by lao<PERSON>ji<PERSON><PERSON> on 2017/6/13.
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Platform,
    ImageBackground,
    BackHandler,
    ScrollView,
    Image,
    Dimensions,
    InteractionManager
} from 'react-native';
import { NavigationActions } from 'react-navigation';
import { ReactNavComponent, Widget,AliYunOSS } from 'rn-yunxi';
import AppWidget from '../../app-widget';
import { inject, observer } from 'mobx-react/native';
import _ from 'lodash';
import PropTypes from 'prop-types';
import ToastUtil from '../../util/ToastUtil';
const IC_BANNER = require('../img/order/ic_banner.png');
const IC_STATUS_CHECK = require('../img/order/ic_status_check.png');
const ICON_ORDER_OK = require('../img/pay/payok.png');

const { width, height } = Dimensions.get('window');
const { Text, LabelCell, Button } = Widget;
const { Header, UploadImagePicker } = AppWidget;
import SubmitPaymentStore from '../../store/SubmitPayment';
import ZoomModel from '../../app-widget/zoom-model';
import Api from '../../util/Api';
import { action, runInAction } from 'mobx';

//模块声名并导出
const backHandlerFunc = function () {
    return true;
};

const uploadProgress = p =>console.log(p.currentSize / p.totalSize); 
//赊销支付
@observer
export default class SubmitPayment extends ReactNavComponent {

    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        let { params } = this.getNavState();
        this.state = {//状态机变量声明
            list: [
                ICON_ORDER_OK,
                IC_STATUS_CHECK,
                ICON_ORDER_OK,
                IC_STATUS_CHECK,
                ICON_ORDER_OK,
                IC_STATUS_CHECK,
            ],
            orderId: params && params.orderId,
            orderType: params && params.orderType,
            orderNo: params && params.orderNo,
            arrearageNo: params && params.arrearageNo,
            uploadFileStatus: params && params.uploadFileStatus,
            images:[],
            pdfs:[],

        };
        this.UploadImagePicker = null;
        this.zoomView = null;
        this.submitPayment = new SubmitPaymentStore();
        this.orderTypeText = this.state.orderType && this.getOrderType(this.state.orderType);
        this.isSumbited = this.state.uploadFileStatus != 0;
        this.canEdit = this.state.uploadFileStatus != 1;//已提交不能修改图片,不能下载欠款单

    }

    componentWillMount() {
        if (this.isSumbited) {
            this.submitPayment.chargeSalesInfo(this.state.orderId,(images,pdfs)=>{
                this.setState({images:images,pdfs:pdfs});
                if(this.UploadImagePicker !=null){
                    this.UploadImagePicker.setHttpImages(images);
                }
                
            })
        }
    }

    componentDidMount() {
        // 增加上传事件监听
        AliYunOSS.addEventListener('uploadProgress', uploadProgress);
    }


    componentWillUnmount() {
        AliYunOSS.removeEventListener('uploadProgress', uploadProgress);
    }
    getOrderType(orderType) {
        //订单类型 : 1标准订单 2海外订单 3费用赠酒 4瓶盖赠酒 (默认是标准订单)
        switch (orderType) {
            case 2: return "海外订单";
            case 3: return "费用赠酒";
            case 4: return "瓶盖赠酒";
            default: return "标准订单";
        }
    }
    @action
    getRejectReason(uploadFileStatus){
        let uploadFileStatusText = ''
        let rejectReason = this.submitPayment.rejectReason;
        if (uploadFileStatus == 1) {
            uploadFileStatusText = '提交成功，请等待财务审核通过';
        } else if (uploadFileStatus == 2) {
            if (rejectReason && rejectReason != '') {
                uploadFileStatusText = '已驳回,' + rejectReason;
            } else {
                uploadFileStatusText = '已驳回';
            }
        } else {
            uploadFileStatusText = '待提交';
        }
        return uploadFileStatusText;
    }
    renderTop() {
        let { arrearageNo, uploadFileStatus } = this.state;
        let uploadFileStatusText = this.getRejectReason(uploadFileStatus);
        return <ImageBackground style={{
            width: width,
            height: Constant.scale(80),
            flexDirection: 'row',
            alignItems: 'center'
        }} source={IC_BANNER}>
            <Image style={{
                width: Constant.scale(50),
                height: Constant.scale(50),
                marginLeft: Constant.scale(33.5)
            }}
                source={IC_STATUS_CHECK} />
            <View style={{ flex: 1, marginLeft: Constant.scale(30) }}>
                <Text style={{ fontSize: Constant.fontSizeNormal, color: '#fff' }}>
                    {uploadFileStatusText}
                </Text>
                <Text style={{ fontSize: Constant.fontSizeNormal, color: '#fff', marginTop: Constant.scale(5) }}>
                    {this.orderTypeText}
                </Text>
                <Text style={{
                    fontSize: Constant.fontSizeNormal,
                    color: '#fff',
                    marginTop: Constant.scale(5)
                }}>
                    欠款单号:{arrearageNo}
                </Text>
            </View>
        </ImageBackground>
    }

    renderStep() {
        let canDownload = this.submitPayment.canDownload && this.canEdit;
        return <View>
            <Text style={styles.stepText}>
                步骤1：下载欠款单
            </Text>
            {canDownload ?
                <TouchableOpacity style={[styles.checkOrderBtn, { backgroundColor: Constant.colorPrimary },]} onPress={() => {
                    this.submitPayment.getArrearageInfo(this.state.orderNo,null);
                }}><Text style={{ color: 'white' }}>下载欠款单</Text>
                </TouchableOpacity> :
                <View style={[styles.checkOrderBtn, { backgroundColor: 'gray' },]} onPress={() => {
                }}><Text style={{ color: 'white' }}>下载欠款单</Text>
                </View>
            }


            <Text style={styles.stepText}>
                步骤2：欠款单请盖章签名，填写完整后提交审核
            </Text>
            <View style={{ flexDirection: 'row' }}>
                <Text style={styles.stepText}>
                    欠款单：
                </Text>
                {this.renderImage()}
            </View>

            {this.state.pdfs.length > 0 && this.renderPDF()}

        </View>
    }
    /**
     * 获取上传的附件
     * */
    @action
    getFileList(uploadFile) {
        if (!uploadFile && uploadFile.length <= 0) return null;
        let fileList = [];
        let localList = this.UploadImagePicker.getLocalImages();
        let imagesMap = this.submitPayment.imagesMap;
        let httplist = this.UploadImagePicker.getHttpImages();
        uploadFile.map((url, index) => {
            try {
                let fileName = '';
                if(imagesMap && imagesMap.size>0 && imagesMap.get(url) !=null){
                    fileName = imagesMap.get(url);
                }else{
                    let fileNames = localList[index-httplist.length].path.split("/");
                    fileName = fileNames[fileNames.length -1];
                }
                let imageurlParams = {
                    fileName: fileName,
                    fileUrl: url,
                };
                fileList.push(imageurlParams);
            } catch (error) {
            }
        })
        return fileList;
    }
    renderImage() {
        let showImages = this.state.images;
        return <View style={{ marginTop: Constant.sizeMarginDefault }}>
            {this.canEdit || (showImages && showImages.length > 0) ?
                <UploadImagePicker
                    enable={this.canEdit}
                    maxItemLength={3}
                    getUploadData={Api.uploadImage}
                    reLoad={() => {
                        Log('是否调用reload');
                        let list = this.UploadImagePicker.getAllUploadPath();
                        runInAction(()=>{
                            this.setState({images:list})
                        })
                    }}
                    onImageClick={(item, index) => {
                        this.zoomView && this.zoomView.showModel(index)
                    }}
                    multiple={true}
                    ref={(ref) => {
                        this.UploadImagePicker = ref;
                    }}
                    uploadValidate={imageList => {
                        if (imageList && imageList.length > 0) {
                            let list = this.UploadImagePicker.getLocalImages();
                            let httplist = this.UploadImagePicker.getHttpImages();
                            if (list.length + httplist.length+ imageList.length > 3) {
                                ToastUtil.show("上传图片不允许超过三张");
                                return true;
                            }
                            for (let imageBean of imageList) {
                                //{"path":"/Users/<USER>/Library/Developer/CoreSimulator/Devices/37A2ED6E-A181-4DCC-81AD-F105397CE3D4/data/Containers/Data/Application/6BF2BB4E-C6BA-414B-B9F9-540EBC0EA276/tmp/react-native-image-crop-picker/CE9A733A-A647-47E5-A3BB-5EE8DA01AA7D.jpg","size":6103836,"data":null,"mime":"image/jpeg","width":4288,"height":2848}
                                if (imageBean != null) {
                                    let imagePath = imageBean.path.toLowerCase();
                                    if (imagePath.lastIndexOf(".jpg") < 0 && imagePath.lastIndexOf(".png") < 0) {
                                        ToastUtil.show("只允许上传JPG、PNG格式的文件");
                                        return true;
                                    }
                                    if (imageBean.size > 10 * 1024 * 1024) {
                                        ToastUtil.show("不允许上传超过10M的图片");
                                        return true;
                                    }
                                }
                            }
                        }
                        return false;
                    }}
                /> : null
            }

            {showImages && showImages.length > 0 ?
                <ZoomModel
                    images={showImages}
                    ref={(zoomView) => {
                        this.zoomView = zoomView;
                    }}
                /> : null
            }
            <Text style={{ fontSize: Constant.fontSizeNormal, color: 'black', marginTop: Constant.sizeMarginDefault }}>
                ●  可上传JPG、PNG格式
            </Text>
            <Text style={{ fontSize: Constant.fontSizeNormal, color: 'black', marginTop: Constant.sizeMarginDefault }}>
                ●   图片不超过10M，保证图片清晰
            </Text>
        </View>
    }
    renderPDF() {
        return <View >
            <Text style={styles.stepText}>
                已上传PDF文件
            </Text>
            {this.state.pdfs.map((item, index) => {
                return <TouchableOpacity
                    key={index}
                    style={{
                        marginTop: Constant.scale(10),
                        padding: Constant.scale(10),
                        marginLeft: Constant.sizeMarginDefault,
                        alignItems: 'center',
                        borderRadius: 5,
                        backgroundColor: Constant.colorPrimary,
                        width: Constant.scale(250),
                    }} onPress={() => {
                        this.submitPayment.getArrearageInfo(null,item.fileUrl);
                    }}><Text style={{ color: 'white' }}>{item.fileName}</Text>
                </TouchableOpacity>
            })}
        </View>
    }
    submit() {
        let fileUrlList = this.state.images;
        if (!fileUrlList || fileUrlList.length <= 0) {
            ToastUtil.show('请先上传欠款单图片');
            return;
        }
        let fileList = this.getFileList(fileUrlList);
        this.submitPayment.uploadFile(this.state.orderId, this.state.arrearageNo, fileList, () => {
            ToastUtil.show('提交成功，请等待财务审核通过');

            setTimeout(() => {
                this.navigate('OrderList')
            }, 500)
            // let { params } = this.getNavState();
            // // let callback = params && params.callback;
            // // callback && callback();
            // Alert.alert('chung', JSON.stringify(params))
            // this.goBack();
        });
    }

    //渲染
    render() {
        return (
            <Header title={"提交欠款单"} >
                <ScrollView
                    style={{ flex: 1, backgroundColor: Constant.colorBackgroundDefault }}
                >
                    {this.renderTop()}
                    {this.renderStep()}
                </ScrollView>
                {this.canEdit ? <TouchableOpacity
                    style={styles.submitBtn}
                    onPress={() => this.submit()}
                >
                    <Text
                        style={{
                            color: 'white',
                            fontSize: Constant.fontSizeBig,
                        }}
                    >提交</Text>
                </TouchableOpacity> : null
                }
            </Header>
        )
            ;
    }
};

const styles = StyleSheet.create({
    scrollView: {
        height: height,
        backgroundColor: 'white',
    },
    container: {
        flex: 1,
        alignItems: 'center',
        backgroundColor: 'white',
    },
    checkOrderBtn: {
        marginTop: Constant.scale(10),
        padding: Constant.scale(10),
        width: Constant.scale(150),
        marginLeft: Constant.sizeMarginDefault,
        alignItems: 'center',
        backgroundColor: Constant.colorPrimary,
        borderRadius: 5,
    },
    submitBtn: {
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: Constant.colorPrimary,
        height: Constant.scale(50)
    },
    stepText: {
        marginTop: Constant.sizeMarginDefault,
        paddingLeft: Constant.sizeMarginDefault,
        fontSize: Constant.fontSizeCaption,
        color: 'black'
    }
});
