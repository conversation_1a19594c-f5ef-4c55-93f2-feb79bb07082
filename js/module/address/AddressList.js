import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    Dimensions
} from 'react-native';
import PropType from 'prop-types';
import {inject, observer} from 'mobx-react/native';
import {toJS} from 'mobx';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget';

const {width, height} = Dimensions.get('window');
const {Header,CommonSearchBar} = AppWidget;
const {CommonFlatList, Text} = Widget;

/**
 * 收货地址
 */
@inject(stores => ({
    address: stores.address,
}))
@observer
export default class AddressList extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
        };
    }

    componentWillMount() {
        this.props.address.obtainAddressList(false).then().catch();
    }

    componentDidMount() {
    }

    componentWillUnmount() {
    }


    renderItem=({item, index})=> {
        let {params}=this.getNavState();
        return (
            <TouchableOpacity
                style={{backgroundColor: 'white',padding:Constant.sizeMarginDefault}}
                onPress={() => {
                    if(params&&params.callBack) {
                        params.callBack(item);
                        this.goBack();
                    }
                }}
            >
                <View style={{flexDirection: 'row', flex: 1}}>

                    <View style={{flex: 1, alignSelf: 'center',}}>
                        <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                            <Text style={[addressStyles.nameText, {fontSize: Constant.fontSizeBig}]}>
                                {item.contactLevel} {item.contactMobile}
                            </Text>
                            <Text style={{
                                height: Constant.scale(18),
                                textAlign: 'center',
                                paddingTop: Constant.scale(1),
                                paddingBottom: Constant.scale(1),
                                paddingLeft: Constant.scale(1.5),
                                paddingRight: Constant.scale(1.5),
                                backgroundColor: Constant.colorPrimary,
                                color: 'white',
                                fontSize: Constant.fontSizeSmall,
                                borderRadius: Constant.scale(3),
                            }}>{item.abbreviation}</Text>
                        </View>

                        <View style={{flexDirection: 'row', marginTop: 5}}>

                            <Text
                                numberOfLines={2}
                                style={[addressStyles.addressText, {fontSize: Constant.fontSizeSmall}]}
                            >{item.address}</Text>
                        </View>
                    </View>

                </View>

            </TouchableOpacity>
        )
    }

    keyExtractor = (item, index) => {
        return 'addressList' + index;
    };
    /**
     * 列表分隔线
     * @return {XML}
     */
    renderSeparator = () => {
        return (
            <View style={{height: Constant.sizeMarginDefault, backgroundColor: Constant.colorBackgroundDefault}}/>
        );
    };

    //渲染
    render() {
        let {listParams} = this.props.address;
        let searchAlpha = "rgba(255,255,255,1 )";
        return (
            <Header style={styles.container} title={'地址列表'}>
                <View style={{ flexDirection: "row", alignItems: "center", height:Constant.scale(40) }}>
                <CommonSearchBar
                    style={{
                    flex: 1,
                    margin: 0,
                    marginLeft: Constant.sizeMarginDefault,
                    marginRight: Constant.sizeMarginDefault / 2
                    }}
                    searchBarTxtInputStyle={{ backgroundColor: searchAlpha }}
                    cancelBtnStyle={{color:Constant.colorPrimary}}
                    placeholder={'搜索地址'}
                    placeholderTextColor={Constant.colorTxtAlert}
                    onSearchBtnClick = {()=>this.props.address.obtainAddressList(false)}
                    onChange={(text)=>this.props.address.setSearchAddress(text)}
                    // cancelClick={() => this.cancelClick()}
                    text={this.props.address.searchAddress}
                    clear={()=>this.props.address.clearSearchAddress()}
                    onFocus={()=>this.props.address.setSearchAddress(this.props.address.searchAddress)}
                    isOnFocus={false}
                />
                </View>
                <CommonFlatList
                    ItemSeparatorComponent={this.renderSeparator}
                    onRefresh={() => {
                        this.props.address.obtainAddressList(false).then().catch();
                    }}
                    onLoadMore={(info) => {
                        if(info.distanceFromEnd>0) {
                            this.props.address.obtainAddressList(true).then().catch();
                        }
                    }}
                    initialNumToRender={6}
                    keyExtractor={this.keyExtractor}
                    renderItem={this.renderItem}
                    data={listParams.dataArray}
                    enableLoadMore={listParams.enableLoadMore}
                    enableRefresh={listParams.enableRefresh}
                    listState={listParams.listState}
                    extraData={this.props}
                    style={{flex: 1}}
                >
                    <View style={[{flex: 1, justifyContent: 'center', alignItems: 'center',}]}>

                        <Text style={{fontSize: Constant.fontSizeBig, color: Constant.colorTxtContent, marginTop: Constant.scale(25)}}>
                            暂无数据
                        </Text>
                    </View>
                </CommonFlatList>
            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    }
});
const addressStyles = StyleSheet.create({
    background: {
        backgroundColor: 'white',
        width: width,
        minHeight: Constant.scale(100),
        paddingLeft: Constant.sizeMarginDefault,


        marginBottom: Constant.sizeMarginDefault,
    },
    nameText: {
        color: Constant.colorTxtContent,
        fontSize: Constant.fontSizeBig,
    },
    addressText: {
        color: Constant.colorTxtContent,
        fontSize: Constant.fontSizeNormal,

    }
});