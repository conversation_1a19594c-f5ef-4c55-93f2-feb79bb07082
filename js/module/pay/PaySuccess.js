/**
 * Created by lao<PERSON>jian<PERSON> on 2017/6/13.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Platform,
    BackHandler,
    ScrollView,
    Image,
    Dimensions,
    InteractionManager
} from 'react-native';
import {NavigationActions} from 'react-navigation';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget';
import {inject, observer} from 'mobx-react/native';
import _ from 'lodash';
import PropTypes from 'prop-types';

const {width, height} = Dimensions.get('window');
const {Text, LabelCell, Button} = Widget;
const {Header} = AppWidget;
const ICON_ORDER_OK = require('../img/pay/payok.png');
//模块声名并导出
const backHandlerFunc = function () {
    return true;
};
@inject(stores => ({
    pay: stores.pay,
    user:stores.user,
}))
@observer
export default class PaySuccess extends ReactNavComponent {
    //禁止侧滑
    static navigationOptions = ({navigation}) => ({
        gesturesEnabled: false
    });
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明

        };
    }

    componentWillMount() {
        if (Platform.OS === 'android') {
            BackHandler.addEventListener('hardwareBackPress', backHandlerFunc)
        }
    }

    componentDidMount() {

    }


    componentWillUnmount() {
        if (Platform.OS === 'android') {
            BackHandler.removeEventListener('hardwareBackPress', backHandlerFunc)
        }
    }


    //渲染
    render() {
        let {params} = this.getNavState();
        let orgName = '';
        if (this.props.pay.payInfo && this.props.pay.payInfo.orderCashierDto) {
            orgName = this.props.pay.payInfo.orderCashierDto.supplierOrgName;
        }
        let isTradition = _.get(params, 'isTradition', false);
        return (
            <Header title={"收银台"} showBackAction={false}>
                <ScrollView
                    style={styles.scrollView}
                >
                    <View style={styles.container}>

                        <View style={{
                            width: width,
                            height: Constant.scale(40),
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: Constant.colorBackgroundDefault
                        }}>
                            <Text style={{color: Constant.colorTxtContent}}>{orgName}</Text>
                        </View>
                        <View style={styles.topContent}>
                            <Image source={ICON_ORDER_OK} style={styles.orderOK}/>
                            <Text style={{
                                marginLeft: Constant.sizeMarginDefault,
                                marginRight: Constant.sizeMarginDefault
                            }}>{isTradition ? "订单支付成功!" : "订单支付成功!"}</Text>
                    </View>


                    <TouchableOpacity style={styles.checkOrderBtn} onPress={() => {
                        let resetAction = NavigationActions.reset({
                            index: 1,
                            actions: [
                                NavigationActions.navigate({routeName: 'Tab'}),
                                NavigationActions.navigate({
                                    routeName: 'OrderList',
                                    params: {type:1}
                                }),
                            ]
                        });
                        this.dispatch(resetAction)
                    }}><Text style={{color: 'white'}}>查看订单</Text></TouchableOpacity>
                </View>


            </ScrollView>
    </Header>
    )
        ;
    }
};

const styles = StyleSheet.create({
    scrollView: {
        height: height,
        backgroundColor: 'white',
    },
    container: {
        flex: 1,
        alignItems: 'center',
        backgroundColor: 'white',
    },
    topContent: {
        marginTop: 50,
        alignItems: 'center',
    },
    orderOK: {
        width: 70,
        height: 70,
        marginBottom: 20,
    },
    highLightTxt: {
        color: Constant.colorTxtPrimary,
    },
    payTxt: {
        color: Constant.colorTxtContent,
    },
    payContent: {
        marginBottom: 30,
        flexDirection: 'row',
        height: 40,
        marginTop: 30,
        justifyContent: 'center',
        alignItems: 'center'
    },
    payContentItem: {
        flexDirection: 'row',
        height: 40,
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center'
    },
    verticalDivider: {
        height: 20,
        backgroundColor: Constant.colorDivider,
        width: 1,
    },
    checkOrderBtn: {
        marginTop: Constant.scale(80),
        height: Constant.scale(40),
        width: Constant.scale(180),
        backgroundColor: Constant.colorPrimary,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
    },
    goHomeBtn: {
        marginTop: 10,
        height: 35,
        width: 86,
    },
    goHomeImg: {
        width: 14,
        height: 14,
    },
    tipView: {
        padding: 20,
        height: 160,
    },
    tipTitle: {
        flexDirection: 'row',
        marginBottom: Constant.sizeMarginDefault
    },
    tipImage: {
        marginRight: 5,
        height: 16,
        width: 16
    }
});
