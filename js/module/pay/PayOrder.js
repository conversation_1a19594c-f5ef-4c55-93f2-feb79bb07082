import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    ScrollView,
    Dimensions,
    AppState,
    Alert
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Util, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget';
import _ from 'lodash';
import ToastUtil from '../../util/ToastUtil'
const {width, height} = Dimensions.get('window');
const {Header, Image, CheckBox, PriceText, DefaultTabBar, DateView} = AppWidget;
const {Text, LabelCell, InputFormCell} = Widget;
import {inject, observer} from 'mobx-react/native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
import ValidatorUtil from '../../util/ValidatorUtil';
import { toJS } from 'mobx'
import Api from '../../util/Api';
const Input = ({title, value, onChangeText,maxLength}) => (
    <InputFormCell maxLength={maxLength} title={<Text><Text style={{color: 'red'}}>*</Text>{title}</Text>} value={value}
                   onChangeText={onChangeText}/>
)

/**
 * 订单支付
 */
@inject(stores => ({
    pay: stores.pay,
    user: stores.user,
}))
@observer
export default class PayOrder extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        let {params} = this.getNavState();
        this.state = {//状态机变量声明
            selectIndex:  this.props.user.isRMB() ? -1 : -2,
            // selectIndex:-1,
            orderTotalAmount:0,
            enterType: params && params.enterType ?params.enterType : 0,    //从哪里跳进来的支付   0正常的结算支付  1杂费的结算支付
            transactionData: {
                date: new Date(),  //日期
                name:  '',  //汇款人
                cheque: '',  //汇款金额
                bankName:  '',  //银行
            },

            order: params && params.order,
            isProceed: params && params.isProceed,
            // order:  [ { businessType: 8,
            //     orderIds: [ '1179372418673817609' ],
            //     parentOrderId: '1179372418663331841',
            //     paymentOrderIds: null,
            //     zeroOrderFlag: false } ],


        }

    }


    componentWillMount() {

        let {params} = this.getNavState()
        if (params && params.payInfoData) {
            this.props.pay.handlePayInfo(params.payInfoData)
        } else {
            this.props.pay.cashier(this.state.order).then(data => {
            }).catch(err => {
            })
        }

        this.props.pay.getHistoryNameAndBankName().then((result)=>{
            // Log('========result',toJS(this.props.pay.lastestDataArray[0]))
            this.setState({
                transactionData:{
                    date: new Date(),  //日期
                    name:  this.props.pay && this.props.pay.lastestDataArray.length>0 && this.props.pay.lastestDataArray[0].name,  //汇款人
                    cheque: '',  //汇款金额
                    bankName:this.props.pay && this.props.pay.lastestDataArray.length>0 && this.props.pay.lastestDataArray[0].bankName,  //银行
                }
            })
        }).catch(e=>{})

        this.didFocusSubscription = this.props.navigation.addListener(
            'didFocus',
            payload => {
                console.debug('didFocus');
            }
        );

        this.willFocusSubscription = this.props.navigation.addListener(
            'willFocus',
            payload => {
                console.debug('willFocus');
            }
        );
        // this.appStateSubscription=AppState.addListener("change",(appState)=>{
        //     Log("appState>>"+appState);
        // })

    }

    componentDidMount() {
        this.props.pay.handlePayCode(this.state.selectIndex);
    }

    componentWillUnmount() {
        this.didFocusSubscription.remove();

        this.willFocusSubscription.remove();

        // this.appStateSubscription.remove();
    }

    renderPayItem = (item, index) => {
        let totalAmount = 0;
        let payTransactionAmount = 0;
        totalAmount = this.props.pay.totalAmount;
        payTransactionAmount = _.get(item, 'payTransactionAmount', 0);
        if (payTransactionAmount != null) {
            totalAmount = totalAmount + payTransactionAmount;
        }
        let isTraditional = item.type === 4;
        let Touch = !isTraditional ? TouchableOpacity : View;
        return (
            <Touch key={index} style={{}}
                   onPress={() => {
                       this.setState({selectIndex: index},this.props.pay.handlePayCode(index))
                   }}>
                <View style={GlobalStyle.styleDividerDefault}/>
                <View style={{
                    flexDirection: 'row',
                    marginTop: Constant.sizeMarginDefault,
                    marginBottom: Constant.sizeMarginDefault,
                }}>
                    <Image source={item.icon}
                           resizeMode={'contain'}
                           style={{width: Constant.scale(23), maxHeight: Constant.scale(28)}}
                    />
                    <View style={{marginLeft: Constant.sizeMarginDefault, flex: 1}}>
                        <Text>{item.label}</Text>
                        <Text style={{color: Constant.colorTxtAlert, fontSize: Constant.fontSizeSmall}}>
                            {
                                isTraditional ? '使用预存款 方式支付' :
                                    item.type === 5 ? `赊销金额${this.props.user.getSymbol()}${totalAmount}，需要先与财务沟通` :
                                        <Text style={{color: Constant.colorTxtAlert, fontSize: Constant.fontSizeSmall}}>
                                            应付金额<PriceText size={-1} price={totalAmount}/> &nbsp;
                                            (含<PriceText size={-1} price={payTransactionAmount}/>手续费)
                                        </Text>
                            }
                        </Text>
                        {
                            !isTraditional ? null :
                                <View style={{flexDirection: 'row', marginTop: Constant.sizeMarginDefault}}>
                                    {/* <CheckBox
                                        disabled={false}
                                        isChecked={this.state.selectIndex === -2}
                                        onClick={() => {
                                            this.setState({selectIndex: -2},this.props.pay.handlePayCode(-2))
                                        }}
                                        style={{width: Constant.scale(75)}}
                                        rightTextStyle={{
                                            color: Constant.colorTxtAlert,
                                            fontSize: Constant.fontSizeNormal
                                        }}
                                        rightText={'支票'}/> */}
                                    <CheckBox
                                        disabled={false}
                                        isChecked={this.state.selectIndex === -3}
                                        onClick={() => {
                                            this.setState({selectIndex: -3},this.props.pay.handlePayCode(-3))
                                        }}
                                        style={{width: Constant.scale(90)}}
                                        rightTextStyle={{
                                            color: Constant.colorTxtAlert,
                                            fontSize: Constant.fontSizeNormal
                                        }}
                                        rightText={'预存款'}/>
                                    {/* <CheckBox
                                        disabled={false}
                                        isChecked={this.state.selectIndex === -4}
                                        onClick={() => {
                                            this.setState({selectIndex: -4},this.props.pay.handlePayCode(-4))
                                        }}
                                        style={{width: Constant.scale(120)}}
                                        rightTextStyle={{
                                            color: Constant.colorTxtAlert,
                                            fontSize: Constant.fontSizeNormal
                                        }}
                                        rightText={'银行承兑汇票'}/> */}
                                </View>
                        }

                    </View>
                    {
                        !isTraditional ? <CheckBox
                                isChecked={index === this.state.selectIndex}
                                style={{alignSelf: 'center', marginRight: Constant.sizeMarginDefault}}/> :
                            <Image
                                source={require('../img/arrow/ic_content_close.png')}
                                style={{
                                    width: Constant.scale(10), height: Constant.scale(5.5),
                                    marginTop: Constant.scale(15), marginRight: Constant.sizeMarginDefault

                                }}/>
                    }

                </View>
            </Touch>

        )
    };

    //渲染
    render() {
        let orgName = '';
        if (this.props.pay.payInfo && this.props.pay.payInfo.orderCashierDto) {
            orgName = this.props.pay.payInfo.orderCashierDto.supplierOrgName;
        }
        let isZeroOrder = false;
        if(this.props.pay && this.props.pay.payInfo && this.props.pay.payInfo.payMethodDtos.length>0 && this.props.pay.payInfo.payMethodDtos[0].payCode+'' == '11'){
            isZeroOrder = true;
        }
        let date = this.state.transactionData.date ? Util.DateUtil.formatDate(this.state.transactionData.date.getTime(), 'yyyy/MM/dd') : '年/月/日';
        // Log('=================',this.props.pay.lastPayCode)
        return (
            <Header style={styles.container} title={'支付订单'}>
                <KeyboardAwareScrollView
                    contentContainerStyle={{flex: 1}}
                    scrollEnabled={false}
                    keyboardShouldPersistTaps={'always'}
                    extraHeight={height < 667 ? Constant.scale(18) : Constant.scale(60)}
                >
                    <ScrollView>
                        {

                                <View
                                    style={{
                                        backgroundColor: 'white',
                                        padding: Constant.sizeMarginDefault,
                                        paddingRight: 0
                                    }}>
                                    {
                                        isZeroOrder ? null : <Text style={{color: Constant.colorTxtContent}}>{orgName}</Text>
                                    }

                                    {
                                        isZeroOrder ? null :
                                            <Text
                                                style={{
                                                    color: Constant.colorTxtAlert,
                                                    marginTop: Constant.sizeMarginDefault,
                                                    marginBottom: Constant.sizeMarginDefault
                                                }}>支付方式：</Text>
                                    }


                                    {
                                        isZeroOrder ? this.renderZeroOrderView() :
                                            this.props.pay.payList.map((item, index) => {
                                                return this.renderPayItem(item, index);
                                            })
                                    }
                                </View>
                        }

                        {
                             this.props.pay.lastPayCode +'' == '5' || this.props.pay.lastPayCode +'' == '6' ||this.props.pay.lastPayCode+'' == '7' ?
                                <View style={{
                                    marginTop: Constant.sizeMarginDefault,
                                }}>
                                    <LabelCell title={"可用预存款："} showRightIcon={false} extra={<PriceText
                                        price={this.props.pay.payInfo.preDeposit!=null?this.props.pay.payInfo.preDeposit:0}/>}/>
                                    <LabelCell
                                        onClick={() => {
                                            Widget.Popup.show(<DateView
                                                initDate={new Date()}
                                                onDismiss={() => {
                                                    Widget.Popup.hide()
                                                }
                                                }
                                                onConfirm={(date) => {
                                                    Widget.Popup.hide();
                                                    this.state.transactionData.date = date;
                                                    this.setState({})
                                                }}
                                            />, {
                                                animationType: 'slide-up', backgroundColor: '#00000000',
                                                onMaskClose: () => {
                                                    Widget.Popup.hide()
                                                }
                                            })
                                        }}
                                        underLine={1}
                                        title={<Text><Text style={{color: 'red'}}>*</Text>汇款日期:</Text>}
                                        extra={date}/>

                                    <Input title={"汇款人户名:"}
                                           maxLength={32}
                                           value={this.state.transactionData.name}
                                           onChangeText={(text) => {
                                               this.state.transactionData.name = text;
                                               this.setState({})
                                           }}/>

                                    <Input title={`汇款金额(${this.props.user.getSymbol()}):`}
                                           value={this.state.transactionData.cheque}
                                           onChangeText={(text) => {
                                               if (ValidatorUtil.validatorNumberValue(text, true)) {
                                                   this.state.transactionData.cheque = text;
                                               }

                                               this.setState({})
                                           }}
                                    />

                                    <Input title={"珠啤收款银行:"}
                                           maxLength={80}
                                           value={this.state.transactionData.bankName}
                                           onChangeText={(text) => {
                                               this.state.transactionData.bankName = text;
                                               this.setState({})
                                           }}
                                    />


                                </View> : null
                        }
                        <TouchableOpacity
                            onPress={() => {
                                this.payClick();
                            }}
                            style={{
                                borderRadius: Constant.scale(4),
                                height: Constant.scale(40),
                                alignItems: 'center',
                                justifyContent: 'center',
                                margin: Constant.sizeMarginDefault,
                                backgroundColor: Constant.colorPrimary
                            }}>

                            <Text style={{color: 'white', fontSize: Constant.fontSizeBig}}>去支付</Text>
                        </TouchableOpacity>
                    </ScrollView>
                </KeyboardAwareScrollView>
            </Header>
        );
    }

    renderZeroOrderView = ()=>{
        let orderCashierDto = this.props.pay && this.props.pay.payInfo && this.props.pay.payInfo.orderCashierDto
        let orderNos = orderCashierDto.orderNos ||[];
        let orgName = orderCashierDto.supplierOrgName || '暂无';
        let payNomney = orderCashierDto.totalAmount;
        return(
            <View>
                {
                    orderNos ? orderNos.map((item,i)=>{
                        return(
                            <Text key={i} style={{lineHeight:Constant.scale(22),color: Constant.colorTxtContent}}>订单号：{item || '暂无'}</Text>
                        )
                    }):null
                }
                <Text style={{lineHeight:Constant.scale(22),color: Constant.colorTxtContent}}>提货组织：{orgName}</Text>
                <Text style={{lineHeight:Constant.scale(22),color: Constant.colorTxtAlert, fontSize: Constant.fontSizeSmall}}>
                    应付金额<PriceText size={1} price={payNomney}/> &nbsp;
                </Text>
            </View>
        )
    }

    payClick = () => {
        Log('=================支付方式====',this.props.pay.lastPayCode)

        let isZeroOrder = false;
        if(this.props.pay && this.props.pay.payInfo && this.props.pay.payInfo.payMethodDtos.length>0 && this.props.pay.payInfo.payMethodDtos[0].payCode+'' == '11'){
            this.props.pay.setLastPayCode(11);
            isZeroOrder = true;
        }

        let  ifNeedCheck = false;
        if (this.props.pay.lastPayCode+'' == '5' ||this.props.pay.lastPayCode+'' == '6'||this.props.pay.lastPayCode+'' == '7' ){
            ifNeedCheck = true;
        }
        let pay=()=>{
            this.props.pay.checkUnPay(this.state.selectIndex,(type)=>{
                Log(type)
                if (type+'' === '1' && this.props.pay.lastPayCode + '' != '13'){ //同组织有未缴费用
                    this.navigate('MergePay',{unPayAmountMap:this.props.pay.unPayAmountMap,transactionData:this.state.transactionData})
                }else if(type +'' === '2'){//其他组织有未缴纳费用
                    Alert.alert('温馨提示','下单成功，但您有杂费未缴纳，请先缴纳后才能给该订单付款。',[{text:'取消'},{text:'去缴纳',onPress:()=>this.gotoPayment()}])
                }else if (type+'' === '1' && this.props.pay.lastPayCode + '' == '13'){
                    Alert.alert('温馨提示','下单成功，但您有杂费未缴纳，请先缴纳后才能给该订单付款。',[{text:'取消'},{text:'去缴纳',onPress:()=>this.gotoPayment()}])
                } else {
                    Log('========','测试银联支付')
                    // let index = this.state.selectIndex;
                    let lastPayCode = this.props.pay.lastPayCode;
                    // Log('payIndex',this.props.pay.payList,this.props.pay.lastPayCode)
                    if (this.props.pay.payList) {
                        Log('测试是否走进来')
                        this.props.pay.payOrder(this.state.transactionData,(success,auditType,orderTips) => {

                            if (this.props.pay && this.props.pay.businessType + '' == '6') {
                                this.props.navigation.goBack()
                            } else {
                                // Log('支付成功后========================')
                                //自动审核
                                let orderNoList = [];
                                let orderNo =  ''
                                 if (this.props.pay.payInfo && this.props.pay.payInfo.orderCashierDto) {
                                     let orderCashierDto = this.props.pay.payInfo.orderCashierDto
                                     try {
                                        orderNoList = orderCashierDto.orderNos;
                                        orderNo = orderNoList[0];
                                     } catch (error) {}
                                 }
                                 if(~~this.state.isProceed !== 1 && (auditType && auditType+''!='1')){
                                    Api.ajaxAutoOrder(orderNoList).then().catch();
                                 }
                                 //赊销支付 跳转页面
                                if(lastPayCode == 13){//赊销支付
                                    if(orderTips){
                                        Alert.alert('温馨提示',`${orderTips}`,[{text:'确定',onPress:()=>this.props.navigation.replace('CreditPaymentSuccess',{orderNo:orderNo})}])
                                    }else{
                                        this.props.navigation.replace('CreditPaymentSuccess',{orderNo:orderNo})
                                    }
                                }else 
                                if (lastPayCode == 5 || lastPayCode == 6 || lastPayCode == 7 || isZeroOrder) {
                                    // Log(this.props.navigation,this.props.navigation.replace)
                                    // this.navigate('PaySuccess',{isTradition:true})
                                    this.props.navigation.replace('PaySuccess', {isTradition: true})
                                } else {
                                    this.props.navigation.replace('InPayment')
                                }
                            }


                        })
                    }

                }
            }).then().catch(e=>{});
        }
        if (ifNeedCheck){
            let rule = {
                date: [
                    {required: true, not: '', msg: '请选择日期'},

                ],
                name: [
                    {required: true, not: '',type:String , msg: '请输入户名'},

                ],
                cheque: [
                    {required: true, not: '', type:String ,msg: '请输入汇款金额'},
                ],

                bankName: [
                    {required: true, not: '',type:String , msg: '请输入银行'},
                ],

            }
            if (ValidatorUtil.validate(this.state.transactionData, rule)) {

                this.props.pay.saveHistoryNameAndBankName({name:this.state.transactionData.name,bankName:this.state.transactionData.bankName}).then().catch(e=>{});

                if(this.state.enterType == 1){
                    if (this.props.pay.payList) {//外币单无支付列表
                        this.props.pay.payOrder(this.state.transactionData,() => {
                            if (this.props.pay && this.props.pay.businessType + '' == '6') {
                                this.props.navigation.goBack()
                            } else {
                                if (this.props.pay.isTraditionalPay()) {
                                    // Log(this.props.navigation,this.props.navigation.replace)
                                    // this.navigate('PaySuccess',{isTradition:true})
                                    this.props.navigation.replace('PaySuccess', {isTradition: true})
                                } else {
                                    this.props.navigation.replace('InPayment')
                                }
                            }


                        })
                    }
                }else {
                    pay();
                }

            }
        }else {
            if (this.state.enterType == 1){  //费用缴纳页面进来
                if (this.props.pay.payList) {
                    this.props.pay.payOrder(this.state.transactionData,() => {
                        if (this.props.pay && this.props.pay.businessType + '' == '6') {
                            this.props.navigation.goBack()
                        } else {
                        if (this.props.pay.isTraditionalPay()) {
                        // Log(this.props.navigation,this.props.navigation.replace)
                                // this.navigate('PaySuccess',{isTradition:true})
                                this.props.navigation.replace('PaySuccess', {isTradition: true})
                            } else {
                                this.props.navigation.replace('InPayment')
                            }
                        }


                    })
                }
            }else {
                pay();
            }
        }

    }


    //去缴纳杂费
    gotoPayment = ()=>{
        this.navigate('MineCompany')
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },

});
