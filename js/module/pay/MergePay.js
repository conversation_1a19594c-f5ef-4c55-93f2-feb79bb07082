/**
 *
 * Created by xiaowz on 2018/6/20.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget,Util} from 'rn-yunxi';
import AppWidget from '../../app-widget'

const {width, height} = Dimensions.get('window');
const {Text, CommonListView, CommonFlatList ,LabelCell,InputFormCell} = Widget;
const {Header, PrimaryHeader, Button, Image,PriceText,DateView,CheckBox} = AppWidget;
import {toJS} from 'mobx';
import {inject, observer} from 'mobx-react/native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
import ValidatorUtil from '../../util/ValidatorUtil';

const Input = ({title, value, onChangeText,maxLength}) => (
    <InputFormCell maxLength={maxLength} title={<Text><Text style={{color: 'red'}}>*</Text>{title}</Text>} value={value}
                   onChangeText={onChangeText}/>
)
//模块声名并导出
@inject(stores => ({
    pay: stores.pay,
    user: stores.user,
}))
@observer
export default class MergePay extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        //状态机变量声明
        this.state = {
            unPayAmountArray:[],
            transactionData: {
                date: new Date(),  //日期
                name: null,  //汇款人
                cheque: '',  //汇款金额
                bankName: null,  //银行
            },
        };
    }



    componentWillMount() {
        let {params} = this.getNavState();
        if (params && params.unPayAmountMap){
            let array=[];
            for (let [key,value] of params.unPayAmountMap){
                array.push(value);
            }
            this.setState({
                unPayAmountArray:array
            })
        }
        if (params && params.transactionData){
            Log('========params.transactionData=======',params.transactionData)
            this.setState({
                transactionData:params.transactionData,


            })
        }

    }

    //渲染
    render() {
        // Log('======支付方式====',toJS(this.props.pay.totalAmount),toJS(this.props.pay.payTransactionAmount))
        let {unPayAmountArray} = this.state;
        let tempArray=[]
        for (let item of unPayAmountArray){
            for (let obj of item.array){
                tempArray.push(obj.amount)
            }
        }

        let result  = 0  //欠费总额
        for (let item of tempArray){
            result = result + item;
        }

        let orderTotalAmount = 0
        orderTotalAmount = this.props.pay.totalAmount; //订单总金额
        let payTransactionAmount = 0  // 手续费
        payTransactionAmount = (result+orderTotalAmount)*this.props.pay.payRate  + this.props.pay.singleAmount;
        let date = this.state.transactionData.date ? Util.DateUtil.formatDate(this.state.transactionData.date.getTime(), 'yyyy/MM/dd') : '年/月/日';

        return (
            <Header title={'合并支付'}>
                <View style={{flex:1,alignItems:'center'}}>
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    style={styles.container}>
                    <View style={{flex:1,alignItems:'center'}}>
                        {/*<Text style={{margin:Constant.scale(10),color:Constant.colorPrimary}}>您有杂费未缴纳，是否与本次订单一起支付？</Text>*/}
                        {
                            unPayAmountArray.map((item ,i )=>{
                                return(
                                    <View key ={i} style={{marginBottom:Constant.scale(10)}}>
                                        <LabelCell extraTextStyle={{color: Constant.colorTxtContent}}
                                                   showRightIcon={false}
                                                   underLine={1}
                                                   title={<Text numberOfLines={1} style={{fontSize:Constant.fontSizeBig,width:'80%'}}>{item.orgName || '暂无'}</Text>}
                                                   extra={ item.opreateTime || '暂无'}
                                        />
                                        {
                                            item.array && item.array.length>0 ?
                                                item.array.map((obj,j)=>{
                                                    return(
                                                        <LabelCell key={j}
                                                                   extraTextStyle={{color: Constant.colorTxtTitle}}
                                                                   titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                                                   underLine={1}
                                                                   title={this.handleTitle(obj.paymentType)}
                                                                   extra={<PriceText size={5} price={obj.amount}/>}/>
                                                    )
                                                })
                                                :null
                                        }

                                    </View>
                                )
                            })
                        }

                        {
                            this.props.pay.lastPayCode +'' == '5' || this.props.pay.lastPayCode +'' == '6' ||this.props.pay.lastPayCode+'' == '7' ?
                                <View style={{width:width,flex:1,marginBottom:Constant.scale(10)}}>
                                    <LabelCell title={"可用预存款："} showRightIcon={false} extra={<PriceText
                                        price={this.props.pay.payInfo.preDeposit!=null?this.props.pay.payInfo.preDeposit:0}/>}
                                    />
                                    <LabelCell
                                        onClick={() => {
                                            Widget.Popup.show(<DateView
                                                initDate={new Date()}
                                                onDismiss={() => {
                                                    Widget.Popup.hide()
                                                }
                                                }
                                                onConfirm={(date) => {
                                                    Widget.Popup.hide();
                                                    this.state.transactionData.date = date;
                                                    this.setState({})
                                                }}
                                            />, {
                                                animationType: 'slide-up', backgroundColor: '#00000000',
                                                onMaskClose: () => {
                                                    Widget.Popup.hide()
                                                }
                                            })
                                        }}
                                        underLine={1}
                                        title={<Text><Text style={{color: 'red'}}>*</Text>汇款日期:</Text>}
                                        extra={date}/>

                                    <Input title={"汇款人户名:"}
                                           maxLength={32}
                                           value={this.state.transactionData.name}
                                           onChangeText={(text) => {
                                               this.state.transactionData.name = text;
                                               this.setState({})
                                           }}/>

                                    <Input title={`汇款金额(${this.props.user.getSymbol()}):`}
                                           value={this.state.transactionData.cheque}
                                           onChangeText={(text) => {
                                               if (ValidatorUtil.validatorNumberValue(text, true)) {
                                                   this.state.transactionData.cheque = text;
                                               }

                                               this.setState({})
                                           }}
                                    />

                                    <Input title={"珠啤收款银行:"}
                                           maxLength={80}
                                           value={this.state.transactionData.bankName}
                                           onChangeText={(text) => {
                                               this.state.transactionData.bankName = text;
                                               this.setState({})
                                           }}
                                    />


                                </View> : null
                        }

                        <View style={{marginBottom:Constant.scale(10),width:width}}>
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}}
                                       showRightIcon={false}
                                       underLine={2} title={'本次需缴纳：'}
                                       extra={ <PriceText price={result || 0}/>}
                                       cellStyle={{height:Constant.scale(30)}}
                            />
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}}
                                       showRightIcon={false}
                                       underLine={2}
                                       title={'订单：'}
                                       extra={<PriceText price={orderTotalAmount || 0}/>}
                                       cellStyle={{height:Constant.scale(30)}}
                            />
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}}
                                       showRightIcon={false}
                                       underLine={2}
                                       title={'总价：'}
                                       extra={
                                               <View style={{height:'100%',justifyContent:'center',alignItems:'flex-end'}}>
                                                   <PriceText size={1} price={result+orderTotalAmount+payTransactionAmount}></PriceText>
                                                   <Text style={{color:Constant.colorPrimary,fontSize:Constant.fontSizeXSmall}}>（手续费<PriceText size={2} price={payTransactionAmount}/>)</Text>
                                               </View>
                                       }
                            />
                        </View>

                    </View>

                </ScrollView>
                <TouchableOpacity onPress={()=>this.mergePayClick()} style={{marginTop:Constant.scale(5),marginBottom:Constant.scale(20),backgroundColor:Constant.colorPrimary,width:Constant.scale(350),height:Constant.scale(40),justifyContent:'center', alignItems:'center',borderRadius:Constant.scale(4)}}>
                    <Text style={{color:'white',fontWeight:'bold'}}>合并支付</Text>
                </TouchableOpacity>
                </View>
            </Header>
        );
    }


    handleTitle = (type)=>{
        switch (type){
            case 1:
                return '装车费';
            case 2:
                return '托板使用费';
            case 3:
                return '纸箱费';
            default:
                return '暂无';
        }
    }


    //点击合并支付
    mergePayClick = ()=>{
        this.props.pay.payOrder(this.state.transactionData,(isTradictionalPay)=>{
            // Log('2222222222222',data)
            if (isTradictionalPay) {
                this.props.navigation.replace('PaySuccess', {isTradition: true})
            } else {
                this.props.navigation.replace('InPayment')
            }
        });
    }
};
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Constant.colorBackgroundDefault
    }
});
