/**
 * Created by whw on 2018/1/26.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import {inject, observer} from 'mobx-react/native';
import AppWidget from '../../app-widget/index';
import MessageItem from './MessageItem';
const {width, height} = Dimensions.get('window');
const {Header,DefaultTabBar}=AppWidget;
const {CommonFlatList, Text} = Widget;
import ScrollableTabView from 'react-native-scrollable-tab-view';
/**
 * 消息中心
 */

const titleList = ['未读','已读'];

@inject(stores => ({
    message: stores.messageCenter,
}))
@observer
export default class MessageCenter extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state={//状态机变量声明
        };
    }
    componentWillMount(){
        this.props.message.getMessageList(false,0).then().catch(e=>{});
    }
    componentDidMount(){}
    componentWillUnmount(){}

    renderRow = ({item,index})=> {
        return (
            <TouchableOpacity key = {index} onPress={()=>this.readClick(item)}>
                <MessageItem data={item}/>
            </TouchableOpacity>

        );
    }


    readClick = (item)=>{
        if (item.isRead == 0){
            this.props.message.readMessage(item.id).then().catch(e=>{})
        }
    }

    renderTab = ()=>{
        return(
            <ScrollableTabView
                style={{ flex: 0}}
                locked={true}
                initialPage={0}
                scrollWithoutAnimation={true}
                renderTabBar={() => {
                    return (
                        <DefaultTabBar
                            activeTextColor={Constant.colorTxtPrimary}
                            textStyle={{ fontSize: Constant.fontSizeNormal }}
                            inactiveTextColor='#777777'

                        />
                    )
                }}

                onChangeTab={(obj) => {
                    // this.props.message.setTabIndex(obj.i)
                    this.props.message.changeTabClick(obj.i, true);
                }}
            >


                {

                    titleList.map((obj, i) => {
                        return(
                            <Text key = {`tabkey+${i}`} tabLabel={obj} />
                        )
                    })

                }

            </ScrollableTabView>
        )
    }

    renderList = ()=>{
        return(
            <CommonFlatList
                style={{flex:1}}
                data={this.props.message.listParams[this.props.message.status].dataArray}
                listState={this.props.message.listParams[this.props.message.status].listState}
                enableLoadMore={this.props.message.listParams[this.props.message.status].enableLoadMore}
                keyExtractor={(item, index) => {
                    return 'message' + index;
                }}
                renderItem={this.renderRow}
                enableRefresh={this.props.message.listParams[this.props.message.status].enableRefresh}
                onRefresh={() => {
                    this.props.message.getMessageList(false,this.props.message.status).then().catch(e=>{});
                }}
                onLoadMore={() => {
                    this.props.message.getMessageList(true,this.props.message.status).then().catch(e=>{});

                }}
            >
                <View style={[{flex: 1, justifyContent: 'center', alignItems: 'center',}]}>

                    <Text style={{fontSize: Constant.fontSizeBig, color: Constant.colorTxtContent, marginTop: Constant.scale(25)}}>
                        暂无数据
                    </Text>
                </View>
            </CommonFlatList>
        )
    }


    //渲染
    render() {
        return (
            <Header style={styles.container} title="全部消息">
                {this.renderTab()}
                {this.renderList()}
            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    }
});