/**
 * Created by whw on 2018/1/26.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget/index';
const {width, height} = Dimensions.get('window');
const {Text} = Widget;
const {PriceText,Image}=AppWidget;
const IC_SPECIAL = require('../img/home-page/ic_special.png')
import {withNavigation} from 'react-navigation';
@withNavigation

/**
 * 首页
 */
export default class HomeItem extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state={//状态机变量声明
        };
    }
    componentWillMount(){}
    componentDidMount(){}
    componentWillUnmount(){}

    //渲染
    render() {
        let data = this.props.data;
        let isReadTxt = '未读'
        let isRead = (this.props.data.isRead == 1);
        if (this.props.data.isRead == 1){
            isReadTxt = '已读'
        }else {
            isReadTxt = '未读'
        }
        return (
            <View style={[styles.container]}>
                <View style={{flexDirection:'row',flex:1,height: Constant.scale(44),justifyContent: 'space-between',alignItems:'center'}}>
                    <Text style={{marginLeft: Constant.sizeMarginDefault,color: Constant.colorTxtTitle,fontSize: Constant.fontSizeNormal}}>
                        {data.date || '--'}
                    </Text>
                    <Text style={{marginRight: Constant.sizeMarginDefault,color:isRead? Constant.colorTxtTitle: Constant.colorPrimary,fontSize: Constant.fontSizeNormal}}>
                        {isReadTxt}
                    </Text>
                </View>
                <View style={{flex:1,marginLeft: Constant.sizeMarginDefault,marginRight: Constant.sizeMarginDefault,height: Constant.sizeDividerNormal,backgroundColor: Constant.colorDivider}}/>
                <Text style={{margin: Constant.sizeMarginDefault,color: Constant.colorTxtContent,fontSize: Constant.fontSizeNormal}}>{data.content||'--'}</Text>
            </View>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Constant.colorDefault,
        margin: Constant.sizeMarginDefault,
        marginBottom: 0,
        borderWidth: Constant.sizeDividerNormal,
        borderColor: Constant.colorDivider
    },

});