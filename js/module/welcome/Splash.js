/**
 * Created by z<PERSON><PERSON><PERSON>ha<PERSON><PERSON> on 2017/4/17.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    Dimensions,
    StatusBar,
    Animated
} from 'react-native';
import { ReactNavComponent, Widget, Util } from 'rn-yunxi';
import { NavigationActions } from 'react-navigation';
const { width, height } = Dimensions.get('window');
const Text = Widget.Text;
const { CountdownUtil } = Util
import BACKGROUND_IMAGE from '../img/welcome/bg_welcome.jpg';
import { inject, observer } from 'mobx-react/native';
@inject(stores => ({
    user: stores.user,
}))
@observer

export default class Splash extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
            bounceValue: new Animated.Value(0), // 弹力值
            rotation: new Animated.Value(0), // logo旋转动画,
            imgUrl: null,
            skipCount: '',
            skipText: '跳过3s'
        };
    }

    componentWillUnmount() {
        this.timer && clearTimeout(this.timer);
        CountdownUtil.stop()
    }

    componentWillMount() {
        let countdownDate = new Date(new Date().getTime() + 3 * 1000)
        CountdownUtil.settimer(countdownDate, (time) => {
            this.setState({
                skipCount: time.sec > 0 ? time.sec + 's' : '',
                skipText: '跳过',
            }, () => {
                if (this.state.skipCount == '') {
                    CountdownUtil.stop()
                }
            })
        })
    }


    componentDidMount() {

        this.timer = setTimeout(() => {
            let resetAction = NavigationActions.reset({
                index: 0,
                actions: [
                    NavigationActions.navigate({ routeName:'Login'}),
                ]
            });
            this.dispatch(resetAction)
        }, 3000);

    }

    //渲染
    render() {
        return (
            <View style={styles.container}>
                <StatusBar barStyle="light-content" />
                <View style={{ backgroundColor: 'white', alignItems: 'center', justifyContent: 'center', flex: 1 }}>
                    <Image
                        resizeMode={'stretch'}
                        style={{ width: width, height: height, alignItems: 'center' }}
                        source={BACKGROUND_IMAGE}
                    >
                    </Image>
                    <View style={{ width: width, flexDirection: 'row', justifyContent: 'center', position: 'absolute', bottom: Constant.scale(36) }}>
                        <Text style={{ color: '#000', fontSize: 12, marginRight: Constant.scale(10) }}>@copyright 广州珠江啤酒股份有限公司</Text>
                    </View>

                    {
                         this.state.skipCount == '' && this.state.skipText == '跳过' ? null : <View style={{
                            borderRadius: 2,
                            position: 'absolute',
                            right: Constant.scale(25),
                            top: Constant.scale(40),
                            justifyContent: 'center',
                            backgroundColor: 'rgba(225,225,225,0.5)',
                            width: Constant.scale(60),
                            height: Constant.scale(25),
                            alignItems: 'center'
                        }}>
                            <TouchableOpacity onPress={() => this.skip()}>
                                <Text style={{ backgroundColor: '#00000000', color: '#FFFFFF', fontSize: 14 }}>{this.state.skipText + this.state.skipCount}</Text>                            
                            </TouchableOpacity>
                        </View>
                    }

                    
                </View>

            </View>
        );
    }

    skip = () => {
        let resetAction = NavigationActions.reset({
            index: 0,
            actions: [
                NavigationActions.navigate({ routeName: 'Login' }),
            ]
        });
        this.dispatch(resetAction)
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#FFFFFF00'
    }
});

