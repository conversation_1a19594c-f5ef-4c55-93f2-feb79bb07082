import React, { Component } from "react";
import {
  StyleSheet,
  View,
  SectionList,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  TextInput,
  Dimensions,
  Platform
} from "react-native";
import PropType from "prop-types";
import { ReactNavComponent, Widget } from "rn-yunxi";
import AppWidget from "../../app-widget";
import { inject, observer } from "mobx-react/native";
import ScrollableTabView from "react-native-scrollable-tab-view";

import { toJS } from "mobx";
import SkuPanelStore from "../../store/SkuPanel";

const { width, height } = Dimensions.get("window");
const {
  Header,
  Image,
  CheckBox,
  PriceText,
  DefaultTabBar,
  ChannelPanel
} = AppWidget;
const { Text, LabelCell } = Widget;
/**
 * User:lao.jianfeng
 * 规格面板
 */

@observer
export default class SkuPanel extends ReactNavComponent {
  //属性声名
  static propTypes = {
    itemId: PropType.any, //商品id
    orgId: PropType.any, //组织id
    awardId: PropType.any, //奖项id
    salesChannelCode: PropType.any, //父渠道id
    subSalesChannelCode: PropType.any, //子渠道id
    plateFlag: PropType.any, //是否带板(0不带，1带板)
    onConfirm: PropType.func,
    onCancel: PropType.func,
    skuPanelStore: PropType.any,
    channelListData: PropType.any,
    isGiftWinePanel: PropType.any
  };
  //默认属性
  static defaultProps = {};

  //构造函数
  constructor(props) {
    super(props);
    this.state = {
      //状态机变量声明
      orgId: this.props.orgId, //组织id
      awardId: this.props.awardId, //奖项id
      salesChannelCode: this.props.salesChannelCode, //父渠道id
      salesChannelName: this.props.salesChannelName, //父渠道名字
      subSalesChannelCode: this.props.subSalesChannelCode, //子渠道id
      subSalesChannelName: this.props.subSalesChannelName, //子渠道名字
      plateFlag: this.props.plateFlag, //是否带板(0不带，1带板)
      batchSaleNum: this.props.batchSaleNum, // 托板规格
      bearing: this.props.bearing // 托板规格文案
    };
    // this.skuPanelStore = new SkuPanelStore();
    // this.skuPanelStore.setItemId(this.props.itemId);
    // this.skuPanelStore.setSelectPlateFlag(this.props.plateFlag);
    // this.skuPanelStore.setSelectOrgId(this.props.orgId);
    // this.skuPanelStore.setSelectAwardId(this.props.awardId);
    // this.skuPanelStore.setSelectChannelId(this.props.salesChannelCode, this.props.subSalesChannelCode);
  }

  componentWillMount() {
    Log(
      "===测试数据00",
      JSON.stringify(this.props.awardsMap.get(this.props.orgId))
    );
    Log("=====测试", this.props.skuPanelStore.getChoose());
  }

  componentDidMount() {}

  componentWillUnmount() {}

  setChannelData = () => {
    if (this.channelPanel) {
      const data = this.channelPanel.getResult();
      if (data) {
        let { channelId, channelName } = data;
        this.setState({
          salesChannelCode: channelId.salesChannelCode,
          salesChannelName: channelName.salesChannelName,
          subSalesChannelCode: channelId.subSalesChannelCode,
          subSalesChannelName: channelName.subSalesChannelName
        });
        return data;
      }
    }
  };

  //渲染
  render() {
    let isGiftWinePanel = this.props.isGiftWinePanel;
    return (
      <View style={{ height: height / 2, backgroundColor: "white" }}>
        {Platform.OS == "android" ? (
          <StatusBar backgroundColor={"#000000"} translucent={true} />
        ) : null}
        <ScrollableTabView
          onChangeTab={obj => {}}
          locked={true}
          style={{
            paddingBottom: Constant.sizeDividerNormal,
            backgroundColor: Constant.colorBackgroundDefault
          }}
          scrollWithoutAnimation={true}
          renderTabBar={() => {
            return <DefaultTabBar activeTextColor={Constant.colorTxtPrimary} />;
          }}
        >
          {isGiftWinePanel
            ? this.props.skuPanelStore &&
              this.props.skuPanelStore.getSkuTabForGiftWine.map(
                (item, index) => {
                  return this.renderList(item, index);
                }
              )
            : this.props.skuPanelStore &&
              this.props.skuPanelStore.getSkuTab.map((item, index) => {
                return this.renderList(item, index);
              })}
        </ScrollableTabView>
        <View style={{ flexDirection: "row", height: Constant.scale(44) }}>
          <TouchableOpacity
            style={[
              styles.filterBtn,
              {
                borderTopColor: Constant.colorDividerDefault,
                borderWidth: Constant.sizeDividerNormal
              }
            ]}
            onPress={() => {
              this.props.onCancel && this.props.onCancel();
            }}
          >
            <Text style={{ color: "#999999" }}>取消</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.filterBtn,
              { backgroundColor: Constant.colorPrimary }
            ]}
            onPress={() => {
              let {
                salesChannelCode,
                subSalesChannelCode,
                salesChannelName,
                subSalesChannelName
              } = this.state;
              if (this.channelPanel) {
                const channelData = this.setChannelData();
                if (channelData) {
                  const { channelId, channelName } = channelData;
                  salesChannelCode = channelId.salesChannelCode;
                  subSalesChannelCode = channelId.subSalesChannelCode;
                  salesChannelName = channelName.salesChannelName;
                  subSalesChannelName = channelName.subSalesChannelName;
                }
              }

              if (this.props.skuPanelStore) {
                this.props.skuPanelStore.setSelectPlateFlag(
                  this.state.plateFlag
                );
                this.props.skuPanelStore.setSelectOrgId(this.state.orgId);
                this.props.skuPanelStore.setSelectChannelId(
                  salesChannelCode,
                  subSalesChannelCode
                );
                this.props.skuPanelStore.setSelectChancelName(
                  salesChannelName,
                  subSalesChannelName
                );
                // this.props.skuPanelStore.setSelectAwardId(this.state.awardId);
                !!this.state.awardId &&
                  this.props.skuPanelStore.setSelectAwardId(this.state.awardId);
                this.props.skuPanelStore.setBatchSaleNum(
                  this.state.batchSaleNum
                );
                this.props.skuPanelStore.setBearing(this.state.bearing);
                this.props.onConfirm &&
                  this.props.onConfirm(this.props.skuPanelStore.getChoose());
              }
            }}
          >
            <Text style={{ color: "white" }}>确定</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  renderList = (tabItem, tabIndex) => {
    let tab = tabItem.tab;
    let array = [];
    let selectId = null;
    // Log('--------测试skupan',toJS(this.props.skuPanelStore.selectOrgId),toJS(this.state.orgId),toJS( this.props.awardsMap.get(this.state.orgId)))

    switch (tab) {
      case 0:
        array = this.props.orgList;
        selectId = this.state.orgId;
        break;
      case 1:
        array = this.props.skuPanelStore.selectOrgId
          ? this.props.awardsMap.get(this.state.orgId)
          : [];
        selectId = this.state.awardId;
        break;
      case 3:
        selectId = this.state.plateFlag;
        array = this.props.skuPanelStore.plateFlagList;
        break;
      case 4:
        selectId = this.state.batchSaleNum;
        // 只有选择了“带板”，才显示
        array = this.state.plateFlag
          ? this.props.skuPanelStore.palletStruList
          : [];
        break;
    }

    return (
      <View
        tabLabel={tabItem.label}
        key={`sku_${tabIndex}`}
        style={{ flex: 1 }}
      >
        {tab === 2 ? (
          <ChannelPanel
            ref={ref => {
              this.channelPanel = ref;
            }}
            onConfirm={data => {
              Log("onConfirm", data);
              let { channelId, channelName } = data;
              this.setState({
                salesChannelCode: channelId.salesChannelCode,
                salesChannelName: channelName.salesChannelName,
                subSalesChannelCode: channelId.subSalesChannelCode,
                subSalesChannelName: channelName.subSalesChannelName
              });
              // this.skuPanel.setSelectChancelName(channelName.salesChannelName,channelName.subSalesChannelName);
            }}
            data={this.props.channelListData}
            showHeader={false}
            salesChannelCode={this.state.salesChannelCode}
            subSalesChannelCode={this.state.subSalesChannelCode}
          />
        ) : (
          <ScrollView>
            <View style={GlobalStyle.styleDividerDefault} />
            {array &&
              array.map((item, index) => {
                let isSelect = false;
                let obj;

                if (tabItem.idKey && item[tabItem.idKey] == selectId) {
                  isSelect = true;
                }
                obj = tabItem.idKey ? item[tabItem.idKey] : item;

                return (
                  <LabelCell
                    titleTextStyle={
                      isSelect ? { color: Constant.colorTxtPrimary } : null
                    }
                    onClick={() => {
                      // this.props.skuPanelStore.setTabSelect(tab, obj)
                      if (tab === 0) {
                        this.setState({ orgId: obj, awardId: "" });
                      } else if (tab === 1) {
                        // this.setSelectAwardId(id);
                        this.setState({ awardId: obj });
                      } else if (tab === 2) {
                        this.setState({
                          salesChannelCode: obj.salesChannelCode,
                          subSalesChannelCode: obj.subSalesChannelCode
                        });
                      } else if (tab === 3) {
                        this.setState({ plateFlag: obj });

                        if (!obj) {
                          // 如果选择了不带托板，则托板规格默认设置为'0'
                          this.setState({ batchSaleNum: "0", bearing: "" });
                        } else {
                          // 如果选择了带托板，则托板规格默认设置为第一个
                          const { palletStruList } = this.props.skuPanelStore;
                          const item =
                            (palletStruList[0] && palletStruList[0]) || null;

                          this.setState({
                            batchSaleNum: (item && item.boxnum) || "0",
                            bearing: (item && item.bearing) || ""
                          });
                        }
                      } else if (tab === 4) {
                        // 托板规格
                        this.setState({
                          batchSaleNum: obj,
                          bearing: item.bearing || ""
                        });
                      }
                    }}
                    key={index}
                    title={tabItem.labelKey ? item[tabItem.labelKey] : item}
                    rightIcon={
                      isSelect ? <CheckBox isChecked={isSelect} /> : <View />
                    }
                  />
                );
              })}
          </ScrollView>
        )}
      </View>
    );
  };
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  checkBox: {
    paddingRight: Constant.sizeMarginDefault,
    width: Constant.scale(15) + Constant.sizeMarginDefault,
    height: Constant.scale(15)
  },
  filterBtn: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center"
  }
});
