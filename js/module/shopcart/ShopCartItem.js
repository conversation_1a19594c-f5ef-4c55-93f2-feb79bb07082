import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    InteractionManager,
    Dimensions
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget';
import {inject, observer} from 'mobx-react/native';
import {toJS} from 'mobx';
import _ from 'lodash';
import SkuPanelStore from '../../store/SkuPanel';
import SkuPanel from './SkuPanel';

const {width, height} = Dimensions.get('window');
const {Header, Image, CheckBox, PriceText, ConfirmInput} = AppWidget;
const {Text,} = Widget;
/**
 * 购物车
 */
@inject(stores => ({
    user: stores.user,
    shopCart: stores.shopCart
}))
@observer
export default class ShopCartItem extends Component {
    //属性声名
    static propTypes = {
        onItemNumChange: PropType.func,
        onItemNumConfirm: PropType.func,
        onItemChoose: PropType.func,
        onShowSkuPanel: PropType.func,
        data: PropType.any,
        cartIndex: PropType.any,
        orgListIndex:PropType.any,
        palletStruList:PropType.any, // 托板承载规格列表
    };
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
            isFocus: false,
        };
        this.skuPanelStore = new SkuPanelStore();
    }


    componentWillMount() {
        this.initSkuPanelStore(this.props.data, true);
    }

    componentDidMount() {

    }

    componentWillUnmount() {
    }

    componentWillReceiveProps(nextProps) {
        let itemId = _.get(this.props, 'data.itemId');
        let newItemId = _.get(nextProps, 'data.itemId');
        let itemNum = _.get(this.props, 'data.itemNum');
        let newItemNum = _.get(nextProps, 'data.itemNum');
        Log('shopcart item nextProps>>', itemId, newItemId, itemNum, newItemNum)
        if (newItemId != itemId || (newItemId == itemId && this.skuPanelStore && this.skuPanelStore.purchaseCount != newItemNum)) {
            this.initSkuPanelStore(nextProps.data, false);
        }
    }

    /**
     * 初始化SkuPanelStore
     * @param data
     */
    initSkuPanelStore(data, obtainDiscount = true) {

        this.skuPanelStore.setPickOrgList(this.props.orgList);
        this.skuPanelStore.setAwards(this.props.awardsMap);
        this.skuPanelStore.setSelectOrgId(data.orgId, false);
        this.skuPanelStore.setSelectPlateFlag(data.withPlateFlag);
        this.skuPanelStore.setSelectAwardId(data.awardsId);
        this.skuPanelStore.setSelectChannelId(data.salesChannelCode, data.subSalesChannelCode);
        // this.skuPanelStore.setSuggestNumber(0);//TODO 等汉成修改购物车列表返回数据
        // this.skuPanelStore.setMandatoryNumber(data.itemNum);
        this.skuPanelStore.setPurchaseCount(data.itemNum, false, data.isZeroFourGoods);
        // Log('传进来的awardsMap',this.props.awardsMap.get(data.orgId))

        this.skuPanelStore.setItemId(data.itemId, false);
        if (obtainDiscount == true) {
            // this.skuPanelStore.obtainDiscount().then().catch(e=>{});
        }

        // 设置可选择的托板承载规格列表
        this.skuPanelStore.setPalletStruList(this.props.palletStruList || []);
        this.skuPanelStore.setBatchSaleNum(data.batchSaleNum || '0');
        this.skuPanelStore.setBearing(data.bearing || '');

        Log('sku panel init>>', data.itemId);
        // this.setState({});
    }

    onShowSkuPanel = (orgListIndex, cartIndex, item) => {
        Widget.Popup.show(
            <SkuPanel
                skuPanelStore={this.skuPanelStore}
                orgList={toJS(this.props.orgList)}
                awardsMap={this.props.awardsMap}
                channelListData={toJS(this.props.user.channelList)}
                orgId={this.skuPanelStore.selectOrgId}
                salesChannelCode={this.skuPanelStore.selectChannelId.salesChannelCode}
                subSalesChannelCode={this.skuPanelStore.selectChannelId.subSalesChannelCode}
                salesChannelName={this.skuPanelStore.selectChannelName.salesChannelName}
                subSalesChannelName={this.skuPanelStore.selectChannelName.subSalesChannelName}
                plateFlag={this.skuPanelStore.selectPlateId}
                awardId={this.skuPanelStore.selectAwardId}
                palletStruList = {this.skuPanelStore.palletStruList}
                batchSaleNum = {this.skuPanelStore.batchSaleNum}
                bearing = {this.skuPanelStore.bearing}
                onConfirm={(result) => {
                    //更新规格，刷新列表
                    Widget.Popup.hide();
                    InteractionManager.runAfterInteractions(() => {
                        // Log('=================',
                        //     toJS(result.orgId),this.skuPanelStore.selectOrgId,
                        //     toJS(result.awardsId),this.skuPanelStore.selectAwardId,
                        //     toJS(result.salesChannelCode),this.skuPanelStore.selectChannelId.salesChannelCode,
                        //     toJS(result.subSalesChannelCode),this.skuPanelStore.selectChannelId.subSalesChannelCode,
                        //     toJS(item)
                        //     )
                        if ((result.orgId == item.orgId) &&
                            (result.awardsId == item.awardsId) &&
                            (result.salesChannelCode == item.salesChannelCode) &&
                            (result.subSalesChannelCode == item.subSalesChannelCode)) {
                            this.props.shopCart.callModifyCartItemApi(orgListIndex, cartIndex, result, () => {
                                this.props.onItemNumConfirm && this.props.onItemNumConfirm(this.skuPanelStore.purchaseCount), this.setState({})
                            }).then().catch(err => {
                            });
                        } else {
                            this.props.shopCart.checkIsSameItem(orgListIndex, cartIndex, result, () => {
                                this.props.shopCart.callModifyCartItemApi(orgListIndex, cartIndex, result, () => {
                                    this.props.onItemNumConfirm && this.props.onItemNumConfirm(this.skuPanelStore.purchaseCount), this.setState({})
                                }).then().catch(err => {
                                });
                            }).then().catch(e => {
                            })

                        }

                        // Log('update skupanel ', orgListIndex, cartIndex, result)

                    })
                }}
                onCancel={() => {
                    Widget.Popup.hide()
                }
                }/>,
            {
                animationType: 'slide-up', backgroundColor: '#00000000',
                onMaskClose: () => {
                    Widget.Popup.hide()
                }
            })
    }

    //渲染
    render() {

        let cartIndex = this.props.cartIndex;
        let orgListIndex = this.props.orgListIndex;
        let isEditStatus = this.props.isEditStatus;
        let cartItem = this.props.data;
        let awardsName = this.skuPanelStore.selectAwardName(this.props.awardsMap);
        let channelName = this.props.user.getChancelFullName(cartItem.salesChannelCode, cartItem.subSalesChannelCode);

        let itemNum = '';

        if (this.state.isFocus) {
            itemNum = this.skuPanelStore.tmpPurchaseCount + '';
        } else {
            itemNum = this.skuPanelStore.purchaseCount + '';
        }
        let purchaseQuantity = this.skuPanelStore.purchaseQuantity;
        // Log('render shopcart item>>',this.props.data.itemId,awardsName)
        // Log('render ~~~',this.skuPanelStore.selectAwardId,this.skuPanelStore.selectOrgId)
        return (
            <TouchableOpacity activeOpacity={1} onPress={() => {
                this.props.onItemChoose && this.props.onItemChoose()
            }}>
                <View style={{padding: Constant.sizeMarginDefault, paddingRight: 0}}>
                    <View style={{flexDirection: 'row', justifyContent: 'center'}}>
                        <View style={{justifyContent: 'center'}}>
                            <CheckBox
                                isChecked={!isEditStatus ? cartItem.normalSelected : cartItem.editSelected}
                                style={styles.checkBox}
                                onClick={() => {
                                    this.props.onItemChoose && this.props.onItemChoose()
                                    // this.setState({})
                                }}
                            />
                        </View>
                        <TouchableOpacity activeOpacity={1}
                                          onPress={() => this.gotoDetailClick(cartItem.itemId, this.skuPanelStore.selectOrgId)}
                                          style={{flex: 1, paddingRight: Constant.sizeMarginDefault}}>
                            <View style={{flexDirection: 'row', marginBottom: Constant.sizeMarginDefault}}>
                                <Image
                                    source={{uri: cartItem.imgUrl ? Constant.ossImgUrl(cartItem.imgUrl, Constant.scale(75)) : ''}}
                                    // defaultBackgroundColor={'#fff'}
                                    style={{
                                        borderWidth: Constant.sizeDividerNormal,
                                        borderColor: Constant.colorDividerDefault,
                                        marginRight: Constant.sizeMarginDefault,
                                        width: Constant.scale(75),
                                        height: Constant.scale(75)
                                    }}/>
                                <View style={{flex: 1, paddingLeft: Constant.scale(10)}}>
                                    <Text numberOfLines={1}>{cartItem.name}</Text>
                                    <View style={{flexDirection: 'row', alignItems: 'flex-end'}}>
                                        <PriceText price={cartItem.price}/>
                                        <View style={{width: Constant.sizeMarginDefault}}/>
                                        <PriceText
                                            unit={`折${this.props.user.getSymbol()}`}
                                            size={-1}
                                            price={cartItem.discountAmount}
                                        />
                                    </View>
                                    <View style={{flex: 1}}/>

                                    <ConfirmInput
                                        isFocus={(isFocus) => {
                                            if (isFocus) {
                                                this.skuPanelStore.setPurchaseCount(this.skuPanelStore.purchaseCount, true,cartItem.isZeroFourGoods);
                                            } else {
                                                this.skuPanelStore.setPurchaseCount(this.skuPanelStore.tmpPurchaseCount, false,cartItem.isZeroFourGoods);
                                            }
                                            this.setState({isFocus: isFocus}, () => {
                                                if (!this.state.isFocus) {
                                                    this.props.onItemNumConfirm && this.props.onItemNumConfirm(this.skuPanelStore.purchaseCount);
                                                }
                                            })

                                        }}
                                        value={itemNum}
                                        onChangeText={(text) => {
                                            this.skuPanelStore.setPurchaseCount(text, true,cartItem.isZeroFourGoods)
                                            this.props.onItemNumChange && this.props.onItemNumChange(text);
                                        }}/>
                                </View>
                                {
                                    purchaseQuantity.multiple > 0 ?
                                        <View style={{marginLeft: Constant.sizeMarginDefault}}>
                                            <Text style={{

                                                borderRadius: Constant.scale(2),
                                                paddingLeft: Constant.scale(2),
                                                paddingRight: Constant.scale(2),
                                                backgroundColor: '#e5e5e5',
                                                color: Constant.colorTxtAlert
                                            }}>{purchaseQuantity.isMandatory?'整板':'建议'}1*{purchaseQuantity.multiple}</Text>
                                        </View> : null
                                }

                            </View>

                        </TouchableOpacity>


                    </View>

                    <TouchableOpacity
                        onPress={() => {
                            this.onShowSkuPanel(orgListIndex, cartIndex, cartItem);
                        }}
                        style={{
                            alignItems: 'center',
                            flexDirection: 'row',
                            padding: Constant.sizeMarginDefault,
                            marginLeft: Constant.scale(25),
                            minHeight: Constant.scale(30),
                            backgroundColor: Constant.colorBackgroundDefault
                        }}>

                        <View style={{flex: 1, paddingRight: 5}}>
                            <View style={{flexDirection: 'row', justifyContent: 'space-between', width: '100%',}}>
                                <Text numberOfLines={1} style={[styles.attrTxt]}>
                                    {awardsName}
                                </Text>

                                <Text style={[styles.attrTxt, {marginRight: 10}]}>
                                    {cartItem.withPlateFlag == '1' ? '带板' : '不带板'}
                                </Text>
                            </View>

                            <View style={{flexDirection: 'row', justifyContent: 'space-between', width: '100%',}}>
                                <Text numberOfLines={1} style={[styles.attrTxt]}>
                                    {cartItem.orgName}  {cartItem.bearing || ''}
                                </Text>

                                <Text style={[styles.attrTxt, {marginRight: 10}]}>
                                    {channelName}
                                </Text>
                            </View>


                        </View>

                        <Image
                            style={{width: Constant.scale(7.5), height: Constant.scale(4)}}
                            source={require('../img/arrow/ic_content_open.png')}/>
                    </TouchableOpacity>

                </View>
            </TouchableOpacity>
        )

    }

    //详情
    gotoDetailClick = (itemId, orgId) => {
        this.props.navigation.navigate('GoodsDetail', {itemId: itemId, defaultOrgId: orgId})
    }

};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    checkBox: {
        paddingRight: Constant.sizeMarginDefault,
        width: Constant.scale(15) + Constant.sizeMarginDefault,
        height: Constant.scale(15),
    },
    attrTxt: {
        fontSize: Constant.fontSizeSmall,
        color: Constant.colorTxtContent,
        // marginRight: Constant.scale(5),
    }


});
