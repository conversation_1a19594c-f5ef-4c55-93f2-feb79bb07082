import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    RefreshControl,
    TouchableOpacity,
    TouchableWithoutFeedback,
    ScrollView,
    TextInput,
    InteractionManager,
    DeviceEventEmitter,
    Dimensions
} from 'react-native';
import PropType from 'prop-types';
import { ReactNavComponent, Widget } from 'rn-yunxi';
import AppWidget from '../../app-widget';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import { inject, observer } from 'mobx-react/native';
import { toJS } from 'mobx';
import ShopCartItem from './ShopCartItem';
import SkuPanel from './SkuPanel';
import GoodsExtensionStore from '../../store/GoodsExtension'
import _ from 'lodash';
const { width, height } = Dimensions.get('window');
const { Header, Image, CheckBox, PriceText } = AppWidget;
const { Text } = Widget;
/**
 * 购物车
 */


// typeName: 'Retail',
@inject(stores => ({
    shopCart: stores.shopCart,
}))
@observer
export default class ShopCartList extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.goodsExtensionStore = new GoodsExtensionStore()
        let {params} = this.getNavState();
        this.state = {//状态机变量声明
            itemIds:params && params.itemIds && params.itemIds.length>0 ?params.itemIds : [],
        };
    }

    componentWillMount() {
        this.obtainShopCartList();

        this.didBlurSubscription = this.props.navigation.addListener(
            'didBlur',
            payload => {
                console.debug('didBlur');
            }
        );

        this.willDidFocusSubscription = this.props.navigation.addListener(
            'didFocus',
            payload => {

                console.debug('didFocus');
            }
        );


    }


    obtainShopCartList() {
        // this.props.shopCart.obtainShopCartList().then().catch(err => {})

        this.props.shopCart.obtainShopCartList((itemIdList) => {
            this.goodsExtensionStore.obtainAllItemOrgList(itemIdList, () => {
                // 获取托板承载规格列表
                this.goodsExtensionStore.getPalletStruList().then(() => {

                    // 托板承载规格列表
                    const palletStruList = this.goodsExtensionStore.palletStruList || [];
                    this.props.shopCart.setPalletStruList(palletStruList);
                    Log('goto merge')
                    this.props.shopCart.merge(null,this.state.itemIds)
                });
            }).then().catch(e => { })
        }).then().catch(e => { });
    }

    componentDidMount() {

    }

    componentWillUnmount() {
        this.didBlurSubscription.remove();
        this.willDidFocusSubscription.remove();
    }

    showSkuPanel(orgListIndex, cartIndex, cartItem, ) {
        Log('点击属性板', toJS(cartItem))

    }

    /**
     * 确认订单
     */
    confirmOrder = () => {
        this.props.shopCart.confirmOrder().then(data => {
            Log('=====结算成功data',toJS(data))
            if (data.data) {
                this.navigate('ConfirmOrder', { data: data.data, type: 2 })
            }
        }).catch(err => {
        })
    };


    //渲染
    render() {
        let shopCartLength = _.get(this.props.shopCart, 'shopCartData.length', 0);
        let { totalItem, totalCount, totalPrice, totalDiscountAmount, totalCartItemDtos } = this.props.shopCart.selectGoodCount();
        Log('选中的商品-----', totalCount)
        let showBack = false;
        let { params } = this.getNavState();
        if (params) {
            showBack = params.showBack ? true : false;
        }
        return (
            <Header showBackAction={showBack}
                title={'购物车'}
                rightAction={() => {
                    this.props.shopCart.setEditStatus(!this.props.shopCart.isEditStatus);
                }}
                rightTitle={this.props.shopCart.isEditStatus ? '完成' : '编辑'}>
                <KeyboardAwareScrollView contentContainerStyle={[styles.container]}>
                    <ScrollView
                        style={{flex:1}}
                        refreshControl={
                            <RefreshControl
                                enabled={true}
                                refreshing={this.props.shopCart.isLoading}
                                onRefresh={() => {
                                    this.obtainShopCartList()
                                }}
                                tintColor={Constant.colorPrimary}
                                title="加载中..."
                                titleColor={'black'}
                                progressBackgroundColor="white"
                            />}


                    >
                        {
                            this.props.shopCart.shopCartData.map((item, index) => {
                                // return <ShopCartItem  data={item}/>
                                return (
                                    <View key={'shopCart' + index}
                                        style={{
                                            backgroundColor: 'white',
                                            marginBottom: Constant.sizeMarginDefault
                                        }}>
                                        <TouchableWithoutFeedback
                                            style={{
                                                flex: 1,
                                            }}
                                            activeOpacity={1}
                                            onPress={() => {
                                                this.props.shopCart.selectCartItem(index, -1);
                                                this.setState({})
                                            }}>
                                            <View style={{
                                                flexDirection: 'row',
                                                alignItems: 'center',
                                                padding: Constant.sizeMarginDefault
                                            }}>
                                                <CheckBox
                                                    onClick={() => {
                                                        this.props.shopCart.selectCartItem(index, -1);
                                                    }}
                                                    style={styles.checkBox}
                                                    isChecked={!this.props.shopCart.isEditStatus ? item.normalSelected : item.editSelected} />
                                                <Image style={{
                                                    marginRight: Constant.sizeMarginDefault,
                                                    width: Constant.scale(17), height: Constant.scale(15)
                                                }}
                                                    source={require('../img/shopcart/ic_delivery_org.png')} />
                                                <Text style={{ color: Constant.colorTxtContent }}>{item.orgName}</Text>
                                            </View>
                                        </TouchableWithoutFeedback>
                                        <View style={GlobalStyle.styleDividerDefault} />
                                        {item.cartItemDtos.map((cartItem, cartIndex) => {
                                            return (
                                                <ShopCartItem
                                                    navigation={this.props.navigation}
                                                    isEditStatus={this.props.shopCart.isEditStatus}
                                                    // onShowSkuPanel={() => {
                                                    //     this.showSkuPanel(index, cartIndex, cartItem,)
                                                    // }}
                                                    orgList={this.goodsExtensionStore.orgListMap.get(cartItem.itemId)}
                                                    awardsMap={this.goodsExtensionStore.obtainItemAwards(cartItem.itemId)}
                                                    orgListIndex={index}
                                                    cartIndex={cartIndex}
                                                    onItemChoose={() => {
                                                        this.props.shopCart.selectCartItem(index, cartIndex);
                                                        this.setState({});
                                                    }}
                                                    onItemNumConfirm={(text) => {
                                                        this.props.shopCart.modifyCartItemNum(index, cartIndex, text, false,cartItem.isZeroFourGoods)
                                                        this.props.shopCart.callModifyCartItemNumApi(index, cartIndex, text).then().catch()
                                                    }}
                                                    // onItemNumChange={(text) => {
                                                    //     Log('onItemNumChange', text);
                                                    //     this.props.shopCart.modifyCartItemNum(index, cartIndex, text, true)
                                                    // }}
                                                    palletStruList = {this.goodsExtensionStore.palletStruList || []}
                                                    key={'cartItem-' + index + '-' + cartIndex}
                                                    data={cartItem}/>
                                            )
                                        })}
                                    </View>
                                )
                            })

                        }
                        {

                            shopCartLength == 0 ? <View style={[{ flex: 1, justifyContent: 'center', alignItems: 'center', }]}>

                                <Text style={{ fontSize: Constant.fontSizeBig, color: Constant.colorTxtContent, marginTop: Constant.scale(250) }}>
                                    暂无数据
                                </Text>
                            </View> : null
                        }

                    </ScrollView>
                </KeyboardAwareScrollView>
                <View style={GlobalStyle.styleDividerDefault} />
                {this.props.shopCart.isEditStatus ?
                    <View style={{
                        height: Constant.scale(55),
                        backgroundColor: 'white',
                        flexDirection: 'row',
                        width: width
                    }}>
                        <TouchableOpacity onPress={()=>this.props.shopCart.selectAll()} style={{ justifyContent: 'center', alignItems: 'center' }}>
                            <CheckBox
                                isChecked={this.props.shopCart.isSelectAllEdit}
                                style={{
                                    paddingLeft: Constant.sizeMarginDefault,
                                    paddingRight: Constant.sizeMarginDefault
                                }}
                                onClick={() => {
                                    this.props.shopCart.selectAll();
                                }} />
                            <Text
                                style={{
                                    marginLeft: Constant.scale(3.8),
                                    marginTop: Constant.scale(5),
                                    color: Constant.colorTxtAlert,
                                    fontSize: Constant.fontSizeSmall,
                                }}>
                                全选</Text>
                        </TouchableOpacity>
                        <View style={{ flex: 1 }} />

                        <Text style={{
                            marginRight: Constant.sizeMarginDefault,
                            color: Constant.colorTxtContent,
                            alignSelf: 'center',
                            fontSize: Constant.fontSizeSmall
                        }}>已选择{totalItem}个</Text>
                        <TouchableOpacity onPress={() => this.addToOftenBuyList(totalCartItemDtos)}
                            style={{
                                width: Constant.scale(105),
                                backgroundColor: '#FFE9EC',
                                justifyContent: 'center',
                                alignItems: 'center'
                            }}>
                            <Text style={{
                                color: Constant.colorPrimary,
                                fontSize: Constant.fontSizeBig
                            }}>加入常订</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            onPress={() => {
                                this.props.shopCart.callDeleteSelectCartItemApi().then().catch(err => {
                                })
                            }}
                            style={{ width: Constant.scale(105), backgroundColor: Constant.colorPrimary, justifyContent: 'center', alignItems: 'center' }}>
                            <Text style={{ color: 'white', fontSize: Constant.fontSizeBig }}>移除</Text>
                        </TouchableOpacity>
                    </View> :
                    <View style={{ height: Constant.scale(55), backgroundColor: 'white', flexDirection: 'row', width: width }}>
                        <TouchableOpacity onPress={()=>this.props.shopCart.selectAll()} style={{ justifyContent: 'center', alignItems: 'center' }}>
                            <CheckBox
                                isChecked={this.props.shopCart.isSelectAllNormal}
                                style={{ paddingLeft: Constant.sizeMarginDefault, paddingRight: Constant.sizeMarginDefault }}
                                onClick={() => {
                                    this.props.shopCart.selectAll();
                                }} />
                            <Text
                                style={{
                                    marginLeft: Constant.scale(3.8),
                                    marginTop: Constant.scale(5),
                                    color: Constant.colorTxtAlert,
                                    fontSize: Constant.fontSizeSmall,
                                }}>
                                全选</Text>
                        </TouchableOpacity>

                        <View style={{ marginRight: Constant.sizeMarginDefault, alignItems: 'flex-end', justifyContent: 'center', flex: 1 }}>
                            <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                                <Text style={{ marginRight: Constant.scale(5), color: Constant.colorTxtContent, alignSelf: 'center', fontSize: Constant.fontSizeSmall }}>已选{totalItem}种商品</Text>
                                <Text style={{ color: Constant.colorTxtContent, alignSelf: 'center', fontSize: Constant.fontSizeSmall }}>
                                    共<Text style={{ color: Constant.colorTxtPrimary }}>{totalCount}</Text>件
                                </Text>
                            </View>
                            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end', }}>
                                <Text style={{color:Constant.colorTxtPrimary}}>总价:<PriceText price={totalPrice}/></Text>
                            </View>
                        </View>


                        <TouchableOpacity
                            onPress={() => {
                                this.confirmOrder();
                            }}
                            style={{ width: Constant.scale(105), backgroundColor: Constant.colorPrimary, justifyContent: 'center', alignItems: 'center' }}>
                            <Text style={{ color: 'white', fontSize: Constant.fontSizeBig }}>结算</Text>
                        </TouchableOpacity>
                    </View>
                }


            </Header>
        );
    }

    addToOftenBuyList = (totalCartItemDtos) => {
        this.props.shopCart.addToOftenBuyList(totalCartItemDtos)
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    checkBox: {
        paddingRight: Constant.sizeMarginDefault,
        width: Constant.scale(15) + Constant.sizeMarginDefault,
        height: Constant.scale(15),
    },

});
