/**
 *
 * Created by <PERSON> on 2018/4/3.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    InteractionManager,
    AsyncStorage,
    Alert
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget/index'
import {toJS} from 'mobx';
import {inject, observer} from 'mobx-react/native';
import SkuPanelModel from '../../store/SkuPanel';
import SkuPanel from "../shopcart/SkuPanel";
import ToastUtil from "../../util/ToastUtil";

const Tag = 'ChargeWineItem';
const {width, height} = Dimensions.get('window');
const {Text} = Widget;
const {Image, PriceText, ConfirmInput} = AppWidget;
const DEFAULT_ICON = require('../img/img_default.png');

//模块声名并导出
@inject(stores => ({
    user: stores.user,
}))

@observer
export default class ChargeWineItem extends ReactNavComponent {
    //属性声名
    static propTypes = {
        onItemNumConfirm: PropType.func,
        chargeWineStore: PropType.object,
        itemData: PropType.object,
    };
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.skuPanel = new SkuPanelModel();
        this.skuPanel.setCheckMandatcory(false);
        //状态机变量声明
        this.state = {
            isFocus: false,
        };
    }

    componentDidMount() {
        this.initSkuPanel(this.props.itemData)
    }

    initSkuPanel = (itemData) => {
        let itemAwards = this.props.goodsExtensionStore.obtainItemAwards(itemData.itemId);

        let channelItem = this.props.channelItem;
        this.skuPanel.setSelectOrgId(this.props.orgId);
        this.skuPanel.setSelectChannelId(channelItem.channelId.salesChannelCode, channelItem.channelId.subSalesChannelCode);
        this.skuPanel.setSelectChancelName(channelItem.channelName.salesChannelName, channelItem.channelName.subSalesChannelName);
        this.skuPanel.setAwards(itemAwards);
        this.skuPanel.setSelectPlateFlag(0);
        this.skuPanel.setSuggestNumber(itemData.suggestNumber);
        this.skuPanel.setMandatoryNumber(itemData.mandatoryNumber);
        this.skuPanel.setPurchaseCount(0, false,channelItem.isZeroFourGoods);
        this.skuPanel.setItemIdAndOrgItem(itemData.itemId,
            {
                mandatoryNumber: itemData.mandatoryNumber,
                suggestNumber: itemData.suggestNumber,
                isWholeSend: this.props.isWholeSend,
                orgId: this.props.orgId,
                orgName: this.props.orgName
            });

        let tmp = this.skuPanel.getChoose();
        this.props.chargeWineStore.setDefaultAwardsIdAndChannelCode(itemData,tmp)

        // 设置可选择的托板承载规格列表
        this.skuPanel.setPalletStruList(this.props.goodsExtensionStore.palletStruList || []);
        this.skuPanel.setBatchSaleNum(itemData.batchSaleNum || '0');
        this.skuPanel.setBearing(itemData.bearing || '');
    };

    //渲染
    render() {
        let itemData = this.props.itemData;   //单个商品的数据
        let purchaseQuantity = this.skuPanel.purchaseQuantity;
        let awardsName = this.skuPanel.selectAwardName(this.props.goodsExtensionStore.obtainItemAwards(itemData.itemId));
        let channelName = this.props.user.getChancelFullName(itemData.salesChannelCode,itemData.subSalesChannelCode);
        let withPlateFlag = this.skuPanel.selectPlateValue;

        let itemNum = '';
        if (this.state.isFocus) {
            itemNum = this.skuPanel.tmpPurchaseCount + '';
        } else {
            itemNum = this.skuPanel.purchaseCount + '';
        }

        return (
            <View>
                <View style={{paddingLeft:Constant.sizeMarginDefault,flexDirection:'row',alignItems:'center',width:width,height:Constant.scale(44),borderBottomWidth:Constant.sizeDividerNormal,borderColor:Constant.colorDividerDefault,backgroundColor:'white'}}>
                    <Text style={{color:Constant.colorTxtContent}}>赠酒类型：</Text>
                    <Text>{itemData.description||'暂无'}</Text>
                </View>
                <View style={{
                    padding: Constant.sizeMarginDefault,
                    paddingRight: 0,
                    borderBottomWidth: Constant.sizeDividerNormal,
                    borderColor: Constant.colorDividerDefault,
                    backgroundColor: 'white',
                    marginBottom:Constant.scale(10)
                }}>
                    <View style={{flexDirection: 'row', justifyContent: 'center'}}>

                        <View style={{flex: 1, paddingRight: Constant.sizeMarginDefault}}>
                            <View style={{flexDirection: 'row', marginBottom: Constant.sizeMarginDefault}}>
                                <Image
                                    resizeMode={'cover'}
                                    source={{uri:itemData.imgUrl}}
                                     //defaultBackgroundColor={'#fff'}
                                    style={{
                                        borderWidth: Constant.sizeDividerNormal,
                                        borderColor: Constant.colorDividerDefault,
                                        marginRight: Constant.sizeMarginDefault,
                                        width: Constant.scale(75),
                                        height: Constant.scale(75)
                                    }}/>
                                <View style={{flex: 1, paddingLeft: Constant.scale(10)}}>
                                    <Text style={{color:Constant.colorTxtContent,fontSize:Constant.fontSizeSmall}} numberOfLines={1}>{itemData.name}</Text>
                                    <View style={{flexDirection: 'row', alignItems: 'flex-end',marginTop:Constant.sizeMarginDefault}}>
                                        <Text>可兑换：{itemData.useNum}</Text>
                                    </View>
                                    <View style={{flex: 1}}/>

                                    <ConfirmInput
                                        isFocus={(isFocus) => {
                                            if (isFocus) {
                                                this.skuPanel.setPurchaseCount(this.skuPanel.purchaseCount, true,itemData.isZeroFourGoods);
                                            } else {
                                                this.skuPanel.setPurchaseCount(this.skuPanel.tmpPurchaseCount, false,itemData.isZeroFourGoods);
                                            }
                                            this.setState({isFocus: isFocus}, () => {
                                                if (!this.state.isFocus) {
                                                    this.props.onItemNumConfirm && this.props.onItemNumConfirm(this.skuPanel.purchaseCount);
                                                }
                                            })

                                        }}
                                        value={itemNum}
                                        onChangeText={(text) => {
                                            if (text > itemData.useNum){
                                                ToastUtil.show('输入商品数量大于可兑换数量');
                                                this.skuPanel.setPurchaseCount(0, true,itemData.isZeroFourGoods)
                                                return;
                                            }
                                            this.skuPanel.setPurchaseCount(text, true,itemData.isZeroFourGoods)
                                        }}/>
                                </View>
                                {
                                    purchaseQuantity.multiple > 0 ||itemData.itemCode.startsWith('04') ? <View style={{marginLeft: Constant.sizeMarginDefault}}>
                                        <Text style={{

                                            borderRadius: Constant.scale(2),
                                            paddingLeft: Constant.scale(2),
                                            paddingRight: Constant.scale(2),
                                            backgroundColor: '#e5e5e5',
                                            color: Constant.colorTxtAlert
                                        }}>{purchaseQuantity.isMandatory?'整板':'建议'}1*{purchaseQuantity.multiple}</Text>
                                    </View> : null
                                }
                            </View>

                        </View>

                    </View>
                    <TouchableOpacity
                        onPress={() => this.showSkuPanel(itemData)}
                        style={{
                            alignItems: 'center',
                            flexDirection: 'row',
                            padding: Constant.sizeMarginDefault,
                            marginLeft:Constant.scale(0),
                            minHeight: Constant.scale(30),
                            width:'100%',
                            backgroundColor: Constant.colorBackgroundDefault
                        }}>
                        <View style={{flex:1,paddingRight:5}}>
                            <View style={{flexDirection: 'row',justifyContent:'space-between',width:'100%',}}>
                                <Text numberOfLines={1} style={[styles.attrTxt]}>
                                    {awardsName}
                                </Text>

                                <Text style={[styles.attrTxt,{marginRight:10}]}>
                                    {withPlateFlag ? '带板' : '不带板'}
                                </Text>
                            </View>

                            <View style={{flexDirection: 'row',justifyContent:'space-between',width:'100%'}}>
                                <Text numberOfLines={1} style={[styles.attrTxt]}>
                                    {this.props.orgName}  {this.skuPanel.bearing || ''}
                                </Text>

                                <Text style={[styles.attrTxt,{marginRight:10}]}>
                                    {channelName}
                                </Text>
                            </View>


                        </View>

                        <Image style={{width: Constant.scale(7.5), height: Constant.scale(4)}}
                               source={require('../img/arrow/ic_content_open.png')}
                        />
                    </TouchableOpacity>

                </View>
            </View>
        )
    }

    // showPanel 选择不同属性
    showSkuPanel(itemData) {
        Widget.Popup.show(
            <SkuPanel
                skuPanelStore={this.skuPanel}
                awardsMap = {this.props.goodsExtensionStore.obtainItemAwards(itemData.itemId)}
                orgId={this.skuPanel.selectOrgId}
                channelListData={toJS(this.props.user.channelList)}
                salesChannelCode={this.skuPanel.selectChannelId.salesChannelCode}
                subSalesChannelCode={this.skuPanel.selectChannelId.subSalesChannelCode}
                salesChannelName={this.skuPanel.selectChannelName.salesChannelName}
                subSalesChannelName={this.skuPanel.selectChannelName.subSalesChannelName}
                plateFlag={this.skuPanel.selectPlateId}
                awardId={this.skuPanel.selectAwardId}
                palletStruList = {this.skuPanel.palletStruList}
                batchSaleNum = {this.skuPanel.batchSaleNum}
                bearing = {this.skuPanel.bearing}
                isGiftWinePanel={true}
                onConfirm={(newSkuPanel) => {
                    //更新规格，刷新列表
                    Widget.Popup.hide();
                    InteractionManager.runAfterInteractions(() => {
                        this.props.chargeWineStore.modifyItem(itemData, toJS(newSkuPanel), this.props.orgId).then().catch(err => {
                        })
                    })
                }}
                onCancel={() => {
                    Widget.Popup.hide()
                }
                }/>,
            {
                animationType: 'slide-up', backgroundColor: '#00000000',
                onMaskClose: () => {
                    Widget.Popup.hide()
                }
            })
    }
};
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Constant.colorBackgroundDefault
    },
    attrTxt: {
        fontSize: Constant.fontSizeSmall,
        color: Constant.colorTxtContent,
        marginRight: Constant.scale(5),
    }
});
