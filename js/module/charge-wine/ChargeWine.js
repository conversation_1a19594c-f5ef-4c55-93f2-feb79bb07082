/**
 *
 * Created by <PERSON> on 2018/4/3.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    ScrollView, TouchableOpacity, InteractionManager,
    FlatList,
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget/index'
import ChargeWineModel from '../../store/ChargeWine';
import {inject, observer} from 'mobx-react/native'
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
import ChargeWineItem from "./ChargeWineItem";
import user from "../../store/User";
import {toJS} from 'mobx';
import GoodsExtension from "../../store/GoodsExtension";

const {width, height} = Dimensions.get('window');
const {Text,CommonFlatList} = Widget;
const {<PERSON><PERSON>,PriceText} = AppWidget;
const Tag = 'ChargeWine';
@inject(stores => ({
    user:stores.user
}))

//费用赠酒
@observer
//模块声名并导出
export default class ChargeWine extends ReactNavComponent {
    //属性声名
    static propTypes = {
    };
    //默认属性
    static defaultProps = {
    };
    //构造函数
    constructor(props) {
        super(props);
         //状态机变量声明
         this.state={

         };
        this.goodsExtensionStore = new GoodsExtension();
        this.chargeWineStore = new ChargeWineModel();

        let { params } = this.getNavState();
        this.accountType = params.accountType;
        this.orgId = params.orgId;
        this.orgName = params.orgName;
        this.marketingCosts = params.marketingCosts;
    }

    componentWillMount(){
    }
    componentDidMount(){
        // 加载托板承载规格列表
        this.goodsExtensionStore.getPalletStruList();
        this.props.user.obtainChannelList().then().catch();
        this.chargeWineStore.getAccountTypeList(this.accountType,this.orgId,(itemIdArray)=>{
            this.goodsExtensionStore.setOrgListMap(itemIdArray,this.orgId,()=>{});
        }).then().catch(err=>{})
    }
    componentWillUnmount(){

    }

    renderBottomView = () => {
        let {totalItem,totalCount,totalPrice} = this.chargeWineStore.selectGoodsCount();
        let canUseMarketingCosts = this.marketingCosts - totalPrice;
        return (
            <View style={{height: Constant.scale(60), backgroundColor: 'white', flexDirection: 'row', width: width, justifyContent:'flex-end'}}>
                {
                    <View style={{justifyContent:'center',alignItems:'flex-end',marginRight:Constant.scale(15)}}>
                        <Text style={{fontSize:Constant.scale(12),color:Constant.colorTxtContent}}>
                            共{totalItem ? totalItem : 0}种商品 共{totalCount ? totalCount : 0}件
                        </Text>
                        <View style={{flexDirection:'row',justifyContent:'center',alignItems:'center'}}>
                            <Text style={{fontSize:Constant.scale(12),color:Constant.colorTxtContent}}>
                                营销费用小计:
                            </Text>
                            <PriceText price={totalPrice ? totalPrice : 0} size={2}/>
                        </View>
                        <View style={{flexDirection:'row',justifyContent:'center',alignItems:'center'}}>
                            <Text style={{fontSize:Constant.scale(12),color:Constant.colorTxtContent}}>
                                可用营销费用:
                            </Text>
                            <PriceText price={canUseMarketingCosts ? canUseMarketingCosts : 0} size={2}/>
                        </View>
                    </View>
                }

                <TouchableOpacity
                    onPress={() => {
                        this.confirmOrderClick();
                    }}
                    style={{width: Constant.scale(105), backgroundColor: Constant.colorPrimary, justifyContent: 'center', alignItems: 'center'}}>
                    <Text style={{color: 'white', fontSize: Constant.fontSizeBig}}>结算</Text>
                </TouchableOpacity>
        </View>
        )
    };

    //渲染
    render() {
        let listParams = this.chargeWineStore.listParams;

        let channelItem = this.props.user.getFirstChannelItem();
        return (
            <Header style={styles.container} title={'费用赠酒'} navigation= {this.props.navigation}>

                <CommonFlatList
                    style={{marginTop:Constant.scale(10)}}
                    data={listParams.data}
                    listState={listParams.listState}
                    enableLoadMore={listParams.enableLoadMore}
                    keyExtractor={(item, index) => {
                        return 'chargeWine' + index;
                    }}
                    renderItem={({item,index})=>{
                        return (<ChargeWineItem
                            listIndex={index}
                            goodsExtensionStore={this.goodsExtensionStore}
                            itemData={item}
                            orgName={this.orgName}
                            orgId={this.orgId}
                            channelItem={channelItem}
                            chargeWineStore={this.chargeWineStore}
                            isWholeSend={item.withPlateFlag}
                            onItemNumConfirm={(text) => {
                                this.chargeWineStore.modifyItemNum(index, text, false,item.isZeroFourGoods);
                                this.setState({})}
                            }
                        />)
                    }}
                    enableRefresh={false}
                >
                    <View style={[{flex: 1, justifyContent: 'center', alignItems: 'center',}]}>

                        <Text style={{fontSize: Constant.fontSizeBig, color: Constant.colorTxtContent, marginTop: Constant.scale(25)}}>
                            暂无数据
                        </Text>
                    </View>
                </CommonFlatList>

                <View style={GlobalStyle.styleDividerDefault}/>
                {this.renderBottomView()}
            </Header>
        );
    }

    confirmOrderClick = () => {
        this.chargeWineStore.confirmOrder(3,this.orgId).then(
            (json)=>{
                if (json && json.data){
                    this.navigate('ConfirmOrder', {data:json.data,type:1,businessType:5});
                }
            }
        ).catch((e)=>{})
    }
};
const styles = StyleSheet.create({
   container: {
       flex: 1,
       backgroundColor: Constant.colorBackgroundDefault
    }
});
