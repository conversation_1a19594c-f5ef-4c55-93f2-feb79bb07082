import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    Dimensions
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget';

const {width, height} = Dimensions.get('window');
import {inject, observer} from 'mobx-react/native';

const {Header} = AppWidget;
const {LabelCell, Text} = Widget;

/**
 * 发票详情
 */
@inject(stores => ({
    invoice: stores.invoice,
}))
@observer
export default class InvoiceInfo extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
        };
    }

    componentWillMount() {

        this.props.invoice.getInvoice().then().catch(err => {
        });
    }

    componentDidMount() {
    }

    componentWillUnmount() {
    }

    renderItem = (key, value) => {
        return (
            <LabelCell titleTextStyle={{color: Constant.colorTxtContent}}
                       title={key}
                       underLine={1}
                       rightIcon={<View/>}
                       extra={<Text style={{
                           color: Constant.colorTxtTitle,
                           fontSize: Constant.fontSizeNormal,

                       }}>{value}</Text>}
            />
        );
    }

    //渲染
    render() {
        let data = this.props.invoice.data;
        return (
            <Header style={styles.container} title={'发票信息'}>
                {data ?
                    <View style={{marginTop: Constant.sizeMarginDefault}}>
                        {/*{this.renderItem('发票类型:', data.invoiceType)}*/}
                        {this.renderItem('纳税人识别码:', data.identificationCode)}
                        {this.renderItem('单位名称:', data.orgName)}
                        {this.renderItem('注册地址:', data.address)}
                        {this.renderItem('注册电话:', data.phone)}
                        {this.renderItem('开户银行:', data.bankName)}
                        {this.renderItem('银行账户:', data.accountNumber)}

                    </View> : null}
            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    }
});
