/**
 * Created by lao.jian<PERSON> on 2017/6/13.
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Platform,
    BackHandler,
    ScrollView,
    Image,
    Dimensions,
    ActivityIndicator,
    InteractionManager
} from 'react-native';
import { NavigationActions } from 'react-navigation';
import { ReactNavComponent, Widget } from 'rn-yunxi';
import AppWidget from '../../app-widget';
const { width, height } = Dimensions.get('window');
const { Text, LabelCell, Button } = Widget;
const { Header } = AppWidget;

const TIPS_ICON = require('../img/pay/tips.png');
const REFRESH_ICON = require('../img/pay/refresh.png');

const CHECK_COUNT_TIME = 20;

//模块声名并导出
const backHandlerFunc = function () {
    return true;
};
export default class InPayment extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
            countTime: 5,
            checkPayStatusTime: 0,
        };
        this.timeOutClick = true;
    }

    componentWillMount() {
        if (Platform.OS === 'android') {
            BackHandler.addEventListener('hardwareBackPress', backHandlerFunc)
        }
    }

    componentDidMount() {
        this.timeOutFun();

    }


    componentWillUnmount() {
        if (Platform.OS === 'android') {
            BackHandler.removeEventListener('hardwareBackPress', backHandlerFunc)
        }
        if (this.callOrderStateTimeOut) {
            clearTimeout(this.callOrderStateTimeOut);
        }

    }

    timeOutFun() {
        if (this.state.countTime > 0) {
            setTimeout(() => {
                this.setState({ countTime: this.state.countTime - 1 });
                this.timeOutFun()
            }, 1000)
        }
    }

    /**
    * 支付后订单状态-轮循刷接口
     */
    checkOrderStatus() {
        if (this.timeOutClick) {
            // let state = this.getNavState();
            // let params = state.params.params;
            // let payNo = params.payNo;
            // Object.assign(params, { payWayName: params.payWayName });
            // Api.orderPayStatus({ payNo: payNo }).then((result) => {
            //     //TODO 检查状态
            //     let endTime = new Date().getTime();
            //     if (result.data && result.data.status == 'S') {
            //         this.timeOutClick = false;
            //         this.setState({ countTime: 0 })
            //         this.navigate('PaySuccess', params);
            //     } else if (result.data && result.data.status == 'F') {
            //         this.timeOutClick = false;
            //         this.setState({ countTime: 0 })
            //         this.navigate('PayFail', params);
            //     } else if (this.state.checkPayStatusTime < CHECK_COUNT_TIME) {
            //         this.setState({ checkPayStatusTime: this.state.checkPayStatusTime + 1 })
            //         if (this.timeOutClick) {
            //             this.callOrderStateTimeOut = setTimeout(() => this.checkOrderStatus(), 2000)
            //         }
            //     }
            // }).catch((err) => {
            //     this.navigate('PayFail', params);
            // });
        }
    }



    //渲染
    render() {
        let { params } = this.getNavState();
        let overCount = this.state.countTime > 0;
        let checkCount = this.state.checkPayStatusTime >= CHECK_COUNT_TIME
        let timeText = overCount ? this.state.countTime + ' S ' : ''
        return (
            <Header title={"确认支付"} showBackAction={false} navigation={this.props.navigation}>
                <View
                    style={styles.scrollView}>
                    <View style={styles.container}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                            <Text style={{ marginRight: overCount ? 10 : 0, fontSize: Constant.fontSizeHeader, }}>支付确认中请稍后</Text>
                            {!checkCount ? <ActivityIndicator
                                size="large"
                                color="#ff6c00"
                            /> : <View />}
                        </View>

                        <Button
                            disabled={overCount}
                            style={[styles.checkOrderBtn,]}
                            txtStyle={overCount ? { color: Constant.colorTxtContent } : { color: Constant.colorTxtTitle }}
                            onPress={() => {
                                this.timeOutClick = false;
                                let resetAction = NavigationActions.reset({
                                    index: 1,
                                    actions: [
                                        NavigationActions.navigate({ routeName: 'DefaultTab' }),
                                        NavigationActions.navigate({ routeName: 'OrderList', params: {type: 1}}),
                                    ]
                                });
                                this.dispatch(resetAction)
                            }}>{timeText} 返回订单</Button>

                        {checkCount==0 ? <TouchableOpacity onPress={() => {
                            this.setState({
                                checkPayStatusTime: 0,
                            })
                            this.checkOrderStatus();
                        }}>
                            <View style={[styles.checkOrderBtn,
                            {
                                flexDirection: 'row',
                                marginTop: Constant.scale(40),
                                borderWidth: 0,
                                alignItems: 'center',
                            }
                            ]}>
                                <Text style={{ color: Constant.colorTxtContent, marginRight: 5 }}>重新加载</Text>
                                <Image source={REFRESH_ICON} /></View>
                        </TouchableOpacity> : null}
                    </View>
                    <View style={{
                        backgroundColor: '#F3F3F3',
                        padding: Constant.sizeMarginDefault,
                        paddingTop: Constant.scale(25),
                        paddingBottom: Constant.scale(25),
                    }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <Image
                                style={{ width: Constant.scale(21), height: Constant.scale(21), marginRight: 5 }}
                                source={TIPS_ICON} />
                            <Text style={{
                                fontSize: Constant.fontSizeNormal,
                                color: Constant.colorTxtContent,
                            }}>温馨提示：</Text>
                        </View>
                        <Text style={styles.textAlert}>1、当前状态为平台确认订单支付，由于某些原因，可能会造成支付确认时间延迟，给您造成不便，还请原谅</Text>
                        <Text style={styles.textAlert}>2、您可选择“重新刷新”查看支付确认结果，或者选择“返回订单”，继续平台购物</Text>
                        {false && <Text style={styles.textAlert}>3、对支付结果有疑虑，可联系平台客服400-400-400</Text>}
                    </View>
                </View>
            </Header >

        );
    }
};
const styles = StyleSheet.create({
    scrollView: {
        backgroundColor: 'white',
        flex: 1
    },
    container: {
        alignItems: 'center',
        backgroundColor: 'white',
        justifyContent: 'flex-start',
        flex: 1,
        paddingTop: 50,
    },
    topContent: {
        marginTop: 50,
        alignItems: 'center',
    },
    orderOK: {
        width: 90,
        height: 90,
        marginBottom: 20,
    },
    highLightTxt: {
        color: Constant.colorTxtPrimary,
    },
    payTxt: {
        color: Constant.colorTxtContent,
    },
    payContent: {
        justifyContent: 'center',
        alignItems: 'flex-start',
        flex: 1,
    },
    payContentItem: {
        flexDirection: 'row',
        height: 40,
        justifyContent: 'center',
        alignItems: 'center'
    },

    checkOrderBtn: {
        height: 36,
        width: '40%',
        borderWidth: Constant.sizeDividerNormal,
        borderColor: Constant.colorTxtContent,
        borderRadius: 2,
        marginTop: 50
    },

    textAlert: {
        marginTop: Constant.sizeMarginDefault,
        fontSize: Constant.fontSizeSmall,
        color: Constant.colorTxtContent,

    }
});

