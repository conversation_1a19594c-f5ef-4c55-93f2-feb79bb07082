/**
 * Created by lao.jianfeng on 2017/7/6.
 */
/**
 * Created by lao.jianfeng on 2017/5/26.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Alert,
    ScrollView,
    ART,
    TouchableHighlight,
    ListView,
    Dimensions,
    NativeModules
} from 'react-native';
import Svg, {
    Circle,
    Ellipse,
    G,
    LinearGradient,
    RadialGradient,
    Line,
    Path,
    Polygon,
    Polyline,
    Rect,
    Symbol,
    Use,
    Defs,
    Stop
} from 'react-native-svg';
import {ReactNavComponent, Widget,AliPay} from 'rn-yunxi';
import ScrollableTabView from 'react-native-scrollable-tab-view';

const {width, height} = Dimensions.get('window');
import AppWidget from '../../app-widget';
const {Text, CommonListView, SwipeListView, SwipeRow,Button,CaptchaView} = Widget;
const {Header, PrimaryHeade, DefaultTabBar, Image} = AppWidget

//模块声名并导出
export default class extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {

        super(props);
        this.dataSource = new ListView.DataSource({
            /*判断这两行是否相同，就是是否发生变化，决定渲染哪些行组件，避免全部渲染，提高渲染效率*/
            rowHasChanged: (oldRow, newRow) => oldRow !== newRow
        });

        this.state = {//状态机变量声明
            time: 0,
            listViewData: Array(20).fill('').map((_, i) => `item #${i}`),
            listState: CommonListView.STATE_HAS_MORE,
            enableLoadMore: true,
        };
    }

    componentWillMount() {

    }

    componentDidMount() {


    }

    componentWillUnmount() {
    }

    componentWillReceiveProps(nextProps) {

    }

    shouldComponentUpdate(nextProps, nextState) {
        return true;
    }


    componentWillUnmount() {
    }

    deleteRow(secId, rowId, rowMap) {
        // Log(rowMap);
        rowMap[`${secId}${rowId}`].closeRow();//
        // Log(rowMap)
        const newData = [...this.state.listViewData];
        newData.splice(rowId, 1);
        this.setState({listViewData: newData});
    }

    onLoadMore = () => {
        this.setState({time: (this.state.time + 1), listState: CommonListView.STATE_LOADING_MORE}, () => {
            setTimeout(() => {
                this.setState({
                    listViewData: this.state.listViewData.concat(Array(20).fill('').map((_, i) => `item #${(this.state.time * 20) + i}`)),
                    listState: CommonListView.STATE_NO_MORE,
                    enableLoadMore: false,
                })
            }, 1000);
        })
    }


    //渲染
    render() {
        return (
            <View style={styles.container}>

                <Button onPress={()=>{
                    // AliPay.pay( 'biz_content=%7B%22out_trade_no%22%3A%2220171219164710000024%22%2C%22total_amount%22%3A%220.01%22%2C%22subject%22%3A%22%E6%94%AF%E4%BB%98%E5%AE%9DAPP%E6%94%AF%E4%BB%98%E6%B5%8B%E8%AF%95%22%2C%22timeout_express%22%3A%2230m%22%2C%22goods_type%22%3A%220%22%2C%22product_code%22%3A%22QUICK_MSECURITY_PAY%22%7D&app_id=2016122604626182&charset=UTF-8&format=JSON&method=alipay.trade.app.pay&notify_url=http%3A%2F%2Fdev.dtyunxi.cn%3A8300%2Fhuieryun-paymentcenter%2Fnotify%2Ftrade%2F20171219164710000024%2Fresult.htm&sign=CJcnoPW8cDzgrR3hI%2FluwNBW76vIIp1%2F2JxSiyVRIMHan31T73yG4M%2FHR1EZqPlSNuIEGJ3T0KMuPkg9jRinvkAV%2FRHhfIsCdV0j2Lgn9K1f%2FiB6YxwFY3M7px8OrAb5XFhl5D3I3u%2BYFazVlhdVeov19n2SFdcJxSE2p5f9nNEz%2FM7dtckiYervEeMnRfxQxhw2YWKr8OrteNdK2oPL2cSsvqXMmmwQalW4yGreK82AATNjOgSPs%2FskeZISddJ0Qa3dcxhsspBl6nyDV62Gr0m1BL8J8yilt1UalOpydJ5%2BzhCQcTuA3i78yxoI0mHmnJ7OTPSEewBm6W1J80bYkg%3D%3D&sign_type=RSA2&timestamp=2017-12-19+16%3A47%3A11&version=1.0')
                    //     .then(data=>{
                    //         Log(data,'111111')
                    //     }).catch(err=>{
                    //         Log(err,'222222222')
                    // })
                    // ;
                    AliPay.payWithUrl('https://ds.alipay.com/?from=mobilecodec&scheme=alipays%3A%2F%2Fplatformapi%2Fstartapp%3FsaId%3D10000007%26clientVersion%3D3.7.0.0718%26qrcode%3Dhttps%253A%252F%252Fqr.alipay.com%252Fbax06608zubgfwgsdp3d4004%253F_s%253Dweb-other').then(data=>{}).catch()
                }}>测试二维码</Button>

                {/*<CaptchaView   onRegionChange = {(event)=>{*/}
                    {/*Log('验证码==========',event.checkCode)*/}
                {/*}}/>*/}

            </View>
        );
    }

    // onRegionChange=(event: Event)=>{
    //     console.log('ios == '+event.checkCode);
    //     this.code = event.checkCode;
    // }


};

const styles = StyleSheet.create({
    container: {
        backgroundColor: 'white',
        alignItems:'center',
        justifyContent:'center',
        flex: 1
    },
    standalone: {
        marginTop: 30,
        marginBottom: 30,
    },
    standaloneRowFront: {
        alignItems: 'center',
        backgroundColor: '#CCC',
        justifyContent: 'center',
        height: 50,
    },
    standaloneRowBack: {
        alignItems: 'center',
        backgroundColor: '#8BC645',
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: 15
    },
    backTextWhite: {
        color: '#FFF'
    },
    rowFront: {
        alignItems: 'center',
        backgroundColor: '#CCC',
        borderBottomColor: 'black',
        borderBottomWidth: 1,
        justifyContent: 'center',
        height: 50,
    },
    rowBack: {
        alignItems: 'center',
        backgroundColor: '#DDD',
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingLeft: 15,
    },
    backRightBtn: {
        alignItems: 'center',
        bottom: 0,
        justifyContent: 'center',
        position: 'absolute',
        top: 0,
        width: 75
    },
    backRightBtnLeft: {
        backgroundColor: 'blue',
        right: 75
    },
    backRightBtnRight: {
        backgroundColor: 'red',
        right: 0
    },
    controls: {
        alignItems: 'center',
        marginBottom: 30
    },
    switchContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginBottom: 5
    },
    switch: {
        alignItems: 'center',
        borderWidth: 1,
        borderColor: 'black',
        paddingVertical: 10,
        width: 100,
    }
});
