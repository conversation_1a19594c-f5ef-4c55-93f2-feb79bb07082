import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    Dimensions
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget,Util} from 'rn-yunxi';
import ValidatorUtil from '../../util/ValidatorUtil';
import AppWidget from '../../app-widget';
import User from '../../store/User'
import {inject} from "mobx-react/native";
import {observer} from "mobx-react/native";
const {width, height} = Dimensions.get('window');
const {Header} = AppWidget;
const {InputFormCell, Text} = Widget;
const {CountdownUtil} = Util;
/**
 * 重置密码
 */
@inject(stores => ({
    user: stores.user,
}))
@observer
export default class ResetPassword extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
            account: '',//登录帐号
            mobile: '',//预留手机号
            password: '',//设置密码
            confirmPassword: '',//确认密码
            checkCode: '',//验证码
            isSentVerify:false,
            timerTitle: '获取验证码',
            uniqueId: null,
        };
    }

    componentWillMount() {
    }

    componentDidMount() {
    }

    resetPassword() {
        let rule = {
            account: [
                {required: true, not: '', msg: '请输入登录账号'},
            ],
            mobile: [
                {required: true, not: '', msg: '请输入预留手机号'},
                {length: [11, 16], msg: '手机号必须是11~16位'},
            ],
            password: [
                {required: true, not: '', msg: '请输入密码'},
                {length: [6, 16], msg: '密码必须是6~16位'},
            ],
            confirmPassword: [
                {required: true, not: '', eq: this.state.password, msg: '确认密码跟设置密码不一致'},
            ],
            checkCode: [
                {required: true, not: '', msg: '请输入验证码'},
            ]

        };
        if (ValidatorUtil.validate(this.state, rule)) {
            Api.resetPassword({
                code: this.state.account,
                phone: this.state.mobile,
                password: this.state.password,
                verifyCode: this.state.checkCode,
            }).then(() => {
                //TODO 激动账号
                this.goBack();
            }).catch(err=>{});
        }
    }

    checkPhone = (mobile) => {
        let rule = {
            account: [
                {required: true, not: '', msg: '请输入登录账号'},
            ],
            mobile: [
                {required: true, not: '', msg: '请输入预留手机号'},
                {length: [11, 16], msg: '手机号必须是11~16位'},
            ],
            password: [
                {required: true, not: '', msg: '请输入密码'},
                {length: [6, 16], msg: '密码号必须是6~16位'},
            ],
            confirmPassword: [
                {required: true, not: '', eq: this.state.password, msg: '确认密码跟设置密码不一致'},
            ],
            // checkCode: [
            //     {required: true, not: '', msg: '请输入验证码'},
            // ]

        };

        // Log('测试===>'+mobile);
        let type = 1;
        if (this.props.user && this.props.user.userInfo && this.props.user.userInfo.type){
            type = this.props.user.userInfo.type
        }
        let params = {}
            params.phone = mobile,
            params.type = type

        if (ValidatorUtil.validate(this.state, rule)) {
            Api.checkPhone(params).then((data) => {
                if (data && data.data) {
                    this.obtainCheckCode();
                }
            }).catch((err) => {
            })
        }


    }

    obtainCheckCode() {
        let rule = {
            mobile: [
                {required: true, not: '', msg: '请输入手机号'},
                {length: [11,16], msg: '手机号必须是11~16位'},
            ]

        };
        if (ValidatorUtil.validate(this.state, rule)) {
            //TODO 获取验证码API
            if (this.state.isSentVerify === false) {
                let countdownDate = new Date(new Date().getTime() + 60 * 1000)
                // 点击之后验证码不能发送网络请求
                this.setState({
                    isSentVerify: true
                }, () => {
                    Api.obtainResetPasswordSmsCode({mobile:this.state.mobile,templateCode:'DEALER_FIND_PASSWORD'}).then((data) => {
                        if (data && data.data) {
                            this.setState({uniqueId: data.data})
                            CountdownUtil.settimer(countdownDate, (time) => {
                                this.setState({
                                    timerTitle: time.sec > 0 ? time.sec + 's' : '重新获取'
                                }, () => {
                                    if (this.state.timerTitle == "重新获取") {
                                        this.setState({
                                            isSentVerify: false
                                        })
                                    }
                                })
                            })
                        }
                    }).catch((err) => {
                        this.setState({
                            isSentVerify: false,
                            timerTitle: '重新获取'
                        })
                    })
                });


            }
        }
    }

    //渲染
    render() {
        return (
            <Header title={"重置密码"}>
                <View style={styles.container}>
                    <InputFormCell
                        value={this.state.account}
                        onChangeText={(txt)=>{this.setState({account:txt})}}
                        underLine={1}
                        titleStyle={styles.titleStyle}
                        cellStyle={styles.cellStyle}
                        title={"登录帐号"} placeholder={"请输入客户编码/用户名"}/>
                    <InputFormCell
                        value={this.state.mobile}
                        onChangeText={(txt)=>{this.setState({mobile:txt})}}
                        underLine={1}
                        titleStyle={styles.titleStyle}
                        cellStyle={styles.cellStyle}
                        title={"预留手机号"} placeholder={"请输入预留手机号"}/>

                    <InputFormCell
                        value={this.state.password}
                        underLine={1}
                        secureTextEntry={true}
                        onChangeText={(txt)=>{this.setState({password:txt})}}
                        titleStyle={styles.titleStyle}
                        cellStyle={styles.cellStyle}
                        title={"设置密码"} placeholder={"建议至少2种字符组合"}/>
                    <InputFormCell
                        value={this.state.confirmPassword}
                        underLine={1}
                        secureTextEntry={true}
                        onChangeText={(txt)=>{this.setState({confirmPassword:txt})}}
                        titleStyle={styles.titleStyle}
                        cellStyle={styles.cellStyle}
                        title={"确认密码"} placeholder={"请再次输入密码"}/>
                    <InputFormCell
                        value={this.state.checkCode}
                        underLine={2}
                        onChangeText={(txt)=>{this.setState({checkCode:txt})}}
                        titleStyle={styles.titleStyle}
                        cellStyle={styles.cellStyle}
                        title={"验证码"} placeholder={"请输入验证码"}
                        rightIcon={<TouchableOpacity
                            onPress={() => {this.checkPhone(this.state.mobile)}}>
                            <Text style={{color: Constant.colorTxtPrimary}}>{this.state.timerTitle}</Text>
                        </TouchableOpacity>}
                    />


                    <TouchableOpacity style={[{
                        marginTop: Constant.sizeMarginDefault,
                        borderRadius: Constant.scale(6),
                        height: Constant.scale(44), width: '94%',
                        backgroundColor: Constant.colorPrimary,
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginBottom: Constant.scale(20)
                    }]}
                                      onPress={() => {
                                          this.resetPassword();
                                      }}>
                        <Text style={{color: 'white', fontSize: Constant.fontSizeBig}}>立即提交</Text>
                    </TouchableOpacity>
                    <View style={{flex: 1}}/>
                    <Text style={{color: Constant.colorPlaceholder, marginBottom: Constant.sizeMarginDefault}}>如果忘记经销商编码请联系工作人员获取</Text>
                </View>
            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center',
        width: width,
    },
    cellStyle: {
        width: width,
        height: Constant.scale(50)
    },
    titleStyle: {

        color: Constant.colorTxtContent,
        width: Constant.scale(90)
    }
});
