import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    Alert
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget, Util} from 'rn-yunxi';
import ValidatorUtil from '../../util/ValidatorUtil';
import AppWidget from '../../app-widget';

const {width, height} = Dimensions.get('window');
const {Header} = AppWidget;
const {InputFormCell, Text} = Widget;
const {CountdownUtil} = Util;
/**
 * 激活账号
 */
export default class ActivateAccount extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
            account: '',//登录帐号
            mobile: '',//预留手机号
            name: '',//用户名
            password: '',//设置密码
            confirmPassword: '',//确认密码
            checkCode: '',//验证码
            isSentVerify: false,
            timerTitle: '获取验证码',
            uniqueId: null,
        };
    }

    componentWillMount() {
    }

    componentDidMount() {
    }

    componentWillUnmount() {
        CountdownUtil.stop();
    }

    checkPhone = (params) => {
        let rule = {
            phone: [
                {required: true, not: '', msg: '请输入预留手机号'},
                {length: [11, 16], msg: '手机号必须是11~16位'},
            ],

        };
        if (ValidatorUtil.validate(params, rule)){
            Api.checkPhone(params).then((data) => {
                if (data && data.data){
                    this.obtainCheckCode();
                }
            }).catch((err) => {
            })
        }

    }

    obtainCheckCode() {
        let rule = {
            account: [
                {required: true, not: '', msg: '请输入客户编码'},
            ],
            mobile: [
                {required: true, not: '', msg: '请输入预留手机号'},
                {length: [11, 16], msg: '手机号必须是11~16位'},
            ],
            name: [
                {required: true, not: '', msg: '请输入用户名'},
                {length: [1, 10], msg: '用户名必须是1~10位'},
            ],
            password: [
                {required: true, not: '', msg: '请输入密码'},
                {length: [6, 16], msg: '密码号必须是6~16位'},
            ],
            confirmPassword: [
                {required: true, not: '', eq: this.state.password, msg: '确认密码跟设置密码不一致'},
            ]


        };
        if (ValidatorUtil.validate(this.state, rule)) {
            //TODO 获取验证码API
            if (this.state.isSentVerify === false) {
                let countdownDate = new Date(new Date().getTime() + 60 * 1000)
                // 点击之后验证码不能发送网络请求
                this.setState({
                    isSentVerify: true
                }, () => {
                    Api.obtainResetPasswordSmsCode({mobile:this.state.mobile,templateCode:'DEALER_ACTIVATE_VERIFICATION_CODE'}).then((data) => {
                        if (data && data.data) {
                            this.setState({uniqueId: data.data})
                            CountdownUtil.settimer(countdownDate, (time) => {
                                this.setState({
                                    timerTitle: time.sec > 0 ? time.sec + 's' : '重新获取'
                                }, () => {
                                    if (this.state.timerTitle == "重新获取") {
                                        this.setState({
                                            isSentVerify: false
                                        })
                                    }
                                })
                            })
                        }
                    }).catch((err) => {
                        this.setState({
                            isSentVerify: false,
                            timerTitle: '重新获取'
                        })
                    })
                });
            }
        }
    }

    /**
     * 激活账号
     */
    activeDealer = () => {
        let rule = {
            account: [
                {required: true, not: '', msg: '请输入客户编码'},
            ],
            mobile: [
                {required: true, not: '', msg: '请输入预留手机号'},
                {length: [11, 16], msg: '手机号必须是11~16位'},
            ],
            name: [
                {required: true, not: '', msg: '请输入用户名'},
                {length: [1, 10], msg: '用户名必须是1~10位'},
            ],
            password: [
                {required: true, not: '', msg: '请输入密码'},
                {length: [6, 16], msg: '密码号必须是1~16位'},
            ],
            confirmPassword: [
                {required: true, not: '', eq: this.state.password, msg: '确认密码跟设置密码不一至'},
            ],
            checkCode: [
                {required: true, not: '', msg: '请输入验证码'},
            ]

        };
        if (ValidatorUtil.validate(this.state, rule)) {

            Api.activeAccount({
                code: this.state.account,
                userName: this.state.name,
                phone: this.state.mobile,
                password: this.state.password,
                verifyCode: this.state.checkCode,
            }).then(() => {
            //TODO 激活账号
                this.goBack();
            }).catch(err=>{
                if (err && (err.resultCode + '' == '1005')) {
                    Alert.alert('温馨提示', err.resultMsg,
                        [
                            { text: '取消', onPress: () => { } },
                            {
                                text: '确认', onPress: () => {
                                    this.goBack();
                                }
                            },
                        ]
                    );
                }
            });
        }
    };

    //渲染
    render() {
        return (
            <Header title={"经销商激活账号"}>
                <View style={styles.container}>
                    <InputFormCell
                        onChangeText={(txt) => {
                            this.setState({account: txt})
                        }}
                        value={this.state.account}
                        underLine={1}
                        titleStyle={styles.titleStyle}
                        cellStyle={styles.cellStyle}
                        maxLength={16}
                        title={"客户编码"} placeholder={"请输入客户编码"}/>
                    <InputFormCell
                        onChangeText={(txt) => {
                            this.setState({mobile: txt})
                        }}
                        value={this.state.mobile}
                        underLine={1}
                        titleStyle={styles.titleStyle}
                        cellStyle={styles.cellStyle}
                        title={"预留手机号"} placeholder={"请输入预留手机号"}/>
                    <InputFormCell
                        onChangeText={(txt) => {
                            if (ValidatorUtil.validatorNameValue(txt, true)) {
                                this.setState({ name: txt })
                            }
                        }}
                        value={this.state.name}
                        underLine={1}
                        titleStyle={styles.titleStyle}
                        cellStyle={styles.cellStyle}
                        maxLength={10}
                        title={"设置用户名"} placeholder={"请输入用户名"}/>
                    <InputFormCell
                        onChangeText={(txt) => {
                            this.setState({password: txt})
                        }}
                        value={this.state.password}
                        underLine={1}
                        titleStyle={styles.titleStyle}
                        cellStyle={styles.cellStyle}
                        secureTextEntry={true}
                        title={"设置密码"} placeholder={"建议至少2种字符组合"}/>
                    <InputFormCell
                        onChangeText={(txt) => {
                            this.setState({confirmPassword: txt})
                        }}
                        secureTextEntry={true}
                        value={this.state.confirmPassword}
                        underLine={1}
                        titleStyle={styles.titleStyle}
                        cellStyle={styles.cellStyle}
                        title={"确认密码"} placeholder={"请再次输入密码"}/>
                    <InputFormCell
                        onChangeText={(txt) => {
                            this.setState({checkCode: txt})
                        }}
                        value={this.state.checkCode}
                        underLine={2}
                        titleStyle={styles.titleStyle}
                        cellStyle={styles.cellStyle}
                        title={"验证码"} placeholder={"请输入验证码"}
                        rightIcon={<TouchableOpacity
                            onPress={() => {
                                this.checkPhone({phone:this.state.mobile,type:'1'})
                            }
                            }>
                            <Text style={{color: Constant.colorTxtPrimary}}>{this.state.timerTitle}</Text>
                        </TouchableOpacity>}
                    />


                    <TouchableOpacity
                        style={[{
                            marginTop: Constant.sizeMarginDefault,
                            borderRadius: Constant.scale(6),
                            height: Constant.scale(44), width: '94%',
                            backgroundColor: Constant.colorPrimary,
                            alignItems: 'center',
                            justifyContent: 'center',
                            marginBottom: Constant.scale(20)
                        }]}
                        onPress={() => {
                            this.activeDealer();
                        }}>
                        <Text style={{color: 'white', fontSize: Constant.fontSizeBig}}>立即激活</Text>
                    </TouchableOpacity>
                    <View style={{flex: 1}}/>
                    <Text style={{color: Constant.colorPlaceholder, marginBottom: Constant.sizeMarginDefault}}>如果忘记经销商编码请联系工作人员获取</Text>
                </View>
            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center',
        width: width,
    },
    cellStyle: {
        width: width,
        height: Constant.scale(50)
    },
    titleStyle: {

        color: Constant.colorTxtContent,
        width: Constant.scale(90)
    }
});
