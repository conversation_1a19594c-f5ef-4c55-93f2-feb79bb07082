/**
 *
 * Created by xiaowz on 2018/3/2.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    ListView,
    Platform,
    Linking
} from 'react-native';
import {ReactNavComponent, Widget, DeviceInfo} from 'rn-yunxi';
import AppWidget from '../../app-widget'
import {NavigationActions} from 'react-navigation'

const {width, height} = Dimensions.get('window');
const {Text, CommonListView, Button, LabelCell} = Widget;
const {Header, PrimaryHeader, Image} = AppWidget;
const HOST = ['ZP_DEV', 'ZP_TEST', 'DEV', 'TEST', 'QA', 'PRO'];

import {inject, observer} from 'mobx-react/native';

@inject(stores => ({
    user: stores.user,
}))
@observer

export default class Setting extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        //状态机变量声明
        this.state = {
            host: Config.HOST,
        };
    }

    //渲染
    render() {

        let env_name = '';
        if (this.state.host == Config.HOST_CONFIG.DEV.HOST) {
            env_name = 'YX开发环境';
        } else if (this.state.host == Config.HOST_CONFIG.TEST.HOST) {
            env_name = 'YX测试环境';
        } else if (this.state.host == Config.HOST_CONFIG.QA.HOST) {
            env_name = 'QA TEST环境';
        } else if (this.state.host == Config.HOST_CONFIG.PRO.HOST) {
            env_name = '生产环境';
        } else if (this.state.host == Config.HOST_CONFIG.ZP_DEV.HOST) {
            env_name = '珠啤开发';
        } else if (this.state.host == Config.HOST_CONFIG.ZP_TEST.HOST) {
            env_name = '珠啤测试';
        }


        return (
            <Header title={'设置'} navigation={this.props.navigation}>
                <ScrollView>
                    <LabelCell title="版本说明" extra={<Text
                        style={{color: '#999999', fontSize: 14}}>{'V' + DeviceInfo.getVersion()}</Text>}
                    />
                    {/* TODO 记得删改环境 */}
                    {Config.isShowEvn ? <View>

                        <LabelCell title="当前环境" extra={
                            <View>
                                <Text style={{color: '#999999', fontSize: 14}}>{env_name}</Text>
                            </View>}
                                   onClick={Config.isCanChangeEvn ? () => {
                                       let nextIndex = 0;
                                       for (let i = 0; i < HOST.length; i++) {
                                           if (HOST[i] === Config.HOST_KEY) {
                                               nextIndex = i + 1;
                                               if (nextIndex >= HOST.length) {
                                                   nextIndex = 0;
                                               }
                                           }
                                       }

                                       Config.changeHost(HOST[nextIndex]).then(data => {
                                           Config.setHost().then(() => {
                                               this.setState({
                                                   host: Config.HOST,
                                               })
                                           }).catch();

                                       }).catch()
                                   } : null}/>

                    </View> : null}
                </ScrollView>
            </Header>
        );
    }


};
const styles = StyleSheet.create({
    container: {
        flex: 1,

    }
});