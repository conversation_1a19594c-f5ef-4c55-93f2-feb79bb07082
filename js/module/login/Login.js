/**
 * Created by whw on 2018/1/3.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Alert,
    TextInput,
    TouchableOpacity,
    ImageBackground,
    Dimensions,
    ScrollView,
    TouchableWithoutFeedback,
    InteractionManager

} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget, Util} from 'rn-yunxi';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
import ValidatorUtil from '../../util/ValidatorUtil';
import AppWidget from '../../app-widget';
import {inject, observer} from 'mobx-react/native';
import {NavigationActions} from 'react-navigation';
import ItemCountUtil from "../../util/ItemCountUtil";

const {width, height} = Dimensions.get('window');
const {Header, Image,ModalCaptchaView} = AppWidget;
const {Text} = Widget;
const {CountdownUtil} = Util;


/**
 * 登录
 */
@inject(stores => ({
    user: stores.user,
}))
@observer
export default class Login extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
            account: '',//分销商
            checkCode: '',//分销商
            dealerAccount: '',//经销商账号
            dealerPassword: '',//经销商密码
            // isDealer: this.props.user && this.props.user.isDealer(),//是否经销商登录
            isDealer: true,//是否经销商登录
            orgList: [],
            isSentVerify: false,
            timerTitle: '获取验证码',
            uniqueId: null,
            showCaptchaView:false,
            captchaValid:true,
            isShowValidateFrequency: false,
            reservePhone: null,
            loginParams: {}
        };
    }

    componentWillMount() {

    }

    componentDidMount() {
        this.goToTab();
    }

    componentWillUnmount() {
        CountdownUtil.stop();
    }

    checkPhone = (params) => {
        let rule = {
            phone: [
                {required: true, not: '', msg: '请输入预留手机号'},
                {length: [11, 16], msg: '手机号必须是11~16位'},
            ],

        };
        if (ValidatorUtil.validate(params, rule)){
            Api.checkPhone(params).then((data) => {
                if (data && data.data) {
                    this.obtainCheckCode();
                }
            }).catch((err) => {
            })
        }

    }

    sendMsg = (params) => {
        let rule = {
            phone: [
                {required: true, not: '', msg: '请输入预留手机号'},
                {length: [11, 16], msg: '手机号必须是11~16位'},
            ],
        };
        if (ValidatorUtil.validate(params, rule)){
            if (this.state.isSentVerify === false) {
                let countdownDate = new Date(new Date().getTime() + 60 * 1000)
                this.setState({
                    isSentVerify: true
                }, () => {
                    Api.sendMsg({mobile: this.state.reservePhone}).then(data => {
                        if(data.data && data.resultCode == 0) {
                            CountdownUtil.settimer(countdownDate, (time) => {
                                this.setState({
                                    timerTitle: time.sec > 0 ? time.sec + 's' : '重新获取'
                                }, () => {
                                    if (this.state.timerTitle == "重新获取") {
                                        this.setState({
                                            isSentVerify: false
                                        })
                                    }
                                })
                            })
                            this.setState({
                                uniqueId: data.data,
                                loginFlag: 3,
                            })
                        }
                    })
                })
            }
        }

    }

    obtainCheckCode() {
        let rule = {
            account: [
                {required: true, not: '', msg: '请输入手机号'},
                {length: [11, 16], msg: '手机号必须是11~16位'},
            ]

        };
        if (ValidatorUtil.validate(this.state, rule)) {
            //TODO 获取验证码API

            if (this.state.isSentVerify === false) {
                let countdownDate = new Date(new Date().getTime() + 60 * 1000)
                // 点击之后验证码不能发送网络请求
                this.setState({
                    isSentVerify: true
                }, () => {
                    Api.obtainLoginCheckCode({mobile:this.state.account}).then((data) => {
                        if (data && data.data) {
                            this.setState({uniqueId: data.data})
                            CountdownUtil.settimer(countdownDate, (time) => {
                                this.setState({
                                    timerTitle: time.sec > 0 ? time.sec + 's' : '重新获取'
                                }, () => {
                                    if (this.state.timerTitle == "重新获取") {
                                        this.setState({
                                            isSentVerify: false
                                        })
                                    }
                                })
                            })
                        }
                    }).catch((err) => {
                        this.setState({
                            isSentVerify: false,
                            timerTitle: '重新获取'
                        })
                    })
                });


            }
        }
    }   

    /**
     * 分销商登录
     */
    loginDistributor() {
        let rule = {
            account: [
                {required: true, not: null, msg: '请输入手机号'},
                {length: [11, 16], msg: '请输入11~16位账号'},
            ],
            checkCode: [
                {length: [1, 6], msg: '请输入验证码'},
            ],

        };
        if (ValidatorUtil.validate(this.state, rule)) {
            //TODO 登录API
            Api.userLogin({
                userCode: this.state.account,
                verifyCode: this.state.checkCode,
                uniqueId: this.state.uniqueId
            }, 1).then((data) => {

                this.loadOrgnList()

            }).catch(data => {

            })
        }
    }


    //分销商登录后选择经销商
    chooseOrg = (item) => {
        Api.saveChooseOrgItem(item).then(
            InteractionManager.runAfterInteractions(()=>{
                this.goToTab();
            })
        ).catch((e) => {
        })

    }

    //网络请求获得分销商登录获取经销商列表
    loadOrgnList = () => {
        // Log('网络')
        Api.loadOrgList().then(
            (json) => {
                if (json && json.data) {
                    this.setState({
                        orgList: json.data
                    })
                }
            }
        ).catch((e) => {
            Log(e)
        })

    }
    loginValidate = async () => {
        if(!this.state.dealerAccount) {
            Alert.alert('提示', '请先输入客户编码进行登录验证');
            return;
        }
      let params = {
        code: this.state.dealerAccount,
        type: 1,
      };
      
      const data = await Api.getCustomerInfo(params);
      if (data.resultCode == 0) {
        this.setState({
          reservePhone: data.data.dealerInfo.reservePhone,
        });
        const submitData = {
          code: this.state.dealerAccount,
          appId: "pearlriver",
        };
        const resp = await Api.getMemberValidateFrequency(submitData);
        if (resp.resultCode == 0) {
          // true：需要校验 false:不需要校验
          if(!resp.data.data) {
        // if(this.state.isShowValidateFrequency){
            this.loginDealer()
            this.setState({
                isShowValidateFrequency: false,
            });
          } else {
            this.setState({
                // isShowValidateFrequency: resp.data.data,
                isShowValidateFrequency: true,
            });
          }
        }
      }
    };

    goToTab = () => {
        if (this.props.user.token) {
            let resetAction = NavigationActions.reset({
                index: 0,
                actions: [
                    NavigationActions.navigate({routeName: 'Tab'})
                ]
            });
            this.dispatch(resetAction)
        }

    };


    /**
     * 经销商登录
     */
    loginDealer() {
        if (!this.state.captchaValid){
            this.setState({
                showCaptchaView:true,
            })
            return
        }
    
        let rule = {
            dealerAccount: [
                {required: true, not: null, msg: '请输入账号或客户编码'},

            ],
            dealerPassword: [
                {required: true, not: null, msg: '请输入密码'},
            ],

        };
        if (ValidatorUtil.validate(this.state, rule)) {
            //TODO 登录API
            const { isShowValidateFrequency } = this.state;
            let param = {}
            if(!isShowValidateFrequency) {
                param = {userCode: this.state.dealerAccount, userPassword: this.state.dealerPassword}
            } else {
                param = {userCode: this.state.dealerAccount, userPassword: this.state.dealerPassword, isAddParams: true, uniqueId: this.state.uniqueId, verifyCode: this.state.checkCode}
            }
            Api.userLogin(param, 0,false).then((data) => {
                this.goToTab();

            }).catch((data) => {
                if (data) {
                    if (data.resultCode == '10007') {
                        this.navigate('ActivateAccount')
                    } else if (data.resultCode == '10013') {
                        this.navigate('ResetPassword')
                    }else if (data.resultCode == '10022'){
                        this.setState({
                            showCaptchaView:true,
                            captchaValid:false
                        })
                    } else if (data.resultCode == '10011') {
                        Alert.alert('提示', data.resultMsg, [
                            {
                                text: '否'
                            },
                            {
                                text: '是',
                                onPress: () => {
                                    this.navigate('ResetPassword')
                                }
                            }])
                    }

                } else {

                }
            })
        }
    }

    //分销商登录
    renderDistributor() {
        return (
            <View style={{width: width, flex: 1, alignItems: 'center'}}>
                <View style={styles.inputBg}>
                    <Image resizeMode={'contain'} style={{width: Constant.scale(17), height: Constant.scale(17)}} source={require('../img/login/ic_user_input.png')}/>
                    <TextInput underlineColorAndroid={'#********'}
                               placeholder={'手机号'}
                               selectionColor = {Constant.colorPrimary}
                               value={this.state.account}
                               style={styles.input}
                               onChangeText={(text) => {
                                   if (ItemCountUtil.validatorInputValue(text, true)) {
                                       this.setState({account: text})
                                   }

                               }}
                    />
                    <TouchableOpacity onPress={() => {
                        this.checkPhone({phone:this.state.account,type:'2'});
                    }}>
                        <Text style={{color: Constant.colorTxtPrimary}}>{this.state.timerTitle}</Text>
                    </TouchableOpacity>

                </View>
                <View style={[GlobalStyle.styleDividerDefault, {width: '80%', marginBottom: Constant.scale(15)}]}/>
                <View style={styles.inputBg}>
                    <Image
                        resizeMode={'contain'}
                        style={{width: Constant.scale(17), height: Constant.scale(17)}}
                        source={require('../img/login/ic_password_input.png')}/>
                    <TextInput
                        underlineColorAndroid={'#********'}
                        placeholder={'请输入验证码'}
                        selectionColor = {Constant.colorPrimary}
                        style={styles.input}
                        returnKeyType={'done'}
                        onSubmitEditing={() => {
                            this.loginDistributor();
                        }}
                        onChangeText={(text) => {
                            this.setState({checkCode: text})
                        }}/>
                </View>
                <View style={[GlobalStyle.styleDividerDefault, {width: '80%', marginBottom: Constant.scale(40)}]}/>
                <TouchableOpacity style={[styles.inputBg, {backgroundColor: Constant.colorPrimary, justifyContent: 'center', marginBottom: Constant.scale(35)}]}
                                  onPress={() => {
                                      this.loginDistributor();
                                  }}>
                    <Text style={{color: 'white', fontSize: Constant.fontSizeBig}}>登录</Text>
                </TouchableOpacity>
                <View style={{flex: 1}}/>
                <Text style={{color: '#AFAFAF', marginBottom: Constant.scale(20)}}>如果预留手机号不对,请联系经销商修改</Text>

            </View>

        )
    }

    //经销商登录
    renderDealer() {
        return (
            <View style={{width: width, flex: 1, alignItems: 'center'}}>
                {
                    !this.state.isShowValidateFrequency ? 
                    <View>
                        <View style={styles.inputBg}>
                            <Image resizeMode={'contain'} style={{width: Constant.scale(17), height: Constant.scale(17)}} source={require('../img/login/ic_user_input.png')}/>
                            <TextInput
                                value={this.state.dealerAccount}
                                underlineColorAndroid={'#********'}
                                placeholder={'客户编码/用户名'}
                                selectionColor = {Constant.colorPrimary}
                                style={styles.input}
                                onChangeText={(text) => {
                                    this.setState({dealerAccount: text})
                                }}
                            />
        
        
                        </View>
                        <View style={[GlobalStyle.styleDividerDefault, {width: '80%', marginBottom: Constant.scale(15)}]}/>
                        <View style={styles.inputBg}>
                            <Image
                                resizeMode={'contain'}
                                style={{width: Constant.scale(17), height: Constant.scale(17)}}
                                source={require('../img/login/ic_password_input.png')}/>
                            <TextInput
                                secureTextEntry={true}
                                value={this.state.dealerPassword}
                                underlineColorAndroid={'#********'}
                                placeholder={'请输入密码'}
                                style={styles.input}
                                returnKeyType={'done'}
                                selectionColor = {Constant.colorPrimary}
                                onSubmitEditing={() => {
                                    this.loginDealer();
                                }}
                                onChangeText={(text) => {
                                    this.setState({dealerPassword: text})
                                }}/>
                        </View>
                        <View style={[GlobalStyle.styleDividerDefault, {width: '80%', marginBottom: Constant.scale(15)}]}/>
                    </View>
                    :  <View>
                    <View style={styles.inputBg}>
                        <Image resizeMode={'contain'} style={{width: Constant.scale(17), height: Constant.scale(17)}} source={require('../img/login/ic_user_input.png')}/>
                        <TextInput underlineColorAndroid={'#********'}
                                   placeholder={'手机号'}
                                   disabled
                                   selectionColor={Constant.colorPrimary}
                                   value={this.state.reservePhone}
                                   style={styles.input}
                        />
                        <TouchableOpacity onPress={() => {
                            this.sendMsg({phone:this.state.reservePhone,type:'2'});
                        }}>
                            <Text style={{color: Constant.colorTxtPrimary}}>{this.state.timerTitle}</Text>
                        </TouchableOpacity>
    
                    </View>
                    <View style={[GlobalStyle.styleDividerDefault, {width: '80%', marginBottom: Constant.scale(15)}]}/>
                    <View style={styles.inputBg}>
                        <Image
                            resizeMode={'contain'}
                            style={{width: Constant.scale(17), height: Constant.scale(17)}}
                            source={require('../img/login/ic_password_input.png')}/>
                        <TextInput
                            underlineColorAndroid={'#********'}
                            placeholder={'请输入验证码'}
                            selectionColor = {Constant.colorPrimary}
                            style={styles.input}
                            returnKeyType={'done'}
                            onSubmitEditing={() => {
                                this.loginDealer();
                            }}
                            onChangeText={(text) => {
                                this.setState({checkCode: text})
                            }}/>
                    </View>
                    <View style={styles.tipsContent}>
                        <Text style={{color: '#e00', marginTop: Constant.scale(20), height: Constant.scale(60)}}>根据风险防控需要，您本次登录需要进行短信验证，验证码将会发送到经销商开户预留的手机号上。</Text>
                    </View>
                </View>

                }
                <TouchableOpacity style={[styles.inputBg, {backgroundColor: Constant.colorPrimary, justifyContent: 'center', marginTop: Constant.scale(40), marginBottom: Constant.scale(20)}]}
                    onPress={() => {
                        this.state.isShowValidateFrequency  ? this.loginDealer() : this.loginValidate();
                    }}>
                    <Text style={{color: 'white', fontSize: Constant.fontSizeBig}}>登录</Text>
                </TouchableOpacity>
                <View style={{width: '80%', flexDirection: 'row', justifyContent: 'space-between'}}>
                    <TouchableOpacity
                        onPress={() => {
                            this.navigate('ActivateAccount')
                        }}>
                        <Text style={{color: Constant.colorTxtContent}}>激活账号</Text>
                    </TouchableOpacity>

                    <TouchableOpacity onPress={() => {
                        this.navigate('ResetPassword')
                    }}>
                        <Text style={{color: Constant.colorTxtContent}}>忘记密码</Text>
                    </TouchableOpacity>
                </View>
                <View style={{flex: 1}}/>
                <Text style={{color: '#AFAFAF', marginBottom: Constant.scale(20)}}>如果没有密码，请使用您的客户编码激活</Text>

            </View>

        )
    }

    //分销商登录后获取经销商列表
    renderOrgList() {
        return (

            <View style={{position: 'absolute', top: 0, left: 0, zIndex: 10, width: '100%', height: '100%', backgroundColor: '#0000004a', justifyContent: 'center', alignItems: 'center'}}>
                <View style={{alignItems: 'center', borderRadius: Constant.scale(8), backgroundColor: 'white', width: Constant.scale(280), minHeight: Constant.scale(210), paddingBottom: Constant.scale(8)}}>
                    <View style={{width: '100%', height: Constant.scale(44), overflow: 'hidden', justifyContent: 'center', alignItems: 'center'}}>
                        <Text style={{fontSize: Constant.fontSizeBig}}>请选择下单经销商</Text>
                    </View>
                    <ScrollView style={{flex: 0}}
                                contentContainerStyle={{width: Constant.scale(280)}}>
                        {
                            this.state.orgList.map((item, index) => {
                                return (
                                    <TouchableOpacity onPress={() => this.chooseOrg(item)} key={index}
                                                      style={[{width: '100%', alignItems: 'center', justifyContent: 'center', minHeight: Constant.scale(44)}, index % 2 == 0 && {backgroundColor: '#fafafa'}]}>
                                        <Text style={{color: Constant.colorTxtContent}}>{item.name}</Text>
                                    </TouchableOpacity>
                                )
                            })
                        }

                    </ScrollView>
                </View>
            </View>
        )
    }

    //渲染
    render() {

        return (
            <KeyboardAwareScrollView
                contentContainerStyle={{flex: 1}}
                scrollEnabled={false}
                keyboardShouldPersistTaps={'always'}
                extraHeight={height < 667 ? Constant.scale(18) : Constant.scale(60)}
            >
                <ScrollView contentContainerStyle={{flex: 1}}>
                    <View style={{height: '100%', alignItems: 'center', width: width, backgroundColor: 'white'}}>
                        {!this.state.isDealer && this.state.orgList.length > 1 ?
                            this.renderOrgList() :
                            !this.state.isDealer && this.state.orgList.length > 0?
                                 this.chooseOrg(this.state.orgList[0])
                            : null}
                        <TouchableOpacity activeOpacity={1} onPress={() => this.changeEnvironment()}>
                            <ImageBackground style={{width: '100%', height: Constant.scale(213), alignItems: 'center'}}
                                             source={require('../img/login/bg_login.jpg')}>
                                <View style={{flex: 1}}/>
                                <View style={{flexDirection: 'row', height: Constant.scale(40)}}>
                                    <TouchableOpacity style={{flex: 1, height: '100%', alignItems: 'center'}}
                                                      onPress={() => {
                                                          this.setState({isDealer: true})
                                                      }}>
                                        <Text style={{color: 'white', fontSize: Constant.fontSizeBig}}>经销商</Text>
                                        <View style={{flex: 1}}/>
                                        {
                                            this.state.isDealer ? <Image style={{width: Constant.scale(13.5), height: Constant.scale(6)}} source={require('../img/login/ic_select.png')}/> : null
                                        }

                                    </TouchableOpacity>
                                    <View style={{height: Constant.scale(18), width: Constant.scale(1.5), backgroundColor: 'white'}}/>

                                    <TouchableOpacity style={{flex: 1, height: '100%', alignItems: 'center'}}
                                                      onPress={() => {
                                                          this.setState({isDealer: false})
                                                      }}>
                                        <Text style={{color: 'white', fontSize: Constant.fontSizeBig}}>分销商</Text>
                                        <View style={{flex: 1}}/>
                                        {
                                            !this.state.isDealer ? <Image style={{width: Constant.scale(13.5), height: Constant.scale(6)}} source={require('../img/login/ic_select.png')}/> : null
                                        }

                                    </TouchableOpacity>
                                </View>


                            </ImageBackground>
                        </TouchableOpacity>
                        <View style={{height: Constant.scale(30)}}/>
                        {this.state.isDealer ? this.renderDealer() : this.renderDistributor()}

                    </View>
                </ScrollView>
                <ModalCaptchaView
                    defaultClick={()=>this.setState({captchaValid:true,showCaptchaView:false})}
                    cancelClick={()=>this.setState({showCaptchaView:false})}
                    modalVisible={this.state.showCaptchaView}
                />
            </KeyboardAwareScrollView>

        );
    }

    changeEnvironment = () => {

            this.navigate('Setting')

    }
}
;

const styles = StyleSheet.create({
    container: {
        height: height,
    },
    inputBg: {
        alignItems: 'center',
        flexDirection: 'row',
        borderRadius: Constant.scale(25),
        width: '80%',
        height: Constant.scale(44),

    },
    
    tipsContent: {
        width: '80%',
        height: Constant.scale(60),

    },
    input: {
        flex: 1,
        marginRight: Constant.scale(10),
        marginLeft: Constant.scale(10),
    },
});