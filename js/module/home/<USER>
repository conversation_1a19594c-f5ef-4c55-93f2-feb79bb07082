/**
 * Created by lao<PERSON>ji<PERSON><PERSON> on 2017/5/26.
 */
import React, { Component } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Dimensions,
  Platform,
  TouchableWithoutFeedback,
  ScrollView,
  StatusBar,
  AppState,
  Alert
} from "react-native";
import PropType from "prop-types";
import { ReactNavComponent, Widget } from "rn-yunxi";
import AppWidget from "../../app-widget";
import QueryString from "querystring";
import { SafeAreaView } from "react-navigation";

const { width, height } = Dimensions.get("window");
import { inject, observer } from "mobx-react/native";
import Carousel from "react-native-looped-carousel";
import HomeItem from "./HomeItem";
import { toJS } from "mobx";
import user from "../../store/User";
import ToastUtil from "../../util/ToastUtil";
import codePush from "react-native-code-push";
import _ from "lodash";

const { CommonListVie<PERSON>, Text } = Widget;
const { CommonSearchBar, Image } = AppWidget;
const IC_CATEGORY = require("../img/home-page/ic_category.png");
const HEADER_MSG_INFO = require("../img/home-page/ic_message.png");
const IC_NEXT = require("../img/next.png");
const IC_NOTICE = require('../img/bell.png');

import NoticeModal from "../../app-widget/notice-modal";



@inject(stores => ({
  homePage: stores.homePage,
  user: stores.user,
  message: stores.messageCenter,
  userNotice: stores.userNotice,
}))
@observer
/**
 * 首页
 */
export default class HomePage extends ReactNavComponent {
  //属性声名
  static propTypes = {};
  //默认属性
  static defaultProps = {};

  //构造函数
  constructor(props) {
    super(props);
    this.state = {
      //状态机变量声明
      headerBackGroundAlpha: 0,

      startIndex: 0, // 未读公告开始下标
      visibleNotice: false, // 公告弹窗
      currentItem: {}, // 当前公告
    };
  }

  componentWillMount() {
    this.didBlurSubscription = this.props.navigation.addListener(
      "didBlur",
      payload => {
        console.debug("didBlur");
      }
    );

    this.willDidFocusSubscription = this.props.navigation.addListener(
      "didFocus",
      payload => {
        console.debug("didFocus");
        this.props.message
          .getUnreadMessageCount()
          .then()
          .catch(e => {});
        this.props.homePage
          .getHomeIndex()
          .then()
          .catch(err => {});
      }
    );
  }

  componentDidMount() {
    AppState.addEventListener("change", newState => {
      if (newState === "active") {
        // this.checkHostUpdate();
      }
    });

    this.props.homePage
      .getUserNoticeReadStatus()
      .then(res => {
        if (!res) {
          // setTimeout(() => {
          //   this.navigate("UserNotice");
          // }, 500);

          this.props.homePage
            .getUserNotice()
            .then(res1 => {
              if (!!res1) {
                setTimeout(() => {
                  this.navigate("UserNotice");
                }, 2000);
              }
            })
            .catch(e => {});
        }
      })
      .catch(e => {});

      // 获取未读公告数量
      this.props.userNotice.getUserNoticeNum();
      // 获取未读公告
      this.props.userNotice.getUserNoticeList(false ,false)
        .then(res => {
          if (res.resultCode === 0) {
            const list = _.get(this.props.userNotice, 'listParams.dataArray', []) || [];
            console.log('xxxx',list)
            if (list && list.length > 0) {
              console.log('xxxx',list)
              this.setState({
                visibleNotice: true,
              });
            }
          }
        });


  }

  componentWillUnmount() {
    this.didBlurSubscription.remove();
    this.willDidFocusSubscription.remove();
  }

  checkHostUpdate = () => {
    Log("进入检查更新");

    let deploymentKey = "";

    if (Constant.isAndroid) {
      Log("进入检查更新android");
      deploymentKey =
        "irrzkDY55yqNKHkQ7uTc9c9OlB1Zf3ff89f9-ede8-4c9b-aa19-edbc44897b18";
    } else {
      Log("进入检查更新iOS");
      deploymentKey =
        "8wqAYU_zjSmd50abPnXRC2xAlqWef3ff89f9-ede8-4c9b-aa19-edbc44897b18";
    }

    codePush.checkForUpdate(deploymentKey).then(update => {
      // 热更新检查是否有最新的版本
      if (!update) {
        // Alert.alert("提示", "已经是最新版本--", [
        //     {
        //         text: "Ok", onPress: () => {
        //             console.log("点了OK");
        //         }
        //     }
        // ]);
      } else {
        codePush.sync(
          {
            deploymentKey: deploymentKey,
            updateDialog: {
              appendReleaseDescription: true,
              mandatoryContinueButtonLabel: "更新",
              optionalIgnoreButtonLabel: "稍后",
              optionalInstallButtonLabel: "立即更新",
              optionalUpdateMessage: "有新版本了，是否更新？",
              descriptionPrefix: "\n更新内容\n",
              mandatoryUpdateMessage: "有新版本了，立即更新",
              title: "更新提示"
            },
            installMode: codePush.InstallMode.IMMEDIATE
          },
          status => {
            switch (status) {
              case codePush.SyncStatus.DOWNLOADING_PACKAGE:
                // console.log("DOWNLOADING_PACKAGE");

                break;
              case codePush.SyncStatus.INSTALLING_UPDATE:
                // console.log(" INSTALLING_UPDATE");
                break;
            }
          },
          progress => {
            // console.log(
            //   progress.receivedBytes +
            //     " of " +
            //     progress.totalBytes +
            //     " received."
            // );
          }
        );
      }
    });
  };

  /**
   * banners
   * */
  renderBanner = rowData => {
    return rowData && rowData.length > 0 ? (
      <Carousel
        delay={2000}
        style={{
          height: Constant.scale(Platform.OS === "android" ? 186 : 186),
          width: width
        }}
        autoplay
        bullets
        chosenBulletStyle={{ margin: 0, marginRight: 5, width: 5, height: 5 }}
        bulletStyle={{ margin: 0, marginRight: 5, width: 5, height: 5 }}
      >
        {rowData.map((item, index) => {
          // Log(Constant.ossImgUrl(item.imgUrl,width));
          return (
            <TouchableWithoutFeedback
              key={index}
              onPress={() => {
                Log("banner click", item.nextUrl);
                // item.nextUrl = 'app://GoodsDetail?itemId=100000228&defaultOrgId=1111111'
                let reg = /^(app:\/\/)(.*?)(\?)(.*?)$/g;
                let result = null;
                if (!item.nextUrl) {
                  return;
                }
                if (item.nextUrl.indexOf("http") != -1) {
                  this.navigate("CommonWebView", { url: item.nextUrl });
                  return;
                }
                result = item.nextUrl.replace(reg, (text, p1, p2, p3, p4) => {
                  let router = p2;
                  let params = QueryString.parse(p4);

                  // Log('111212121', text, p1, p2, p3, p4, QueryString.parse(p4))
                  if (!p2 || p2 == "") {
                    return "";
                  }

                  return JSON.stringify({
                    router,
                    params
                  });
                });
                // Log('banner result', result);
                try {
                  let data = JSON.parse(result);
                  // Log(data)
                  if (data && data.router) {
                    this.navigate(data.router, { ...data.params });
                  }
                } catch (err) {
                  Log(err);
                }
              }}
            >
              <Image
                style={styles.bannerImage}
                resizeMode={"stretch"}
                source={{
                  uri: item.imgUrl ? Constant.ossImgUrl(item.imgUrl, width) : ""
                }}
              />
            </TouchableWithoutFeedback>
          );
        })}
      </Carousel>
    ) : (
      <Image
        style={{
          height: Constant.scale(Platform.OS === "android" ? 206 : 186),
          width: width
        }}
      />
    );
  };

  /**
   * 分类
   * */
  renderCategory = data => {
    return (
      <View>
        <View
          style={{
            width: width,
            height: Constant.sizeDividerNormal
          }}
        />
        <View
          style={{
            flexDirection: "row",
            width: width,
            height: Constant.scale(44),
            backgroundColor: Constant.colorDefault,
            justifyContent: "center",
            alignItems: "center"
          }}
        >
          <View
            style={{
              flexDirection: "row",
              height: "100%",
              alignItems: "center",
              justifyContent: "center"
            }}
          >
            <Image
              source={IC_CATEGORY}
              style={{
                marginLeft: Constant.sizeMarginDefault,
                width: Constant.scale(12),
                height: Constant.scale(13)
              }}
            />
            <Text style={styles.categoryTxtStyle}>分类</Text>
            <View
              style={{
                height: Constant.scale(30),
                backgroundColor: Constant.colorDivider,
                width: Constant.sizeDividerNormal
              }}
            />
          </View>
          <ScrollView
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              flexDirection: "row",
              justifyContent: "center",
              alignItems: "center"
            }}
            style={{ flex: 1 }}
            horizontal={true}
          >
            {data && data.length > 0
              ? data.map((item, index) => {
                  return (
                    <TouchableOpacity
                      key={index}
                      onPress={() => this.categoryClick(item.id)}
                    >
                      <Text
                        style={[
                          styles.categoryTxtStyle,
                          { color: Constant.colorTxtTitle }
                        ]}
                      >
                        {item.name}
                      </Text>
                    </TouchableOpacity>
                  );
                })
              : null}
          </ScrollView>
          <Image
            style={{
              marginRight: Constant.sizeMarginDefault,
              width: Constant.scale(12),
              height: Constant.scale(13)
            }}
            source={IC_NEXT}
          />
        </View>
        <View
          style={{
            width: width,
            height: Constant.sizeDividerNormal,
            backgroundColor: Constant.colorDivider
          }}
        />
      </View>
    );
  };

  /**
   * 订单等item
   * */
  renderOrderItem = data => {
    return (
      <View
        style={{
          flexDirection: "row",
          width: width,
          // height: Constant.scale(104),
          backgroundColor: Constant.colorBackgroundDefault,
          // backgroundColor: 'pink',
          justifyContent: "space-between",
          flexWrap: "wrap"
          // alignItems: 'center'
        }}
      >
        {data.map((item, index) => {
          const flag = index < 4 && this.props.user.isDealer();
          return (
            <TouchableOpacity
              key={index}
              style={{
                backgroundColor: "white",
                width: (width - Constant.sizeDividerNormal) / (flag ? 4 : 2),
                height: flag ? Constant.scale(90) : Constant.scale(64),
                flexDirection: flag ? "column" : "row",
                justifyContent: flag ? "center" : undefined,
                alignItems: "center",
                marginBottom: Constant.sizeDividerNormal
              }}
              onPress={() => {
                this.itemClick(index);
              }}
            >
              <Image
                source={item.imgUrl}
                resizeMode={"contain"}
                style={{
                  width: index > 3 ? Constant.scale(40) : Constant.scale(30),
                  height: index > 3 ? Constant.scale(40) : Constant.scale(30),
                  // margin: Constant.sizeMarginDefault
                  marginLeft: flag ? 0 : Constant.scale(25)
                }}
              />
              <Text
                style={{
                  marginTop: !flag ? 0 : Constant.scale(10),
                  marginLeft: flag ? 0 : Constant.scale(10),
                  // color: Constant.colorTxtContent,
                  fontSize: Constant.fontSizeNormal
                }}
              >
                {item.name}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  itemClick = index => {
    switch (index) {
      case 0:
        this.navigate("OrderList", { type: 1 });
        break;
      case 1:
        this.navigate("RecycleBottleList");
        break;
      case 2:
        if (this.props.user.isDealer()) {
          this.navigate("MineCompany");
        } else {
          //门店核销
          this.navigate("VerificationList");
        }
        break;
      case 3:
        if (this.props.user.isDealer()) {
          this.navigate("OrderList", { type: 3 });
        } else {
          //申请补货
          this.navigate("ApplyReplenish");
        }
        break;
      case 4:
        if (this.props.user.isDealer()) {
          //分销商核销
          this.navigate("VerificationList");
        }
        break;
      case 5:
        if (this.props.user.isDealer()) {
          //申请转入
          this.navigate("ApplyTransfer");
        }
        break;
      default:
        break;
    }
  };

  /**
   * 推荐商品View
   * */
  renderRecommend = () => {
    return (
      <View
        style={{
          width: width,
          height: Constant.scale(37),
          justifyContent: "center",
          alignItems: "center"
        }}
      >
        <Text
          style={{
            color: Constant.colorTxtContent,
            fontSize: Constant.fontSizeNormal
          }}
        >
          —— 推荐商品 ——
        </Text>
      </View>
    );
  };

  renderRecommendEmpty = () => {
    return (
      <View
        style={[
          {
            width: width,
            height: height / 2.5,
            justifyContent: "center",
            alignItems: "center"
          }
        ]}
      >
        <Text
          style={{
            fontSize: Constant.fontSizeNormal,
            color: Constant.colorTxtContent,
            marginTop: Constant.scale(25)
          }}
        >
          暂无数据
        </Text>
      </View>
    );
  };

  renderItem = item => {
    return <HomeItem data={item} showStock={false} isDealer={this.props.user.isDealer()}/>;
  };

  renderRow = (item, index) => {
    let type = item.type ? item.type : -1;
    let data = item.data ? item.data : item;
    // Log('item === ', item);
    switch (type) {
      case 1:
        return this.renderBanner(data);
      case 2:
        return this.renderCategory(data);
      case 3:
        return this.renderOrderItem(data);
      case 4:
        return this.renderRecommend(data);
      case 5:
        return this.renderRecommendEmpty(data);
      default:
        return this.renderItem(item);
    }
  };

  //点击分类按钮
  categoryClick = catalogId => {
    this.navigate("SearchPage", { catalogId: catalogId });
  };

  /**
   * 搜索header 设置透明度
   * @param event
   */
  onListScroll = event => {
    // Log('onScroll', event.nativeEvent);
    let { contentSize, contentOffset } = event.nativeEvent;
    let y = contentOffset.y > 300 ? 300 : contentOffset.y;
    let alpha = y / 300;
    this.setState({ headerBackGroundAlpha: alpha });
  };

  goToSearch = () => {
    this.navigate("SearchPage");
  };

  handleNextRead = (data) => {
    this.props.userNotice.postNoticeRead({
      type: 1,
      applicationType: 1,
      id: data.id,
    }, data)
  };

  handleConfirmRead = (data) => {
    this.setState({
      visibleNotice: false,
    });
    if (data && !data.isRead) {
      this.props.userNotice.postNoticeRead({
        type: 1,
        applicationType: 1,
        id: data.id,
      }, data)
    }
  };

  //渲染
  render() {
    let alpha = "rgba(255,255,255," + this.state.headerBackGroundAlpha + ")";
    // let searchAlpha = 'rgba(255,255,255,' + this.state.headerBackGroundAlpha + ')';
    let searchAlpha = "rgba(255,255,255,1 )";
    let searchPlaceHolderTextAlpha = "rgba(255,255,255," + 0.7 + ")";

    const notReadNum = _.get(this.props.userNotice, 'notReadNum', 0);
    let dataArray = _.get(this.props.userNotice, "listParams.dataArray", []);
    return (
      <SafeAreaView
        forceInset={{ top: 0 }}
        style={{ flex: 1, backgroundColor: "white" }}
      >
        <View style={[styles.container]}>
          <CommonListView
            style={{
              flex: 1,
              backgroundColor: Constant.colorBackgroundDefault
            }}
            contentContainerStyle={{
              backgroundColor: Constant.colorBackgroundDefault,
              flexDirection: "row",
              flexWrap: "wrap",
              justifyContent: "space-between"
            }}
            showsVerticalScrollIndicator={false}
            onRefresh={() => {
              Log("onRefresh");
              this.props.homePage
                .getHomeIndex()
                .then()
                .catch(err => {});
            }}
            onLoadMore={() => {}}
            renderRow={this.renderRow}
            dataSource={this.props.homePage.dataSource}
            enableLoadMore={this.props.homePage.enableLoadMore}
            enableRefresh={true}
            onScroll={this.onListScroll}
            listState={this.props.homePage.listState}
          >
            <View
              style={[
                {
                  flex: 1,
                  width: "100%",
                  justifyContent: "center",
                  alignItems: "center",
                  backgroundColor: Constant.colorBackgroundDefault
                }
              ]}
            >
              <Text
                style={{
                  fontSize: Constant.fontSizeBig,
                  color: Constant.colorTxtContent,
                  marginTop: Constant.scale(25)
                }}
              >
                暂无数据
              </Text>
            </View>
          </CommonListView>
          <View
            style={[
              styles.headerView,
              {
                backgroundColor: alpha,
                paddingTop:
                  Constant.sizeSafeArea +
                  (Platform.OS == "android" ? StatusBar.currentHeight : 0)
              }
            ]}
          >
            <View style={{ flexDirection: "row", alignItems: "center" }}>
              <CommonSearchBar
                style={{
                  flex: 1,
                  margin: 0,
                  marginLeft: Constant.sizeMarginDefault,
                  marginRight: Constant.sizeMarginDefault / 2
                }}
                searchBarTxtInputStyle={{ backgroundColor: searchAlpha }}
                placeholderTextColor={
                  // this.state.headerBackGroundAlpha < 0.5 ? searchPlaceHolderTextAlpha : Constant.colorTxtContent
                  Constant.colorTxtGrayDefault
                }
                placeholder={"搜索"}
                editable={false}
                onPress={this.goToSearch}
              />
              <TouchableWithoutFeedback
                onPress={() => {
                  this.navigate("MessageCenter");
                }}
              >
                <View
                  style={{
                    // backgroundColor:'pink',
                    justifyContent: "center",
                    alignItems: "flex-end",
                    width: Constant.scale(30) + Constant.sizeMarginDefault,
                    height: Constant.scale(35),
                    // marginRight: Constant.scale(20)
                  }}
                >
                  <View
                    style={{
                      justifyContent: "center",
                      alignItems: "center",
                      width: Constant.scale(30),
                      height: Constant.scale(30),
                      borderRadius: Constant.scale(15),
                      backgroundColor: "rgba(0,0,0,0.3)"
                    }}
                  >
                    <Image
                      style={{
                        width: Constant.scale(18),
                        height: Constant.scale(15)
                        // marginRight: Constant.sizeMarginDefault
                      }}
                      source={HEADER_MSG_INFO}
                    />
                    {this.props.message &&
                    this.props.message.unReadCount > 0 ? (
                      <View
                        style={{
                          position: "absolute",
                          width: 6,
                          height: 6,
                          borderRadius: 3,
                          backgroundColor: Constant.colorPrimary,
                          top: Constant.scale(5),
                          left: Constant.scale(22)
                        }}
                      />
                    ) : null}
                  </View>
                </View>
              </TouchableWithoutFeedback>

              <TouchableWithoutFeedback
                onPress={() => {
                  this.navigate("UserNoticeList");
                }}
              >
                <View
                  style={{
                    // backgroundColor:'pink',
                    justifyContent: "center",
                    alignItems: "flex-end",
                    width: Constant.scale(30) + Constant.sizeMarginDefault,
                    height: Constant.scale(35),
                    marginRight: Constant.scale(20)
                  }}
                >
                  <View
                    style={{
                      justifyContent: "center",
                      alignItems: "center",
                      width: Constant.scale(30),
                      height: Constant.scale(30),
                      borderRadius: Constant.scale(15),
                      backgroundColor: "rgba(0,0,0,0.3)"
                    }}
                  >
                    <Image
                      style={{
                        width: Constant.scale(18),
                        height: Constant.scale(15)
                        // marginRight: Constant.sizeMarginDefault
                      }}
                      source={IC_NOTICE}
                    />

                    {notReadNum  ? (
                      <View
                        style={{
                          position: "absolute",
                          width: 6,
                          height: 6,
                          borderRadius: 3,
                          backgroundColor: Constant.colorPrimary,
                          top: Constant.scale(5),
                          left: Constant.scale(22)
                        }}
                      />
                    ) : null}
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </View>
          </View>
        </View>
        {Platform.OS == "android" ? (
          <StatusBar
            backgroundColor={"transparent"}
            barStyle={"dark-content"}
            translucent={true}
          />
        ) : null}

      <NoticeModal
          startIndex={this.state.startIndex}
          visible={this.state.visibleNotice}
          dataList={dataArray}
          onNext={this.handleNextRead}
          onConfirm={this.handleConfirmRead}
          onClose={() => {
            console.warn("close");
            this.setState({
              visibleNotice: false,
            });
            // CountdownUtil.stop();
          }}
        ></NoticeModal>

      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  bannerImage: {
    flex: 1,
    width: width,
    height: Constant.scale(Platform.OS === "android" ? 186 : 186)
  },
  categoryTxtStyle: {
    fontSize: Constant.fontSizeNormal,
    color: Constant.colorTxtPrimary,
    marginLeft: Constant.sizeMarginDefault,
    marginRight: Constant.sizeMarginDefault
  },
  headerView: {
    width: width,
    paddingBottom: 10,
    height:
      Constant.sizeHeaderWithSafeArea +
      (Platform.OS == "android" ? StatusBar.currentHeight : 0),
    position: "absolute"
  },
  tipWrap: {
    position: 'absolute',
    right: 2,
    top: 0,
    width: Constant.scale(10),
    height: Constant.scale(10),
    backgroundColor: '#ff0022',
    borderRadius: Constant.scale(10),
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  tipNum: {
    color: '#fff',
    fontSize: Constant.fontSizeBig,
    fontWeight: 'bold',
  }
});

//  TODO ********** 解决codepush自动回滚 **********
let codePushOptions = {
  //设置检查更新的频率
  //ON_APP_RESUME APP恢复到前台的时候
  //ON_APP_START APP开启的时候
  //MANUAL 手动检查
  checkFrequency: codePush.CheckFrequency.MANUAL
};

HomePage = codePush(codePushOptions)(HomePage);
