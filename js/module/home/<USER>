/**
 * Created by whw on 2018/1/17.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget';

const {width, height} = Dimensions.get('window');
const {Text} = Widget;
const {PriceText, Image} = AppWidget;
const IC_SPECIAL = require('../img/home-page/ic_special.png')
import {withNavigation} from 'react-navigation';

/**
 * 首页
 */
@withNavigation
export default class HomeItem extends ReactNavComponent {
    //属性声名
    static propTypes = {
        showStock: PropType.bool,
    };
    //默认属性
    static defaultProps = {
        showStock: true,
    };

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
        };
    }

    componentWillMount() {
    }

    componentDidMount() {
    }

    componentWillUnmount() {
    }

    goToGoodsDetail = () => {
        this.props.navigation.navigate('GoodsDetail', {itemId: this.props.data.itemId,defaultOrgId:this.props.data.defaultOrgId});
    };

    //渲染
    render() {
        let data = this.props.data;
        let isDealer = this.props.isDealer;
        let imgWidth = ITEM_WIDTH - Constant.sizeMarginDefault;
        let stock = '暂无库存';
        if (data.stock || (data.stock+'' == '0')){
            stock = data.stock+''
        }else {
            stock = '库存充足';
        }

        return (
            <View style={[styles.container]}>
                <TouchableOpacity style={{flex: 1, justifyContent: 'flex-start',alignItems:'center'}} onPress={this.goToGoodsDetail}>
                    <View style={{
                        width:Constant.scale(165),
                        height:Constant.scale(165),
                        marginTop: Constant.sizeMarginDefault,
                        marginBottom:Constant.sizeMarginDefault,}}>
                        <Image
                            source={{uri: data.imgUrl ? Constant.ossImgUrl(data.imgUrl, imgWidth) : null}}
                            style={{width: '100%', height: '100%', }}/>

                    </View>

                    <Text
                        numberOfLines={2}
                        style={{ width:Constant.scale(165),color: Constant.colorTxtContent, fontSize: Constant.fontSizeNormal}}>
                        {data.name}
                    </Text>
                    {isDealer?
                    <View style={styles.bottomStyle}>
                        <View style={{
                            width:Constant.scale(165),
                            marginTop:Constant.sizeMarginDefault,
                            flexDirection: 'row',
                            alignItems: 'center'}}>
                            <PriceText price={data.specialPrice ? data.specialPrice : data.price}
                                       />
                            {data.specialPrice ?
                                <Image style={{marginLeft: Constant.scale(3), width: Constant.scale(17), height: Constant.scale(14)}} source={IC_SPECIAL}/> : null}
                        </View>
                        {
                            data.specialPrice ? <Text style={{fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert}}>库存:{stock}</Text> : null
                        }

                    </View>:null
                    }
                </TouchableOpacity>

            </View>
        );
    }
};
const ITEM_WIDTH=(width - Constant.sizeMarginDefault / 2) / 2;
const styles = StyleSheet.create({
    container: {
        width: ITEM_WIDTH ,
        height: Constant.scale(275),
        backgroundColor: Constant.colorDefault,
        marginBottom: Constant.sizeMarginDefault / 2
    },
    bottomStyle: {
    }
});