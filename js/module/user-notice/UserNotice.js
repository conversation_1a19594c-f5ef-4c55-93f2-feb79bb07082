/*
 * @Author: duo<PERSON>
 * @Date: 2020-07-16 11:26:47
 * @Last Modified by: duowen
 * @Last Modified time: 2020-07-27 15:58:50
 */
import React, { Component } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Dimensions,
  Platform,
  TouchableWithoutFeedback,
  ScrollView,
  StatusBar,
  AppState,
  Text
} from "react-native";
import PropType from "prop-types";
import { ReactNavComponent, Widget, Util } from "rn-yunxi";
import AppWidget from "../../app-widget";
import { SafeAreaView } from "react-navigation";

const { width, height } = Dimensions.get("window");
import { inject, observer } from "mobx-react/native";

const { WebView } = Widget;
const { StorageUtil } = Util;
const { Header, CheckBox, Button } = AppWidget;

@inject(stores => ({
  userNotice: stores.userNotice
}))
@observer
/**
 * 首页
 */
export default class UserNotice extends ReactNavComponent {
  //属性声名
  static propTypes = {};
  //默认属性
  static defaultProps = {};

  //构造函数
  constructor(props) {
    super(props);
    this.state = {
      html: "",
      readed: true
    };
  }

  componentWillMount() {
    this.didBlurSubscription = this.props.navigation.addListener(
      "didBlur",
      payload => {
        console.debug("didBlur");
      }
    );

    this.willDidFocusSubscription = this.props.navigation.addListener(
      "didFocus",
      payload => {
        console.debug("didFocus");
      }
    );
  }

  componentDidMount() {
    this.props.userNotice.getUserNotice();

    StorageUtil.getString("USER_NOTICE_READED", "false")
      .then(readed => {
        console.log("xxxxx =", readed);
        if (readed == "false") {
          this.props.userNotice.postUseNoticeReaded();
        }
        // this.setState({
        //   readed: readed == "true"
        // });
      })
      .catch(e => {});
  }

  componentWillUnmount() {
    this.didBlurSubscription.remove();
    this.willDidFocusSubscription.remove();
  }

  render() {
    const { html } = this.props.userNotice;
    return (
      <SafeAreaView
        forceInset={{ top: 0 }}
        style={{ flex: 1, backgroundColor: "white" }}
      >
        <Header
          style={styles.container}
          title="用户须知"
          navigation={this.props.navigation}
        >
          <WebView
            ref={ref => {
              this.webView = ref;
            }}
            onMessage={event => {}}
            scalesPageToFit={false}
            source={{ html: html }}
            onError={() => {}}
            onNavigationStateChange={o => {}}
          ></WebView>

          <CheckBox
            disabled={false}
            isChecked={this.state.readed}
            style={{ marginLeft: 10, marginTop: 10 }}
            rightText={"确认阅读用户须知"}
            onClick={() => {
              this.setState({
                readed: !this.state.readed
              });
            }}
          />

          <TouchableOpacity
            onPress={() => {
              this.props.navigation.pop();
            }}
            style={styles.applyBtnStyle}
          >
            <Text style={{ color: "white", fontSize: 16, fontWeight: "bold" }}>
              确 认
            </Text>
          </TouchableOpacity>
        </Header>
        {Platform.OS == "android" ? (
          <StatusBar
            backgroundColor={"transparent"}
            barStyle={"dark-content"}
            translucent={true}
          />
        ) : null}
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  row: {},
  checkBox: {
    paddingRight: Constant.sizeMarginDefault,
    width: Constant.scale(15) + Constant.sizeMarginDefault,
    height: Constant.scale(15)
  },

  applyBtnStyle: {
    width: Constant.scale(351),
    height: Constant.scale(40),
    backgroundColor: Constant.colorPrimary,
    borderRadius: Constant.scale(4),
    marginLeft: Constant.scale(12),
    marginBottom: Constant.scale(15),
    marginTop: Constant.scale(15),
    justifyContent: "center",
    alignItems: "center"
  }
});
