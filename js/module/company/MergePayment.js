/**
 *
 * Created by xiaowz on 2018/6/27.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager,
} from 'react-native';
import PropType from 'prop-types';
import { ReactNavComponent, Widget } from 'rn-yunxi';
import AppWidget from '../../app-widget';

const { width, height } = Dimensions.get('window');
const { Text, CommonListView, CommonFlatList, LabelCell } = Widget;
const { Header, PrimaryHeader, Button, Image, PriceText, CheckBox } = AppWidget;
import { toJS } from 'mobx';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import PaymentDetailModel from '../../store/PaymentDetail';
import { inject, observer } from 'mobx-react/native';
//模块声名并导出


@inject(stores => ({
    pay:stores.pay,
    user:stores.user,
}))
@observer
export default class MergePayment extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title:'',
    };
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        //状态机变量声明
        this.paymentDetailStore = new PaymentDetailModel();
        this.state = {
            dataArray:[],
            itemIds:new Set(),
        };
    }

    componentWillMount() {

        let { params } = this.getNavState();
        if (params && params.orgId) {
            this.paymentDetailStore.getUnpayList(params.orgId, (unPaymentListMap) => {
                let array = [];
                let tempIds = new Set();
                for (let [ key, value ] of unPaymentListMap) {
                    array.push(value);
                }
                let filterArray = [];
                filterArray = array.filter((item) => {
                    for (let obj of item.array){
                        if (obj.canPayFlag == true) {
                            return true;
                        } else {
                            return false;
                        }
                    }

                });


                this.setState({
                    dataArray:filterArray,
                }, () => {
                    for (let obj of this.state.dataArray) {
                        for (let obj2 of obj.array) {
                                tempIds.add(obj2.id);
                        }
                    }
                    this.setState({ itemIds:tempIds });
                });
            }).then().catch(e => {
            });
        }

    }

    //渲染
    render() {

        let { itemIds } = this.state;

        return (
            <Header title={'合并支付'}>
                <View style={{ flex:1, alignItems:'center' }}>
                    <ScrollView
                        showsVerticalScrollIndicator={false}
                        style={styles.container}
                    >
                        {
                            this.state.dataArray.length > 0 ?
                                <View style={{ flex:1, alignItems:'center' }}>
                                    {/*<Text style={{margin:Constant.scale(10),color:Constant.colorPrimary}}>您有杂费未缴纳，是否与本次订单一起支付？</Text>*/}
                                    {
                                        this.state.dataArray && this.state.dataArray.length > 0 ? this.state.dataArray.map((item, i) => {
                                            return (
                                                <View
                                                    key={i}
                                                    style={{
                                                        marginBottom:Constant.scale(10),
                                                        marginTop:Constant.scale(10),
                                                    }}
                                                >
                                                    <LabelCell
                                                        extraTextStyle={{ color:Constant.colorTxtContent }}
                                                        showRightIcon={false}
                                                        underLine={1}
                                                        title={<Text
                                                            numberOfLines={1}
                                                            style={{
                                                                fontSize:Constant.fontSizeBig,
                                                                width:'80%',
                                                            }}
                                                        >{item.orgName || '暂无'}</Text>}
                                                        extra={item.opreateTime || '暂无'}
                                                    />
                                                    {
                                                        item.array && item.array.length > 0 ?
                                                            item.array.map((obj, j) => {
                                                                let has = itemIds.has(obj.id);
                                                                return (
                                                                    <LabelCell
                                                                        key={j}
                                                                        onClick={() => {
                                                                            if (has) {
                                                                                itemIds.delete(obj.id);
                                                                                this.setState({});
                                                                            } else {
                                                                                itemIds.add(obj.id);
                                                                                this.setState({});
                                                                            }
                                                                        }}
                                                                        extraTextStyle={{ color:Constant.colorTxtTitle }}
                                                                        titleTextStyle={{ color:Constant.colorTxtContent }}
                                                                        showRightIcon={false}
                                                                        underLine={1}
                                                                        title={this.handleTitle(obj.paymentType)}
                                                                        extra={<PriceText
                                                                            size={5}
                                                                            price={obj.amount}
                                                                        />}
                                                                        icon={
                                                                            <CheckBox
                                                                                onClick={() => {
                                                                                    if (has) {
                                                                                        itemIds.delete(obj.id);
                                                                                        this.setState({});
                                                                                    } else {
                                                                                        itemIds.add(obj.id);
                                                                                        this.setState({});
                                                                                    }
                                                                                }}
                                                                                style={styles.checkBox}
                                                                                isChecked={has}
                                                                            />
                                                                        }
                                                                    />
                                                                );
                                                            })
                                                            : null
                                                    }

                                                </View>
                                            );
                                        }) : null
                                    }


                                    <View style={{ marginBottom:Constant.scale(10), width:width }}>
                                        <LabelCell
                                            extraTextStyle={{ color:Constant.colorTxtTitle }}
                                            titleTextStyle={{ color:Constant.colorTxtContent }}
                                            showRightIcon={false}
                                            underLine={2}
                                            title={'本次需缴纳：'}
                                            extra={
                                                <PriceText price={this.paymentDetailStore.computeResult(itemIds, this.state.dataArray) || 0} />}
                                            cellStyle={{ height:Constant.scale(30) }}
                                        />
                                    </View>
                                </View>
                                :
                                <View style={{ flex:1, alignItems:'center' }}>
                                    <Text
                                        style={{
                                            color:Constant.colorTxtContent,
                                            marginTop:Constant.scale(250),
                                        }}
                                    >暂无数据</Text>
                                </View>
                        }


                    </ScrollView>
                    <TouchableOpacity
                        onPress={() => this.mergePayClick()}
                        style={{
                            marginTop:Constant.scale(5),
                            marginBottom:Constant.scale(20),
                            backgroundColor:Constant.colorPrimary,
                            width:Constant.scale(350),
                            height:Constant.scale(40),
                            justifyContent:'center',
                            alignItems:'center',
                            borderRadius:Constant.scale(4),
                        }}
                    >
                        <Text style={{ color:'white', fontWeight:'bold' }}>合并支付</Text>
                    </TouchableOpacity>
                </View>
            </Header>

        );
    }


    handleTitle = (type) => {
        switch (type) {
            case 1:
                return '装车费';
            case 2:
                return '托板使用费';
            case 3:
                return '纸箱费';
            default:
                return '暂无';
        }
    };


    /**
     * 合并支付
     */
    mergePayClick = () => {
        let itemIds = Array.from(this.state.itemIds);
        this.paymentDetailStore.costSinglePayment(itemIds).then(
            (json) => {
                if (json && json.data) {
                    this.navigate('PayOrder', { payInfoData:json.data, enterType:1 });
                }
            },
        ).catch(e => {
        });
    };
};
const styles = StyleSheet.create({
    container:{
        flex:1,
        backgroundColor:Constant.colorBackgroundDefault,
    },
    checkBox:{
        // paddingRight: Constant.sizeMarginDefault,
        width:Constant.scale(15) + Constant.sizeMarginDefault,
        height:Constant.scale(15),
        // backgroundColor:'pink'
    },
});
