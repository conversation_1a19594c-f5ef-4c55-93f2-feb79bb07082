/**
 *
 * Created by xiaowz on 2018/1/22.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import {inject, observer} from 'mobx-react/native';
import AppWidget from '../../app-widget'
const {width, height} = Dimensions.get('window');
const {Text, CommonFlatList} = Widget;
const {Header,PrimaryHeader,Button,Image} = AppWidget
import BottleBalanceModel from '../../store/BottleBalance';
/**
 * 瓶箱余额
 */
@inject(stores => ({
}))
@observer
//模块声名并导出
export default class BottleBalance extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {
    };
    //默认属性
    static defaultProps = {
    };
    //构造函数
    constructor(props) {
        super(props);
         //状态机变量声明
        let {params} =this.getNavState();
         this.state={
             orgId: params.orgId || ''
         };
         this.bottleBalance = new BottleBalanceModel();
    }

    componentWillMount(){
        this.bottleBalance.getAccountTypeList(1,this.state.orgId);
    }
    componentDidMount(){}
    componentWillUnmount(){}

    renderRow = ({item,index})=> {
        return (
            <View key={`balance+${index}`}
                  style={{backgroundColor: index % 2 ? Constant.colorBackgroundDefault : Constant.colorDefault,height:Constant.scale(44),flexDirection: 'row',alignItems: 'center'}}
            >
                <View style={{flex: 2}}><Text numberOfLines={1} style={{fontSize: Constant.fontSizeSmall,color: Constant.colorTxtContent,marginLeft: Constant.sizeMarginDefault}}>{item.description}</Text></View>
                <Text style={{fontSize: Constant.fontSizeSmall,flex: 1,color: Constant.colorTxtContent,textAlign: 'center'}}>{item.balance}</Text>
                <Text style={{fontSize: Constant.fontSizeSmall,flex: 1,color: Constant.colorTxtContent,textAlign: 'center'}}>{item.occupiedAmount}</Text>
            </View>
        );
    }

    //渲染
    render() {
        return (
            <Header style={styles.container} title={'瓶箱余额'} >
                <View style={{height:Constant.scale(34),flexDirection: 'row',backgroundColor: Constant.colorDefault,alignItems: 'center'}}>
                    <View style={{flex: 2}}><Text style={{fontSize: Constant.fontSizeSmall,color: Constant.colorTxtContent,marginLeft: Constant.sizeMarginDefault}}>瓶箱描述</Text></View>
                    <Text style={{fontSize: Constant.fontSizeSmall,flex: 1,color: Constant.colorTxtContent,textAlign: 'center'}}>瓶箱余额(支)</Text>
                    <Text style={{fontSize: Constant.fontSizeSmall,flex: 1,color: Constant.colorTxtContent,textAlign: 'center'}}>占用(支)</Text>
                </View>
                <CommonFlatList
                    style={{flex:1}}
                    data={this.bottleBalance.listParams.data}
                    listState={this.bottleBalance.listParams.listState}
                    enableLoadMore={false}
                    keyExtractor={(item, index) => {
                                            return 'bottleBalance' + index;
                                        }}
                    renderItem={this.renderRow}
                    enableRefresh={false}
                >
                    <View style={[{flex: 1, justifyContent: 'center', alignItems: 'center',}]}>

                        <Text style={{fontSize: Constant.fontSizeBig, color: Constant.colorTxtContent, marginTop: Constant.scale(25)}}>
                            暂无数据
                        </Text>
                    </View>
                </CommonFlatList>
            </Header>
        );
    }
};
const styles = StyleSheet.create({
   container: {
         flex: 1,
    }
});
