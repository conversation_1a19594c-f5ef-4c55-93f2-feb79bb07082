/**
 *
 * Created by xiaowz on 2018/1/22.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import {inject, observer} from 'mobx-react/native';
import AppWidget from '../../app-widget'
const {width, height} = Dimensions.get('window');
const {Text, CommonFlatList} = Widget;
const {Header,PrimaryHeader,Button,Image} = AppWidget

/**
 * 瓶箱余额
 */
@inject(stores => ({
    bottleBanlance: stores.bottleBanlance,
}))
@observer
//模块声名并导出
export default class CapForWine extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {
    };
    //默认属性
    static defaultProps = {
    };
    //构造函数
    constructor(props) {
        super(props);
         //状态机变量声明
         this.state={

         };
    }

    componentWillMount(){
        this.props.bottleBanlance.getAccountTypeList();
    }
    componentDidMount(){}
    componentWillUnmount(){}

    renderRow = ({item,index})=> {
        return (
            <View key={`balance+${index}`}
                  style={{backgroundColor: index % 2 ? Constant.colorBackgroundDefault : Constant.colorDefault,minHeight:Constant.scale(44),flexDirection: 'row',alignItems: 'center'}}
            >
                <View style={{flex: 1}}>
                    <Text style={{fontSize: Constant.fontSizeSmall,color: Constant.colorTxtContent,margin: Constant.sizeMarginDefault}}>一元兑奖</Text>
                </View>
                <View style={{flex: 2}}>
                    <Text style={{fontSize: Constant.fontSizeSmall,color: Constant.colorTxtContent,margin: Constant.sizeMarginDefault,}}>9度珠江纯生660ml*12</Text>
                </View>
                <View style={{flex: 1}}>
                    <Text style={{fontSize: Constant.fontSizeSmall,color: Constant.colorTxtContent,margin: Constant.sizeMarginDefault,textAlign: 'center'}}>210</Text>
                </View>
            </View>
        );
    }

    //渲染
    render() {
        return (
            <Header style={styles.container} title={'瓶盖赠酒'} navigation={this.props.navigation}>
                <View style={{height:Constant.scale(34),flexDirection: 'row',backgroundColor: Constant.colorDefault,alignItems: 'center'}}>
                    <View style={{flex: 1}}><Text style={{fontSize: Constant.fontSizeSmall,color: Constant.colorTxtContent,margin: Constant.sizeMarginDefault}}>奖项内容</Text></View>
                    <View style={{flex: 2}}><Text style={{fontSize: Constant.fontSizeSmall,color: Constant.colorTxtContent,margin: Constant.sizeMarginDefault,textAlign: 'center'}}>商品名称</Text></View>
                    <View style={{flex: 1}}><Text style={{fontSize: Constant.fontSizeSmall,color: Constant.colorTxtContent,margin: Constant.sizeMarginDefault,textAlign: 'center'}}>可兑换(箱)</Text></View>
                </View>
                <CommonFlatList
                    style={{flex:1}}
                    data={[{key:1},{key:2},{key:3},{key:4},]}
                    listState={this.props.bottleBanlance.listParams.listState}
                    enableLoadMore={this.props.bottleBanlance.listParams.enableLoadMore}
                    keyExtractor={(item, index) => {
                                            return 'bottleBanlance' + index;
                                        }}
                    renderItem={this.renderRow}
                    enableRefresh={this.props.bottleBanlance.listParams.enableRefresh}
                >
                    <View style={[{flex: 1, justifyContent: 'center', alignItems: 'center',}]}>

                        <Text style={{fontSize: Constant.fontSizeBig, color: Constant.colorTxtContent, marginTop: Constant.scale(25)}}>
                            暂无数据
                        </Text>
                    </View>
                </CommonFlatList>
            </Header>
        );
    }
};
const styles = StyleSheet.create({
   container: {
         flex: 1,
    }
});
