import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget';
import UserStore from '../../store/User';
import {inject, observer} from "mobx-react/native";

const {Header, PriceText, Image} = AppWidget;
// import Image from "js/app-widget/image/Image";

const RIGHT_ICON = require('.././img/next.png')
const {width, height} = Dimensions.get('window');
const {Text} = Widget;

//模块声名并导出
@inject(stores => ({
    user: stores.user,
}))
@observer
export default class PaymentDetailItem extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {
        onClickItem: PropType.func
    };
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        //状态机变量声明
        this.state = {};
    }

    renderItem = (item) => {
        let status = item.status;
        if (status === 0) {
            return (
                <TouchableOpacity onPress={() => this.itemClick()}>
                    <View style={styles.timeView}>
                        <Text style={styles.timeStyle}>{item.opreateTime}</Text>
                    </View>
                    <View style={styles.contentView}>
                        <Text style={styles.timeStyle}>{item.orgName}</Text>
                        <View style={{flexDirection: 'row', justifyContent: 'center'}}>
                            <PriceText size={5} price={item.amount}/>
                            {/*<Text style={styles.timeStyle}>{this.props.user.getSymbol()}{item.amount}</Text>*/}
                            <Image resizeMode={'contain'} source={RIGHT_ICON} style={{
                                width: Constant.scale(15),
                                height: Constant.scale(15),
                                marginLeft: Constant.scale(5)
                            }}/>
                        </View>

                    </View>
                </TouchableOpacity>
            )
        } else if (status === 1) {
            return (
                <View>
                    <View style={styles.timeView}>
                        <Text style={styles.timeStyle}>{item.opreateTime}</Text>
                    </View>
                    <View style={styles.contentView}>
                        <Text style={styles.timeStyle}>{item.orgName}</Text>
                        <PriceText size={5} price={item.amount}/>
                    </View>
                </View>
            )
        }
    }

    //渲染
    render() {
        let item = this.props.data;
        return (
            <View style={styles.container}>
                {this.renderItem(item)}
            </View>
        );
    }

    itemClick = () => {
        this.props.onClickItem && this.props.onClickItem()
    }
};
const styles = StyleSheet.create({
    container: {
        flex: 1
    },
    timeStyle: {
        fontSize: Constant.fontSizeNormal,
        color: Constant.colorTxtTitle
    },
    timeView: {
        width: width,
        height: Constant.scale(40),
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: Constant.colorBackgroundDefault
    },
    contentView: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: width,
        height: Constant.scale(40),
        paddingRight: Constant.scale(10),
        paddingLeft: Constant.scale(10),
        backgroundColor: Constant.colorDefault
    }
});
