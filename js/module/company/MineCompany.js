import React, { Component } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Image,
  DeviceEventEmitter,
  InteractionManager,
  Dimensions,
  Alert,
  ScrollView
} from "react-native";
import PropType from "prop-types";
import { ReactNavComponent, Widget } from "rn-yunxi";
import AppWidget from "../../app-widget";
import { inject, observer } from "mobx-react/native";
import { toJS } from "mobx";
import H5Util from "../../util/H5Util";
import ChargeWineModel from "../../store/ChargeWine";
import { Util } from "rn-yunxi";
import ToastUtil from "../../util/ToastUtil";
// import Api from "js/util/Api";
const StorageUtil = Util.StorageUtil;
const { width, height } = Dimensions.get("window");
const { Header, PriceText, SelectPop } = AppWidget;
const { Text, LabelCell } = Widget;

const Tab1 = [
  {
    label: "发票信息",
    icon: require("../img/company/invoice.png"),
    type: 1,
    style: { width: Constant.scale(17.5), height: Constant.scale(20) }
  },
  {
    label: "收货地址",
    icon: require("../img/company/address.png"),
    type: 2,
    style: { width: Constant.scale(22.5), height: Constant.scale(17) }
  },
  {
    label: "分销商订单",
    icon: require("../img/company/distributor_order.png"),
    type: 3,
    style: { width: Constant.scale(16), height: Constant.scale(19) }
  },
  {
    label: "我的订单",
    icon: require("../img/company/my_order.png"),
    type: 4,
    style: { width: Constant.scale(18), height: Constant.scale(19.5) }
  },
  {
    label: "分销商核销",
    icon: require("../img/company/fenxiaoshanghexiao.png"),
    type: 15,
    style: { width: Constant.scale(17.5), height: Constant.scale(19.5) }
  },
  {
    label: "我的奖券",
    icon: require("../img/company/jiangquan.png"),
    type: 16,
    style: { width: Constant.scale(21), height: Constant.scale(19) }
  },
  {
    label: "申请转入",
    icon: require("../img/company/shenqingzhuanru2.png"),
    type: 19,
    style: { width: Constant.scale(17.5), height: Constant.scale(19.5) }
  },
  {
    label: "我的转入",
    icon: require("../img/company/wodezhuanru.png"),
    type: 20,
    style: { width: Constant.scale(21), height: Constant.scale(21) }
  },
  {
    label: "售后列表",
    icon: require("../img/company/wodezhuanru.png"),
    type: 21,
    style: { width: Constant.scale(21), height: Constant.scale(21) }
  },
  {
    label: "用户须知",
    icon: require("../img/company/my_order.png"),
    type: 22,
    style: { width: Constant.scale(18), height: Constant.scale(19.5) }
  },
  {
    label: "基本信息",
    icon: require("../img/company/my_order.png"),
    type: 23,
    style: { width: Constant.scale(18), height: Constant.scale(19.5) }
  }
];
const Tab2 = [
  {
    label: "收货地址",
    icon: require("../img/company/address.png"),
    type: 2,
    style: { width: Constant.scale(22.5), height: Constant.scale(17) }
  },
  {
    label: "待收货",
    icon: require("../img/company/wait_order.png"),
    type: 5,
    style: { width: Constant.scale(22.5), height: Constant.scale(17.5) }
  },
  {
    label: "待转单",
    icon: require("../img/company/change_order.png"),
    type: 6,
    style: { width: Constant.scale(16.5), height: Constant.scale(19.5) }
  },
  {
    label: "我的订单",
    icon: require("../img/company/my_order.png"),
    type: 4,
    style: { width: Constant.scale(18), height: Constant.scale(19.5) }
  },
  {
    label: "门店核销",
    icon: require("../img/company/mendianhexiao.png"),
    type: 15,
    style: { width: Constant.scale(20.5), height: Constant.scale(18) }
  },
  {
    label: "我的奖券",
    icon: require("../img/company/jiangquan.png"),
    type: 16,
    style: { width: Constant.scale(21), height: Constant.scale(19) }
  },
  {
    label: "申请补货",
    icon: require("../img/company/buhuo.png"),
    type: 17,
    style: { width: Constant.scale(21), height: Constant.scale(19.75) }
  },
  {
    label: "我的补货单",
    icon: require("../img/company/buhuodan.png"),
    type: 18,
    style: { width: Constant.scale(17.35), height: Constant.scale(19.5) }
  },
  {
    label: "用户须知",
    icon: require("../img/company/my_order.png"),
    type: 22,
    style: { width: Constant.scale(18), height: Constant.scale(19.5) }
  }
];

//瓶箱余额1,费用赠酒2,瓶盖赠酒3,预存款4,营销费用5
const AccountType = [
  { key: "bottleBalance", value: 1 },
  { key: "costsGiveWine", value: 2 },
  {
    key: "capGiveWine",
    value: 3
  }
];

/**
 * 我的公司
 */
@inject(stores => ({
  user: stores.user
}))
@observer
export default class MineCompany extends ReactNavComponent {
  // static navigationOptions={
  //     tabBarOnPress:({previousScene,scene,jumpToIndex})=>{
  //         Log('tabBarOnPress',this.props);
  //         if(jumpToIndex){
  //             jumpToIndex(scene.index)
  //         }
  //     }
  // }
  //属性声名
  static propTypes = {};
  //默认属性
  static defaultProps = {};

  //构造函数
  constructor(props) {
    super(props);
    this.state = {
      //状态机变量声明
      title: "暂无经销商"
      // dealerList:[]
    };
  }

  updateInfo() {
    this.props.user
      .getUserInfo()
      .then()
      .catch(err => {});

    this.props.user
      .getCompanyInfo()
      .then()
      .catch(err => {});

    this.props.user
      .getUserLimits()
      .then()
      .catch(err => {});

    this.props.user
      .getUserId()
      .then()
      .catch(err => {});
    if (this.props.user.isDealer()) {
      this.props.user
        .getAllTakeDeliveryOrgList()
        .then()
        .catch(err => {});
      this.loadDealerList();

      this.props.user
        .getTakeDeliveryOrgInfo()
        .then()
        .catch(err => {});
    }

    this.props.user.isDealer()
      ? this.getDealerTitle()
      : this.getChooseOrgItemClick();
  }

  getDealerTitle = () => {
    let name =
      this.props.user.companyInfo && this.props.user.companyInfo.customerName;
    this.setState({
      title: name
    });
  };

  paymentDetailInfo = () => {
    if (this.props.user.isDealer()) {
      this.props.user
        .getPaymentDetail()
        .then()
        .catch(err => {});
    }
  };

  loadDealerList = () => {
    if (
      this.props.user.companyInfo &&
      this.props.user.isDealer() &&
      !this.props.user.isSubAccount()
    ) {
      this.props.user
        .loadDealerList()
        .then()
        .catch(e => {});
    }
  };

  //分销商展示名称
  getChooseOrgItemClick = () => {
    Api.getChooseOrgItem()
      .then(item => {
        this.setState({
          title: item.name
        });
      })
      .catch(e => {});
  };

  componentWillMount() {
    this.didBlurSubscription = this.props.navigation.addListener(
      "didBlur",
      payload => {
        console.debug("didBlur");
      }
    );

    this.willDidFocusSubscription = this.props.navigation.addListener(
      "didFocus",
      payload => {
        console.debug("didFocus");
        this.reloadData();
      }
    );
  }

  reloadData = () => {
    this.updateInfo();
    this.paymentDetailInfo();
  };

  componentDidMount() {}

  componentWillUnmount() {
    this.didBlurSubscription.remove();
    this.willDidFocusSubscription.remove();
  }

  renderHeader() {
    return (
      <View>
        <TouchableOpacity
          onPress={() => this.chooseDealerClick()}
          style={{
            flexDirection: "row",
            justifyContent: "center",
            alignItems: "center"
          }}
        >
          <Text
            style={{
              textAlign: "center",
              fontSize: Constant.fontSizeCaption,
              width: "90%"
            }}
          >
            {this.state.title}
          </Text>
          {/*<Image*/}
          {/*source={require('../img/company/back_down_arrow.png')}*/}
          {/*style={{marginLeft: Constant.scale(5), width: Constant.scale(6), height: Constant.scale(4)}}*/}
          {/*/>*/}
        </TouchableOpacity>
      </View>
    );
  }

  chooseDealerClick = async () => {
    let isDealer = this.props.user.isDealer();
    let distributorDealerData = [];
    distributorDealerData = await Api.getLocalOrgList()
      .then()
      .catch(e => {});
    let defaultChooseItem = null;
    if (!isDealer) {
      defaultChooseItem = await Api.getChooseOrgItem()
        .then()
        .catch(e => {});
    }
    let title = "选择经销商";
    let data = isDealer ? this.props.user.dealerList : distributorDealerData;
    let selectId = isDealer
      ? this.props.user.companyInfo.code
      : defaultChooseItem.code;
    let selectKey = "code";
    let labelName = "name";
    // Log(selectId);
    Widget.Popup.show(
      <SelectPop
        popTitle={title}
        listData={data}
        labelName={labelName}
        selectId={selectId}
        selectKey={selectKey}
        selectCallBack={selectData => {
          //TODO
          Widget.Popup.hide();
          InteractionManager.runAfterInteractions(() => {
            // Log('选中的经销商',toJS(selectData.code))
            if (isDealer) {
              if (selectData.id != selectId) {
                this.props.user
                  .getNewUserInfo(selectData.code)
                  .then(data => {
                    Api.userLogin(
                      {
                        userCode: selectData.code,
                        userPassword: data.password
                      },
                      0,
                      true
                    )
                      .then(() => {
                        this.updateInfo();
                        this.paymentDetailInfo();
                      })
                      .catch(e => {});
                  })
                  .catch(e => {});
              }
            } else {
              if (selectData.id != selectId) {
                Api.saveChooseOrgItem(selectData)
                  .then(() => {
                    this.reloadData();
                  })
                  .catch(e => {});
              }
            }
          });
        }}
      />,
      {
        animationType: "slide-up",
        backgroundColor: "#00000000",
        onMaskClose: () => {
          Widget.Popup.hide();
        }
      }
    );
  };

  /**
   * Tab按钮点击
   * @param item
   * @param index
   */
  onTabItemPress = (item, index) => {
    Log("=====item", item, index);
    switch (item.type) {
      case 1:
        this.navigate("InvoiceInfo");
        break;
      case 2:
        this.navigate("AddressList");
        break;
      case 3:
        this.navigate("OrderList", { type: 3 });
        break;
      case 4:
        this.navigate("OrderList", { type: 1 });
        break;
      case 5:
        this.navigate("OrderList", { type: 1, tabIndex: 2 });
        break;
      case 6:
        this.navigate("OrderList", { type: 1, tabIndex: 1 });
        break;
      case 15:
        this.navigate("VerificationList");
        break;
      case 16:
        this.navigate("MyCoupons");
        break;
      case 17:
        this.navigate("ApplyReplenish");
        break;
      case 18:
        this.navigate("MyReplenishments");
        break;
      case 19:
        //申请转入
        this.navigate("ApplyTransfer");
        break;
      case 20:
        //我的转入
        this.navigate("MyTransfers");
        break;
      case 21:
        //我的转入
        this.navigate("AfterSaleList");
        break;
      case 22:
        // 用户须知
        this.navigate("UserNotice");
        break;
      case 23:
        // 基本信息
        this.navigate("UserInfo");
        break;
      default:
        break;
    }
  };

  /**
   * 切换提货组织
   */
  changeTakeDeliveryOrg = () => {
    Widget.Popup.show(
      <SelectPop
        listData={this.props.user.allTakeDeliveryOrgList}
        selectId={this.props.user.selectTakeDeliveryOrg.orgId}
        popTitle={"提货组织选择"}
        labelName={"orgName"}
        selectKey={"orgId"}
        selectCallBack={selectData => {
          //TODO
          Widget.Popup.hide();
          InteractionManager.runAfterInteractions(() => {
            this.props.user.setSelectTakeDeliveryOrg(selectData);
          });
        }}
      />,
      {
        animationType: "slide-up",
        backgroundColor: "#00000000",
        onMaskClose: () => {
          Widget.Popup.hide();
        }
      }
    );
  };

  /**
   * 提货组织
   * @return {XML}
   */
  renderTakeDeliveryOrganization = () => {
    let info = this.props.user.selectTakeDeliveryOrg;
    let takeDeliveryOrgInfo = null;
    if (info && info.orgId) {
      takeDeliveryOrgInfo = this.props.user.takeDeliveryOrgInfoMap.get(
        info.orgId
      );
    }
    return (
      <View
        style={{
          marginTop: Constant.sizeMarginDefault,
          paddingBottom: Constant.sizeMarginDefault,
          backgroundColor: "white"
        }}
      >
        <View
          style={{
            flexDirection: "row",
            paddingLeft: Constant.sizeMarginDefault,
            paddingRight: Constant.sizeMarginDefault,
            height: Constant.scale(44),
            alignItems: "center",
            justifyContent: "space-between"
          }}
        >
          <Text
            style={{
              fontSize: Constant.fontSizeSmall,
              color: Constant.colorTxtAlert
            }}
          >
            企业财务信息
          </Text>

          <TouchableOpacity
            onPress={this.changeTakeDeliveryOrg}
            style={{
              flexDirection: "row",
              justifyContent: "center",
              alignItems: "center"
            }}
          >
            <Text
              style={{
                fontSize: Constant.fontSizeNormal
              }}
            >
              {info.orgName}
            </Text>
            <Image
              source={require("../img/arrow/ic_content_open.png")}
              style={{
                marginLeft: Constant.sizeMarginDefault,
                width: Constant.scale(10),
                height: Constant.scale(5.5)
              }}
            />
          </TouchableOpacity>
        </View>
        <View style={GlobalStyle.styleDividerDefault} />
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            padding: Constant.sizeMarginDefault
          }}
        >
          <View
            style={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center"
            }}
          >
            <PriceText
              price={
                takeDeliveryOrgInfo && takeDeliveryOrgInfo.reDeposit
                  ? takeDeliveryOrgInfo.reDeposit
                  : 0
              }
            />
            <Text
              style={{
                color: Constant.colorTxtContent,
                fontSize: Constant.fontSizeSmall,
                marginTop: Constant.scale(5)
              }}
            >
              预存款
            </Text>

            <Text
              style={{
                color: Constant.colorTxtAlert,
                fontSize: Constant.fontSizeSmall,
                marginTop: Constant.scale(0)
              }}
            >
              占用
              <PriceText
                size={-1}
                price={
                  takeDeliveryOrgInfo &&
                  takeDeliveryOrgInfo.occupiedReDeposit != null
                    ? takeDeliveryOrgInfo.occupiedReDeposit
                    : 0
                }
              />
            </Text>
          </View>
          <View
            style={{
              width: Constant.sizeDividerNormal,
              height: Constant.scale(32),
              backgroundColor: Constant.colorDividerDefault
            }}
          />
          <View
            style={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center"
            }}
          >
            <PriceText
              price={
                takeDeliveryOrgInfo &&
                takeDeliveryOrgInfo.marketingCosts != null
                  ? takeDeliveryOrgInfo.marketingCosts
                  : 0
              }
            />
            <Text
              style={{
                color: Constant.colorTxtContent,
                fontSize: Constant.fontSizeSmall,
                marginTop: Constant.scale(5)
              }}
            >
              营销费用
            </Text>
            <Text
              style={{
                color: Constant.colorTxtAlert,
                fontSize: Constant.fontSizeSmall,
                marginTop: Constant.scale(0)
              }}
            >
              占用
              <PriceText
                size={-1}
                price={
                  takeDeliveryOrgInfo &&
                  takeDeliveryOrgInfo.occupiedMarketingCosts != null
                    ? takeDeliveryOrgInfo.occupiedMarketingCosts
                    : 0
                }
              />
            </Text>
          </View>
        </View>
        <View
          style={{
            flexDirection: "row"
          }}
        >
          <TouchableOpacity
            onPress={() => {
              this.navigate("MarginBalance", { orgId: info.orgId });
            }}
            style={{
              alignItems: "center",
              flex: 1,
              padding: Constant.sizeMarginDefault
            }}
          >
            <Image
              style={{ width: Constant.scale(30), height: Constant.scale(30) }}
              source={require("../img/company/ic_marginBalance_icon.png")}
            />
            <Text
              style={{
                color: Constant.colorTxtContent,
                marginTop: Constant.scale(5),
                fontSize: Constant.fontSizeSmall
              }}
            >
              按金余额
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              this.navigate("BottleBalance", { orgId: info.orgId });
              // this.navigate('CommonWebView',{url:H5Util.getBottleBoxSurplusUrl()})
            }}
            style={{
              alignItems: "center",
              flex: 1,
              padding: Constant.sizeMarginDefault
            }}
          >
            <Image
              style={{ width: Constant.scale(30), height: Constant.scale(30) }}
              source={require("../img/company/remain.png")}
            />
            <Text
              style={{
                color: Constant.colorTxtContent,
                marginTop: Constant.scale(5),
                fontSize: Constant.fontSizeSmall
              }}
            >
              瓶箱余额
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              this.navigate("ChargeWine", {
                accountType: AccountType[1].value,
                orgId: info.orgId,
                orgName: info.orgName,
                marketingCosts:
                  takeDeliveryOrgInfo && takeDeliveryOrgInfo.marketingCosts
                    ? takeDeliveryOrgInfo.marketingCosts
                    : 0
              });
            }}
            style={{
              alignItems: "center",
              flex: 1,
              padding: Constant.sizeMarginDefault
            }}
          >
            <Image
              style={{ width: Constant.scale(30), height: Constant.scale(30) }}
              source={require("../img/company/cost.png")}
            />
            <Text
              style={{
                color: Constant.colorTxtContent,
                marginTop: Constant.scale(5),
                fontSize: Constant.fontSizeSmall
              }}
            >
              费用赠酒
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              this.navigate("BottleCapWine", {
                orgId: info.orgId,
                accountType: 3
              });
            }}
            style={{
              alignItems: "center",
              flex: 1,
              padding: Constant.sizeMarginDefault
            }}
          >
            <Image
              style={{ width: Constant.scale(30), height: Constant.scale(30) }}
              source={require("../img/company/bottle_cap.png")}
            />
            <Text
              style={{
                color: Constant.colorTxtContent,
                marginTop: Constant.scale(5),
                fontSize: Constant.fontSizeSmall
              }}
            >
              瓶盖赠酒
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  renderTab() {
    let array = this.props.user.isDealer() ? Tab1 : Tab2;
    return (
      <View
        style={{
          flexDirection: "row",
          backgroundColor: "white",
          flexWrap: "wrap",
          marginBottom: Constant.scale(10)
        }}
      >
        {array.map((item, index) => {
          return (
            <TouchableOpacity
              onPress={() => {
                this.onTabItemPress(item, index);
              }}
              key={index}
              style={{
                width: width / 4,
                height: index > 3 ? Constant.scale(79) : Constant.scale(89),
                justifyContent: index > 3 ? undefined : "center",
                alignItems: "center"
              }}
            >
              <View
                style={{
                  width: Constant.scale(24),
                  height: Constant.scale(24),
                  justifyContent: "center",
                  alignItems: "center",
                  marginTop: index > 3 ? Constant.scale(8) : 0
                }}
              >
                <Image source={item.icon} style={item.style} />
              </View>
              <Text
                style={{
                  marginTop: Constant.scale(8),
                  color: Constant.colorTxtContent
                }}
              >
                {item.label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  }

  renderPaymentDetails = () => {
    return [
      { key: "loadingFee", title: "卸车费", type: 1 },
      { key: "cartonFee", title: "纸箱费", type: 3 },
      { key: "tuobanFee", title: "托板使用费", type: 2 }
    ].map((item, index) => {
      let status = this.props.user.paymentDetailMap.get(item.key);
      return (
        <TouchableOpacity
          key={index}
          onPress={() => {
            this.navigate("PaymentDetails", {
              title: item.title,
              type: item.type
            });
          }}
        >
          <View style={styles.item}>
            <View style={{ paddingLeft: 15 }}>
              <Text style={{ color: "#333333" }}>{item.title}</Text>
            </View>
            <View
              style={{
                paddingRight: 5,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center"
              }}
            >
              <Text
                style={{
                  color:
                    status === "已结清"
                      ? Constant.colorTxtAlert
                      : Constant.colorTxtPrimary
                }}
              >
                {status}
              </Text>
              <Image
                style={{
                  width: Constant.scale(15),
                  height: Constant.scale(15),
                  resizeMode: "contain"
                }}
                source={require("../img/next.png")}
              />
            </View>
          </View>
        </TouchableOpacity>
      );
    });
  };

  //渲染
  render() {
    // Log('aaaaa',this.props.user.userLimits);
    return (
      <Header
        style={styles.container}
        showBackAction={false}
        rightAction={() => {
          Alert.alert("", "确定退出？", [
            { text: "取消" },
            {
              text: "确定",
              onPress: () => {
                Api.logout().then(() => {
                  this.navigate("Login");
                });
              }
            }
          ]);
        }}
        rightTitle={"退出"}
        titleView={this.renderHeader()}
      >
        <ScrollView style={styles.container}>
          {this.renderTab()}

          {this.props.user.showPaymentDetails
            ? this.renderPaymentDetails()
            : null}

          {this.props.user.allTakeDeliveryOrgList &&
          this.props.user.allTakeDeliveryOrgList.length > 0 &&
          this.props.user.showOrgInfo
            ? this.renderTakeDeliveryOrganization()
            : null}
        </ScrollView>
      </Header>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  item: {
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: "white",
    borderTopWidth: 1,
    borderColor: Constant.colorDividerDefault,
    alignItems: "center",
    height: Constant.scale(40)
  }
});
