import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    Dimensions,
} from 'react-native';
import PropType from 'prop-types';
import {inject, observer} from 'mobx-react/native';
import { toJS } from 'mobx';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget';
import ScrollableTabView from "react-native-scrollable-tab-view";
import DefaultTabBar from "../../app-widget/scroll-tab-bar/DefaultTabBar";
import PaymentDetailItem from "./PaymentDetailItem";
import PaymentDetailModel from '../../store/PaymentDetail'

const {width, height} = Dimensions.get('window');
const {Header,SelectPop} = AppWidget;
const {CommonFlatList, Text} = Widget;
// 费用类型(1：装车费；2：托板使用费；3：纸箱费 )
const paymentType = [
    { title: '卸车费', type: 1},
    { title: '纸箱费', type: 3},
    { title: '托板使用费', type: 2}
];
const paymentStatus = ['待支付','已支付'];

@inject(stores => ({
  pay:stores.pay
}))

/**
 * 费用缴纳详情
 */
@observer
export default class PaymentDetails extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.paymentDetailStore = new PaymentDetailModel();
        this.state = {//状态机变量声明
            title:'暂无',
            type:null,
            tab:0,
        };
    }

    componentWillMount() {

        this.props.pay.cleanPayInfo();
        this.didBlurSubscription = this.props.navigation.addListener(
            'didBlur',
            payload => {
                console.debug('didBlur');
            }
        );

        this.willDidFocusSubscription = this.props.navigation.addListener(
            'didFocus',
            payload => {
                console.debug('didFocus');
                this.loadListData()
            }
        );




    }

    componentDidMount() {
    }

    componentWillUnmount() {
        this.didBlurSubscription.remove()
        this.willDidFocusSubscription.remove()
    }

    loadListData = ()=>{
        let {params} = this.getNavState()
        if (params && params.title && params.type) {
            this.setState({
                    title: params.title,
                    type: params.type
                }, () => {
                    this.paymentDetailStore.changeTabClick(0, true, this.state.type)
                }
            )
        }
    }

    //渲染
    render() {
        let selectId = this.state.title;
        let selectKey = 'title';
        let labelName = 'title';
        return (
            <Header style={styles.container}
                    titleView={<TouchableOpacity style={{flexDirection:'row',justifyContent:'center',alignItems:'center'}}
                                                 onPress={() => {
                                                    Widget.Popup.show(
                                                        <SelectPop
                                                            popTitle={'费用项选择'}
                                                            listData={paymentType}
                                                            labelName={labelName}
                                                            selectId={selectId}
                                                            selectKey={selectKey}
                                                            showPleaseChooseBtn={false}
                                                            selectCallBack={(selectData) => {
                                                                Widget.Popup.hide();
                                                                this.setState({
                                                                    title:selectData.title,
                                                                    type:selectData.type
                                                                })
                                                                this.paymentDetailStore.changeTabClick(0,true, selectData.type)
                                                            }}/>,
                                                        {
                                                            animationType: 'slide-up', backgroundColor: '#00000000',
                                                            onMaskClose: () => {
                                                                Widget.Popup.hide()
                                                            }
                                                        }
                                                    )
                                                }}>
                                    <Text style={styles.titleStyle}>{this.state.title}</Text>
                                    <Image style={styles.titleImgStyle} source={require('../img/arrow/ic_content_open.png')}/>
                                </TouchableOpacity>}>
                <View style={styles.container}>
                    {this.renderTab()}
                </View>
            </Header>
        );
    }

    renderTab = ()=>{
        return (
            <ScrollableTabView style={{flex:1}}
                               page={this.paymentDetailStore.page}
                               scrollWithoutAnimation={true}
                               renderTabBar={() => <DefaultTabBar/>}
                               onChangeTab={(object) => {
                                   if (this.listView) {
                                       this.listView.scrollTo({ x: 0, y: 0, animated: false });
                                   }
                                   this.paymentDetailStore.changeTabClick(object.i, true, this.state.type);
                               }}>
                {
                    paymentStatus.map((obj,i)=>{
                        return (this.renderListView(obj,i))
                    })
                }
            </ScrollableTabView>
        )
    }

    renderListView = (tabName,tabIndex)=>{
        return (
            <View tabLabel={tabName} key={`paymentDetail_list${tabIndex}`} style={{flex:1}}>
                <CommonFlatList
                    listViewRef={ref => this.listView = ref}
                    style={{flex:1}}
                    data={this.paymentDetailStore.listParams[tabIndex].dataArray}
                    listState={this.paymentDetailStore.listParams[tabIndex].listState}
                    enableLoadMore={this.paymentDetailStore.listParams[tabIndex].enableLoadMore}
                    keyExtractor={(item, index) => {
                        return tabIndex + 'paymentDetail' + index;
                    }}
                    onLoadMore={() => {
                        this.paymentDetailStore.getPaymentDetailListData(tabIndex, true, this.state.type);

                    }}
                    renderItem={this.renderItem}
                    enableRefresh={true}
                >
                    <View style={[{flex: 1, justifyContent: 'center', alignItems: 'center',}]}>

                        <Text style={{fontSize: Constant.fontSizeBig, color: Constant.colorTxtContent, marginTop: Constant.scale(25)}}>
                            暂无数据
                        </Text>
                    </View>
                </CommonFlatList>
            </View>
        );
    }

    //列表item
    renderItem = ({item,index})=>{
        return(
            <PaymentDetailItem data = {item}
                               onClickItem={()=>this.clickItem(item)}
            />
        )
    }

    clickItem = (item)=>{
       if(item.canPayFlag){
           this.navigate('MergePayment',{orgId:item.orgId})
       }
        // this.paymentDetailStore.getUnpayList(item).then().catch(e=>{});
        Log('---------点击',toJS(item))
        // if (item && item.id){
        //     this.paymentDetailStore.costSinglePayment(item.id).then(
        //         (json)=>{
        //             if (json && json.data){
        //                 this.navigate('PayOrder', {payInfoData:json.data})
        //             }
        //         }
        //     ).catch(e=>{})
        // }

    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    titleStyle: {
      fontSize:Constant.scale(18),
      color:Constant.colorTxtDefault,
    },
    titleImgStyle: {
        width:Constant.scale(10),
        height:Constant.scale(10),
        marginLeft:Constant.scale(5)
    },
    tabStyle: {
        flex: 1,
        backgroundColor: Constant.colorBackgroundDefault
    }
});