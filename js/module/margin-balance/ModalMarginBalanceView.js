/**
 *
 * Created by xiaowz on 2018/11/9.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager,
    Modal
} from 'react-native';
// import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'
const {width, height} = Dimensions.get('window');
const {Text, CommonListView,CommonFlatList} = Widget;
const {Header,PrimaryHeader,Button,Image} = AppWidget;
import {toJS} from 'mobx';
import PropTypes from 'prop-types';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
//模块声名并导出

const dataArray = ['1','2','3','4','5'];
export default class ModalMarginBalanceView extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {
        modalVisible:PropTypes.bool.isRequired,    //显示隐藏
        // title: PropTypes.string.isRequired,        //标题             必须
        // content: PropTypes.any, //内容
        // inputText: PropTypes.any.isRequired,    //输入框内容        必须
        // cancelClick: PropTypes.func.isRequired,    //取消方法          必须
        defaultClick: PropTypes.func.isRequired,   //确定方法          必须
        // onChangeText:PropTypes.func.isRequired     // 输入完成回调方法  必须
    };
    //默认属性
    static defaultProps = {

    };
    //构造函数
    constructor(props) {
        super(props);
         //状态机变量声明
         this.state={
             modalVisible:false,
         };
    }

    //渲染
    render() {
        return (

            <Modal
                visible={this.props.modalVisible ? this.props.modalVisible :this.state.modalVisible}
                animationType={'fade'}
                transparent={true}
                onRequestClose={() => {

                }}>
                <View style={styles.modalViewStyle}>
                    <View style={styles.dialogViewStyle}>
                        <Text style={{
                            marginTop: Platform.OS === 'ios' ? Constant.scale(25) : Constant.scale(15),
                            fontSize: 18,
                            fontWeight: '500'
                        }}>
                            {this.props.title ? this.props.title : '提示'}
                        </Text>
                        <View style={{width:'100%',height:Constant.sizeDividerNormal,backgroundColor:Constant.colorDivider,marginTop:10,marginBottom:10}}></View>
                        <Text style={{color:Constant.colorTxtContent,marginLeft:10,marginRight:10,textAlign:'center'}}>您有按金未退，请退按后才能下单，需要退按品种如下：</Text>
                        <View style={{borderColor:Constant.colorDivider,borderBottomWidth:Constant.sizeDividerNormal,borderLeftWidth:Constant.sizeDividerNormal,width:'90%',minHeight:Constant.scale(35),marginTop:10,backgroundColor:Constant.colorBackgroundDefault}}>
                            <View style={{width:'100%',height:Constant.scale(35),flexDirection:'row'}}>
                                <View style={{borderColor:Constant.colorDivider,borderTopWidth:Constant.sizeDividerNormal,borderRightWidth:Constant.sizeDividerNormal,flex:3,justifyContent:'center',alignItems:'center'}}>
                                    <Text style={{fontWeight:'bold'}}>瓶箱描述</Text>
                                </View>
                                <View style={{borderColor:Constant.colorDivider,borderTopWidth:Constant.sizeDividerNormal,borderRightWidth:Constant.sizeDividerNormal,flex:1.5,justifyContent:'center',alignItems:'center'}}>
                                    <Text style={{fontWeight:'bold'}}>期间</Text>
                                </View>
                                <View style={{borderColor:Constant.colorDivider,borderTopWidth:Constant.sizeDividerNormal,borderRightWidth:Constant.sizeDividerNormal,flex:1.5,justifyContent:'center',alignItems:'center'}}>
                                    <Text style={{fontWeight:'bold'}}>数量</Text>
                                </View>

                            </View>
                            <View style={{width:'100%',height:Constant.scale(90)}}>
                                <ScrollView
                                    style={{
                                        flex:1
                                    }}>
                                    {
                                        this.props.data ?
                                            this.props.data.map((item, index) => {
                                                return(
                                                    <View key={index} style={{width:'100%',height:Constant.scale(30),flexDirection:'row'}}>
                                                        <View style={{borderColor:Constant.colorDivider,borderTopWidth:Constant.sizeDividerNormal,borderRightWidth:Constant.sizeDividerNormal,flex:3,justifyContent:'center',alignItems:'center'}}>
                                                            <Text numberOfLines={1} style={{color:Constant.colorTxtContent}}>{item.itemDesc}</Text>
                                                        </View>
                                                        <View style={{borderColor:Constant.colorDivider,borderTopWidth:Constant.sizeDividerNormal,borderRightWidth:Constant.sizeDividerNormal,flex:1.5,justifyContent:'center',alignItems:'center'}}>
                                                            <Text style={{color:Constant.colorTxtContent}}>{item.period}</Text>
                                                        </View>
                                                        <View style={{borderColor:Constant.colorDivider,borderTopWidth:Constant.sizeDividerNormal,borderRightWidth:Constant.sizeDividerNormal,flex:1.5,justifyContent:'center',alignItems:'center'}}>
                                                            <Text style={{color:Constant.colorTxtContent}}>{item.amount}</Text>
                                                        </View>

                                                    </View>
                                                )
                                            }) : null
                                    }
                                </ScrollView>
                            </View>




                        </View>



                        <View style={{flex: 1,justifyContent:'center',alignItems:'center'}}>
                            <Button onPress={() => this.defaultBtnClick()}
                                    btnStyle={1}
                            >
                                确定
                            </Button>
                        </View>


                    </View>
                </View>
            </Modal>
        );
    }




    defaultBtnClick = () => {
        if (this.props.defaultClick) {
            this.props.defaultClick();
        }
    }


};
const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    modalViewStyle: {
        marginTop: 0,
        marginLeft: 0,
        width: width,
        height: height,
        backgroundColor: 'rgba(0,0,0,0.4)',
        alignItems: 'center',
    },
    dialogViewStyle: {
        marginTop: height * 0.35,
        width: '80%',
        height: Constant.scale(300),
        backgroundColor: 'rgba(255,255,255,1)',
        borderRadius: Platform.OS === 'ios' ? Constant.scale(5) : Constant.scale(5),
        alignItems: 'center',
        justifyContent: 'center'

    },

    textInputStyle: {
        marginLeft: Constant.scale(15),
        marginRight: Constant.scale(15),
        width: Constant.scale(270),
        height: Constant.scale(36),
        marginTop: Constant.scale(15),
        borderWidth: Constant.sizeDividerNormal,
        borderColor: 'rgb(145,145,145)',
        borderRadius: Constant.scale(4),
        padding: Constant.scale(8),
        fontSize: 14
    }
});