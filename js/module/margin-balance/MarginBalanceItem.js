/**
 *
 * Created by xiaowz on 2018/11/7.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'
const {width, height} = Dimensions.get('window');
const {Text, CommonListView,CommonFlatList} = Widget;
const {Header,PrimaryHeader,Button,Image} = AppWidget;
import {toJS} from 'mobx';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
//模块声名并导出
export default class MarginBalanceItem extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {
    };
    //默认属性
    static defaultProps = {
    };
    //构造函数
    constructor(props) {
        super(props);
         //状态机变量声明
         this.state={

         };
    }

    //渲染
    render() {
        let item= this.props.data;
        return (
            <View style={{width:'100%',minHeight:Constant.scale(60),backgroundColor:'white',marginBottom:Constant.scale(10)}}>
                <View style={{borderBottomWidth:Constant.sizeDividerNormal,borderBottomColor:Constant.colorDivider,width:'100%',height:Constant.scale(40),flexDirection:'row',justifyContent:'space-between',alignItems: 'center',paddingRight:Constant.sizeMarginDefault,paddingLeft:Constant.sizeMarginDefault}}>
                    <Text style={{width:'60%'}}>{item.itemDesc}</Text>
                    <Text>{'期间   '+item.period}</Text>
                </View>
                <View style={{width:'100%',minHeight:Constant.scale(65),flexDirection:'row',alignItems:'center',justifyContent:'space-around'}}>
                    <Text>{'数量   '+item.num}</Text>
                    <Text>{'单价   '+item.price}</Text>
                    <Text>{'金额   '+item.amount}</Text>

                </View>

            </View>
        );
    }
};
const styles = StyleSheet.create({
   container: {
        flex: 1,
        backgroundColor:Constant.colorBackgroundDefault
    }
});
