/**
 *
 * Created by xiaowz on 2018/11/7.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'
const {width, height} = Dimensions.get('window');
const {Text, CommonListView,CommonFlatList} = Widget;
const {Head<PERSON>,PrimaryHeader,Button,Image} = AppWidget;
import {toJS} from 'mobx';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
import MarginBalanceItem from './MarginBalanceItem'


import { inject } from 'mobx-react/native';
import { observer } from 'mobx-react/native';
//模块声名并导出

/**
 * 按金余额
 */
@inject(stores => ({
    marginBalanceStore: stores.marginBalance,
}))
@observer
export default class MarginBalance extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {
    };
    //默认属性
    static defaultProps = {
    };
    //构造函数
    constructor(props) {
        super(props);
         //状态机变量声明
         this.state={

         };
        let { params } = this.getNavState();
        this.orgId = params.orgId;
    }

    componentWillMount() {
        this.props.marginBalanceStore.getMarginBalanceList(false,this.orgId)
    }

    //渲染
    render() {
        let {listParams} = this.props.marginBalanceStore;
        return (
            <Header style={styles.container} title={'按金余额'} navigation= {this.props.navigation}>

                <CommonFlatList
                    style={{marginTop:Constant.scale(10)}}
                    data={listParams.dataArray}
                    listState={listParams.listState}
                    enableLoadMore={listParams.enableLoadMore}
                    keyExtractor={(item, index) => {
                        return 'margin-balance' + index;
                    }}
                    renderItem={({item,index})=>{
                        return (<MarginBalanceItem data={item} />)
                    }}
                    enableRefresh={true}
                    onLoadMore={() => {
                        this.props.marginBalanceStore.getMarginBalanceList(true,this.orgId)
                    }}
                    onRefresh={() => {
                        this.props.marginBalanceStore.getMarginBalanceList(false,this.orgId)
                    }}
                >
                    <View style={[{flex: 1, justifyContent: 'center', alignItems: 'center',}]}>

                        <Text style={{fontSize: Constant.fontSizeBig, color: Constant.colorTxtContent, marginTop: Constant.scale(25)}}>
                            暂无数据
                        </Text>
                    </View>
                </CommonFlatList>

                <View style={GlobalStyle.styleDividerDefault}/>
            </Header>
        );
    }
};
const styles = StyleSheet.create({
   container: {
        flex: 1,
        backgroundColor:Constant.colorBackgroundDefault
    }
});
