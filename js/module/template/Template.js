/**
 * Created by lao.jian<PERSON> on 2017/5/26.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions
} from 'react-native';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget/index';
import PropTypes from 'prop-types';
const {width, height} = Dimensions.get('window');

//模块声名并导出
export default class  extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state={//状态机变量声明
        };
    }
    componentWillMount(){}
    componentDidMount(){}
    componentWillUnmount(){}
    componentWillReceiveProps(nextProps){

    }
    shouldComponentUpdate(nextProps,nextState){
        return true;
    }
    //渲染
    render() {
        return (
            <View style={styles.container}>

            </View>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    }
});
