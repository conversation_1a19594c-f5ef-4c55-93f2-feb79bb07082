/**
 *
 * Created by x<PERSON>owz on 2018/2/6.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'
const {width, height} = Dimensions.get('window');
const {Text, CommonListView,CommonFlatList} = Widget;
const {Header,PrimaryHeader,Button,Image,DefaultTabBar} = AppWidget;
import {toJS} from 'mobx';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
import RecycleBottleItem from './RecycleBottleItem'
import {inject, observer} from 'mobx-react/native';
import {DeviceEventEmitter} from "react-native";
//模块声名并导出

@inject(stores => ({
    recycleBottleStore: stores.recycleBottle,
}))
@observer
export default class RecycleBottleList extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {
    };
    //默认属性
    static defaultProps = {
    };
    //构造函数
    constructor(props) {
        super(props);
         //状态机变量声明
         this.state={

         };
    }

    componentWillMount() {
        this.props.recycleBottleStore.changeTabClick(this.props.recycleBottleStore.page, true)
        this.didBlurSubscription = this.props.navigation.addListener(
            'didBlur',
            payload => {
                console.debug('didBlur');
            }
        );

        this.willDidFocusSubscription = this.props.navigation.addListener(
            'didFocus',
            payload => {
                console.debug('didFocus');
            }
        );


    }
    componentWillUnmount() {

        this.didBlurSubscription.remove();
        this.willDidFocusSubscription.remove();
    }

    //渲染
    render() {
        return (
            <Header showBackAction={true}
                    rightAction={()=>this.applyRecycleBottleClick()}
                    rightTitle={'回瓶申请'}
                    title={'回瓶申请列表'}
            >
                <View style={styles.container}>
                    {this.renderTab()}
                </View>


            </Header>
        );
    }

    //头部tab
    renderTab = ()=>{
        return (
            <ScrollableTabView
                style={{ flex: 1 }}
                locked={true}
                initialPage={this.props.recycleBottleStore.initialPage}
                scrollWithoutAnimation={true}
                renderTabBar={() => {
                    return (
                        <DefaultTabBar
                            activeTextColor={Constant.colorTxtPrimary}
                            textStyle={{ fontSize: Constant.fontSizeNormal }}
                            inactiveTextColor='#777777'

                        />
                    )
                }}
                onChangeTab={(object) => {
                    if (this.listView) {
                        this.listView.scrollTo({ x: 0, y: 0, animated: false });
                    }
                    this.props.recycleBottleStore.changeTabClick(object.i, true)

                }}>
                {
                    this.props.recycleBottleStore.renderTabName.map((obj, i) => {
                        return (
                            this.renderListView(obj,i)
                        )
                    })
                }
            </ScrollableTabView>

        );
    }

    //回瓶申请列表
    renderListView = (tanName,tabIndex)=>{
        return (
            <View tabLabel={tanName} key={`recycleBottle_list${tabIndex}`} style={{flex:1,marginTop:Constant.scale(10)}}>
                <CommonFlatList
                    listViewRef={ref => this.listView = ref}
                    style={{flex:1}}
                    data={this.props.recycleBottleStore.listParams[tabIndex].dataArray}
                    listState={this.props.recycleBottleStore.listParams[tabIndex].listState}
                    enableLoadMore={this.props.recycleBottleStore.listParams[tabIndex].enableLoadMore}
                    onRefresh={() => {
                        this.props.recycleBottleStore.changeTabClick(tabIndex,false)

                    }}
                    onLoadMore={() => {

                            this.props.recycleBottleStore.getRecycleBottleListData(tabIndex,true).then().catch();

                    }}
                    keyExtractor={(item, index) => {
                        return tabIndex + 'recycleBottle' + index;
                    }}
                    renderItem={this.renderItem}
                    enableRefresh={true}
                   >
                    <View style={[{flex: 1, justifyContent: 'center', alignItems: 'center',}]}>

                        <Text style={{fontSize: Constant.fontSizeBig, color: Constant.colorTxtContent, marginTop: Constant.scale(25)}}>
                            暂无数据
                        </Text>
                    </View>
                </CommonFlatList>
            </View>
        );
    }



    //列表item
    renderItem = ({item,index})=>{
        return(
            <RecycleBottleItem checkApplyDetail={()=>this.checkApplyDetail(item)}
                               checkInspected={()=>this.checkInspected(item)}
                               deleteBottle={()=>this.deleteBottle(item.id)}
                               editBottle={()=>this.editBottle(item.id)}
                               cancelClick = {()=>this.cancelRecycleOrder(item.id)}
                               data = {item}
            />
        )
    }

    //申请回瓶
    applyRecycleBottleClick = ()=>{
      this.navigate('ApplyRecycleBottle')
    }

    //查看回瓶详情
    checkApplyDetail = (item)=>{
        // this.props.recycleBottleStore.getBottleDetail({applyId:item.id},()=>{this.navigate('RecycleBottleDetail')}).then().catch(e=>{});
        this.navigate('RecycleBottleDetail',{applyId:item.id})
    }
    //查看回瓶验收单
    checkInspected = (item)=>{
        this.navigate('RecycleBottleInspected',{applyId:item.id})

        // this.props.recycleBottleStore.getBottleDetail({applyId:item.id},()=>{this.navigate('RecycleBottleInspected')}).then().catch(e=>{});
    }

    //删除回瓶单
    deleteBottle = (itemId)=>{
       Alert.alert('温馨提示','确定要删除该回瓶申请单？',[{text:'取消'},{text:'确定',onPress:()=>{
               Api.deletRecycleBottle({id:itemId}).then(
                   (result)=>{
                       if (result && result.resultCode == 0){
                           InteractionManager.runAfterInteractions(()=>{
                               this.props.recycleBottleStore.changeTabClick(this.props.recycleBottleStore.page, true)
                           })
                       }
                   }
               ).catch(e=>{

               })
           }}])
    }

    //编辑回瓶单
    editBottle  = (itemId)=>{
        this.props.recycleBottleStore.getBottleDetail({applyId:itemId}).then((data)=>{
            // Log('=============数据=====',toJS(data))
            this.navigate('ApplyRecycleBottle',{recycleData:toJS(data)})
        }).catch(e=>{})


    }

    //作废回瓶单
    cancelRecycleOrder = (itemId)=>{
        Alert.alert('温馨提示','确定要作废该回瓶申请单？',[{text:'取消'},{text:'确定',onPress:()=>{
                Api.cancelRecycleBottle({id:itemId}).then(
                    (result)=>{
                        if (result && result.resultCode == 0){
                            InteractionManager.runAfterInteractions(()=>{
                                this.props.recycleBottleStore.changeTabClick(this.props.recycleBottleStore.page, true)
                            })
                        }
                    }
                ).catch(e=>{

                })
            }}])
    }

};
const styles = StyleSheet.create({
   container: {
        flex: 1,
        backgroundColor:Constant.colorBackgroundDefault
    }
});
