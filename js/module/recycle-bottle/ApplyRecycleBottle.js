/**
 *
 * Created by x<PERSON>owz on 2018/2/6.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager,
} from 'react-native';
import PropType from 'prop-types';
import { ReactNavComponent, Widget } from 'rn-yunxi';
import AppWidget from '../../app-widget';

const { width, height } = Dimensions.get('window');
const { Text, CommonListView, InputFormCell, LabelCell } = Widget;
const { Header, PrimaryHeader, Button, Image, SelectPop } = AppWidget;
import { toJS } from 'mobx';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { inject, observer } from 'mobx-react/native';
import Toast from 'react-native-root-toast';
//模块声名并导出
@inject(stores => ({
    recycleBottleStore:stores.recycleBottle,
}))
@observer
export default class ApplyRecycleBottle extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title:'',
    };
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        //状态机变量声明
        this.state = {};
    }

    componentWillMount() {
        this.props.recycleBottleStore.getOrgInfo().then().catch();
        this.props.recycleBottleStore.getAdressListData().then().catch();
        this.props.recycleBottleStore.getBottleTypeData().then().catch();
        this.props.recycleBottleStore.getSpecsOfBottlePackingData({ leval:0 }).then().catch();

        // this.props.recycleBottleStore.getSpecsOfBottlePackingData({ leval:1 }).then().catch();
        
        this.props.recycleBottleStore.getTakeBottleTypeData();
        this.props.recycleBottleStore.getBoxTypeData().then().catch();
        this.props.recycleBottleStore.getPalletTypeData().then().catch();
        let { params } = this.getNavState();
        if (params && params.deliveryOrderNo) {
            Log(111, params.deliveryOrderNo);
            this.props.recycleBottleStore.setDeliveryOrderNo(params.deliveryOrderNo, () => {
                this.props.recycleBottleStore.getBillOfLadingInfo().then().catch(e => {
                });
            });

        }

        if (params && params.recycleData && params.recycleData.recycleOrderDto && params.recycleData.recycleOrderDto.deliveryOrderNo) {
            let deliveryOrderNo = params.recycleData.recycleOrderDto.deliveryOrderNo;
            this.props.recycleBottleStore.setDeliveryOrderNo(deliveryOrderNo, () => {
                this.props.recycleBottleStore.getBillOfLadingInfo(() => {
                    this.props.recycleBottleStore.setDefaultRecycleDetailData(params.recycleData);
                    // this.setState({});
                }).then().catch(e => {
                });

            });
        }
    }

    //渲染
    render() {
        let { deliveryOrderNo } = this.props.recycleBottleStore;
        let dispersePackSpec = this.props.recycleBottleStore.selectSpecsBottlePackName;
        return (
            <Header
                showBackAction={true}
                rightTitle={'清空'}
                rightAction={() => this.clearAllData()}
                title={'回瓶申请'}
            >
                <KeyboardAwareScrollView>
                    <ScrollView contentContainerStyle={styles.container}>
                        <View style={{ marginTop:Constant.scale(10), backgroundColor:'white' }}>
                            <InputFormCell
                                underLine={1}
                                title={'提货单单号'}
                                placeholder={'请输入提货单单号'}
                                value={this.props.recycleBottleStore.deliveryOrderNo}
                                onChangeText={(text) => {
                                    this.props.recycleBottleStore.setDeliveryOrderNo(text);
                                }}
                                onBlur={() => this.props.recycleBottleStore.getBillOfLadingInfo()}

                            />
                            <LabelCell
                                underLine={1}
                                title={'提货组织'}
                                extra={this.props.recycleBottleStore.selectOrgName}
                                showRightIcon={false}
                                // onClick={deliveryOrderNo && deliveryOrderNo.length > 0 ? () => this.selectItem(0) : () => this.showAlert('请填写正确的提货单单号')}
                            />
                            <LabelCell
                                underLine={1}
                                titleTextStyle={{ width:Constant.scale(80) }}
                                title={'起始地址'}
                                extra={<Text
                                    numberOfLines={2}
                                    style={{ width:'80%', textAlign:'right', color:Constant.colorTxtContent }}
                                >{this.props.recycleBottleStore.selectAdressName}</Text>}
                                onClick={deliveryOrderNo && deliveryOrderNo.length > 0 ? () => this.selectItem(1) : () => this.showAlert('请填写正确的提货单单号')}
                            />
                            {
                                this.props.recycleBottleStore.getdealerName == '暂无' ? null :
                                    <LabelCell
                                        underLine={1}
                                        titleTextStyle={{ width:Constant.scale(80) }}
                                        title={'经销商'}
                                        extra={<Text
                                            numberOfLines={2}
                                            style={{
                                                width:'80%',
                                                textAlign:'right',
                                                color:Constant.colorTxtContent,
                                            }}
                                        >{this.props.recycleBottleStore.getdealerName}</Text>}
                                        showRightIcon={false}
                                    />
                            }

                            {
                                this.props.recycleBottleStore.getDistributorName == '暂无' ? null :
                                    <LabelCell
                                        underLine={2}
                                        titleTextStyle={{ width:Constant.scale(80) }}
                                        title={'分销商'}
                                        extra={<Text
                                            numberOfLines={2}
                                            style={{
                                                width:'80%',
                                                textAlign:'right',
                                                color:Constant.colorTxtContent,
                                            }}
                                        >{this.props.recycleBottleStore.getDistributorName}</Text>}
                                        showRightIcon={false}
                                    />
                            }


                            {/*<TouchableOpacity onPress={deliveryOrderNo&&deliveryOrderNo.length>0?()=>this.selectItem(1):()=>this.showAlert('请填写正确的提货单单号')}>*/}
                            {/*<View style={{minHeight:Constant.scale(44),width:width,padding:Constant.sizeMarginDefault,flexDirection:'row',alignItems:'center'}}>*/}
                            {/*<Text>起始地址</Text>*/}
                            {/*<Text style={{textAlign:'right',width:'80%',lineHeight:Constant.scale(15),color:Constant.colorTxtContent,marginLeft:Constant.scale(10)}}>{this.props.recycleBottleStore.selectAdressName}</Text>*/}
                            {/*</View>*/}
                            {/*</TouchableOpacity>*/}
                        </View>

                        <View style={{ marginTop:Constant.scale(10) }}>
                            <LabelCell
                                underLine={1}
                                title={'散瓶包装规格'}
                                extra={dispersePackSpec}
                                onClick={() => !this.props.recycleBottleStore.itemType && this.selectItem(3)}
                            />
                            {(this.props.recycleBottleStore.subSpecsOfBottlePackingData 
                            && this.props.recycleBottleStore.subSpecsOfBottlePackingData.length>0) 
                            || dispersePackSpec == '麻包瓶'?
                                <LabelCell
                                    underLine={1}
                                    title={'子规格'}
                                    extra={this.props.recycleBottleStore.selectSubSpecsBottlePackName}
                                    onClick={() => !this.props.recycleBottleStore.itemType && this.selectItem(4)}
                                /> : null}
                            {
                                (dispersePackSpec != '麻包瓶') ||(dispersePackSpec && this.props.recycleBottleStore.selectSubSpecsBottlePackName != '托板') || (this.props.recycleBottleStore.specsOfBottlePackingId && this.props.recycleBottleStore.selectSubSpecsBottlePackName != '托板') || this.props.recycleBottleStore.itemNum ?
                                    <InputFormCell
                                        underLine={1}
                                        title={'数量'}
                                        placeholder={'请输入数量'}
                                        onChangeText={(text) => this.props.recycleBottleStore.setItemNum(text)}
                                        value={this.props.recycleBottleStore.itemNum}
                                        rightIcon={
                                            <Text style={{ color:Constant.colorTxtContent }}>{this.props.recycleBottleStore.unit}</Text>}

                                    /> : null
                            }

                            {
                                this.props.recycleBottleStore.unit == '箱' || this.props.recycleBottleStore.unit == '瓶' ?
                                    <InputFormCell
                                        underLine={1}
                                        title={'回瓶兑酒有标率:'}
                                        placeholder={'请输入'}
                                        onChangeText={(text) => this.props.recycleBottleStore.setWineRate(text)}
                                        value={this.props.recycleBottleStore.wineRate}
                                        rightIcon={<Text style={{ color:Constant.colorTxtContent }}>{'%'}</Text>}

                                    />
                                    : null

                            }

                            <LabelCell
                                underLine={2}
                                title={'瓶承载形式'}
                                extra={this.props.recycleBottleStore.selectTakeBottleTypeName}
                                onClick={() => !this.props.recycleBottleStore.itemType && this.selectItem(5)}
                            />

                        </View>

                        {
                            this.props.recycleBottleStore.selectSubSpecsBottlePackName == '托板' ? null :
                                <View style={{ marginTop:Constant.scale(10) }}>
                                    <LabelCell
                                        underLine={1}
                                        title={'瓶品种'}
                                        extra={this.props.recycleBottleStore.selectBottleTypeName}
                                        onClick={() => this.selectItem(2)}
                                    />
                                    {
                                        this.props.recycleBottleStore.bottleCategoryCode ?
                                            <LabelCell
                                                underLine={1}
                                                title={'瓶数量'}
                                                extra={this.props.recycleBottleStore.getBottleNum()}
                                                showRightIcon={false}

                                            /> : null
                                    }

                                </View>
                        }


                        {
                            (dispersePackSpec != '请选择') || (this.props.recycleBottleStore.selectPalletTypeName != '请选择') ?
                                null :
                                <View style={{ marginTop:Constant.scale(10) }}>
                                    <LabelCell
                                        underLine={1}
                                        title={'箱品种'}
                                        extra={this.props.recycleBottleStore.selectBoxTypeName}
                                        onClick={() => this.selectItem(6)}
                                    />
                                    {
                                        this.props.recycleBottleStore.boxCategoryCode ?
                                            <InputFormCell
                                                underLine={2}
                                                title={'箱数量'}
                                                placeholder={'请输入箱的数量'}
                                                onChangeText={(text) => this.props.recycleBottleStore.setBoxNum(text)}
                                                value={this.props.recycleBottleStore.boxNum}

                                            />
                                            : null
                                    }
                                </View>
                        }


                        {
                            this.props.recycleBottleStore.selectBoxTypeName != '请选择' || !dispersePackSpec
                                ? null :
                                <View style={{ marginTop:Constant.scale(10), marginBottom:Constant.scale(10) }}>
                                    <LabelCell
                                        underLine={1}
                                        title={'托板'}
                                        extra={this.props.recycleBottleStore.selectPalletTypeName}
                                        onClick={() => !this.props.recycleBottleStore.itemType && this.selectItem(7)}
                                    />
                                    {
                                        this.props.recycleBottleStore.palletCategoryCode ?
                                            <View>
                                                <InputFormCell
                                                    underLine={1}
                                                    title={'托板总数量'}
                                                    placeholder={'请输入托板总数量'}
                                                    onChangeText={(text) => this.props.recycleBottleStore.setPalletNum(text)}
                                                    value={this.props.recycleBottleStore.palletNum}

                                                />
                                                <InputFormCell
                                                    underLine={1}
                                                    title={'其中空托板数量'}
                                                    placeholder={'请输入其中空托板数量'}
                                                    onChangeText={(text) => this.props.recycleBottleStore.setNullPalletNum(text)}
                                                    value={this.props.recycleBottleStore.nullPalletNum}

                                                />

                                                <InputFormCell
                                                    underLine={2}
                                                    title={'托板承载规格'}
                                                    maxLength={3}
                                                    placeholder={'请输入托板承载规格'}
                                                    onChangeText={(text) => !this.props.recycleBottleStore.itemType && this.props.recycleBottleStore.setPlateSpecification(text)}
                                                    value={this.props.recycleBottleStore.plateSpecification}
                                                    rightIcon={
                                                        <Text style={{ color:Constant.colorTxtContent }}>{'箱、包/板'}</Text>}

                                                />
                                            </View>
                                            : null
                                    }
                                </View>
                        }


                    </ScrollView>
                </KeyboardAwareScrollView>
                <TouchableOpacity
                    onPress={() => this.submitApplyClick()}
                    style={styles.applyBtnStyle}
                >
                    <Text style={{ color:'white', fontSize:16, fontWeight:'bold' }}>提交申请</Text>
                </TouchableOpacity>

            </Header>
        );
    }

    //点击选择
    selectItem = (index) => {
        // 0 提货组织  1 提货地址 2瓶品种 3散瓶包装规格 4子规格 5瓶承载形式 6箱品种 7 托板
        let title = '';
        let data = [];
        let selectId;
        let selectKey = '';
        let labelName = '';
        if (index == 0) {
            title = '提货组织';
            data = this.props.recycleBottleStore.orgListOfLadingBill;
            selectId = this.props.recycleBottleStore.selectOrgId;
            selectKey = 'id';
            labelName = 'orgName';

        }
        else if (index == 1) {
            title = '起始地址';
            data = this.props.recycleBottleStore.adressListData;
            selectId = this.props.recycleBottleStore.selectAdressId;
            selectKey = 'id';
            labelName = 'address';
        }
        else if (index == 2) {
            title = '瓶品种';
            data = this.props.recycleBottleStore.bottleTypeData;
            selectId = this.props.recycleBottleStore.bottleCategoryCode;
            selectKey = 'code';
            labelName = 'name';
        }
        else if (index == 3) {
            title = '散瓶包装规格';
            data = this.props.recycleBottleStore.specsOfBottlePackingData;
            selectId = this.props.recycleBottleStore.specsOfBottlePackingId;
            selectKey = 'id';
            labelName = 'name';
        }
        else if (index == 4) {
            title = '子规格';
            data = this.props.recycleBottleStore.subSpecsOfBottlePackingData;
            selectId = this.props.recycleBottleStore.subSpecsOfBottlePackingId;
            selectKey = 'id';
            labelName = 'name';
        }
        else if (index == 5) {
            title = '瓶承载形式';
            data = this.props.recycleBottleStore.takeBottleTypeData;
            selectId = this.props.recycleBottleStore.takeBottleType;
            selectKey = 'name';
            labelName = 'name';
        }
        else if (index == 6) {
            title = '箱品种';
            data = this.props.recycleBottleStore.boxTypeData;
            selectId = this.props.recycleBottleStore.boxCategoryCode;
            selectKey = 'code';
            labelName = 'name';
        }
        else if (index == 7) {
            title = '托板品种';
            data = this.props.recycleBottleStore.palletTypeData;
            selectId = this.props.recycleBottleStore.palletCategoryCode;
            selectKey = 'code';
            labelName = 'name';
        }
        else {
            title = '选择测试';
        }
        // Log(selectId);
        Widget.Popup.show(
            <SelectPop
                popTitle={title}
                listData={data}
                labelName={labelName}
                selectId={selectId}
                selectKey={selectKey}
                showPleaseChooseBtn={true}
                selectCallBack={(selectData) => {
                    //TODO
                    Widget.Popup.hide();
                    InteractionManager.runAfterInteractions(() => {
                        if (index == 0) {
                            Log('sel == ', toJS(selectData));
                            this.props.recycleBottleStore.setSelectId(0, selectData);


                        }
                        else if (index == 1) {
                            Log('sel == ', toJS(selectData));
                            this.props.recycleBottleStore.setSelectId(1, selectData);

                        }
                        else if (index == 2) {
                            Log('sel == ', toJS(selectData));
                            this.props.recycleBottleStore.setSelectId(2, selectData);

                        }
                        else if (index == 3) { //散瓶包装规格
                            Log('sel == ', toJS(selectData));
                            this.props.recycleBottleStore.setSelectId(3, selectData);

                        }
                        else if (index == 4) { //散瓶包装子规格
                            Log('sel == ', toJS(selectData));
                            this.props.recycleBottleStore.setSelectId(4, selectData);
                        }
                        else if (index == 5) {
                            Log('sel == ', toJS(selectData));
                            this.props.recycleBottleStore.setSelectId(5, selectData);

                        }
                        else if (index == 6) {
                            Log('sel == ', toJS(selectData));
                            this.props.recycleBottleStore.setSelectId(6, selectData);
                        }
                        else if (index == 7) {
                            Log('sel == ', toJS(selectData));
                            this.props.recycleBottleStore.setSelectId(7, selectData);
                        }
                        // else {
                        //     this.skuPanel.setSelectChancelId(selectData.salesChannelCode, selectData.subSalesChannelCode);
                        //  }
                    });
                }}

            />,
            {
                animationType:'slide-up', backgroundColor:'#00000000',
                onMaskClose:() => {
                    Widget.Popup.hide();
                },
            });
    };

    //提示框
    showAlert = (title) => {
        Toast.show(title, {
            duration:Toast.durations.SHORT,
            position:Toast.positions.CENTER,
            shadow:true,
            animation:true,
            hideOnPress:true,
            delay:0,
            backgroundColor:'#000000',
        });
    };
    //提交申请
    submitApplyClick = () => {
        let { params } = this.getNavState();
        let isUpdate = false;
        if (params && params.recycleData) {
            isUpdate = true;
        }
        this.props.recycleBottleStore.submitApplyClick(isUpdate, () => {
            this.goBack();
        });
    };

    //清空所有数据
    clearAllData = () => {
        Alert.alert('提示', '确定要清空所有信息?', [
            { text:'取消' },
            {
                text:'确认', onPress:() => {
                    this.props.recycleBottleStore.clearData();
                },
            },
        ]);
    };


};
const styles = StyleSheet.create({
    container:{
        flex:1,
        backgroundColor:Constant.colorBackgroundDefault,
        // marginBottom:Constant.scale(70)
    },
    applyBtnStyle:{
        width:Constant.scale(351),
        height:Constant.scale(40),
        backgroundColor:Constant.colorPrimary,
        borderRadius:Constant.scale(4),
        marginLeft:Constant.scale(12),
        marginBottom:Constant.scale(15),
        marginTop:Constant.scale(15),
        justifyContent:'center',
        alignItems:'center',
    },
});
