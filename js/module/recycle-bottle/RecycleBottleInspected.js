/**
 *
 * Created by xiaowz on 2018/2/6.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager,
    Image
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'
const {width, height} = Dimensions.get('window');
const {Text, CommonListView} = Widget;
const {Header,PrimaryHeader,Button} = AppWidget;
import {toJS} from 'mobx';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
import LabelCell from "rn-yunxi/lib/widget/list-item/LabelCell";
import {inject, observer} from 'mobx-react/native'

@inject(stores => ({
    recycleBottleStore: stores.recycleBottle,
}))


//模块声名并导出
@observer
export default class RecycleBottleInspected extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {
    };
    //默认属性
    static defaultProps = {
    };
    //构造函数
    constructor(props) {
        super(props);
         //状态机变量声明
         this.state={

         };
    }

    componentWillMount() {
        let {params} = this.getNavState();
        if (params && params.applyId){
            let param = {}
            param.applyId = params.applyId;
            this.props.recycleBottleStore.getBottleDetail(param).then().catch(e=>{})
        }
    }


    //渲染
    render() {
        if (!this.props.recycleBottleStore.bottleDetailData){
            return(
                <Header showBackAction={true}
                        title={'回瓶验收单'}>
                <View style={{flex: 1, marginTop:Constant.scale(250), alignItems: 'center'}}>
                    <Text style={{fontSize: Constant.fontSizeBig, color: Constant.colorTxtContent}}>
                        暂无数据
                    </Text>
                </View>
                </Header>
            );
        }

        let data = this.props.recycleBottleStore.bottleDetailData;
        let boxCheckDto = data.boxCheckDto;  //验收单详情
        let recycleOrderDto  = data.recycleOrderDto; // 申请单详情
        let status = '';
        if(boxCheckDto.status == 1){  //状态,1为草稿、2、确认验收、3审核通过、4审核驳回、5作废
            status = '草稿'
        }else if(boxCheckDto.status == 2){
            status = '确认验收'
        }else if (boxCheckDto.status == 3){
            status = '审核通过'
        }else if (boxCheckDto.status == 4){
            status = '审核驳回'
        }else if (boxCheckDto.status == 5){
            status = '作废'
        }

        let checkType = '';
        if(boxCheckDto.checkType == 1){
            checkType = '抽检'
        }else if(boxCheckDto.checkType == 2){
            checkType = '全检'
        }

        let failDtos = '';
        data.boxCheckDto.failDtos && data.boxCheckDto.failDtos.map((item,index) => {
            failDtos += item.failCause + ':' + item.failNum + '支,'
        });

        let picUrlArray =  boxCheckDto.picUrl && (boxCheckDto.picUrl != '') ? boxCheckDto.picUrl.split(",") :[];

        return (
            <Header showBackAction={true}
                    title={'回瓶验收单'}>
                <KeyboardAwareScrollView>
                    <ScrollView contentContainerStyle={styles.container}>
                        <View style={{width:width,height:Constant.scale(30),paddingLeft:Constant.scale(10),justifyContent:'center'}}>
                            <Text style={{fontSize:12,color:Constant.colorTxtAlert}}>基本信息</Text>
                        </View>
                        <LabelView underLine={1} title={'运输公司'} extra={recycleOrderDto.logisticsCompanyName || '暂无'} />
                        <LabelView underLine={1} title={'接单司机'} extra={recycleOrderDto.driverName ||'暂无'} />
                        <LabelView underLine={1} title={'车号'} extra={recycleOrderDto.carNum ||'暂无'} />
                        <LabelView underLine={1} title={'接单时间'} extra={recycleOrderDto.orderTime || '暂无'} />
                        <LabelView underLine={1} title={'验收状态'} extra={status} />
                        <LabelView underLine={1} title={'验收人'} extra={recycleOrderDto.checkPerson || '暂无'} />
                        <LabelView underLine={1} title={'验收时间'} extra={recycleOrderDto.checkTime || '暂无'} />
                        <LabelView underLine={1} title={'审核人'} extra={boxCheckDto.verifyPerson || '暂无'} />
                        <LabelView underLine={1} title={'审核时间'} extra={boxCheckDto.verifyTime || '暂无'} />
                        <LabelView underLine={1} title={'作废人'} extra={boxCheckDto.cancellationPerson || '暂无'} />
                        <LabelView underLine={1} title={'作废时间'} extra={boxCheckDto.cancellationTime || '暂无'} />
                        <LabelView underLine={2} title={'作废关联单号'} extra={recycleOrderDto.oldCode || '暂无'} />



                        <View style={{width:width,height:Constant.scale(30),paddingLeft:Constant.scale(10),justifyContent:'center'}}>
                            <Text style={{fontSize:12,color:Constant.colorTxtAlert}}>验收单详情</Text>
                        </View>
                        <LabelView underLine={1} title={'瓶品种'} extra={boxCheckDto.bottleCategory || '暂无'} />
                        <LabelView underLine={1} title={'检验方式'} extra={checkType} />
                        <LabelView underLine={1} title={'散瓶包装规格'} extra={boxCheckDto.dispersePackSpec || '暂无'} />
                        <LabelView underLine={1} title={'子规格'} extra={boxCheckDto.subSpec || '暂无'} />
                        {
                            boxCheckDto.wineRate ||( boxCheckDto.wineRate+ '' == '0') ? <LabelView underLine={1} title={'回瓶兑酒有标率'} extra={boxCheckDto.wineRate +'%' || '暂无'} />  : null
                        }
                        <LabelView underLine={1} title={'瓶承载形式'} extra={boxCheckDto.loadWay || '暂无'}  />
                        <LabelView underLine={1} title={'抽检数量'} extra={boxCheckDto.checkNum?boxCheckDto.checkNum +'': '暂无'} />
                        <LabelView underLine={1} title={'不合格数量'} extra={boxCheckDto.checkFailNum ?boxCheckDto.checkFailNum+'': '暂无'} />
                        <LabelView underLine={1} title={'卸车总瓶数'} extra={boxCheckDto.unloadCountBottleNum?boxCheckDto.unloadCountBottleNum+'': '暂无'} />
                        <LabelView underLine={1} title={'总不合格数量'} extra={boxCheckDto.countFailNum?boxCheckDto.countFailNum+'': '暂无'} />
                        <LabelView underLine={1} title={'合格率'} extra={boxCheckDto.qualifiedOdds ?boxCheckDto.qualifiedOdds+' %' :'暂无'} />
                        <LabelView underLine={1} title={'总合格数量'} extra={boxCheckDto.countQualifiedNum ? boxCheckDto.countQualifiedNum +'' : '暂无'} />
                        <LabelView underLine={1} title={'可洗率'} extra={boxCheckDto.washRate ? boxCheckDto.washRate +'%' : '暂无'} />

                        <LabelView underLine={1} title={'不合格原因'} extra={failDtos} />
                        <View style={{backgroundColor:'white',}}>
                            {
                                picUrlArray && picUrlArray.length>0?
                                    <View style={{flexDirection:'row',alignItems:'center',width:width,height:Constant.scale(84), paddingLeft:Constant.scale(10)}}>
                                        <Text style={{color:Constant.colorTxtAlert}}>验收图片</Text>
                                        <View style={{flex:1,paddingRight:Constant.scale(10),flexDirection:'row',alignItems:'center',justifyContent:'flex-end'}}>
                                            {picUrlArray.map((item) => {
                                                <Image style={{width:Constant.scale(64),height:Constant.scale(64),marginLeft:Constant.scale(10)}}
                                                       source={{uri:item}} />
                                            })}
                                        </View>
                                    </View>
                                    :null
                            }
                        <View style={[GlobalStyle.styleDividerDefault, { marginLeft: Constant.sizeMarginDefault }]} />
                        </View>
                        <LabelView underLine={1} title={'箱品种'} extra={boxCheckDto.boxCategory || '暂无'} />
                        <LabelView underLine={1} title={'整套塑箱'} extra={(boxCheckDto.fullBox ||0) + '箱'} />
                        <LabelView underLine={1} title={'合格散塑箱'} extra={(boxCheckDto.qualifiedBox || 0) + '个'} />
                        <LabelView underLine={1} title={'不合格塑箱'} extra={(boxCheckDto.failBox || 0) + '个'} />
                        <LabelView underLine={1} title={'托板'} extra={boxCheckDto.pallet || '暂无'} />
                        <LabelView underLine={1} title={'托板总数量'} extra={(boxCheckDto.palletNum || 0) + '个'} />
                        <LabelView underLine={1} title={'其中空托板数量'} extra={(boxCheckDto.notNullPalletNum || 0) + '个'} />
                        <LabelView underLine={1} title={'破损托板'} extra={(boxCheckDto.brokenPlate || 0) + '个'} />
                        <LabelView underLine={1} title={'托板承载规格'} extra={boxCheckDto.plateSpecification ? boxCheckDto.plateSpecification  + '箱、包/板':'暂无'} />
                        <LabelView underLine={2} title={'验收时间'} extra={recycleOrderDto.checkTime || '暂无'} />
                        <View style={{width:width,height:Constant.scale(50)}}/>

                    </ScrollView>
                </KeyboardAwareScrollView>

            </Header>
        );
    }
};
const LabelView  = (props) =>(
    <LabelCell extraTextStyle={{color:Constant.colorTxtTitle}} titleTextStyle={{color:Constant.colorTxtContent}} showRightIcon={false} underLine={props.underLine} title={props.title} extra={props.extra}/>
)






const styles = StyleSheet.create({
   container: {
        flex: 1,
        backgroundColor:Constant.colorBackgroundDefault
    }
});
