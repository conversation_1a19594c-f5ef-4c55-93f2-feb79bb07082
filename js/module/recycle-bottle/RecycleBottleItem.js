/**
 *
 * Created by xiaowz on 2018/2/6.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'
const {width, height} = Dimensions.get('window');
const {Text, CommonListView} = Widget;
const {Header,PrimaryHeader,Button,Image} = AppWidget;
import {toJS} from 'mobx';
import QRCode from 'react-native-qrcode-svg';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
const CHECK_QRCODE = require('../img/recycle-bottle/check_qrcode.png')
//模块声名并导出
export default class RecycleBottleItem extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {
    };
    //默认属性
    static defaultProps = {
    };
    //构造函数
    constructor(props) {
        super(props);
         //状态机变量声明
         this.state={

         };
    }

    //渲染
    render() {
        let item = this.props.data
        let status = '暂无'
        if (item.status == 1){
            status = '待验收'
        }else if (item.status == 2){
            status = '已验收'
        }else if (item.status == 3){
            status = '已作废'
        }

        return (
            <View style={styles.container}>
                <View style={styles.topViewStyle}>
                    <Text>{'申请单号：'+(item.recycleOrderNo||'暂无')}</Text>
                    <TouchableOpacity onPress={()=>this.showDeliverOrderNo(item.recycleOrderNo,'申请单号')} style={{flex:1}}>
                    <Image source={CHECK_QRCODE} style={{marginLeft:Constant.scale(20),width:Constant.scale(13),height:Constant.scale(13)}}/>
                    </TouchableOpacity>
                    <Text style={{color:'#FF7E00'}}>{status}</Text>
                </View>

                {
                    item.bottleCategory ?
                        <View style={styles.midViewStyle}>
                            <Text numberOfLines={2} style={{width:'60%',fontSize:16}}>{item.bottleCategory+item.bottleCategoryCode}</Text>
                            <Text style={{color:Constant.colorTxtAlert}}>{'x'+(item.bottleNum || 0)}</Text>
                        </View>
                        :null
                }
                {
                    item.boxCategory ?
                        <View style={styles.midViewStyle}>
                            <Text numberOfLines={2} style={{width:'60%',fontSize:16}}>{item.boxCategory+item.boxCategoryCode}</Text>
                            <Text style={{color:Constant.colorTxtAlert}}>{'x'+(item.boxNum || 0)}</Text>
                        </View>
                        :null
                }
                {
                    item.pallet ?
                        <View style={styles.midViewStyle}>
                            <Text numberOfLines={2} style={{width:'60%',fontSize:16}}>{item.pallet+item.palletCode}</Text>
                            <Text style={{color:Constant.colorTxtAlert}}>{'x'+(item.palletNum || 0)}</Text>
                        </View>
                        :null
                }

                <View style={styles.bottomViewStyle}>
                    {item.status == 1 && item.transportStatus == 1 ?
                        <Button onPress={()=>this.deleteBottle()}>删除</Button>
                        :null}
                    {item.status == 1 && item.transportStatus == 1 ?
                        <Button onPress={()=>this.editBottle()}>编辑</Button>
                        :null}

                    {item.status == 1 && item.transportStatus == 2 ?
                     <Button onPress={()=>this.cancelClick()}>作废</Button>
                                                                   :null}
                    <Button onPress={()=>this.checkApplyDetail()}>查看申请单</Button>
                    {item.status == 2 || (item.status == 3 && item.transportStatus != 1 && (item.isDeleteItself == 0)) ? <Button btnStyle={1} onPress={()=>this.checkInspected()}>查看验收单</Button>:null}
                </View>


            </View>
        );
    }


    //查看提货单二维码
    showDeliverOrderNo = (qrcode,title)=>{
        Widget.Popup.show(
            <TouchableOpacity
                activeOpacity={1.0}
                onPress={() => {
                    Widget.Popup.hide();
                }}
                style={{justifyContent: 'center', alignItems: 'center', height: height, width}}>
                <View style={[styles.bgViewStyle]}>

                    {!!qrcode ? <View>
                        <Text style={[styles.titleStyle, {fontSize: 16, marginTop: Constant.scale(15)}]}>{title}</Text>
                        <View style={{justifyContent: 'center', alignItems: 'center', marginTop: Constant.scale(15)}}>
                            <QRCode 
                                value={!!qrcode ? qrcode : ''}
                                size = {Constant.scale(120)}
                            />
                        </View>
                        <Text
                            style={{color: Constant.colorTxtAlert, fontSize: Constant.fontSizeNormal, margin: Constant.scale(15), textAlign: 'center'}}
                        >{qrcode}</Text>
                    </View> : null}

                </View>
            </TouchableOpacity>, {
                animationType: 'none', backgroundColor: '#00000000', onMaskClose: () => {
                    this.setState({showParamsPop: true})
                }
            })
    }

    //查看申请单
    checkApplyDetail = ()=>{
        this.props.checkApplyDetail && this.props.checkApplyDetail()
    }

    //查看验收单
    checkInspected = ()=>{
        this.props.checkInspected && this.props.checkInspected()
    }

    //删除回瓶单
    deleteBottle = ()=>{
        this.props.deleteBottle && this.props.deleteBottle()
    }

    //编辑回瓶单
    editBottle = ()=>{
        this.props.editBottle && this.props.editBottle()
    }

    //作废回瓶单
    cancelClick = ()=>{
        this.props.cancelClick && this.props.cancelClick()
    }

};
const styles = StyleSheet.create({
   container: {
        flex: 1,
        backgroundColor:'#FFFFFF',
        marginBottom:Constant.scale(10)
    },
    topViewStyle:{
        width:width,
        height:Constant.scale(40),
        paddingLeft:Constant.scale(15),
        paddingRight:Constant.scale(15),
        alignItems:'center',
        flexDirection:'row'

    },
    midViewStyle:{
        width:width,
        height:Constant.scale(62),
        borderBottomWidth:Constant.sizeDividerNormal,
        borderColor:Constant.colorDividerDefault,
        paddingLeft:Constant.scale(15),
        paddingRight:Constant.scale(15),
        flexDirection:'row',
        alignItems:'center',
        justifyContent:'space-between'
    },
    bottomViewStyle:{
        width:width,
        height:Constant.scale(44),
        flexDirection:'row',
        alignItems:'center',
        justifyContent:'flex-end'
    },
    bgViewStyle: {
        // width: '60%',
        // borderRadius: 5,
        // backgroundColor: '#ffffff',
        // flexDirection: 'row',
        alignItems: 'center',
        // justifyContent: 'space-between',
        // overflow: 'scroll',
        width: Constant.scale(250),
        height: Constant.scale(250),
        backgroundColor: '#FFFFFF',
    },
    titleStyle: {
        color: '#333333',
        margin: Constant.sizeMarginDefault,
        textAlign: 'center',
    },
});
