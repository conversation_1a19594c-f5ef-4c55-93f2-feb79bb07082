/**
 *
 * Created by xiaowz on 2018/2/6.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'

const {width, height} = Dimensions.get('window');
const {Text, CommonListView, InputFormCell, LabelCell} = Widget;
const {Header, PrimaryHeader, Button, Image} = AppWidget;
import {toJS} from 'mobx';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
//模块声名并导出

import {inject, observer} from 'mobx-react/native'

@inject(stores => ({
    recycleBottleStore: stores.recycleBottle,
}))
@observer
export default class RecycleBottleDetail extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        //状态机变量声明
        this.state = {
            //showRightIcon
        };
    }

    componentWillMount() {
        let {params} = this.getNavState();
        if (params && params.applyId){
            let param = {}
            param.applyId = params.applyId;
            this.props.recycleBottleStore.getBottleDetail(param).then().catch(e=>{})
        }
    }

    //渲染
    render() {
        if (!this.props.recycleBottleStore.bottleDetailData){
            return(
                <Header showBackAction={true}
                        title={'回瓶申请'}>
                <View style={{flex: 1, marginTop:Constant.scale(250), alignItems: 'center'}}>
                    <Text style={{fontSize: Constant.fontSizeBig, color: Constant.colorTxtContent}}>
                        暂无数据
                    </Text>
                </View>
                </Header>
            );
        }
        let data = this.props.recycleBottleStore.bottleDetailData;
        let boxCheckDto = data.boxCheckDto;  //验收单详情
        let recycleOrderDto  = data.recycleOrderDto; // 申请单详情
        let status = '';
        if(recycleOrderDto.status == 1){  // 状态：1未验收、2已验收
            status = '未验收'
        }else if(recycleOrderDto.status == 2){
            status = '已验收'
        }

        return (
            <Header showBackAction={true}
                    title={'回瓶申请'}>
                <KeyboardAwareScrollView>
                    <ScrollView contentContainerStyle={styles.container}>
                        <View style={{marginTop: Constant.scale(10)}}>
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                       underLine={1} title={'申请日期'} extra={recycleOrderDto.applyDate || '暂无'}/>
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                       underLine={1} title={'提货单单号'} extra={ recycleOrderDto.deliveryOrderNo||'暂无'}/>
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                       underLine={1} title={'提货组织'} extra={recycleOrderDto.supplierName||'暂无'}/>
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                       underLine={1} title={'起始地址'} extra={ recycleOrderDto.address||'暂无'}/>
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                       underLine={1} title={'经销商'} extra={ recycleOrderDto.dealerName||'暂无'}/>
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                       underLine={1} title={'分销商'} extra={ recycleOrderDto.distributorName||'暂无'}/>
                            {
                                recycleOrderDto.deleteName && recycleOrderDto.deleteName.length>1? <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                                                       underLine={2} title={'作废人'} extra={ recycleOrderDto.deleteName}/>:null
                            }
                        </View>

                        <View style={{marginTop: Constant.scale(10)}}>
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                       underLine={1} title={'瓶品种'} extra={ recycleOrderDto.bottleCategory||'暂无'}/>
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                       underLine={1} title={'瓶数量'} extra={ recycleOrderDto.bottleNum?recycleOrderDto.bottleNum+'':'暂无'}/>
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                       underLine={1} title={'散瓶包装规格'} extra={ recycleOrderDto.dispersePackSpec ||'暂无'}/>
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                       underLine={1} title={'子规格'} extra={ recycleOrderDto.subSpec||'暂无'}/>
                            {
                                recycleOrderDto.wineRate ||( recycleOrderDto.wineRate+ '' == '0') ?
                                    <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                               titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                               underLine={1} title={'回瓶兑酒有标率'} extra={ recycleOrderDto.wineRate+'%'||'暂无'}/>
                                    :null
                            }
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                       underLine={2} title={'瓶承载形式'} extra={ recycleOrderDto.loadWay||'暂无'}/>
                        </View>

                        <View style={{marginTop: Constant.scale(10)}}>
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                       underLine={1} title={'箱品种'} extra={ recycleOrderDto.boxCategory||'暂无'}/>
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                       underLine={2} title={'箱数量'} extra={ recycleOrderDto.boxNum?recycleOrderDto.boxNum+'':'暂无'}/>
                        </View>

                        <View style={{marginTop: Constant.scale(10), marginBottom: Constant.scale(10)}}>
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                       underLine={1} title={'托板'} extra={ recycleOrderDto.pallet||'暂无'}/>
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                       underLine={1} title={'托板总数量'} extra={ recycleOrderDto.palletNum?recycleOrderDto.palletNum+'个':'暂无'}/>
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                       underLine={1} title={'其中空托板数量'} extra={ recycleOrderDto.nullPalletNum?recycleOrderDto.nullPalletNum+'个':'暂无'}/>
                            <LabelCell extraTextStyle={{color: Constant.colorTxtTitle}}
                                       titleTextStyle={{color: Constant.colorTxtContent}} showRightIcon={false}
                                       underLine={2} title={'托板承载规格'} extra={ recycleOrderDto.plateSpecification?recycleOrderDto.plateSpecification+'  箱、包/板':'暂无'}/>
                        </View>

                    </ScrollView>
                </KeyboardAwareScrollView>

            </Header>
        );
    }
};
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Constant.colorBackgroundDefault
    }
});
