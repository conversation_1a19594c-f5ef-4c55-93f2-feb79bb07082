import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    Alert,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import {inject, observer} from 'mobx-react/native';
import AppWidget from '../../app-widget';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'

const {width, height} = Dimensions.get('window');
const {Header,Image,PriceText,TextInputView,Button} = AppWidget;
const { Text,CommonFlatList} = Widget;
import {toJS} from 'mobx';
import AfterSaleListItem from './AfterSaleListItem'
const IC_SHOP_ICON = require('../img/order/ic_shop_icon.png');
const IC_SPECIAL = require('../img/home-page/ic_special.png');
const IC_NEXT = require('../img/next.png');

import AfterSaleListStore from '../../store/AfterSaleList';

/**
 * 申请售后
 */

@inject(stores => ({
    user: stores.user,
}))
@observer
export default class AfterSaleList extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};


    //构造函数
    constructor(props) {
        super(props);

        this.afterSaleList = new AfterSaleListStore();
        this.state = {//状态机变量声明
        };


    }


    componentWillMount() {

    }


    componentDidMount() {

        this.afterSaleList.getAfterSaleList(false);
    }

    componentWillUnmount() {
    }


    renderRow = ({item, index})=> {
        return (
            <AfterSaleListItem data={item}
                               editOrder={() => {
                                   this.navigate('AfterSaleDetail', {type: 1,afterId: item.afterId})
                               }}
                               cancelOrder={() => {
                                   Alert.alert(null, '确定要取消吗？',
                                       [{
                                           text: '确定', onPress: () => {
                                               this.afterSaleList.cancel(item.afterId);
                                           }
                                       }])
                               }}
                               gotoDetail={() => {
                                   this.navigate('AfterSaleDetail',{afterId: item.afterId})
                               }}
            />
        )
    }

    //渲染
    render() {


        return (
            <Header title={'售后列表'}
                    navigation={this.props.navigation}
            >
                <CommonFlatList
                    style={{flex: 1}}
                    data={this.afterSaleList.listParams.data}
                    listState={this.afterSaleList.listParams.listState}
                    enableLoadMore={this.afterSaleList.listParams.enableLoadMore}
                    keyExtractor={(item, index) => {
                        return 'afterslaelist' + index;
                    }}
                    renderItem={this.renderRow}
                    onLoadMore={() => {
                        this.afterSaleList.getAfterSaleList(true).then().catch(err => {
                        })
                    }}
                    enableRefresh={true}
                    onRefresh={() => {
                        this.afterSaleList.getAfterSaleList(false).then().catch(err => {
                        })
                    }}
                >
                    <View style={[{flex: 1, justifyContent: 'center', alignItems: 'center',}]}>

                        <Text style={{
                            fontSize: Constant.fontSizeBig,
                            color: Constant.colorTxtContent,
                            marginTop: Constant.scale(25)
                        }}>
                            暂无数据
                        </Text>
                    </View>
                </CommonFlatList>
            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff'
    }
});
