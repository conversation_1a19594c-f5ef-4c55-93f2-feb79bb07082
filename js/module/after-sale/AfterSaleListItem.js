/**
 * Created by whw on 2018/1/19.
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    ScrollView, InteractionManager, Alert,
} from 'react-native';
import PropType from 'prop-types';
import { ReactNavComponent, Widget } from 'rn-yunxi';
import AppWidget from '../../app-widget';

const { width, height } = Dimensions.get('window');
const { Image, Button, PriceText, SelectPop, CheckBox } = AppWidget;
const { Text } = Widget;
const IC_SHOP_ICON = require('../img/order/ic_shop_icon.png');
const IC_SPECIAL = require('../img/home-page/ic_special.png');
const IC_NEXT = require('../img/next.png');
import { withNavigation } from 'react-navigation';
import OrderGoodsItem from '../order/OrderGoodsItem';
import { inject, observer } from 'mobx-react/native';
import { toJS } from 'mobx';
import EmText from "../../app-widget/em-text";
import QRCode from 'react-native-qrcode-svg'
import ToastUtil from "../../util/ToastUtil";

@withNavigation

/**
 * 订单支付
 */
@inject(stores => ({
    pay: stores.pay,
    orderUtil: stores.orderUtil,
    user: stores.user
}))

/**
 * 订单列表Item
 */
@observer
export default class OrderListItem extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {
        showCheckBox: false
    };

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
            showCheckBox: this.props.showCheckBox,
        };
    }

    componentWillMount() {
    }

    componentDidMount() {
    }

    componentWillUnmount() {
    }

    renderStatus = (status)=> {
        let statusText = '';
        switch (status)
        {
            case 1:
                statusText = '待审核';
                break;
            case 4:
                statusText = '已拒绝';
                break;
            case 2:
                statusText = '已退单';
                break;
            case 3:
                statusText = '已取消';
                break;
        }
        return statusText;
    }

    editOrder = ()=> {
        this.props.editOrder && this.props.editOrder();
    }

    cancelOrder = ()=> {
        this.props.cancelOrder && this.props.cancelOrder();
    }

    gotoDetail = ()=> {
        this.props.gotoDetail && this.props.gotoDetail();
    }
    //渲染
    render() {
        let data = this.props.data;
        return (
            <TouchableOpacity onPress={this.gotoDetail}>
                <View style={{ width: width, backgroundColor: Constant.colorDefault, marginBottom:Constant.sizeMarginDefault}}>
                    <View style={{width,flexDirection: 'row',alignItems: 'center',justifyContent: 'center',height: Constant.scale(84)}}>
                        <View style={{flex: 1,marginLeft: Constant.sizeMarginDefault}}>
                            <Text style={{marginTop: Constant.scale(8),fontSize: Constant.fontSizeNormal,color: Constant.colorTxtTitle}}>售后单号：{data.afterOrderNo}</Text>
                            <Text style={{marginTop: Constant.scale(8),fontSize: Constant.fontSizeNormal,color: Constant.colorTxtTitle}}>提货单号：{data.deliveryOrderNo}</Text>
                        </View>
                        <Text
                            style={{
                                color: '#FF7E00',
                                fontSize: Constant.fontSizeNormal,
                                marginRight: Constant.sizeMarginDefault
                            }}
                        >
                            {this.renderStatus(data.status)}
                        </Text>
                    </View>

                    <View style={GlobalStyle.styleDividerDefault}/>
                    <View style={{margin: Constant.sizeMarginDefault,flex: 1}}>
                        <Text style={{fontSize: Constant.fontSizeNormal,color: Constant.colorTxtTitle}}>{data.itemName}</Text>
                        <Text style={{marginTop: Constant.scale(8),fontSize: Constant.fontSizeNormal,color: Constant.colorTxtTitle}}>申请时间：{data.afterOrderTime}</Text>
                    </View>

                    {data.status == 1 && data.afterSalesType == 1 ?
                        <View style={{width}}>
                            <View style={GlobalStyle.styleDividerDefault}/>
                            <View style={{
                                width: width,
                                alignItems: 'center',
                                justifyContent: 'flex-end',
                                flexDirection: 'row',
                                marginBottom: Constant.sizeMarginDefault,
                                marginTop: Constant.sizeMarginDefault
                            }}>
                                <Button style={{ borderRadius: 5, borderColor: Constant.colorTxtPrimary }}
                                        txtStyle={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtPrimary }}
                                        onPress={this.editOrder}
                                >
                                    修改
                                </Button>

                                <Button style={{ borderRadius: 5, borderColor: Constant.colorTxtAlert }}
                                        txtStyle={{ fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert }}
                                        onPress={this.cancelOrder}
                                >
                                    取消
                                </Button>
                            </View>
                        </View>
                        : null}
                </View>
            </TouchableOpacity>
        );
    }




};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    checkBox: {
        paddingRight: Constant.sizeMarginDefault,
        width: Constant.scale(15) + Constant.sizeMarginDefault,
        height: Constant.scale(15),
    },
    bgViewStyle: {
        alignItems: 'center',
        justifyContent: 'flex-start',
        overflow: 'scroll',
        backgroundColor: 'white',
        width: Constant.scale(250),
        height: Constant.scale(286.5)
    },

});