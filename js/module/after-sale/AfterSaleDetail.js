import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    Alert,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import {inject, observer} from 'mobx-react/native';
import AppWidget from '../../app-widget';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'

const {width, height} = Dimensions.get('window');
const {Header,Image,PriceText,TextInputView,Button} = AppWidget;
const { Text} = Widget;
import {toJS} from 'mobx';
import OrderGoodsItem from '../order/OrderGoodsItem'
const IC_SHOP_ICON = require('../img/order/ic_shop_icon.png');
const IC_SPECIAL = require('../img/home-page/ic_special.png');
const IC_NEXT = require('../img/next.png');

import ApplyAfterSaleStore from '../../store/ApplyAfterSale';

/**
 * 编辑或售后详情
 */

@inject(stores => ({
    user: stores.user,
}))
@observer
export default class AfterSaleDetail extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};


    //构造函数
    constructor(props) {
        super(props);

        let {params} = this.getNavState();
        this.applyAfterSale = new ApplyAfterSaleStore();
        this.state = {//状态机变量声明
            type: params && params.type,
            afterId: params && params.afterId
        };


    }


    componentWillMount() {

    }


    componentDidMount() {
        this.applyAfterSale.getDetail(this.state.afterId);
    }

    componentWillUnmount() {
    }

    submit = ()=> {
        this.applyAfterSale.update(()=> {
            this.goBack();
        });
    }

    renderStatus = (status)=> {
        let statusText = '';
        switch (status)
        {
            case 1:
                statusText = '待审核';
                break;
            case 4:
                statusText = '已拒绝';
                break;
            case 2:
                statusText = '已退单';
                break;
            case 3:
                statusText = '已取消';
                break;
        }
        return statusText;
    }

    renderItem = (title,content)=> {
        return (
            <View style={{width,flexDirection: 'row',marginTop: Constant.scale(5)}}>
                <Text style={{width: Constant.scale(70),fontSize: Constant.fontSizeNormal,color: Constant.colorTxtTitle,marginLeft: Constant.sizeMarginDefault}}>
                    {title}:
                </Text>
                <Text style={{flex: 1,fontSize: Constant.fontSizeNormal,color: Constant.colorTxtContent,marginLeft: Constant.scale(8),marginRight: Constant.sizeMarginDefault}}>
                    {content}
                </Text>
            </View>
        )
    }
    //渲染
    render() {
        const {type} = this.state;
        let data = this.applyAfterSale.data;
        if (!data)
        {
            return (
                <Header title={type == 1 ? '修改售后单' : '售后详情'}
                        navigation={this.props.navigation}
                >
                    <View/>
                </Header>
            )
        }
        return (
            <Header title={type == 1 ? '修改售后单' : '售后详情'}
                    navigation={this.props.navigation}
            >
                <KeyboardAwareScrollView style={styles.container}>
                    <View style={{width}}>
                        <View
                            style={{
                                width: width,
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                // backgroundColor:'pink'
                            }}>
                            <View style={{ flex: 1, justifyContent: 'center',marginTop: Constant.sizeMarginDefault,marginBottom: Constant.sizeMarginDefault }}>

                                <Text
                                    style={{
                                        flex: 1,
                                        color: Constant.colorTxtTitle,
                                        fontSize: Constant.fontSizeNormal,
                                        marginLeft: Constant.sizeMarginDefault,

                                    }}>
                                    售后单号：{data.afterOrderNo}
                                </Text>

                                <Text
                                    style={{
                                        flex: 1,
                                        color: Constant.colorTxtTitle,
                                        fontSize: Constant.fontSizeNormal,
                                        marginLeft: Constant.sizeMarginDefault,
                                        marginTop: Constant.scale(8)
                                    }}>
                                    提货单号：{data.deliveryOrderNo}
                                </Text>

                                {
                                    data.erpDeliveryOrderNo ? <Text
                                        style={{
                                            flex: 1,
                                            color: Constant.colorTxtTitle,
                                            fontSize: Constant.fontSizeNormal,
                                            marginLeft: Constant.sizeMarginDefault,
                                            marginTop: Constant.scale(8)
                                        }}>
                                        ERP提货单号：{data.erpDeliveryOrderNo}
                                    </Text> : null
                                }
                            </View>
                            <Text
                                style={{
                                    color: '#FF7E00',
                                    fontSize: Constant.fontSizeNormal,
                                    marginRight: Constant.sizeMarginDefault
                                }}
                            >
                                {this.renderStatus(data.status)}
                            </Text>
                        </View>
                    </View>
                    <View
                        style={{ width: width, height: Constant.sizeDividerNormal, backgroundColor: Constant.colorDivider }} />

                    <View style={{width,marginTop: Constant.sizeMarginDefault,marginBottom: Constant.sizeMarginDefault}}>
                        {this.renderItem('商品名称',data.itemName)}
                        {this.renderItem('子批次',data.itemBatch)}
                        {this.renderItem('数量',data.itemNum)}
                    </View>
                    <View
                        style={{ width: width, height: Constant.sizeDividerNormal, backgroundColor: Constant.colorDivider }} />
                    <View style={{width,marginTop: Constant.sizeMarginDefault}}>
                        <View style={{width,marginTop: Constant.scale(8),flexDirection: 'row',alignItems: 'center'}}>
                            <Text style={{fontSize: Constant.fontSizeNormal,color: Constant.colorTxtTitle,marginLeft: Constant.sizeMarginDefault}}>
                                <Text style={{fontSize: Constant.fontSizeNormal,color: Constant.colorTxtPrimary}}>*</Text>
                                售后类型:
                            </Text>
                            <Text style={{fontSize: Constant.fontSizeNormal,color: Constant.colorTxtContent,marginLeft: Constant.scale(8)}}>
                                退单
                            </Text>
                        </View>

                        <View style={{width,marginTop: Constant.scale(8),flexDirection: 'row',alignItems: 'center'}}>
                            <Text style={{fontSize: Constant.fontSizeNormal,color: Constant.colorTxtTitle,marginLeft: Constant.sizeMarginDefault}}>
                                <Text style={{fontSize: Constant.fontSizeNormal,color: Constant.colorTxtPrimary}}>*</Text>
                                退回方式:
                            </Text>
                            <Text style={{fontSize: Constant.fontSizeNormal,color: Constant.colorTxtContent,marginLeft: Constant.scale(8)}}>
                                退回余额
                            </Text>
                        </View>

                        <View style={{width}}>
                            <Text style={{fontSize: Constant.fontSizeNormal,color: Constant.colorTxtTitle,marginLeft: Constant.sizeMarginDefault,marginTop: Constant.scale(8),marginBottom: Constant.sizeMarginDefault}}>
                                {'  '+'问题描述:'}
                            </Text>
                            {type == 1 ? <TextInputView placeholder={'请输入问题描述'} remarkText={''} maxLength={320} value={this.applyAfterSale.memo} onChangeText={(text)=> {
                                this.applyAfterSale.setMemo(text);
                            }} /> : <Text style={{fontSize: Constant.fontSizeNormal,color: Constant.colorTxtContent,marginLeft: Constant.sizeMarginDefault,marginRight: Constant.sizeMarginDefault}}>
                                    {data.memo}
                            </Text>
                            }
                        </View>
                    </View>

                    {type == 1 ? <View style={{marginTop: Constant.scale(30),justifyContent: 'center',alignItems: 'center'}}>
                        <Button style={{ borderRadius: 5, borderColor: Constant.colorTxtPrimary }}
                                txtStyle={{
                                    fontSize: Constant.fontSizeSmall,
                                    color: Constant.colorTxtPrimary
                                }}
                                onPress={this.submit}
                        >
                            提交
                        </Button>
                    </View> : null}
                </KeyboardAwareScrollView>


            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff'
    }
});
