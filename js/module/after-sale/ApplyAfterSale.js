import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    Alert,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import {inject, observer} from 'mobx-react/native';
import AppWidget from '../../app-widget';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'

const {width, height} = Dimensions.get('window');
const {Header,Image,PriceText,TextInputView,Button} = AppWidget;
const { Text} = Widget;
import {toJS} from 'mobx';
import OrderGoodsItem from '../order/OrderGoodsItem'
const IC_SHOP_ICON = require('../img/order/ic_shop_icon.png');
const IC_SPECIAL = require('../img/home-page/ic_special.png');
const IC_NEXT = require('../img/next.png');

import ApplyAfterSaleStore from '../../store/ApplyAfterSale';

/**
 * 申请售后
 */

@inject(stores => ({
    user: stores.user,
}))
@observer
export default class ApplyAfterSale extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};


    //构造函数
    constructor(props) {
        super(props);

        let {params} = this.getNavState();
        this.applyAfterSale = new ApplyAfterSaleStore();
        this.state = {//状态机变量声明
            data: params && params.data
        };


    }


    componentWillMount() {

    }


    componentDidMount() {

    }

    componentWillUnmount() {
    }

    renderItem = (item,index) => {
        return (
            <View key={`applyAfterSaleGoodsItem${index}`}>
                {item.deliveryOrderId && <TouchableOpacity onPress={() => {
                    this.goToDeliveryOrderDetail(item.deliveryOrderId, item.status,);
                }}>
                    <View
                        style={{
                            width: width,
                            height: Constant.scale(40),
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            // backgroundColor:'pink'
                        }}>
                        <View style={{ flex: 1, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                            <Text
                                style={{
                                    flex: 1,
                                    color: Constant.colorTxtTitle,
                                    fontSize: Constant.fontSizeNormal,
                                    marginLeft: Constant.sizeMarginDefault
                                }}>
                                提货单号：{item.deliveryOrderNo}
                            </Text>
                        </View>
                    </View>
                    {
                        item.externalDeliveryOrderNo ?

                            <Text style={{ marginLeft: Constant.sizeMarginDefault,marginBottom:Constant.scale(5)}}>ERP提货单号：{item.externalDeliveryOrderNo}</Text>

                            :null
                    }

                </TouchableOpacity>}

                {
                    item.items && item.items.map((obj, index1) => {
                        return (
                            <OrderGoodsItem
                                data={obj}
                                underLine={2}
                                key={index1}
                                style={{ backgroundColor: Constant.colorBackgroundDefault }}
                            />


                        );
                    })
                }

                <View
                    style={{ width: width, height: Constant.sizeDividerNormal, backgroundColor: Constant.colorDivider }} />
            </View>
        );
    }

    submit = ()=> {
        let data = this.state.data;
        let deliveryOrderItemId = data.deliverys[0].items && data.deliverys[0].items.length && data.deliverys[0].items[0].deliveryOrderItemId;
        let params = {afterSalesType: "1",deliveryOrderItemId,exchangeOrgId: data.orgId,memo: this.applyAfterSale.memo};
        this.applyAfterSale.submit(params,()=> {
            this.goBack();
        });
    }

    //渲染
    render() {
        let {data} = this.state;
        let total = 0;
        let itemNum = 0;
        data.deliverys && data.deliverys.map((item)=>{
            total = total + item.subTotalAmount;
            item.items && item.items.map((obj) => {
                itemNum = itemNum + obj.itemNum;
            })
        })
        return (
            <Header title={'申请售后'}
                    navigation={this.props.navigation}
            >
                <KeyboardAwareScrollView style={styles.container}>
                    <View style={{ width: width, backgroundColor: Constant.colorDefault, marginBottom:Constant.sizeMarginDefault}}>
                        <View
                            style={{
                                width: width,
                                flexDirection: 'row',
                                minHeight: Constant.scale(63),
                                alignItems: 'center',
                                justifyContent: 'space-between'
                            }}>
                            <View style={{ flex: 1, flexDirection: 'row', alignItems: 'center', justifyContent: 'center',paddingBottom:Constant.scale(10),paddingTop:Constant.scale(10) }}>
                                <Image
                                    style={{
                                        width: Constant.scale(17),
                                        height: Constant.scale(15),
                                        marginLeft: Constant.sizeMarginDefault
                                    }}
                                    source={IC_SHOP_ICON} />
                                <View style={{ flex: 1 }}>
                                    <Text
                                        style={{
                                            color: Constant.colorTxtTitle,
                                            fontSize: Constant.fontSizeNormal,
                                            marginLeft: Constant.sizeMarginDefault
                                        }}>
                                        {data.supplierOrgName}
                                    </Text>
                                    <Text
                                        style={{
                                            color: Constant.colorTxtTitle,
                                            fontSize: Constant.fontSizeNormal,
                                            marginLeft: Constant.sizeMarginDefault,
                                            marginTop: Constant.scale(5)
                                        }}
                                    >
                                        订单号：{data.orderNo}
                                    </Text>
                                    { data.zpOrderTypeName?
                                        <Text
                                            style={{
                                                color: Constant.colorTxtTitle,
                                                fontSize: Constant.fontSizeNormal,
                                                marginLeft: Constant.sizeMarginDefault,
                                                marginTop: Constant.scale(5)
                                            }}
                                        >
                                            {data.zpOrderTypeName}
                                        </Text>:null
                                    }


                                </View>
                            </View>
                        </View>
                        <View
                            style={{ width: width, height: Constant.sizeDividerNormal, backgroundColor: Constant.colorDivider }} />
                        {
                            data.deliverys && data.deliverys.length ? data.deliverys.map((item, index) => {
                                return this.renderItem(item,index);
                            }) : null
                        }

                    </View>
                    <View style={{ width: width }}>
                        <View
                            style={{
                                flexDirection: 'row',
                                alignItems: 'flex-end',
                                justifyContent: 'flex-end',
                                margin: Constant.sizeMarginDefault
                            }}>
                            <Text style={{
                                fontSize: Constant.fontSizeSmall,
                                color: Constant.colorTxtAlert
                            }}>共{itemNum}件商品，实付款</Text>
                            <PriceText price={total}
                                       size={1} />
                        </View>
                    </View>
                    <View
                        style={{ width: width, height: Constant.sizeDividerNormal, backgroundColor: Constant.colorDivider }} />


                    <View style={{width,marginTop: Constant.sizeMarginDefault}}>
                        <View style={{width,height: Constant.scale(30),flexDirection: 'row',alignItems: 'center'}}>
                            <Text style={{fontSize: Constant.fontSizeNormal,color: Constant.colorTxtTitle,marginLeft: Constant.sizeMarginDefault}}>
                                <Text style={{fontSize: Constant.fontSizeNormal,color: Constant.colorTxtPrimary}}>*</Text>
                                售后类型:
                            </Text>
                            <Text style={{fontSize: Constant.fontSizeNormal,color: Constant.colorTxtContent,marginLeft: Constant.scale(8)}}>
                                退单
                            </Text>
                        </View>

                        <View style={{width,height: Constant.scale(30),flexDirection: 'row',alignItems: 'center'}}>
                            <Text style={{fontSize: Constant.fontSizeNormal,color: Constant.colorTxtTitle,marginLeft: Constant.sizeMarginDefault}}>
                                <Text style={{fontSize: Constant.fontSizeNormal,color: Constant.colorTxtPrimary}}>*</Text>
                                退回方式:
                            </Text>
                            <Text style={{fontSize: Constant.fontSizeNormal,color: Constant.colorTxtContent,marginLeft: Constant.scale(8)}}>
                                退回余额
                            </Text>
                        </View>

                        <View style={{width}}>
                            <Text style={{fontSize: Constant.fontSizeNormal,color: Constant.colorTxtTitle,marginLeft: Constant.sizeMarginDefault,marginBottom: Constant.sizeMarginDefault}}>
                                {'  '+'问题描述:'}
                            </Text>
                            <TextInputView placeholder={'请输入问题描述'} remarkText={''} maxLength={320} value={this.applyAfterSale.memo} onChangeText={(text)=> {
                                this.applyAfterSale.setMemo(text);
                            }} />
                        </View>
                    </View>

                    <View style={{marginTop: Constant.scale(30),justifyContent: 'center',alignItems: 'center'}}>
                        <Button style={{ borderRadius: 5, borderColor: Constant.colorTxtPrimary }}
                                txtStyle={{
                                    fontSize: Constant.fontSizeSmall,
                                    color: Constant.colorTxtPrimary
                                }}
                                onPress={this.submit}
                        >
                            提交
                        </Button>
                    </View>
                </KeyboardAwareScrollView>


            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff'
    }
});
