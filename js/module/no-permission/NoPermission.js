/**
 * Created by lao<PERSON>jian<PERSON> on 2017/5/26.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions
} from 'react-native';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget/index';
import PropTypes from 'prop-types';

const {Header} = AppWidget;
const {Text} = Widget;
const {width, height} = Dimensions.get('window');

//模块声名并导出
export default class NoPermission extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
        };
    }

    componentWillMount() {
    }

    componentDidMount() {
    }

    componentWillUnmount() {
    }

    //渲染
    render() {
        return (
            <Header title={"权限提示"}>
                <View style={{justifyContent: 'center', alignItems: 'center',width:'100%',height:'100%'}}>
                    <Text style={{fontSize:Constant.fontSizeBig,marginBottom:Constant.sizeMarginDefault}}>您暂无权限访问</Text>
                    <Text style={{color:Constant.colorTxtContent}}>如需开通请联系管理人员</Text>
                </View>
            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    }
});
