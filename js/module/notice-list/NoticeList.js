/** 公告列表
 * @Author: 老鸦
 * @Date: 2022-03-28 14:18:30
 * @Last Modified by: <PERSON><PERSON>
 * @Last Modified time: 2022-04-11 13:57:22
 */

import React, { Component } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Alert,
  InteractionManager,
  Dimensions,
  DeviceEventEmitter,
} from "react-native";
import PropType from "prop-types";
import { ReactNavComponent, Widget, Util } from "rn-yunxi";
import _ from 'lodash';
import { inject, observer } from "mobx-react/native";
import ScrollableTabView from "react-native-scrollable-tab-view";
import { getSimpleText } from "../../util/HtmlUtils";
const { CommonFlatList, Text } = Widget;
import AppWidget from "../../app-widget";
import { toJS } from "mobx";
const { width, height } = Dimensions.get("window");
import NoticeModal from "../../app-widget/notice-modal";
// const { PrimaryHeader, DefaultTabBar } = AppWidget;
const {Header,DefaultTabBar}=AppWidget;
const { CountdownUtil } = Util;

@inject((stores) => ({
  userNotice: stores.userNotice,
  user: stores.user,
}))
@observer
export default class NoticeList extends ReactNavComponent {
  //属性声名
  static propTypes = {};
  //默认属性
  static defaultProps = {};

  //构造函数
  constructor(props) {
    super(props);
    this.state = {
      height: 0,
      visibleNotice: false,
      currentItem: {},
      disabled: true,
      startIndex: 0,
    };
  }

  componentDidMount() {
    this.props.userNotice.getUserNoticeList(false, false);
  }
  handleShowNotice = (item, index) => {
    this.setState(
      {
        visibleNotice: true,
        startIndex: index,
        currentItem: item,
      },
      () => {}
    );
  };
  renderItem = ({ item, index }) => {
    let contentStr = getSimpleText(item.content);
    contentStr = contentStr ? contentStr.substring(0, 10) : "";
    return (
      <View style={styles.noticeItem}>
        <TouchableOpacity
          style={{ height: "100%" }}
          onPress={() => {
            this.handleShowNotice(item, index);
          }}
        >
          <View
            style={{
              flexDirection: "row",
              flex: 1,
              justifyContent: "space-between",
            }}
          >
            <Text style={styles.noticeItemTitle}>{item.title}</Text>
            <Text style={styles.noticeItemTime}>
              {item.sendTime ? item.sendTime.split(" ")[0] : item.sendTime}
            </Text>
          </View>
          <View>
            <Text style={styles.noticeItemContent}>{`${contentStr}...`}</Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };
  renderList = (item) => {
    const { listParams } = this.props.userNotice;
    return (
      <View
        tabLabel={item.label}
        key={`delivery_tab${item.tabIndex}`}
        style={{ height: "100%" }}
      >
        <CommonFlatList
          onRefresh={() => {
            //   this.props.deliveryTask
            //     .obtainTaskList(tabIndex, false)
            //     .then()
            //     .catch((e) => {});
          }}
          //   ListHeaderComponent={<Text>{item.label}</Text>}
          onLoadMore={() => {
            this.props.userNotice
              .getUserNoticeList(true, item.tabIndex === 2)
              .then()
              .catch((e) => {});
          }}
          // ItemSeparatorComponent={this.renderSeparator}
          // keyExtractor={this.keyExtractor}
          renderItem={this.renderItem.bind(this)}
          data={listParams.dataArray}
          // enableLoadMore={
          //   this.props.deliveryTask.dataListParamArray[tabIndex].enableLoadMore
          // }
          enableRefresh={true}
          listState={this.props.userNotice.listParams.listState}
          extraData={this.state}
          style={{ flex: 1, marginTop: Constant.sizeMarginDefault }}
        >
          <View
            style={[
              { flex: 1, justifyContent: "center", alignItems: "center" },
            ]}
          >
            {listParams.dataArray.length === 0 ? (
              <Text
                style={{
                  fontSize: 16,
                  color: Constant.colorTxtContent,
                  marginTop: Constant.scale(25),
                }}
              >
                暂无数据
              </Text>
            ) : null}
          </View>
        </CommonFlatList>
      </View>
    );
  };

  //渲染
  render() {
    const { tabIndex, listParams } = this.props.userNotice;
    const { currentItem } = this.state;
    return (
      <Header style={styles.container} title="公告列表">
        <ScrollableTabView
          onChangeTab={(obj) => {
            this.props.userNotice.setTabIndex(obj.i);
          }}
          locked={true}
          style={{
            paddingBottom: Constant.sizeDividerNormal,
            backgroundColor: Constant.colorBackgroundDefault,
          }}
          scrollWithoutAnimation={true}
          renderTabBar={() => {
            return <DefaultTabBar activeTextColor={Constant.colorTxtPrimary} />;
          }}
        >
          {[
            {
              label: "未读公告",
              tabIndex: 1,
            },
            {
              label: "已读公告",
              tabIndex: 2,
            },
          ].map((item, index) => {
            return this.renderList(item, index);
          })}
        </ScrollableTabView>

        <NoticeModal
          startIndex={0}
          visible={this.state.visibleNotice}
          // hasRead={currentItem.isRead}
          dataList={[currentItem]}
          onConfirm={(data) => {
            this.setState({
              visibleNotice: false,
            });
            if (tabIndex === 0) {
              this.props.userNotice.postNoticeRead({
                type: 1,
                applicationType: 1,
                id: data.id,
              }, data)
            }
          }}
          onClose={() => {
            this.setState({
              visibleNotice: false,
            });
            // CountdownUtil.stop();
          }}
        ></NoticeModal>
      </Header>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  noticeItem: {
    padding: Constant.scale(6),
    backgroundColor: "#fff",
    height: Constant.scale(65),
    borderBottomColor: Constant.colorDivider,
    borderBottomWidth: Constant.sizeDividerNormal,
  },
  noticeItemTitle: {
    fontSize: Constant.fontSizeCaption,
    height: Constant.scale(18),
    color: "#333",
  },
  noticeItemContent: {
    fontSize: Constant.fontSizeNormal,
    color: "#999",
  },
  noticeItemTime: {
    color: "#999",
    fontSize: Constant.fontSizeNormal,
  },
  container: {
    flex: 1,
  },
  modalViewStyle: {
    alignItems: "center",
    justifyContent: "center",
    borderRadius: Constant.scale(4),
  },
  bgViewStyle: {
    width: Dimensions.get("window").width * 0.8,
    backgroundColor: "#fff",
    padding: Constant.scale(10),
  },
});
