/**
 * Created by z<PERSON><PERSON><PERSON>ha<PERSON><PERSON> on 17/5/18.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 * 外界使用方法:
 *  this.navigate('CommonWebView', {url:'https://www.baidu.com', title:'网页'}) // title:可以不传入
 */


import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    Alert,
    Dimensions,
    ActivityIndicator

} from 'react-native';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget';
import {inject, observer} from 'mobx-react/native';
const {width, height} = Dimensions.get('window');
const {WebView, Text} = Widget;
const {Header} = AppWidget;


//模块声名并导出
@inject(stores => ({
    user: stores.user,
}))
@observer
export default class CommonWebView extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
            animating: true, // 加载菊花
            error: false,
            title: null,
            canPost: true,
        };
        this.webView = null;
    }

    componentWillMount() {
    }

    componentDidMount() {
    }

    componentWillUnmount() {
    }

    onMessage(event) {
        Log('this is webview call back', event.nativeEvent.data);
        let data = null;
        try {
            data = JSON.parse(event.nativeEvent.data);
        } catch (err) {
            Log(err);
        }

        if (data && data.action) {
            if (data.action == 'token') {
                let orgId=this.props.user.selectTakeDeliveryOrg?this.props.user.selectTakeDeliveryOrg.orgId:null;
                this.webView.postMessage(JSON.stringify({action: 'token', data: {token: this.props.user.token, urlMosaic: Config.HOST,orgId:orgId}}))
            } else if (data.action == 'navigate') {
                this.navigate(data.data)
            } else if (data.action == 'alert') {
                Alert.alert('', data.data)
            } else if (data.action == 'orderList') {
                let {params} = this.getNavState();
                Log('orderList === ',params.orderList);
                this.webView.postMessage(JSON.stringify({action: 'orderList', data: params && params.orderList}))
            } else if(data.action=='deliveryId'){
                //let {params} = this.getNavState();
                //this.webView.postMessage(JSON.stringify({action:'deliveryId',data:params && params.deliveryId}))
            }

        }

    }

    //渲染
    render() {

        let {params} = this.getNavState();
        let source = params.url ? {uri: params.url} : {html: params.html};
        return (
            <Header title={params.title ? params.title : this.state.title} navigation={this.props.navigation}>
                {
                    this.state.error == false ?
                        <WebView
                            ref={(ref => {
                                this.webView = ref;
                            })}
                            onMessage={(event) => {
                                this.onMessage(event)
                            }}
                            scalesPageToFit={false}
                            source={source}
                            onError={() => this.onError()}
                            onNavigationStateChange={(o) => this.change(o)}
                        >
                        </WebView> :
                        <View style={styles.container}>
                            {/* <Image style={{width:Constant.scale(80), height:Constant.scale(80), resizeMode:'contain'}}
                                   source={require('../img/wineculture/nodata.png')}  /> */}
                            <Text style={{fontSize: 16, color: Constant.colorTxtContent, marginTop: Constant.scale(25)}}>网络连接失败哦~</Text>
                        </View>
                }
            </Header>
        );
    }

    change(o) {
        if (!o.loading) {
            let {params} = this.getNavState();
            if (params.html) {
                return
            }
            this.setState({
                title: o.title,
            })

            if (this.state.canPost)
            {
                this.setState({canPost: false},()=> {
                    setTimeout(()=> {
                        this.webView && this.webView.runjs('window.appWebViewReady()');
                    },500)
                });



            }

        }

    }

    onError() {
        this.setState({
            error: true
        })
    }


};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: Constant.colorBackgroundDefault,
    },

    centering: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: 8,
    },
});