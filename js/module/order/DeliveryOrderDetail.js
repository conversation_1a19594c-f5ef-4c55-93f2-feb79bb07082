import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    ScrollView,
    ImageBackground,
    Switch
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Util, Widget} from 'rn-yunxi';
import {inject, observer} from 'mobx-react/native';
import OrderListItem from './OrderListItem';
import AppWidget from '../../app-widget';
import DeliveryOrderDetailModel from '../../store/DeliveryOrderDetail';
import OrderGoodsItem from './OrderGoodsItem';
import {toJS} from 'mobx';
import H5Util from '../../util/H5Util';

const {width, height} = Dimensions.get('window');
const {Header, Image, Button} = AppWidget;
const {Text, LabelCell} = Widget;
const IC_BANNER = require('../img/order/ic_banner.png');
const IC_ADDRESS = require('../img/order/ic_adress.png');
const IC_SHOP_ICON = require('../img/order/ic_shop_icon.png');


const IC_STATUS_FINISH = require('../img/order/ic_status_finish.png');
const IC_STATUS_SEND = require('../img/order/ic_status_send.png');
const IC_STATUS_DISTRIBUTION = require('../img/order/ic_status_distribution.png');
const IC_STATUS_RECEIVE = require('../img/order/ic_status_receive.png');
const IC_STATUS_CANCEL = require('../img/order/ic_status_cancel.png');

/**
 * 提货单详情
 */
@inject(stores => ({
    orderUtil: stores.orderUtil,
    user : stores.user
}))
@observer
export default class DeliveryOrderDetail extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        let {params} = this.getNavState();
        this.state = {//状态机变量声明
            data: params && params.data
        };
        this.delivery = new DeliveryOrderDetailModel();
    }

    componentWillMount() {
    }

    componentDidMount() {
        let {params} = this.getNavState();
        this.delivery.getDeliveryOrderDetail(params.deliveryOrderId, this.props.orderUtil.type);

        if (params.deliveryOrderStatus === 3) {//提货单 待收货状态
            this.delivery.getLogisticsOrbit(params.deliveryOrderId);
        }
    }

    componentWillUnmount() {
    }


    /**
     * 确认收货
     * */
    confirmGoods = () => {
        let{params} = this.getNavState();
        this.delivery.confirmReceive({deliveryOrderNos: this.delivery.data.deliveryOrderNo},()=>{
            this.delivery.getDeliveryOrderDetail(params.deliveryOrderId, this.props.orderUtil.type);
        });
    }

    /**
     * 评价物流
     * */
    evaluate = () => {
        let newItem = {};
        let newObj = {};
        let {params} = this.getNavState();
        Object.assign(newItem, params.data ? params.data : {});
        Object.assign(newObj, this.delivery.data);
        newItem.deliverys = [newObj];
        this.navigate('CommonWebView', {orderList: [toJS(newItem)], url: H5Util.getLogisticsUrl()});

    }

    /**
     * 申请售后
     * */
    applyAfterSale = () => {
        let newItem = {};
        let newObj = {};
        let {params} = this.getNavState();
        Object.assign(newItem, params.data ? params.data : {});
        Object.assign(newObj, this.delivery.data);
        newItem.deliverys = [newObj];
        this.navigate('ApplyAfterSale', {data: newItem});

    }

    /**
     * 评价物流
     * */
    commentDelivery = (data) => {
        if (data) {
            let isEvaluated = data.estimate && data.estimate.grade != null ? 1 : 0;
            this.navigate('DeliveryComment', {
                isEvaluated: isEvaluated,
                itemData: data,
                deliveryOrderId: data.deliveryOrderId,
                orderNo: data.orderNo,
                supplierOrgName: data.supplierOrgName
            });
        }
    }

    /**
     * 物流地图轨迹
     */
    deliveryOrbit = (deliveryId, status) => {
        this.navigate('CommonWebView', {url: H5Util.getMapUrl(deliveryId, status)});
    }

    /**
     * 渲染物流位置
     * */
    location = () => {

        let logisticsListArray = this.delivery.logisticsList && this.delivery.logisticsList.length > 0 ? this.delivery.logisticsList : [];
        let startLocation = logisticsListArray.length >0 ? logisticsListArray[logisticsListArray.length - 1].address:'暂无';
        let endLocation =  logisticsListArray.length >0 ?logisticsListArray[0].address:'暂无';
        if (this.delivery.logisticsList.length == 0) {
            return <View/>;
        }
        return (
            <View style={{paddingBottom:Constant.scale(10)}}>
                <Text style={[styles.locatinTxtStyle,{marginBottom:Constant.scale(10)}]}>
                    起点:{startLocation}
                </Text>
                <Text style={[styles.locatinTxtStyle, {marginBottom: Constant.sizeMarginDefault}]}>
                    当前位置:{endLocation}
                </Text>
                {/*<Text style={[styles.locatinTxtStyle, {marginBottom: Constant.sizeMarginDefault}]}>*/}
                    {/*终点:{this.delivery.logisticsList[2].address}*/}
                {/*</Text>*/}
            </View>
        )
    }

    goToApplyRecycleBottle = (deliveryOrderNo) => {
        this.navigate('ApplyRecycleBottle', {
            deliveryOrderNo: deliveryOrderNo
        });
    }

    //渲染
    render() {
        let data = this.delivery.data;
        let isDealer = this.props.user && this.props.user.isDealer();
        let iconStatus = null;
        let status = data ? data.status : -1;
        switch (status) {
            case 1://待配货
                iconStatus = IC_STATUS_DISTRIBUTION;
                break;
            case 2://待发货
                iconStatus = IC_STATUS_SEND;
                break;
            case 3://待收货
                iconStatus = IC_STATUS_RECEIVE;
                break;
            case 4://已完成
                iconStatus = IC_STATUS_FINISH;
                break;
            case 6://已取消
                iconStatus = IC_STATUS_CANCEL;
                break;
            default:
                break;
        }

        if (!data) {
            return (<Header style={styles.container} title="提货单详情">
                <View/>
            </Header>);
        }
        let isEvaluated = data.estimate && data.estimate.grade != null ? 1 : 0;

        let expectedDeliveryDate = '暂无';
        if (data && data.deliveryTime){
            let tmp =   Util.DateUtil.parserTimestamp(data.deliveryTime);
            expectedDeliveryDate =  Util.DateUtil.formatDate(tmp, 'yyyy-MM-dd')
        }

        let isRibband = '否';
        if (data && data.items && data.items.length>0){
            if (data.items[0].withPlateFlag+''=='1'){
                isRibband = '是';
            }
        }

        return (
            <Header style={styles.container} title="提货单详情">
                {
                    data ? <View style={{flex: 1}}>
                            <ScrollView style={{flex: 1, backgroundColor: Constant.colorBackgroundDefault}}>
                                <ImageBackground
                                    style={{
                                        width: width,
                                        height: Constant.scale(80),
                                        flexDirection: 'row',
                                        alignItems: 'center'
                                    }}
                                    source={IC_BANNER}>
                                    <Image
                                        style={{
                                            width: Constant.scale(50),
                                            height: Constant.scale(50),
                                            marginLeft: Constant.scale(33.5)
                                        }}
                                        source={iconStatus}/>
                                    <View style={{flex: 1, marginLeft: Constant.scale(37)}}>
                                        <Text style={{fontSize: Constant.fontSizeCaption, color: '#fff'}}>
                                            {data.statusName}
                                        </Text>
                                        <Text
                                            style={{
                                                fontSize: Constant.fontSizeSmall,
                                                color: '#fff',
                                                marginTop: Constant.scale(5)
                                            }}>
                                            提货单号:{data.deliveryOrderNo}
                                        </Text>
                                    </View>
                                </ImageBackground>
                                {/*地址信息*/}
                                <View style={{width: width, backgroundColor: Constant.colorDefault}}>
                                    <View
                                        style={{
                                            width: width,
                                            height: Constant.scale(44),
                                            alignItems: 'center',
                                            flexDirection: 'row'
                                        }}>
                                        <Text
                                            style={{
                                                fontSize: Constant.fontSizeNormal,
                                                color: Constant.colorTxtContent,
                                                marginLeft: Constant.scale(33)
                                            }}>
                                            {data.deliveryName}
                                        </Text>
                                        <Text
                                            style={{
                                                fontSize: Constant.fontSizeNormal,
                                                color: Constant.colorTxtContent,
                                                marginLeft: Constant.scale(18)
                                            }}>
                                            {data.deliveryMobile}
                                        </Text>
                                    </View>
                                    <View style={{width: width, flexDirection: 'row'}}>
                                        <Image
                                            style={{
                                                marginLeft: Constant.scale(14),
                                                width: Constant.scale(13),
                                                height: Constant.scale(16)
                                            }}
                                            source={IC_ADDRESS}/>
                                        <Text
                                            style={{
                                                fontSize: Constant.fontSizeSmall,
                                                color: Constant.colorTxtContent,
                                                margin: Constant.sizeMarginDefault,
                                                marginLeft: Constant.scale(5),
                                                marginTop: 0
                                            }}>
                                            {data.address}
                                        </Text>
                                    </View>
                                </View>

                                <View
                                    style={{
                                        width: width,
                                        backgroundColor: Constant.colorDefault,
                                        marginTop: Constant.sizeMarginDefault
                                    }}>

                                    <View
                                        style={{
                                            width: width,
                                            height: Constant.scale(40),
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'center'
                                        }}>
                                        <Image
                                            style={{
                                                width: Constant.scale(17),
                                                height: Constant.scale(15),
                                                marginLeft: Constant.sizeMarginDefault
                                            }}
                                            source={IC_SHOP_ICON}/>
                                        <Text
                                            style={{
                                                flex: 1,
                                                color: Constant.colorTxtTitle,
                                                fontSize: Constant.fontSizeNormal,
                                                marginLeft: Constant.sizeMarginDefault
                                            }}>
                                            {data.supplierOrgName}
                                        </Text>
                                    </View>

                                    <View>
                                        {
                                            data.items ? data.items.map((item, index) => {
                                                return <OrderGoodsItem
                                                    data={item}
                                                    isDealer={isDealer}
                                                    buyerType = {data && data.buyerType}
                                                    key={index}/>
                                            }) : null
                                        }
                                    </View>
                                    <LabelCell titleTextStyle={{color: Constant.colorTxtContent}}
                                               title="配送方式"
                                               underLine={1}
                                               rightIcon={<View/>}
                                               extra={<Text style={{
                                                   color: Constant.colorTxtTitle,
                                                   fontSize: Constant.fontSizeNormal
                                               }}>{data.deliveryMethodName}</Text>}
                                    />
                                    <LabelCell titleTextStyle={{color: Constant.colorTxtContent}}
                                               title="发货日期"
                                               underLine={1}
                                               rightIcon={<View/>}
                                               extra={<Text style={{
                                                   color: Constant.colorTxtTitle,
                                                   fontSize: Constant.fontSizeNormal
                                               }}>
                                                   {expectedDeliveryDate}
                                               </Text>}
                                    />
                                    <LabelCell titleTextStyle={{color: Constant.colorTxtContent}}
                                               title="是否带板"
                                               underLine={1}
                                               rightIcon={<View/>}
                                               extra={<Text style={{
                                                   color: Constant.colorTxtTitle,
                                                   fontSize: Constant.fontSizeNormal
                                               }}>
                                                   {isRibband}
                                               </Text>}
                                    />

                                    {data.driver?<LabelCell titleTextStyle={{color: Constant.colorTxtContent}}
                                                            title="司机名称"
                                                            showRightIcon={false}
                                                            underLine={1}
                                                            extra={<Text style={{
                                                                color: Constant.colorTxtAlert,
                                                                fontSize: Constant.fontSizeNormal
                                                            }}>{data.driver}</Text>}


                                    />:null}

                                    {data.driverPhone?<LabelCell titleTextStyle={{color: Constant.colorTxtContent}}
                                                            title="司机手机"
                                                                 showRightIcon={false}
                                                            underLine={1}
                                                            extra={<Text style={{
                                                                color: Constant.colorTxtAlert,
                                                                fontSize: Constant.fontSizeNormal
                                                            }}>{data.driverPhone}</Text>}


                                    />:null}
                                    <LabelCell titleTextStyle={{color: Constant.colorTxtContent}}
                                               title="物流信息"
                                               underLine={2}
                                               onClick={() => {
                                                   this.deliveryOrbit(data.deliveryOrderId, data.status)
                                               }}
                                               extra={<Text style={{
                                                   color: Constant.colorTxtAlert,
                                                   fontSize: Constant.fontSizeNormal
                                               }}>详细</Text>}
                                    />

                                    {
                                        data.deliveryTime ? <View
                                            style={{
                                                margin: Constant.sizeMarginDefault,
                                                marginTop: 0,
                                                flex: 1,
                                                backgroundColor: Constant.colorBackgroundDefault,
                                                borderRadius: 5
                                            }}>
                                            <View
                                                style={{
                                                    height: Constant.scale(40),
                                                    justifyContent: 'center',
                                                    alignItems: 'flex-start',
                                                    marginLeft: Constant.sizeMarginDefault
                                                }}>

                                                <Text
                                                    style={{
                                                        color: Constant.colorTxtTitle,
                                                        fontSize: Constant.fontSizeNormal
                                                    }}>{data.deliveryTime} 发货</Text>
                                            </View>
                                            {this.location()}
                                        </View> : null
                                    }
                                </View>

                            </ScrollView>
                        {(this.state.data.status == 2 || this.state.data.status == 31 ) && data.status == 1 ?  <View
                                style={{
                                    width: width,
                                    height: Constant.scale(44),
                                    alignItems: 'center',
                                    justifyContent: 'flex-end',
                                    flexDirection: 'row',
                                    backgroundColor: Constant.colorDefault
                                }}><Button style={{borderRadius: 5, borderColor: Constant.colorTxtPrimary}}
                                            txtStyle={{
                                                fontSize: Constant.fontSizeSmall,
                                                color: Constant.colorTxtPrimary
                                            }}
                                            onPress={this.applyAfterSale}
                        >
                            申请售后
                        </Button>
                        </View> : null}
                            {data.status == 3 || data.status == 4 ? <View
                                style={{
                                    width: width,
                                    height: Constant.scale(44),
                                    alignItems: 'center',
                                    justifyContent: 'flex-end',
                                    flexDirection: 'row',
                                    backgroundColor: Constant.colorDefault
                                }}>
                                {
                                    data.status == 3 ?
                                        <Button style={{borderRadius: 5, borderColor: Constant.colorTxtPrimary}}
                                                txtStyle={{
                                                    fontSize: Constant.fontSizeSmall,
                                                    color: Constant.colorTxtPrimary
                                                }}
                                                onPress={this.confirmGoods}
                                        >
                                            确认收货
                                        </Button> :
                                        <View style={{
                                            flex: 1,
                                            alignItems: 'center',
                                            justifyContent: 'flex-end',
                                            flexDirection: 'row'
                                        }}>
                                            <Button style={{borderRadius: 5, borderColor: Constant.colorTxtPrimary}}
                                                    txtStyle={{
                                                        fontSize: Constant.fontSizeSmall,
                                                        color: Constant.colorTxtPrimary
                                                    }}
                                                    onPress={() => {
                                                        //this.evaluate(item) //调起H5页面
                                                        this.goToApplyRecycleBottle(data.deliveryOrderNo)
                                                    }}>申请回瓶</Button>
                                            <Button style={{borderRadius: 5, borderColor: Constant.colorTxtPrimary}}
                                                    txtStyle={{
                                                        fontSize: Constant.fontSizeSmall,
                                                        color: Constant.colorTxtPrimary
                                                    }}
                                                    onPress={() => {
                                                        //this.evaluate(item) //调起H5页面
                                                        this.commentDelivery(data)
                                                    }}>{isEvaluated ? '查看评价' : '评价物流'}</Button>

                                        </View>
                                }


                            </View> : null}
                        </View>
                        : null
                }
            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    locatinTxtStyle: {
        color: Constant.colorTxtTitle,
        fontSize: Constant.fontSizeSmall,
        marginLeft: Constant.sizeMarginDefault
    }
});
