/**
 * Created by whw on 2018/2/24.
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    ScrollView,
} from 'react-native';
import PropType from 'prop-types';
import { ReactNavComponent, Widget } from 'rn-yunxi';
import AppWidget from '../../app-widget';
import OrderGoodsItem from './OrderGoodsItem';

const { width, height } = Dimensions.get('window');
const { Header } = AppWidget;
const { CommonFlatList, Text } = Widget;
import { toJS } from 'mobx';
import GoodsExtension from '../../store/GoodsExtension';
import ToastUtil from '../../util/ToastUtil';
import { inject } from 'mobx-react/native';
import { observer } from 'mobx-react/native';

/**
 * 商品清单
 */
@inject(stores => ({
    goodDetailList:stores.goodDetailList,
    orderUtil: stores.orderUtil,

}))
@observer
export default class GoodsDetailList extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        let { params } = this.getNavState();
        this.goodsExtensionStore = new GoodsExtension();
        this.state = {//状态机变量声明


        };
    }

    componentWillMount() {
        Log('=====测试声明周期1');
        let { params } = this.getNavState();
        let data = params && params.data ? params.data : [];
        // let paramsArray = [];
        // Log('========rrrr',this.state.priceList)
        // if (this.state.priceList.length > 0) {
        //     this.setState({
        //         defaultPrice:this.state.priceList[ 0 ].price,
        //         defaultPriceListId:this.state.priceList[ 0 ].priceListId,
        //     });
        // }
        // if(data.length>0){
        //     for (let item of data){
        //         paramsArray.push({
        //             itemId: item.itemId,
        //             orgId: item.orgId
        //         })
        //     }
        //  this.getAwardsListAndPriceList(paramsArray).then().catch(e=>{})
        // }


    }

    componentDidMount() {
        Log('=====测试声明周期2');
    }

    componentWillUnmount() {
        Log('=====测试声明周期3');
    }


    // getAwardsListAndPriceList = async(params)=>{
    //     let result1 = await Api.getAwardList(params)
    //     let result2 = await Api.getPriceList(params)
    //     if (result1 && result1.data){
    //         this.setState({
    //             awardsList:result1.data
    //         })
    //     }
    //
    //     if (result2 && result2.data){
    //         this.setState({
    //             priceList:result2.data
    //         },()=>{
    //             if (this.state.priceList.length>0){
    //                 this.setState({
    //                     defaultPrice:this.state.priceList[0].price,
    //                     defaultPriceListId:this.state.priceList[0].priceListId,
    //                 })
    //             }
    //         })
    //     }
    //
    //     Log('=====数据',toJS(this.state.awardsList),toJS(this.state.priceList))
    //
    // }
    //渲染
    render() {
        let { params } = this.getNavState();
        let data = params && params.data ? params.data : [];
        let isTurnOrder = false;
        let showBottomView = false;
        let isConfirmOrder = false;
        let awardsList = params && params.awardsList ?params.awardsList:[] ;
        let priceList = params && params.priceList ? params.priceList:[];

        if (params && params.isTurnOrder) {
            isTurnOrder = params.isTurnOrder;

        }
        if (params && params.showBottomView) {
            showBottomView = params.showBottomView;

        }

        if (params && params.isConfirmOrder) {
            isConfirmOrder = params.isConfirmOrder;

        }

        Log('-----',showBottomView)
        return (
            <Header
                style={styles.container}
                title="商品清单"
            >
                <ScrollView style={{ flex:1 }}>
                    {data ? data.map((item, index) => {
                        return (
                            <OrderGoodsItem
                                canEditPrice={showBottomView}
                                isTurnOrder={isTurnOrder}
                                isConfirmOrder={isConfirmOrder}
                                awardsList={awardsList [ index ]}
                                priceList={priceList[ index ]}
                                data={item}
                                key={`ordergoods${index}`}
                                showWithPlate={true}
                                awardsIdCallback={(awardsId)=>{
                                    Object.assign(item,{newAwardsId:awardsId})
                                }}

                                priceTypeCallback={(priceType)=>{
                                    Object.assign(item,{newPriceType:priceType})
                                }}

                                priceNatureCallback={(priceNature)=>{
                                    Object.assign(item,{newPriceNature:priceNature})
                                }}
                            />
                        );
                    }) : null}
                </ScrollView>

                {isTurnOrder && showBottomView ? <TouchableOpacity
                    onPress={() => this.confirmTurnOrder()}
                    style={{
                        width:'100%',
                        height:Constant.scale(45),
                        position:'absolute',
                        justifyContent:'center',
                        alignItems:'center',
                        backgroundColor:Constant.colorPrimary,
                        bottom:0,
                        left:0,
                    }}
                >
                    <Text style={{ color:'white', fontSize:Constant.fontSizeBig }}>确认转单</Text>
                </TouchableOpacity> : null}

            </Header>
        );
    }

    confirmTurnOrder = () => {
        let { params } = this.getNavState();
        let awardsList = params && params.awardsList ?params.awardsList:[] ;
        let priceList = params && params.priceList ? params.priceList:[];
        let data = params && params.data ? params.data : [];
        let orderDetail = params && params.orderDetail ? params.orderDetail : {};
        let orderId = params && params.orderId ? params.orderId : null;
        let hasNoPrice = false;
        for (let list of priceList){
            if (list.length == 0){
                hasNoPrice  = true;
            }
        }

        let transferItemDtos = [];

        if (!hasNoPrice){

            let params = {}
            params.orderId = orderId

            data && data.map((item,index)=>{
                let obj={};
                Object.assign(obj,{orderItemId:item.orderItemId})
                Object.assign(obj,{priceType:this.getPriceType(item,index)})
                Object.assign(obj,{priceNature:this.getPriceNature(item,index)})
                Object.assign(obj,{awardsId:this.getAwardsId(item,index)})

                transferItemDtos.push(obj)
            })

            params.transferItemDtos = transferItemDtos;
            this.props.orderUtil.turnOrder(params,(data)=>{
                if (data) {
                    this.navigate('ConfirmOrder', {data: data, type: 3,trunOrderParams:params});
                }
            }).then().catch(e=>{Log(e)})

        }else {
            ToastUtil.show('该单含有不带价格的商品，无法转单');
            return;
        }

    };


    getPriceType = (item,index)=>{
      if (item.newPriceType){
          return item.newPriceType
      }
    }

    getPriceNature = (item,index)=>{
        if (item.newPriceNature){
            return item.newPriceNature
        }
    }

    getAwardsId =(item ,index)=>{
        if (item.newAwardsId){
            return item.newAwardsId;
        }
        return item.awardsId;
    }
};

const styles = StyleSheet.create({
    container:{
        flex:1,
    },
});