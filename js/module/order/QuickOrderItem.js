/**
 *
 * Created by x<PERSON>owz on 2018/2/2.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'
const {width, height} = Dimensions.get('window');
const {Text, CommonListView} = Widget;
const {Image, CheckBox, PriceText, ConfirmInput} = AppWidget;
import {toJS} from 'mobx';
import _ from 'lodash';
import SkuPanelStore from '../../store/SkuPanel';
import {inject, observer} from 'mobx-react/native';
import SkuPanel from '../shopcart/SkuPanel';



//模块声名并导出
@inject(stores => ({
    user: stores.user,
    quickOrderStore: stores.quickOrder
}))
@observer
export default class QuickOrderItem extends Component {
    //属性声名
    static propTypes = {
        // onItemNumChange: PropType.func,
        onItemNumConfirm: PropType.func,
        onItemChoose: PropType.func,
        onShowSkuPanel: PropType.func,
        listIndex: PropType.any,
        data: PropType.any,
        orgList:PropType.any,
        awardsMap:PropType.any,
        palletStruList:PropType.any, // 托板承载规格列表
    };
    //默认属性
    static defaultProps = {
        orgList:[],
        awardsMap: new Map(),
    };

    //构造函数
    constructor(props) {
        super(props);

        //状态机变量声明
        this.state = {
            isFocus: false,
        };
        this.skuPanelStore = new SkuPanelStore();
    }

    componentWillMount()  {
        // {this.getFirstSkuInfo()}
        this.initSkuPanel(this.props.data);

    }

    componentWillReceiveProps(nextProps) {
        // Log('-----属性改变-----', toJS(nextProps.data.itemNum), toJS(this.skuPanelStore.purchaseCount))
        let itemId = _.get(this.props, 'data.itemId');
        let newItemId = _.get(nextProps, 'data.itemId');
        if (newItemId != itemId) {
            this.initSkuPanel(nextProps.data);
        } else if (nextProps.data.itemNum !== this.skuPanelStore.purchaseCount) {
            this.initSkuPanel(nextProps.data);
            this.skuPanelStore.setPurchaseCount(nextProps.data.itemNum)
        }


    }

    //初始化skupanel
    initSkuPanel = (oftenItem) => {
        // Log('初始化商品数据------', toJS(oftenItem))
        this.skuPanelStore.setSelectAwardId(oftenItem.awardsId);
        this.skuPanelStore.setPickOrgList(this.props.orgList);
        this.skuPanelStore.setAwards(this.props.awardsMap);
        this.skuPanelStore.setSelectOrgId(oftenItem.orgId);
        this.skuPanelStore.setSelectChannelId(oftenItem.salesChannelCode, oftenItem.subSalesChannelCode);
        this.skuPanelStore.setSelectPlateFlag(oftenItem.withPlateFlag);
        // this.skuPanelStore.setSuggestNumber(oftenItem.suggestNumber);
        // this.skuPanelStore.setMandatoryNumber(oftenItem.mandatoryNumber);
        this.skuPanelStore.setPurchaseCount(0, false, oftenItem.isZeroFourGoods);
        this.skuPanelStore.setItemId(oftenItem.itemId,false);
        // this.skuPanelStore.obtainDiscount().then().catch(e=>{});
        let tmp = this.skuPanelStore.getChoose();
        this.props.quickOrderStore.setDefaultAwardsIdAndChannelCode(oftenItem,tmp,()=>{
            this.skuPanelStore.obtainDiscount().then().catch(e => {})
        })

        // 设置可选择的托板承载规格列表
        this.skuPanelStore.setPalletStruList(this.props.palletStruList || []);
        this.skuPanelStore.setBatchSaleNum(oftenItem.batchSaleNum || '0');
        this.skuPanelStore.setBearing(oftenItem.bearing || '');
    }


    //渲染
    render() {
        // Log('========渲染板数据')
        let isEditStatus = this.props.isEditStatus;  //是否编辑状态
        let oftenItem = this.props.data;   //单个商品的数据

        // let awardsName = _.get(oftenItem, 'awardsName');
        let awardsName = this.skuPanelStore.selectAwardName(this.props.awardsMap);
        let channelName = this.props.user.getChancelFullName(oftenItem.salesChannelCode,oftenItem.subSalesChannelCode);


        let itemNum = '';
        if (this.state.isFocus) {
            itemNum = this.skuPanelStore.tmpPurchaseCount + '';
        } else {
            itemNum = this.skuPanelStore.purchaseCount + '';
        }
        let purchaseQuantity = this.skuPanelStore.purchaseQuantity;

        return (
            <View style={{
                padding: Constant.sizeMarginDefault,
                paddingRight: 0,
                borderBottomWidth: Constant.sizeDividerNormal,
                borderColor: Constant.colorDividerDefault,
                backgroundColor: 'white'
            }}>
                <View style={{flexDirection: 'row', justifyContent: 'center'}}>
                    {
                        isEditStatus ?
                            <TouchableOpacity onPress={()=>this.chooseItemClick()} style={{justifyContent: 'center'}}>
                                <CheckBox
                                    isChecked={oftenItem.editSelected}
                                    style={styles.checkBox}
                                    onClick={() => {
                                        ()=>this.chooseItemClick()
                                    }}
                                />
                            </TouchableOpacity>
                            : null
                    }

                    <TouchableOpacity activeOpacity={1} onPress={()=>this.goToDetailClick(oftenItem.itemId,oftenItem.orgId)} style={{flex: 1, paddingRight: Constant.sizeMarginDefault }}>
                        <View style={{flexDirection: 'row', marginBottom: Constant.sizeMarginDefault}}>
                            <Image
                                resizeMode={'cover'}
                                source={{uri: oftenItem.imgUrl}}
                                // defaultBackgroundColor={'#fff'}
                                style={{
                                    borderWidth: Constant.sizeDividerNormal,
                                    borderColor: Constant.colorDividerDefault,
                                    marginRight: Constant.sizeMarginDefault,
                                    width: Constant.scale(75),
                                    height: Constant.scale(75)
                                }}/>
                            <View style={{flex: 1, paddingLeft: Constant.scale(10)}}>
                                <Text numberOfLines={1}>{oftenItem.name}</Text>
                                <View style={{flexDirection: 'row', alignItems: 'flex-end'}}>
                                    <PriceText price={oftenItem.price}/>
                                    <View style={{width: Constant.sizeMarginDefault}}/>
                                    <PriceText
                                        unit={`折${this.props.user.getSymbol()}`}
                                        size={-1}
                                        price={this.skuPanelStore.discount}
                                    />
                                </View>
                                <View style={{flex: 1}}/>

                                <ConfirmInput
                                    isFocus={(isFocus) => {
                                        if (isFocus) {
                                            this.skuPanelStore.setPurchaseCount(this.skuPanelStore.purchaseCount, true,oftenItem.isZeroFourGoods);
                                        } else {
                                            Log('=====测试临时数据====',this.skuPanelStore.tmpPurchaseCount)
                                            this.skuPanelStore.setPurchaseCount(this.skuPanelStore.tmpPurchaseCount, false,oftenItem.isZeroFourGoods);
                                        }
                                        this.setState({isFocus: isFocus}, () => {
                                            if (!this.state.isFocus) {
                                                this.props.onItemNumConfirm && this.props.onItemNumConfirm(this.skuPanelStore.purchaseCount);
                                            }
                                        })

                                    }}
                                    value={itemNum}
                                    onChangeText={(text) => {
                                        Log('=====数量发生改变====',text)
                                        this.skuPanelStore.setPurchaseCount(text, true,oftenItem.isZeroFourGoods)
                                        // this.props.onItemNumChange&&this.props.onItemNumChange(text);
                                    }}/>
                            </View>
                            {
                                purchaseQuantity.multiple > 0 ? <View style={{marginLeft: Constant.sizeMarginDefault}}>
                                    <Text style={{

                                        borderRadius: Constant.scale(2),
                                        paddingLeft: Constant.scale(2),
                                        paddingRight: Constant.scale(2),
                                        backgroundColor: '#e5e5e5',
                                        color: Constant.colorTxtAlert
                                    }}>{purchaseQuantity.isMandatory?'整板':'建议'}1*{purchaseQuantity.multiple}</Text>
                                </View> : null
                            }
                        </View>

                    </TouchableOpacity>


                </View>
                <TouchableOpacity
                    onPress={() => this.showSkuPanel(oftenItem,()=>{
                        this.setState({})
                    })}
                    style={{
                        alignItems: 'center',
                        flexDirection: 'row',
                        padding: Constant.sizeMarginDefault,
                        marginLeft: isEditStatus ? Constant.scale(25) : Constant.scale(0),
                        minHeight: Constant.scale(30),
                        width:'100%',
                        backgroundColor: Constant.colorBackgroundDefault
                    }}>
                    <View style={{flex:1,paddingRight:5}}>
                        <View style={{flexDirection: 'row',justifyContent:'space-between',width:'100%',}}>
                            <Text numberOfLines={1} style={[styles.attrTxt]}>
                                {awardsName}
                            </Text>

                            <Text style={[styles.attrTxt,{marginRight:10}]}>
                                {oftenItem.withPlateFlag == '1' ? '带板' : '不带板'}
                            </Text>
                        </View>

                        <View style={{flexDirection: 'row',justifyContent:'space-between',width:'100%'}}>
                            <Text numberOfLines={1} style={[styles.attrTxt]}>
                                {oftenItem.orgName}   {oftenItem.bearing || ''}
                            </Text>

                            <Text style={[styles.attrTxt,{marginRight:10}]}>
                                {channelName}
                            </Text>
                        </View>


                    </View>

                    <Image style={{width: Constant.scale(7.5), height: Constant.scale(4)}}
                           source={require('../img/arrow/ic_content_open.png')}
                    />
                </TouchableOpacity>

            </View>
        )
    }
    //选中按钮
    chooseItemClick = ()=>{
        this.props.onItemChoose && this.props.onItemChoose()
    }

    //点击进入详情
    goToDetailClick = (itemId,orgId)=>{
        this.props.navigation.navigate('GoodsDetail',{itemId:itemId,defaultOrgId:orgId})
    }

    // showPanel 选择不同属性
    showSkuPanel(item,callback) {
        // let tmp = this.skuPanel.getChoose();
        // Log('点击属性板',toJS(item))
        // Log('点击属性板',toJS(this.props.user.channelList))
        Widget.Popup.show(
            <SkuPanel
                skuPanelStore={this.skuPanelStore}
                // itemId={this.skuPanelStore.itemId}
                orgList = {toJS(this.props.orgList)}
                awardsMap = {this.props.awardsMap}
                channelListData={toJS(this.props.user.channelList)}
                orgId={this.skuPanelStore.selectOrgId}
                salesChannelCode = { this.skuPanelStore.selectChannelId.salesChannelCode}
                subSalesChannelCode = {this.skuPanelStore.selectChannelId.subSalesChannelCode}
                salesChannelName= {this.skuPanelStore.selectChannelName.salesChannelName}
                subSalesChannelName= {this.skuPanelStore.selectChannelName.subSalesChannelName}
                plateFlag={this.skuPanelStore.selectPlateId}
                awardId={this.skuPanelStore.selectAwardId}
                palletStruList = {this.skuPanelStore.palletStruList}
                batchSaleNum = {this.skuPanelStore.batchSaleNum}
                bearing = {this.skuPanelStore.bearing}
                onConfirm={(result) => {
                    //更新规格，刷新列表
                    let newResult = toJS(result)
                    Widget.Popup.hide();
                    InteractionManager.runAfterInteractions(() => {
                        this.props.quickOrderStore.callModifyItemApi(item, newResult,
                            () => {
                                this.props.onItemNumConfirm && this.props.onItemNumConfirm(this.skuPanelStore.purchaseCount);
                                this.skuPanelStore.obtainDiscount().then().catch(e => {});
                                callback && callback();
                            }
                        ).then().catch(e=>{})
                    })
                }}
                onCancel={() => {
                    Widget.Popup.hide()
                }
                }/>,
            {
                animationType: 'slide-up', backgroundColor: '#00000000',
                onMaskClose: () => {
                    Widget.Popup.hide()
                }
            })
    }
};
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Constant.colorBackgroundDefault
    },
    checkBox: {
        paddingRight: Constant.sizeMarginDefault,
        width: Constant.scale(15) + Constant.sizeMarginDefault,
        height: Constant.scale(15),
        // backgroundColor:'pink'
    },
    attrTxt: {
        fontSize: Constant.fontSizeSmall,
        color: Constant.colorTxtContent,
        // marginRight: Constant.scale(5),
    }

});
