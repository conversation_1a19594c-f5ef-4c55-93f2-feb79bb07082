import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    TextInput,
    Dimensions,
    ScrollView
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'
import {inject, observer} from 'mobx-react/native';

const {width, height} = Dimensions.get('window');

const {Text} = Widget;
const {CheckBox, PriceText} = AppWidget;
/**
 * 选择营销费用面板
 */
@observer
export default class MarketingAmountPanel extends ReactNavComponent {
    //属性声名
    static propTypes = {

        store: PropType.any,
        orderIndex: PropType.any,
    };
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {}

    }

    componentWillMount() {
    }

    componentDidMount() {
    }

    componentWillUnmount() {
    }


    renderItem = (item, index) => {
        return (
            <View key={index} style={{paddingBottom: Constant.scale(1)}}>
                <TouchableOpacity

                    onPress={() => {
                        this.props.store.setOrderItemUseMarketingAmount(this.props.orderIndex, index);
                    }}
                    style={{
                        flexDirection: 'row', marginTop: Constant.scale(15),
                        marginLeft: Constant.scale(15),
                        marginBottom: Constant.scale(15)
                    }}>
                    <CheckBox
                        style={{alignSelf: 'center'}}
                        isChecked={item.useMarketingAmountFlag == 1 ? true : false}
                    />
                    <View style={{
                        flex: 1,
                        marginLeft: Constant.scale(15),
                        marginRight: Constant.sizeMarginDefault
                    }}>
                        <Text style={{color: Constant.colorTxtAlert}}>{item.name}</Text>
                        <Text style={{color: Constant.colorTxtAlert}}>数量：{item.itemNum}</Text>
                        <View style={{
                            marginTop: Constant.scale(10),
                            flexDirection: 'row',
                            justifyContent: 'space-between'
                        }}>
                            <Text>折让金额</Text>
                            <PriceText price={item.totalDiscountAmount}/>

                        </View>
                    </View>


                </TouchableOpacity>
                <View style={[GlobalStyle.styleDividerDefault,
                    {marginLeft: Constant.sizeMarginDefault}]}/>
            </View>
        )
    }

    //渲染
    render() {
        let data = this.props.store ? this.props.store.orders[this.props.orderIndex] : {};
        return (
            <View
                style={{
                    backgroundColor: 'white',
                    width: width,
                    height: height / 1.6,

                }}>
                <View
                    style={{
                        width: '100%',
                        height: Constant.scale(44),
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>
                    <Text
                        style={{fontSize: Constant.fontSizeCaption}}>使用金额</Text>
                    <TouchableOpacity
                        style={{
                            position: 'absolute',
                            right: Constant.sizeMarginDefault
                        }}
                        onPress={() => {
                            Widget.Popup.hide()
                        }}
                    >
                        <Image
                            style={{
                                width: Constant.scale(15),
                                height: Constant.scale(15)
                            }}
                            source={require('../../app-widget/select-pop/ic_popclose.png')}/>
                    </TouchableOpacity>

                </View>
                <View style={GlobalStyle.styleDividerDefault}/>
                <ScrollView
                    contentContainerStyle={{
                        height: '100%',
                        width: '100%'
                    }}>
                    {
                        data.itemDtos ?
                            data.itemDtos.map((item, index) => {
                                return this.renderItem(item, index)
                            }) : null
                    }
                </ScrollView>

                <View style={GlobalStyle.styleDividerDefault}/>
                <View style={{
                    flexDirection: 'row',
                    height: Constant.scale(44),
                    alignItems: 'center'
                }}>
                    <TouchableOpacity
                        style={{
                            justifyContent: 'center',
                            alignItems: 'center',
                            flexDirection: 'row',
                            marginLeft: Constant.sizeMarginDefault,
                        }}
                        onPress={() => {
                            this.props.store.setOrderUseMarketingAmount(this.props.orderIndex);
                        }}>
                        <CheckBox
                            isChecked={data.useMarketingAmountFlag == 1 ? true : false}

                            rightTextStyle={{
                                marginLeft: Constant.scale(15),
                                color: Constant.colorTxtAlert,
                            }}
                            rightText={'全选'}
                            style={{
                                width: Constant.scale(80)
                            }}/>

                    </TouchableOpacity>

                </View>


            </View>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },

});
