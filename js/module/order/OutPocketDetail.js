/**
 * Created by whw on 2018/1/24.
 */
import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
} from 'react-native';
import PropType from 'prop-types';
import { ReactNavComponent, Widget } from 'rn-yunxi';
import AppWidget from '../../app-widget';
import userStore from '../../store/User';

const { width, height } = Dimensions.get('window');
const { Image, Button, PriceText } = AppWidget;
const { Text } = Widget;
const IC_CLOSE = require('../img/order/ic_popclose.png');
/**
 * 订单列表Item
 */

export default class OutPocketDetail extends ReactNavComponent {
    //属性声名
    static propTypes = {
        cancel: PropType.func,
        data: PropType.any,
        zpOrderType: PropType.any,//1标准订单 2海外订单 3费用赠酒 4瓶盖赠酒 (默认是标准订单)
        pageType: PropType.any,//0结算页 1订单详情页
    };
    //默认属性
    static defaultProps = {
        type: 1,
        pageType: 0
    };

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
        };
    }

    componentWillMount() {
    }

    componentDidMount() {
    }

    componentWillUnmount() {
    }

    getPrice = (price) => {
        return price ? price : 0;
    }

    /**
     * 通过是否 “使用瓶箱余额” 和 “使用托板余额”，算出正确的“瓶箱按金费用小计”
     */
    getRealBottleAmount = (item) => {

        let resultAmount = null;
        const {useBottleRemain, usePalletRemain} = item;
        const {
            bottleAmount,
            bottleCashPayAmount,
            bottlePayAmount,
            palletCashPayAmount,
            palletPayAmount
        } = item;

        if (!useBottleRemain && !usePalletRemain) {
            resultAmount = bottleAmount;
        } else if (useBottleRemain && !usePalletRemain) {
            resultAmount = bottleCashPayAmount + palletCashPayAmount + palletPayAmount;
        } else if (!useBottleRemain && usePalletRemain) {
            resultAmount = bottleCashPayAmount + bottlePayAmount + palletCashPayAmount;
        } else if (useBottleRemain && usePalletRemain) {
            resultAmount = bottleCashPayAmount + palletCashPayAmount;
        }

        return resultAmount;
    };

    getPriceList = () => {
        let data = this.props.data ? this.props.data : {};
        let result = this.props.result ? this.props.result : {};
        let bottleAmount = this.getRealBottleAmount(data);
        let prepayAmount = data.useBottleRemain ? data.prepayAmountWithDeduct : data.prepayAmountWithoutDeduct;
        const isComfirmOrder = this.props.pageType == 0;
        let freight= result.freight ? result.freight: 0;
        let premium= result.premium ? result.premium : 0;
        let incidentals= result.incidentals ? result.incidentals:0;
        let list = [];


        list.push({
            title: (isComfirmOrder ? data.totalItemNum : result.totalItemNum) + '件商品，总商品金额:',
            price: this.getPrice(data.totalItemAmount),
            isPlus: true
        });

        //经销商
        if (userStore.isDealer()) {
            if (this.props.zpOrderType == 4) {
                list.push({
                    title: '瓶盖赠酒抵扣:',
                    price: this.getPrice(-data.totalItemAmount),
                    isPlus: false
                });
                list.push({
                    title: '瓶盖赠酒数量:',
                    num: isComfirmOrder ? data.totalItemNum : data.bottlecapNum,
                    isPlus: true
                });
            }
            if (this.props.zpOrderType == 3) {
                list.push({
                    title: '费用赠酒抵扣数量:',
                    num: isComfirmOrder ? data.totalItemNum : result.totalItemNum,
                    isPlus: true
                });
            }
            if (this.props.zpOrderType !== 3 && this.props.zpOrderType != 4) {
                list.push({
                    title: '折价金额:',
                    price: this.getPrice(isComfirmOrder ? data.currentUseMarketingAmount : data.totalDiscountAmount),
                    isPlus: false
                });
            }
            list.push({
                title: '装车费:',
                price: this.getPrice(isComfirmOrder ? data.saleAmount : data.totalSaleAmount),
                isPlus: true
            });
            list.push({
                title: '预付款:',
                price: this.getPrice(isComfirmOrder ? prepayAmount : data.totalPrepayAmount),
                isPlus: true
            });
            list.push({
                title: '折旧费:',
                price: this.getPrice(data.depreciationAmount),
                isPlus: true
            });
            list.push({
                title: '瓶箱按金:',
                price: this.getPrice(isComfirmOrder ? bottleAmount : data.totalBottlePayAmount),
                isPlus: true
            });
            if (this.props.zpOrderType == 3 || this.props.zpOrderType == 4) {
                list.push({
                    title: '营销费用抵扣:',
                    price: this.getPrice(isComfirmOrder ? -data.marketingAmount : -marketingChargeAmount),
                    isPlus: false
                });
            }

            if(!userStore.isRMB()){
                list.push({
                    title: '外币订单运保杂费:',
                    price: this.getPrice(isComfirmOrder ? data.foreignCurrency.total : freight + incidentals + premium),
                    isPlus: false
                });
            }



        }

        return list;
    }

    //渲染
    render() {
        Log('type ', this.props.zpOrderType)
        let data = this.props.data ? this.props.data : {};
        let result = this.props.result ? this.props.result : {};
        let bottleAmount = data.useBottleRemain ? data.bottleCashPayAmount : data.bottlePayAmount + data.bottleCashPayAmount;
        let prepayAmount = data.useBottleRemain ? data.prepayAmountWithDeduct : data.prepayAmountWithoutDeduct;
        const isComfirmOrder = this.props.pageType == 0;

        const priceList = this.getPriceList();
        return (
            <View style={{ width: width, backgroundColor: Constant.colorDefault }}>
                <View
                    style={{ width: width, height: Constant.scale(44), justifyContent: 'center', alignItems: 'center' }}>
                    <Text style={{ fontSize: Constant.fontSizeCaption, color: Constant.colorTxtTitle }}>实付款明细</Text>
                    <TouchableOpacity
                        style={{
                            position: 'absolute',
                            right: 0,
                            top: 0,
                            height: Constant.scale(44),
                            width: Constant.scale(44),
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}
                        onPress={() => {
                            Widget.Popup.hide();
                        }}
                    >
                        <Image style={{ width: Constant.scale(15), height: Constant.scale(15) }} source={IC_CLOSE} />
                    </TouchableOpacity>
                </View>

                <View style={{
                    width: width - 2 * Constant.sizeMarginDefault,
                    margin: Constant.sizeMarginDefault,
                    justifyContent: 'flex-end',
                    flexDirection: 'row'
                }}>
                    <View style={{ marginRight: Constant.scale(5) }}>

                        {priceList.map((item, index) =>
                            <Text style={styles.leftTxtStyle} key={'leftList' + index}>{item.title}</Text>
                        )}

                    </View>
                    <View>

                        {priceList.map((item, index) =>{
                               if(item.num){
                                   return(
                                       <Text key ={index} style={{marginBottom:Constant.scale(8)}}> {item.num}</Text>
                                       )

                               }else {
                                   return <PriceText
                                       key={'rightList' + index}
                                       textStyle={styles.rightTxtStyle}
                                       isPlus={item.isPlus}
                                       price={item.price }
                                       size={1} />
                               }
                        }
                        )}

                    </View>
                </View>
                <View style={{
                    width: width - 2 * Constant.sizeMarginDefault,
                    height: Constant.sizeDividerNormal,
                    marginRight: Constant.sizeMarginDefault,
                    marginLeft: Constant.sizeMarginDefault,
                    backgroundColor: Constant.sizeDividerNormal
                }} />
                {
                    (!userStore.isRMB()) && (!isComfirmOrder) ?
                        <View style={{
                            width: width - 2 * Constant.sizeMarginDefault,
                            margin: Constant.sizeMarginDefault,
                            justifyContent: 'flex-end',
                            flexDirection: 'row',
                            alignItems: 'center'
                        }}>
                            <Text style={{ fontSize: Constant.fontSizeNormal, color: Constant.colorTxtContent }}>合同编码: {result.contractCode || '暂无'}</Text>
                        </View>
                        : null
                }

                <View style={{
                    width: width - 2 * Constant.sizeMarginDefault,
                    margin: Constant.sizeMarginDefault,
                    justifyContent: 'flex-end',
                    flexDirection: 'row',
                    alignItems: 'center'
                }}>

                    <Text style={{ fontSize: Constant.fontSizeNormal, color: Constant.colorTxtTitle }}>应付金额:</Text>
                    <PriceText
                        price={this.getPrice(isComfirmOrder ? data.dealAmount : data.totalAmount)}
                        size={1} />
                </View>
            </View>
        );
    }
};


const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    leftTxtStyle: {
        height: Constant.scale(24),
        textAlign: 'right',
        color: Constant.colorTxtContent,
        fontSize: Constant.fontSizeNormal
    },
    rightTxtStyle: {
        height: Constant.scale(24),
        color: Constant.colorTxtTitle,
        fontSize: Constant.fontSizeNormal
    }
});