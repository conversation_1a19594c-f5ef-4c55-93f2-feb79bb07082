import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    ScrollView,
    InteractionManager
} from 'react-native';
import {ReactNavComponent, Util, Widget} from 'rn-yunxi';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import AppWidget from '../../app-widget'
const { width, height } = Dimensions.get('window');
const { InputFormCell, LabelCell, Popup } = Widget;
const { Header, Button } = AppWidget
import DateFilter from './DateFilter'

//模块声名并导出
export default class OutStockFilter extends ReactNavComponent {

    //属性声名
    static propTypes = {
    };
    //默认属性
    static defaultProps = {
    };
    //构造函数
    constructor(props) {
        super(props);
        //状态机变量声明


        let { params } = this.getNavState();

        this.state = {
            startTime: params && params.searchParams && params.searchParams.startTime || new Date(),
            endTime: params && params.searchParams && params.searchParams.endTime || new Date(),
            startTimeText: params && params.searchParams && params.searchParams.startTimeText || Util.DateUtil.formatDate(new Date().getTime(), 'yyyy-MM-dd'),
            endTimeText: params && params.searchParams && params.searchParams.endTimeText || Util.DateUtil.formatDate(new Date().getTime(), 'yyyy-MM-dd'),
            };
    }

    componentDidMount() {

    }

    search = ()=> {
        let { params } = this.getNavState();
        let callback = params && params.callback;
        let obj = {startTime: this.state.startTime,endTime: this.state.endTime,endTimeText: this.state.endTimeText,startTimeText: this.state.startTimeText};
        callback && callback(obj);
        this.goBack();
    }

    resetClick = ()=> {
        this.setState({startTime: null,endTime: null,endTimeText: null,startTimeText: null});
    }

    //渲染
    render() {
        return (
            <Header title={'订单筛选'} showBackAction={true} navigation={this.props.navigation} rightTitle={'重置'} rightTextStyle={{ color: '#666', fontWeight: 'bold' }} rightAction={() => this.resetClick()} >
                <KeyboardAwareScrollView
                    ref="scroll"
                    scrollEnabled={false}
                    keyboardShouldPersistTaps={'always'}
                    extraHeight={height < 667 ? Constant.scale(18) : Constant.scale(60)}
                >

                    <View style={{ marginTop: Constant.scale(15) }}>
                        <DateFilter
                            title={'日期'}
                            startDate={this.state.startTime || new Date()}
                            startDateText={this.state.startTimeText}
                            endDate={this.state.endTime || new Date()}
                            endDateText={this.state.endTimeText}
                            onStartDateChange={(text, date) => {
                                this.setState({
                                    startTime: date,
                                    startTimeText: text
                                })
                            }}

                            onEndDateChange={(text, date) => {
                                this.setState({
                                    endTime: date,
                                    endTimeText: text
                                })
                            }}

                        />
                    </View>
                </KeyboardAwareScrollView>
                <View style={{ width: width, height: 80, alignItems: 'center' }}>
                    <Button style={styles.buttonStyle} txtStyle={styles.textStyle} onPress={() => this.search()}>搜索</Button>
                </View>
            </Header>
        );
    }

};
const styles = StyleSheet.create({
    container: {
        flex: 1,

    },
    buttonStyle: {
        backgroundColor: Constant.colorPrimary,
        width: Constant.scale(350),
        height: Constant.scale(40),
        position: 'absolute',
        left: Constant.scale(12),
        bottom: Constant.scale(15),
        borderRadius: 4

    },
    textStyle: {
        fontSize: Constant.fontSizeNormal,
        fontWeight: 'bold',
        color: 'white'
    }
});