/**
 * Created by whw on 2018/2/10.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    ScrollView
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget';
const {width, height} = Dimensions.get('window');
const {Image,Button,PriceText}=AppWidget;
const {Text} = Widget;
const IC_CLOSE = require('../img/order/ic_popclose.png');
/**
 * 瓶箱余额
 */

export default class BottleBalanceDetail extends ReactNavComponent {
    //属性声名
    static propTypes = {
        cancel: PropType.func,

    };
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state={//状态机变量声明
        };
    }
    componentWillMount(){}
    componentDidMount(){}
    componentWillUnmount(){}


    renderRow = (item,index)=> {
        let btlUseFlag = this.props.btlUseFlag;
        return (
            <View key={`balance+${index}`}
                  style={{backgroundColor: index % 2 ? Constant.colorBackgroundDefault : Constant.colorDefault,height:Constant.scale(44),flexDirection: 'row',alignItems: 'center'}}
            >
                <View style={{flex: 2}}>
                    <Text style={{fontSize: Constant.fontSizeSmall,color: Constant.colorTxtContent,margin: Constant.sizeMarginDefault,}}>{item.bottleInfo}</Text>
                </View>
                <View style={{flex: 1}}>
                    <Text style={{fontSize: Constant.fontSizeSmall,color: Constant.colorTxtContent,margin: Constant.sizeMarginDefault,textAlign: 'center'}}>{item.requiredNum}</Text>
                </View>
                <View style={{flex: 1.2}}>
                    <Text style={{fontSize: Constant.fontSizeSmall,color: Constant.colorTxtContent,margin: Constant.sizeMarginDefault,textAlign: 'center'}}>{btlUseFlag? item.deductibleAmount: item.beforeOrderRemainNum}</Text>
                </View>
                <View style={{flex: 1}}>
                    <Text style={{fontSize: Constant.fontSizeSmall,color: Constant.colorTxtContent,margin: Constant.sizeMarginDefault,textAlign: 'center'}}>{item.diffNum}</Text>
                </View>
            </View>
        );
    }



    //渲染
    render() {
        let data = this.props.data;
        let btlUseFlag = this.props.btlUseFlag;
        return (
            <View style={{width: width,backgroundColor: Constant.colorDefault}}>
                <View style={{width: width,height: Constant.scale(44),justifyContent: 'center',alignItems: 'center'}}>
                    <Text style={{fontSize: Constant.fontSizeCaption,color: Constant.colorTxtTitle}}>使用瓶箱余额详情</Text>
                    <TouchableOpacity
                        style={{position: 'absolute',right: 0,top: 0,height: Constant.scale(44),width: Constant.scale(44),justifyContent: 'center',alignItems: 'center'}}
                        onPress={()=> {
                            Widget.Popup.hide();
                        }}
                    >
                        <Image style={{width: Constant.scale(15),height: Constant.scale(15)}} source={IC_CLOSE}/>
                    </TouchableOpacity>
                </View>

                <View style={{width: width-2*Constant.sizeMarginDefault,margin: Constant.sizeMarginDefault}}>
                    <View style={{height:Constant.scale(34),flexDirection: 'row',backgroundColor: Constant.colorDefault,alignItems: 'center',justifyContent:'center'}}>
                        <View style={{flex: 2}}><Text style={{fontSize: Constant.fontSizeSmall,color: Constant.colorTxtContent,marginLeft: Constant.sizeMarginDefault,flex:1}}>瓶箱描述</Text></View>
                        <View style={{flex: 1}}><Text style={{fontSize: Constant.fontSizeSmall,flex: 1,color: Constant.colorTxtContent,textAlign: 'center',}}>所需数量</Text></View>
                        <View style={{flex: 1.2}}><Text style={{fontSize: Constant.fontSizeSmall,flex: 1,color: Constant.colorTxtContent,textAlign: 'center'}}>{btlUseFlag?'可抵扣':'可用瓶箱余额'}</Text></View>
                        <View style={{flex: 1}}><Text style={{fontSize: Constant.fontSizeSmall,flex: 1,color: Constant.colorTxtContent,textAlign: 'center'}}>瓶箱差额</Text></View>
                    </View>
                    {
                        data ? data.map((item,index)=> {
                            return this.renderRow(item,index)
                        }) : null
                    }
                </View>
            </View>
        );
    }
};


const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    leftTxtStyle: {
        height: Constant.scale(24),
        textAlign: 'right',
        color: Constant.colorTxtContent,
        fontSize: Constant.fontSizeNormal
    },
    rightTxtStyle: {
        height: Constant.scale(24),
        color: Constant.colorTxtTitle,
        fontSize: Constant.fontSizeNormal
    }
});