import React, { Component } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Dimensions,
  Alert,
  InteractionManager
} from "react-native";
import PropType from "prop-types";
import { ReactNavComponent, Widget } from "rn-yunxi";
import ScrollableTabView from "react-native-scrollable-tab-view";
import { inject, observer } from "mobx-react/native";
import OrderListItem from "./OrderListItem";
import AppWidget from "../../app-widget";

const { width, height } = Dimensions.get("window");
const { Header, Image, DefaultTabBar, CheckBox } = AppWidget;
const { CommonFlatList, Text } = Widget;
import { toJS } from "mobx";
import H5Util from "../../util/H5Util";
import UserStore from "../../store/User";
import OrderListStore from "../../store/OrderList";
import ToastUtil from "../../util/ToastUtil";
import ConfirmOrder from "./ConfirmOrder";
import ShopCartList from "../shopcart/ShopCartList";

/**
 * 订单列表
 */

@inject(stores => ({
  user: stores.user,
  orderUtil: stores.orderUtil
}))
@observer
export default class OrderList extends ReactNavComponent {
  //属性声名
  static propTypes = {};
  //默认属性
  static defaultProps = {};

  //构造函数
  constructor(props) {
    super(props);

    this.orderList = new OrderListStore();
    this.state = {
      //状态机变量声明
      awardsList: [],
      priceList: [],
      orderId: null
    };
  }

  reloadListData(tabIndex) {
    this.orderList
      .getOrderList(tabIndex, false)
      .then()
      .catch(err => {});
  }

  componentWillMount() {
    let { params } = this.getNavState();
    let type = params.type;
    if (type != 3) {
      type = this.props.user.isDealer() ? 1 : 2;
    }
    Log("type", type);
    this.orderList.setType(type);
    this.props.orderUtil.setType(type);
    // this.orderList.setTabIndex(params.tabIndex ? params.tabIndex : 0);
    let pageIndex = params.tabIndex ? params.tabIndex : 0;
    this.orderList.changeTabClick(pageIndex, false);
  }

  componentDidMount() {
    this.didBlurSubscription = this.props.navigation.addListener(
      "didBlur",
      payload => {
        console.debug("didBlur");
      }
    );

    this.willDidFocusSubscription = this.props.navigation.addListener(
      "didFocus",
      payload => {
        console.debug("didFocus");
        this.reloadListData(this.orderList.tabIndex);
      }
    );
  }

  componentWillUnmount() {
    // this.orderList.clearOrderList();
  }

  renderRow = ({ item, index }) => {
    let tabIndex = this.orderList.tabIndex;
    return (
      <OrderListItem
        data={item}
        orderType={this.orderList.type}
        checkBoxClick={selectIndex => {
          this.checkBoxClick(tabIndex, index, selectIndex);
        }}
        cancelOrder={() => {
          this.cancelOrder(item, tabIndex);
        }}
        payOrder={orderArray => {
          //this.pay(item);
          this.props.user.isDealer() && this.pay(orderArray, tabIndex);
        }}
        buyAgain={() => {
          this.buyAgain(item, tabIndex);
        }}
        confirmGoods={obj => {
          this.confirmGoods(obj, tabIndex);
        }}
        // evaluate={(obj) => {
        //     this.evaluate(item, obj);
        // }}
        applyAfterSale={obj => {
          this.applyAfterSale(obj, item);
        }}
        turnOrder={obj => {
          this.turnOrder(obj, item.orderId, item);
        }}
      />
    );
  };

  checkBoxClick = (selectTab, orderIndex, index) => {
    this.orderList.setCheckBox(selectTab, orderIndex, index);
  };

  /**
   * 取消订单
   * */
  cancelOrder = (item, tabIndex) => {
    Alert.alert("提示", "确定取消订单吗?", [
      { text: "取消" },
      {
        text: "确定",
        onPress: () => {
          this.props.orderUtil
            .cancelOrder({ orderId: item.orderId })
            .then(() => {
              this.reloadListData(tabIndex);
            })
            .catch(err => {});
        }
      }
    ]);
  };

  /**
   * 去支付
   * */
  pay = (orderArray, tabIndex) => {
    this.navigate("PayOrder", { order: orderArray });
  };

  /**
   * 再次购买
   * */
  buyAgain = (item, tabIndex) => {
    // Log('--------item-----',toJS(item))
    let itemIds = [];
    if (item.deliverys && item.deliverys.length > 0) {
      for (let itemObj of item.deliverys) {
        if (itemObj.items && itemObj.items.length > 0) {
          for (let obj of itemObj.items) {
            itemIds.push(obj.itemId);
          }
        }
      }
    }

    this.props.orderUtil.buyAgain(item.orderId, () => {
      this.navigate("ShopCartPage", {
        showBack: true,
        isRebuy: true,
        itemIds: itemIds
      });
    });
  };

  /**
   * 申请售后
   * */
  applyAfterSale = (item, data) => {
    let obj = Object.assign({}, data);
    obj.deliverys = [item];
    Log("to == ", toJS(obj), toJS(obj.deliverys[0]));
    this.navigate("ApplyAfterSale", { data: obj });
  };

  /**
   * 确认收货
   * */
  confirmGoods = (item, tabIndex) => {
    Alert.alert("提示", "确定确认收货吗?", [
      { text: "取消" },
      {
        text: "确定",
        onPress: () => {
          this.props.orderUtil
            .confirmReceive({ deliveryOrderNos: item.deliveryOrderNo })
            .then(() => {
              this.reloadListData(tabIndex);
            })
            .catch(err => {});
        }
      }
    ]);
  };

  /**
   * 转单
   * */
  turnOrder = (item, orderId, orderDetail) => {
    Log("----", toJS(item));
    let data = item.items;
    let paramsArray = [];
    if (data.length > 0) {
      for (let item of data) {
        paramsArray.push({
          itemId: item.itemId,
          orgId: item.orgId
        });
      }
      this.getAwardsListAndPriceList(paramsArray, () => {
        InteractionManager.runAfterInteractions(() => {
          this.navigate("GoodsDetailList", {
            showBottomView: true,
            orderDetail: orderDetail,
            data: item.items,
            orderId: orderId,
            isTurnOrder: true,
            awardsList: this.state.awardsList,
            priceList: this.state.priceList
          });
        });
      })
        .then()
        .catch(e => {});
    }

    // this.props.orderUtil.turnOrder(orderId, (data) => {
    //     this.navigate('ConfirmOrder', {
    //         data: data,
    //         type: 3,
    //         orderId: orderId,
    //         callback: () => {
    //            this.reloadListData();
    //         }
    //     });
    // }).then().catch(err => {
    // })
  };

  getAwardsListAndPriceList = async (params, callback) => {
    let result1 = await Api.getAwardList(params);
    let result2 = await Api.getPriceList(params);
    if (result1 && result1.data) {
      this.setState({
        awardsList: result1.data
      });
    }

    if (result2 && result2.data) {
      this.setState({
        priceList: result2.data
      });
    }

    callback && callback();
  };

  renderTab = () => {
    return (
      <View
        style={{
          width,
          flexDirection: "row",
          backgroundColor: "#fff",
          height: Constant.scale(44)
        }}
      >
        <ScrollableTabView
          style={{ flex: 1 }}
          locked={true}
          initialPage={this.orderList.tabIndex}
          scrollWithoutAnimation={true}
          renderTabBar={() => {
            return (
              <DefaultTabBar
                activeTextColor={Constant.colorTxtPrimary}
                textStyle={{ fontSize: Constant.fontSizeNormal }}
                inactiveTextColor="#777777"
              />
            );
          }}
          onChangeTab={obj => {
            this.orderList.setTabIndex(obj.i);
            this.orderList.changeTabClick(obj.i, true);
          }}
        >
          {this.orderList.getTabs.map((obj, i) => {
            return <Text key={`tabkey+${i}`} tabLabel={obj.label} />;
          })}
        </ScrollableTabView>
        <View
          style={{
            height: Constant.scale(38),
            backgroundColor: Constant.colorDivider,
            width: Constant.sizeDividerNormal,
            marginTop: Constant.scale(3)
          }}
        />
        <TouchableOpacity
          onPress={() => {
            this.navigate("OrderFilter", {
              callback: obj => {
                this.orderList.setSearchParams(obj);
                this.reloadListData(this.orderList.tabIndex);
              },
              searchParams: this.orderList.searchParams
            });
          }}
        >
          <View
            style={{
              width: Constant.scale(60),
              alignItems: "center",
              justifyContent: "center",
              height: Constant.scale(43)
            }}
          >
            <Text
              style={{
                fontSize: Constant.fontSizeNormal,
                color: Constant.colorTxtTitle
              }}
            >
              筛选
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  renderListView = () => {
    let tabIndex = this.orderList.tabIndex;
    return (
      <CommonFlatList
        style={{ flex: 1 }}
        data={this.orderList.listParamsArray[tabIndex].data}
        listState={this.orderList.listParamsArray[tabIndex].listState}
        enableLoadMore={this.orderList.listParamsArray[tabIndex].enableLoadMore}
        keyExtractor={(item, index) => {
          return "tab" + tabIndex + "goods" + index;
        }}
        renderItem={this.renderRow}
        onLoadMore={() => {
          this.orderList
            .getOrderList(tabIndex, true)
            .then()
            .catch(err => {});
        }}
        enableRefresh={true}
        onRefresh={() => {
          this.orderList
            .getOrderList(tabIndex, false)
            .then()
            .catch(err => {});
        }}
      >
        <View
          style={[{ flex: 1, justifyContent: "center", alignItems: "center" }]}
        >
          <Text
            style={{
              fontSize: Constant.fontSizeBig,
              color: Constant.colorTxtContent,
              marginTop: Constant.scale(25)
            }}
          >
            暂无数据
          </Text>
        </View>
      </CommonFlatList>
    );
  };

  //渲染
  render() {
    let title = this.orderList.type == 3 ? "分销商订单" : "订单列表";
    // Log('this.orderList.tabIndex',toJS(this.orderList.tabIndex))
    return (
      <Header title={title} navigation={this.props.navigation}>
        <View style={styles.container}>
          {this.renderTab()}
          {this.renderListView()}
        </View>
      </Header>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  }
});
