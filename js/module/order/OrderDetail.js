/**
 * Created by whw on 2018/1/23.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    ScrollView,
    ImageBackground,
    Switch,
    Alert,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget, Util} from 'rn-yunxi';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import {inject, observer} from 'mobx-react/native';
import AppWidget from '../../app-widget';
import OrderDetailModel from '../../store/OrderDetail';
import OrderGoodsItem from './OrderGoodsItem';
import OutPocketDetail from './OutPocketDetail';
import InvoiceInfo from './InvoiceInfo';
import BottleBanlanceDetail from './BottleBalanceDetail';
import MergerOrder from './MergerOrder'
import {toJS} from "mobx";

const {width, height} = Dimensions.get('window');
const {Header, Image, Button, PriceText} = AppWidget;
const {Text, LabelCell, LabelView} = Widget;
const IC_BANNER = require('../img/order/ic_banner.png');
const IC_ADDRESS = require('../img/order/ic_adress.png');
const IC_SHOP_ICON = require('../img/order/ic_shop_icon.png');

const IC_STATUS_CANCEL = require('../img/order/ic_status_cancel.png');
const IC_STATUS_CHECK = require('../img/order/ic_status_check.png');
const IC_STATUS_FINISH = require('../img/order/ic_status_finish.png');
const IC_STATUS_PAY = require('../img/order/ic_status_pay.png');
const IC_STATUS_RECEIVE = require('../img/order/ic_status_receive.png');
const IC_STATUS_REFUND = require('../img/order/ic_status_refund.png');
const IC_STATUS_SEND = require('../img/order/ic_status_send.png');

/**
 * 订单详情
 */
@inject(stores => ({
    orderUtil: stores.orderUtil,
    user: stores.user
}))
@observer
export default class OrderDetail extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        let {params} = this.getNavState();
        this.state = {//状态机变量声明
            orderType : params && params.orderType,
        };
        this.orderDetail = new OrderDetailModel();
    }

    componentWillMount() {
    }

    componentDidMount() {
        let {params} = this.getNavState();
        this.orderDetail.getOrderDetail(params.orderId).catch(err => {
        });
    }

    componentWillUnmount() {
    }

    showOutPocket = () => {
        Widget.Popup.show(<OutPocketDetail data={this.orderDetail.data.payInfo} result={this.orderDetail.data}
                                           pageType={1} cancel={() => {
                Widget.Popup.hide();
            }}/>,
            {
                animationType: 'slide-up', backgroundColor: '#00000000',
                onMaskClose: () => {
                    Widget.Popup.hide()
                }
            })
    }

    showInvoiceInfo = () => {

        Widget.Popup.show(<InvoiceInfo data={this.orderDetail.data.invoiceDto} cancel={() => {
                Widget.Popup.hide();
            }}/>,
            {
                animationType: 'slide-up', backgroundColor: '#00000000',
                onMaskClose: () => {
                    Widget.Popup.hide()
                }
            })
    }

    /**
     * 取消订单
     * */
    cancelOrder = (item) => {
        Alert.alert('提示', '确定取消订单吗?', [{text: '取消'}, {
            text: '确定', onPress: () => {
                this.props.orderUtil.cancelOrder({orderId: this.orderDetail.data.orderId}, () => {
                    this.orderDetail.getOrderDetail(this.orderDetail.data.orderId).catch(err => {
                    });
                });
            }
        }]);
    }

    /**
     * 去支付
     * */
    pay = () => {
        this.props.orderUtil.getMergerOrderList(this.orderDetail.data.orderId).then((data) => {
            Widget.Popup.show(
                <MergerOrder OrderList={data}
                             callBack={(orderArray) => {
                                 // if (this.props.payOrder) {
                                 if (orderArray[0].orderIds.length === 0) {
                                     Alert.alert(null, '请选择支付订单!', [{text: '确定'}]);
                                     return;
                                 }
                                 Widget.Popup.hide();
                                 InteractionManager.runAfterInteractions(() => {
                                     this.navigate('PayOrder', {order: orderArray});
                                 })
                                 // }
                             }}/>,

                {
                    animationType: 'slide-up', backgroundColor: '#ffffff',
                    onMaskClose: () => {
                        Widget.Popup.hide()
                    }
                }
            )
        }).catch(err => {
        });
        // this.navigate('PayOrder',{orderId: this.orderDetail.data.orderId});
    }

    /**
     * 再次购买
     * */
    buyAgain = (deliverys) => {
        let itemIds = [];
        if (deliverys && deliverys.length > 0) {
            for (let itemObj of deliverys) {
                if (itemObj.items && itemObj.items.length > 0) {
                    for (let obj of itemObj.items) {
                        itemIds.push(obj.itemId);
                    }
                }

            }
        }

        this.props.orderUtil.buyAgain(this.orderDetail.data.orderId, () => {
            this.navigate('ShopCartPage', {showBack: true, isRebuy: true, itemIds: itemIds});
        });
    }

    /**
     * 转单
     */
    turnOrder = () => {
        let {params} = this.getNavState();
        if (params && params.turnOrderClickCallback){
            params.turnOrderClickCallback()
        }
        // this.props.orderUtil.turnOrder(this.orderDetail.data.orderId, (data) => {
        //     this.navigate('ConfirmOrder', {
        //         data: data,
        //         type: 3,
        //         orderId: this.orderDetail.data.orderId,
        //         callback: () => {
        //             this.orderDetail.getOrderDetail(this.orderDetail.data.orderId).catch(err => {
        //             });
        //         }
        //     });
        // }).then().catch(err => {
        // })
    }

    /**
     * 展示瓶箱明细
     * */

    showBottleInfo = (data) => {
        Widget.Popup.show(<BottleBanlanceDetail
                data={data}
            />,
            {
                animationType: 'slide-up', backgroundColor: '#00000000',
                onMaskClose: () => {
                    Widget.Popup.hide()
                }
            })
    }

    renderStatus = (status) => {
        let statusText = '';
        {/*0：待转单 1：待付款，2：待审核 31：待配货 4：待发货 41：待收货 6：已完成 9：已取消 91：已退单 */
        }
        switch (status) {
            case 0:
                statusText = '待转单';
                break;
            case 1:
                statusText = '待付款';
                break;
            case 2:
                statusText = '待审核';
                break;
            case 6:
                statusText = '已完成';
                break;
            case 9:
                statusText = '已取消';
                break;
            case 91:
                statusText = '已退单';
                break;
        }
        return statusText;
    }

    //渲染
    render() {
        let isDealer = this.props.user && this.props.user.isDealer();
        let data = this.orderDetail.data;
        let deliverys = data && data.deliverys ? data.deliverys : [];
        let iconStatus = null;
        let status = data ? data.status : -1;
        switch (status) {
            case 0://待转单
                break;
            case 1://待付款
                iconStatus = IC_STATUS_PAY;
                break;
            case 2://待审核
                iconStatus = IC_STATUS_CHECK;
                break;
            case 31://待发货
                iconStatus = IC_STATUS_SEND;
                break;
            case 4://待收货
                iconStatus = IC_STATUS_RECEIVE;
                break;
            case 5://已收货
                iconStatus = IC_STATUS_FINISH;
                break;
            case 6://已完成
                iconStatus = IC_STATUS_FINISH;
                break;
            case 9://已取消
                iconStatus = IC_STATUS_CANCEL;
                break;
            default:
                break;
        }

        let statusText = '';
        if (data) {
            statusText = this.renderStatus(data.status);
        }

        let expectedDeliveryDate = '暂无';
        if (data && data.expectedDeliveryDate){
          let tmp =   Util.DateUtil.parserTimestamp(data.expectedDeliveryDate);
          expectedDeliveryDate =  Util.DateUtil.formatDate(tmp, 'yyyy-MM-dd')
        }

        return (
            <Header style={styles.container} title="订单详情">
                {
                    data ? <View style={{flex: 1}}>
                            <ScrollView style={{flex: 1, backgroundColor: Constant.colorBackgroundDefault}}>

                                <ImageBackground style={{
                                    width: width,
                                    height: Constant.scale(80),
                                    flexDirection: 'row',
                                    alignItems: 'center'
                                }} source={IC_BANNER}>
                                    <Image style={{
                                        width: Constant.scale(50),
                                        height: Constant.scale(50),
                                        marginLeft: Constant.scale(33.5)
                                    }}
                                           source={iconStatus}/>
                                    <View style={{flex: 1, marginLeft: Constant.scale(37)}}>
                                        {statusText != '' &&
                                        <Text style={{fontSize: Constant.fontSizeCaption, color: '#fff'}}>
                                            {statusText}
                                        </Text>}
                                        {
                                            data.zpOrderTypeName ?  <Text style={{
                                                fontSize: Constant.fontSizeSmall,
                                                color: '#fff',
                                                marginTop: Constant.scale(5)
                                            }}>
                                                {data.zpOrderTypeName}
                                            </Text>:null
                                        }
                                        <Text style={{
                                            fontSize: Constant.fontSizeSmall,
                                            color: '#fff',
                                            marginTop: Constant.scale(5)
                                        }}>
                                            订单号:{data.orderNo}
                                        </Text>
                                    </View>
                                </ImageBackground>
                                {/*地址信息*/}
                                <View style={{width: width, backgroundColor: Constant.colorDefault}}>
                                    <View style={{
                                        width: width,
                                        height: Constant.scale(44),
                                        alignItems: 'center',
                                        flexDirection: 'row'
                                    }}>
                                        <Text style={{
                                            fontSize: Constant.fontSizeNormal,
                                            color: Constant.colorTxtContent,
                                            marginLeft: Constant.scale(33)
                                        }}>
                                            {data.addressDto && data.addressDto.deliveryName}
                                        </Text>
                                        <Text style={{
                                            fontSize: Constant.fontSizeNormal,
                                            color: Constant.colorTxtContent,
                                            marginLeft: Constant.scale(18)
                                        }}>
                                            {data.addressDto && data.addressDto.deliveryMobile}
                                        </Text>
                                    </View>
                                    <View style={{width: width, flexDirection: 'row'}}>
                                        <Image style={{
                                            marginLeft: Constant.scale(14),
                                            width: Constant.scale(13),
                                            height: Constant.scale(16)
                                        }} source={IC_ADDRESS}/>
                                        <Text
                                            style={{
                                                fontSize: Constant.fontSizeSmall,
                                                color: Constant.colorTxtContent,
                                                margin: Constant.sizeMarginDefault,
                                                marginLeft: Constant.scale(5),
                                                marginTop: 0
                                            }}>
                                            {data.addressDto && data.addressDto.address}
                                        </Text>
                                    </View>
                                    {
                                        this.props.user.isDealer() ?
                                        null
                                            // data.buyerType == 5 || data.buyerType == 2 || data.buyerType == 4 ?
                                            //     <View style={{marginLeft: Constant.scale(15)}}>
                                            //         <Text style={{
                                            //             fontSize: Constant.fontSizeSmall,
                                            //             color: Constant.colorTxtContent,
                                            //             marginBottom: Constant.scale(5),
                                            //         }}>分销商：{data.distributorName || '暂无'}</Text>
                                            //         {
                                            //             data.distributorOrderNo ? <Text style={{
                                            //                 fontSize: Constant.fontSizeSmall,
                                            //                 color: Constant.colorTxtContent,
                                            //                 marginBottom: Constant.scale(5),
                                            //             }}>分销商订单号：{data.distributorOrderNo}</Text> : null
                                            //         }
                                            //
                                            //     </View>
                                            //     : null
                                            :
                                            data.buyerType == 5 || data.buyerType == 2 ?
                                                <View style={{marginLeft: Constant.scale(15)}}>
                                                    <Text style={{
                                                        fontSize: Constant.fontSizeSmall,
                                                        color: Constant.colorTxtContent,
                                                        marginBottom: Constant.scale(5),
                                                    }}>经销商：{data.dealerName || '暂无'}</Text>

                                                </View>
                                                : null
                                    }
                                </View>

                                <View style={{
                                    width: width,
                                    backgroundColor: Constant.colorDefault,
                                    marginTop: Constant.sizeMarginDefault
                                }}>

                                    <View style={{
                                        width: width,
                                        height: Constant.scale(40),
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        justifyContent: 'center'
                                    }}>
                                        <Image style={{
                                            width: Constant.scale(17),
                                            height: Constant.scale(15),
                                            marginLeft: Constant.sizeMarginDefault
                                        }} source={IC_SHOP_ICON}/>
                                        <Text style={{
                                            flex: 1,
                                            color: Constant.colorTxtTitle,
                                            fontSize: Constant.fontSizeNormal,
                                            marginLeft: Constant.sizeMarginDefault
                                        }}>
                                            {data.supplierOrgName}
                                        </Text>
                                    </View>
                                    <View style={{
                                        width: width,
                                        height: Constant.scale(1),
                                        backgroundColor: Constant.colorDividerDefault
                                    }}/>
                                    <View>
                                        {
                                            data.deliverys ? data.deliverys.map((item, index) => {
                                                return (<View key={`orderDetail${index}`}>
                                                    {
                                                        item.items && item.items.map((obj, index1) => {
                                                            return <OrderGoodsItem data={obj}
                                                                                   orderType = {this.state.orderType}
                                                                                   isDealer={isDealer}
                                                                                   buyerType={data && data.buyerType}
                                                                                   key={`orderDetail${index}+${index1}`}/>
                                                        })
                                                    }
                                                </View>)
                                            }) : null
                                        }
                                    </View>
                                    <LabelCell titleTextStyle={{color: Constant.colorTxtContent}}
                                               title="配送方式"
                                               underLine={1}
                                               rightIcon={<View/>}
                                               extra={<Text style={{
                                                   color: Constant.colorTxtTitle,
                                                   fontSize: Constant.fontSizeNormal
                                               }}>{data.deliveryMethodName}</Text>}
                                    />
                                    <LabelCell titleTextStyle={{color: Constant.colorTxtContent}}
                                               title="期望发货日期"
                                               underLine={1}
                                               rightIcon={<View/>}
                                               extra={<Text style={{
                                                   color: Constant.colorTxtTitle,
                                                   fontSize: Constant.fontSizeNormal
                                               }}>
                                                   {expectedDeliveryDate}
                                               </Text>}
                                    />
                                    {
                                        data.payInfo.marketingChargeAmount !== 0 && isDealer ?
                                            <LabelCell titleTextStyle={{color: Constant.colorTxtContent}}
                                                       title={
                                                           <Text style={{
                                                               color: Constant.colorTxtContent,
                                                               fontSize: Constant.fontSizeNormal
                                                           }}>
                                                               使用营销费用：
                                                               <PriceText
                                                                   price={data.payInfo.marketingChargeAmount}/>
                                                           </Text>
                                                       }
                                                       underLine={1}
                                                       rightIcon={<View/>}
                                                       extra={null}/> : null
                                    }
                                    {
                                        data.bottleFees.length > 0 && isDealer ?
                                            <View style={{
                                                width: width,
                                                flexDirection: 'row',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                marginRight: Constant.sizeMarginDefault
                                            }}>
                                                <View style={{margin: Constant.sizeMarginDefault, flex: 1}}>
                                                    <Text style={{
                                                        fontSize: Constant.fontSizeNormal,
                                                        color: Constant.colorTxtContent
                                                    }}>使用瓶箱余额</Text>
                                                    <View style={{
                                                        marginTop: Constant.scale(5),
                                                        flexDirection: 'row',
                                                        alignItems: 'flex-end'
                                                    }}>
                                                        <Text style={{
                                                            fontSize: Constant.fontSizeSmall,
                                                            color: Constant.colorTxtAlert,
                                                        }}>瓶箱按金费用小计:
                                                        </Text>
                                                        <PriceText
                                                            size={2}
                                                            price={(data.bottleFees[0].bottlePayAmount).toFixed(2)}/>
                                                        <TouchableOpacity
                                                            style={{height: '100%'}}
                                                            onPress={() => {
                                                                this.showBottleInfo(data.bottleFees)
                                                            }}>
                                                            <Text style={{

                                                                color: Constant.colorTxtContent,
                                                                marginLeft: Constant.sizeMarginDefault
                                                            }}>
                                                                详情
                                                            </Text>
                                                        </TouchableOpacity>


                                                    </View>
                                                </View>
                                                {/*<Switch style={{marginRight: Constant.sizeMarginDefault}} value={this.orderDetail.isUseBottle} onValueChange={(value)=> {this.orderDetail.setIsUseBottle(value)}}/>*/}
                                            </View> : null
                                    }

                                    <LabelCell title={'备注:'}
                                               extra={data.remark || '暂无'}
                                               underLine={1}
                                               showRightIcon={false}
                                               titleTextStyle={{color: Constant.colorTxtContent}}
                                               contentStyle={{
                                                   color: Constant.colorTxtTitle,
                                                   fontSize: Constant.fontSizeNormal
                                               }}/>

                                    {
                                        !isDealer && (data && data.buyerType == 5) ? null :
                                            <LabelCell title={
                                                <Text style={{
                                                    color: Constant.colorTxtContent,
                                                    fontSize: Constant.fontSizeNormal
                                                }}>
                                                    实付款：<PriceText price={data.payInfo.totalAmount}/>

                                                </Text>}
                                                       underLine={2}
                                                       extra={<Text style={{
                                                           color: Constant.colorTxtAlert,
                                                           fontSize: Constant.fontSizeNormal
                                                       }}>
                                                           明细
                                                       </Text>}
                                                       onClick={() => {
                                                           this.showOutPocket();
                                                       }}
                                            />
                                    }
                                </View>

                                {
                                    isDealer && (data && data.status != 0) ?
                                        <View style={{width: width, marginTop: Constant.sizeMarginDefault}}>
                                            <LabelCell
                                                titleTextStyle={{color: Constant.colorTxtContent}}
                                                title="发票类型"
                                                underLine={1}
                                                showRightIcon={false}
                                                extra={
                                                    <Text style={{
                                                        color: Constant.colorTxtAlert,
                                                        fontSize: Constant.fontSizeNormal
                                                    }}>
                                                        {data.invoiceDto && data.invoiceDto.invoiceTypeName}
                                                    </Text>
                                                }

                                            />
                                            <LabelCell
                                                titleTextStyle={{color: Constant.colorTxtContent}}
                                                title="开票信息"
                                                underLine={1}
                                                extra={
                                                    <Text style={{
                                                        color: Constant.colorTxtAlert,
                                                        fontSize: Constant.fontSizeNormal
                                                    }}>
                                                        {data.invoiceDto && data.invoiceDto.orgName}
                                                    </Text>
                                                }
                                                onClick={() => {
                                                    this.showInvoiceInfo();
                                                }}
                                            />
                                        </View> : null
                                }

                            </ScrollView>


                            <View style={{
                                width: width,
                                height: Constant.scale(44),
                                alignItems: 'center',
                                justifyContent: 'flex-end',
                                flexDirection: 'row',
                                backgroundColor: Constant.colorDefault
                            }}>
                                {
                                    data.buyerType == 5 && !isDealer ? null :
                                        <Button style={{borderRadius: 5, borderColor: Constant.colorTxtAlert}}
                                                txtStyle={{fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert}}
                                                onPress={() => this.buyAgain(deliverys)}
                                        >
                                            再次购买
                                        </Button>
                                }
                                {
                                    isDealer ?
                                    (data.status == 0 || data.status == 1)?
                                    <Button style={{borderRadius: 5, borderColor: Constant.colorTxtAlert}}
                                            txtStyle={{
                                                fontSize: Constant.fontSizeSmall,
                                                color: Constant.colorTxtAlert
                                            }}
                                            onPress={this.cancelOrder}
                                    >
                                        取消订单
                                    </Button> : null
                                             :
                                    ((data.status == 0|| data.status == 1)&& data.buyerType != 5)?
                                    <Button style={{borderRadius: 5, borderColor: Constant.colorTxtAlert}}
                                            txtStyle={{
                                                fontSize: Constant.fontSizeSmall,
                                                color: Constant.colorTxtAlert
                                            }}
                                            onPress={this.cancelOrder}
                                    >
                                        取消订单
                                    </Button> : null

                                }

                                {/*{(data.status == 0 || data.status == 1) && data.buyerType != 5 ?*/}
                                    {/*<Button style={{borderRadius: 5, borderColor: Constant.colorTxtAlert}}*/}
                                            {/*txtStyle={{*/}
                                                {/*fontSize: Constant.fontSizeSmall,*/}
                                                {/*color: Constant.colorTxtAlert*/}
                                            {/*}}*/}
                                            {/*onPress={this.cancelOrder}*/}
                                    {/*>*/}
                                        {/*取消订单*/}
                                    {/*</Button> : null}*/}

                                {data.status == 1 && this.props.user.isDealer() ?
                                    <Button style={{borderRadius: 5, borderColor: Constant.colorTxtPrimary}}
                                            txtStyle={{fontSize: Constant.fontSizeSmall, color: Constant.colorTxtPrimary}}
                                            onPress={this.pay}
                                    >
                                        去付款
                                    </Button> : null}

                                {
                                    data.status == 0 && this.props.user.isDealer() ?
                                        <Button style={{borderRadius: 5, borderColor: Constant.colorTxtPrimary}}
                                                txtStyle={{
                                                    fontSize: Constant.fontSizeSmall,
                                                    color: Constant.colorTxtPrimary
                                                }}
                                                onPress={() => this.turnOrder()}
                                        >
                                            转单
                                        </Button> : null

                                }
                            </View>
                        </View>
                        : null
                }
            </Header>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Constant.colorBackgroundDefault
    },

});