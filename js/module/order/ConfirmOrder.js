import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    ScrollView,
    TextInput,
    Switch,
    Alert,
    Dimensions,
    StatusBar,
    InteractionManager,
} from 'react-native';
import PropType from 'prop-types';
import { ReactNavComponent, Widget, Util } from 'rn-yunxi';
import AppWidget from '../../app-widget';
import { inject, observer } from 'mobx-react/native';
import ScrollableTabView from 'react-native-scrollable-tab-view';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import MarketingAmountPanel from './MarketingAmountPanel';
import { toJS } from 'mobx';
import _ from 'lodash';
import ADDRESS_LINE from '../img/order/addressLine.png';
import ADDRESS_ICON from '../img/order/adressIcon.png';
import ConfirmOrderStore from '../../store/ConfirmOrder';

const { width, height } = Dimensions.get('window');
const { Header, Image, CheckBox, PriceText, DefaultTabBar, DateView, SelectPop, ModalInputView } = AppWidget;
const { Text, LabelCell, InputFormCell } = Widget;

import InvoiceInfo from './InvoiceInfo';
import OutPocketDetail from './OutPocketDetail';
import BottleBanlanceDetail from './BottleBalanceDetail';
import ToastUtil from '../../util/ToastUtil';
import { NavigationActions } from 'react-navigation';
import ValidateUtil from '../../util/ValidatorUtil';
import ModalMarginBalanceView from '../margin-balance/ModalMarginBalanceView';
import { Platform } from 'react-native';
import { SafeAreaView } from 'react-navigation';

const invoicetypeData = [
    { title:'普票', type:1 },
    { title:'专票', type:2 },
];
const invoicetypeData2 = [
    { title:'普票', type:1 },
];


/**
 * User:lao.jianfeng
 * 规格面板
 */
@inject(stores => ({
    invoice:stores.invoice,
    user:stores.user,
    address:stores.address,
    pay:stores.pay,
}))
@observer
export default class ConfirmOrder extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
            title:'普票',
            type:1,
            modalVisible:false,
            marginListDataArray:[],

        };
        let { params } = this.getNavState();
        this.confirmOrder = new ConfirmOrderStore();
        if (params.data) {
            let orders = params.data.orderDtos;
            Log('中间的orders',orders)
            this.confirmOrder.setZpOrderType(params.data.zpOrderType);
            this.confirmOrder.setOrders(orders);
            // this.confirmOrder.setAddress(params.data.)

        }

        this.type = _.get(this.getNavState(), 'params.type', 1);//1：立即购买（包含费用赠酒，瓶盖赠酒，快捷下单）、2：购物车3：转开单
    }


    componentWillMount() {
        this.props.pay.cleanPayInfo();
        if (this.type != 3) {
            this.props.address.obtainAddressList(false, () => {
                if (this.confirmOrder.address == null) {
                    if (this.props.address.listParams.dataArray &&
                        this.props.address.listParams.dataArray.length > 0) {
                        this.confirmOrder.setAddress(this.props.address.listParams.dataArray[ 0 ]);
                    }
                }

            }).then().catch();
        } else {
            const data = _.get(this.getNavState(), 'params.data', {});
            if (data && data.orderDtos && data.orderDtos.length > 0 && data.orderDtos[ 0 ].receivingAddressId) {
                this.confirmOrder.getAddress(data.orderDtos[ 0 ].receivingAddressId).then().catch();
            }
        }

        this.props.user && this.props.user.isDealer() ?
            this.props.invoice.getInvoice().then(() => {
                this.confirmOrder.setOrderInvoice(this.props.invoice.data);
                if (this.props.invoice.data) {
                    if (this.props.invoice.data.invoiceType == '专票')
                    {
                        this.setState({
                            title:'专票',
                            type:2,
                        });
                    }else
                    {
                        this.setState({
                            title:'普票',
                            type:1,
                        });
                    }
                }
            }).catch(err => {
            })
            : null;

    }

    componentDidMount() {


    }

    componentWillUnmount() {
    }

    /**
     * 地址UI
     * @return {XML}
     */
    renderAddress() {
        const address = this.confirmOrder.address;

        if (address) {
            return (
                <TouchableOpacity
                    disabled={this.type == 3}
                    onPress={() => this.navigate('AddressList', {
                        type:'Choose', callBack:(item) => {
                            this.confirmOrder.setAddress(item);
                        },
                    })}
                    style={addressStyles.background}
                >
                    <View style={{ flexDirection:'row', flex:1 }}>

                        <View style={{ flex:1, alignSelf:'center' }}>
                            <View
                                style={{
                                    flexDirection:'row',
                                    justifyContent:'space-between',
                                    marginLeft:Constant.scale(23),
                                }}
                            >
                                <Text
                                    style={[
                                        addressStyles.nameText,
                                        { fontSize:Constant.fontSizeBig },
                                    ]}
                                >{address.contactLevel} {address.contactMobile}</Text>
                                <Text
                                    style={{
                                        height:Constant.scale(18),
                                        textAlign:'center',
                                        paddingTop:Constant.scale(1),
                                        paddingBottom:Constant.scale(1),
                                        paddingLeft:Constant.scale(1.5),
                                        paddingRight:Constant.scale(1.5),
                                        backgroundColor:Constant.colorPrimary,
                                        marginRight:Constant.scale(20),
                                        color:'white',
                                        fontSize:Constant.fontSizeSmall,
                                        borderRadius:Constant.scale(3),
                                    }}
                                >{address.abbreviation}</Text>
                            </View>

                            <View style={{ flexDirection:'row', marginTop:5 }}>
                                <Image

                                    style={{
                                        width:Constant.scale(13),
                                        marginRight:Constant.scale(10),
                                        height:Constant.scale(16),
                                        marginTop:Constant.scale(1),
                                    }}
                                    source={ADDRESS_ICON}
                                />
                                <Text
                                    numberOfLines={2}
                                    style={[
                                        addressStyles.addressText,
                                        { fontSize:Constant.fontSizeSmall },
                                    ]}
                                >收货地址：{address.address}</Text>
                            </View>
                        </View>
                        <View style={{ alignSelf:'center', marginRight:Constant.sizeMarginDefault }}>
                            {this.type != 3 && <Image

                                source={require('../img/ic_center_right_arrow.png')}
                                style={{
                                    width:Constant.scale(6),
                                    height:Constant.scale(12),
                                    alignItems:'center',
                                }}
                            />}
                        </View>
                    </View>

                    <Image
                        style={{ width:width, height:5, position:'absolute', bottom:0, left:0 }}
                        source={ADDRESS_LINE}
                    />
                </TouchableOpacity>
            );
        } else {
            return (
                <TouchableOpacity
                    disabled={this.type == 3}
                    onPress={() => this.navigate('AddressList', {
                        type:'Choose', callBack:(item) => {
                            this.confirmOrder.setAddress(item);
                        },
                    })}
                    style={[
                        addressStyles.background,
                        { justifyContent:'center' },
                    ]}
                >
                    <Text style={[ addressStyles.nameText ]}>{this.type == 3 ? '暂无地址' : '请先选择收货地址'}</Text>
                    <Image
                        style={{ width:width, height:5, position:'absolute', bottom:0, left:0 }}
                        source={ADDRESS_LINE}
                    />

                </TouchableOpacity>
            );

        }
    }

    /**
     * 下单提货组织列表
     * @return {XML}
     */
    renderOrder() {
        let { params } = this.getNavState();
        let isTurnOrder = false;
        if (params && params.type && params.type + '' == '3') {
            isTurnOrder = true;
        }
        let maxDate = new Date();
        maxDate.setDate(maxDate.getDate() + 30);

        let zpOrderType = _.get(this.getNavState(), 'params.data.zpOrderType', 1);
        Log('render Order');
        return (
            <View style={{ width:width }}>
                {
                    this.confirmOrder.orders.map((item, index) => {
                        let orgName = item.itemDtos && item.itemDtos.length > 0 ? item.itemDtos[ 0 ].orgName : null;
                        Log('====shijian ',toJS(item.expectedDeliveryDate))
                        let tmp = item.expectedDeliveryDate ? Util.DateUtil.parserTimestamp(item.expectedDeliveryDate):null
                        let date = item.expectedDeliveryDate ? Util.DateUtil.formatDate(tmp, 'yyyy-MM-dd') : '配送时间';
                        let bottleAmount = this.confirmOrder.getRealBottleAmount(item);
                        //外币单属性
                        let foreignCurrency = item.foreignCurrency;
                        let freight = foreignCurrency.freight;//运费
                        let premium = foreignCurrency.premium;//保费
                        let incidentals = foreignCurrency.incidentals;//杂费
                        let total = foreignCurrency.total;//合计
                        let contractCode = foreignCurrency.contractCode;//合同编码
                        return (
                            <View
                                style={{
                                    width:width,
                                    backgroundColor:'white',
                                    marginBottom:Constant.sizeMarginDefault,
                                }}
                                key={`confirm${index}`}
                            >
                                <View
                                    style={{
                                        marginLeft:Constant.sizeMarginDefault,
                                        height:Constant.scale(40),
                                        flexDirection:'row',
                                        alignItems:'center',
                                    }}
                                >
                                    <Image
                                        style={{
                                            marginRight:Constant.sizeMarginDefault,
                                            width:Constant.scale(17),
                                            height:Constant.scale(15),
                                        }}
                                        source={require('../img/order/ic_shop_icon.png')}
                                    />
                                    <Text style={{ color:Constant.colorTxtContent }}>{orgName}</Text>
                                </View>
                                <View style={GlobalStyle.styleDividerDefault} />
                                <TouchableOpacity
                                    style={{
                                        padding:Constant.sizeMarginDefault,
                                        flexDirection:'row',
                                        alignItems:'center',
                                    }}
                                    onPress={() => {
                                        this.navigate('GoodsDetailList', {
                                            data:item.itemDtos,
                                            isTurnOrder:isTurnOrder,
                                            showBottomView:false,
                                            isConfirmOrder:true
                                        });
                                    }}
                                >
                                    {
                                        item.itemDtos && item.itemDtos.map((goods, index) => {
                                            return (
                                                <View
                                                    key={`good${index}`}
                                                    style={{ marginRight:Constant.scale(6) }}
                                                >
                                                    <Image
                                                        source={{ uri:goods.imgUrl }}
                                                        style={{
                                                            borderWidth:Constant.sizeDividerNormal,
                                                            borderColor:Constant.colorDividerDefault,
                                                            width:Constant.scale(84),
                                                            height:Constant.scale(84),
                                                        }}
                                                    />
                                                </View>
                                            );
                                        })
                                    }


                                    <View style={{ flex:1 }} />

                                    <Text
                                        style={{
                                            marginRight:Constant.sizeMarginDefault,
                                            color:Constant.colorTxtAlert,
                                        }}
                                    >共{item.itemDtos.length}种</Text>
                                    <Image
                                        source={require('../img/ic_center_right_arrow.png')}
                                        style={{
                                            width:Constant.scale(6),
                                            height:Constant.scale(12),
                                            alignItems:'center',
                                        }}
                                    />
                                </TouchableOpacity>
                                <View
                                    style={[
                                        GlobalStyle.styleDividerDefault,
                                        { marginLeft:Constant.sizeMarginDefault },
                                    ]}
                                />
                                <LabelCell
                                    underLine={1}
                                    title={'配送方式'}
                                    rightIcon={<View />}
                                    extra={item.deliveryMethodName ? item.deliveryMethodName : ' '}
                                />

                                <LabelCell
                                    onClick={() => {
                                        Widget.Popup.show(<DateView
                                            minDate={new Date()}
                                            maxDate={maxDate}
                                            initDate={new Date()}
                                            onDismiss={() => {
                                                Widget.Popup.hide();
                                            }
                                            }
                                            onConfirm={(date) => {
                                                let newDate = date || new Date()
                                                let tmp = Util.DateUtil.parserStandardTime(newDate)
                                                Widget.Popup.hide();
                                                this.confirmOrder.setExpectedDeliveryDate(index, tmp);
                                            }}
                                        />, {
                                            animationType:'slide-up', backgroundColor:'#00000000',
                                            onMaskClose:() => {
                                                Widget.Popup.hide();
                                            },
                                        });
                                    }}
                                    showRightIcon={this.type != 3}
                                    disabled={this.type == 3}
                                    underLine={1}
                                    title={'配送日期'}
                                    extra={date}
                                />

                                {this.props.user.isDealer() && zpOrderType == 1 ?
                                    <View style={{ width:width }}>
                                        <TouchableOpacity
                                            style={{ height:Constant.scale(67.5) }}
                                            onPress={() => {
                                                this.showMarketingAmountPanel(index, item);
                                                // this.confirmOrder.useSaleAmount(index, !item.useSaleAmount)
                                            }}
                                        >
                                            <View
                                                style={{
                                                    flexDirection:'row',
                                                    justifyContent:'space-between',
                                                    alignItems:'center',
                                                    flex:1,
                                                }}
                                            >
                                                <View style={{ margin:Constant.sizeMarginDefault, flex:1 }}>
                                                    <Text>使用营销费用</Text>
                                                    <Text
                                                        style={{
                                                            color:Constant.colorTxtAlert,
                                                            fontSize:Constant.fontSizeSmall,
                                                        }}
                                                    >
                                                        共：
                                                        <PriceText
                                                            size={7}
                                                            price={item.marketingRemainAmount}
                                                        />
                                                        可用营销费用
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            color:Constant.colorTxtAlert,
                                                            fontSize:Constant.fontSizeSmall,
                                                        }}
                                                    >
                                                        使用金额：
                                                        <PriceText
                                                            size={7}
                                                            price={item.currentUseMarketingAmount}
                                                        />
                                                    </Text>
                                                </View>
                                                <Image
                                                    source={require('../img/ic_center_right_arrow.png')}
                                                    style={{
                                                        marginRight:Constant.sizeMarginDefault,
                                                        width:Constant.scale(6),
                                                        height:Constant.scale(12),
                                                        alignItems:'center',
                                                    }}
                                                />
                                            </View>


                                        </TouchableOpacity>
                                        <View
                                            style={[
                                                GlobalStyle.styleDividerDefault,
                                                { marginLeft:Constant.sizeMarginDefault },
                                            ]}
                                        />
                                    </View>
                                    : null}

                                {
                                    this.props.user.isDealer() &&
                                    item.orderBottleFeeDtos &&
                                    item.orderBottleFeeDtos.length > 0 ?
                                        <View style={{ width:width }}>

                                            <TouchableOpacity
                                                style={{ height:Constant.scale(55.5) }}
                                                onPress={() => {
                                                    this.confirmOrder.useBottleRemain(index, !item.useBottleRemain);
                                                }}
                                            >
                                                <View
                                                    style={{
                                                        flexDirection:'row',
                                                        justifyContent:'space-between',
                                                        alignItems:'center',
                                                    }}
                                                >
                                                    <View style={{ padding:Constant.sizeMarginDefault }}>
                                                        <Text>使用瓶箱余额</Text>
                                                        <View style={{ flexDirection:'row' }}>
                                                            <Text
                                                                style={{
                                                                    color:Constant.colorTxtAlert,
                                                                    fontSize:Constant.fontSizeSmall,
                                                                }}
                                                            >瓶箱按金费用小计:¥{bottleAmount.toFixed(2)}</Text>
                                                            <TouchableOpacity
                                                                onPress={() => {
                                                                    //TODO 弹窗显示详情
                                                                    this.showBottleInfo(index, item.orderBottleFeeDtos,item.btlUseFlag);
                                                                }}
                                                                style={{
                                                                    paddingLeft:Constant.sizeMarginDefault,
                                                                    paddingRight:Constant.sizeMarginDefault,
                                                                }}
                                                            >
                                                                <Text
                                                                    style={{
                                                                        color:Constant.colorTxtContent,
                                                                        fontSize:Constant.fontSizeSmall,
                                                                    }}
                                                                >详情</Text>
                                                            </TouchableOpacity>
                                                        </View>

                                                    </View>
                                                    <Switch
                                                        style={{ marginRight:Constant.sizeMarginDefault }}
                                                        value={item.useBottleRemain}
                                                        onValueChange={(value) => {
                                                            this.confirmOrder.useBottleRemain(index, value);
                                                        }}
                                                    />
                                                </View>


                                            </TouchableOpacity>
                                            <View
                                                style={[
                                                    GlobalStyle.styleDividerDefault,
                                                    { marginLeft:Constant.sizeMarginDefault },
                                                ]}
                                            />
                                        </View> : null
                                }

                                {
                                    this.props.user.isDealer() &&
                                    item.orderBottleFeeDtos &&
                                    item.orderBottleFeeDtos.length > 0 ? (
                                        <View style={{ width:width }}>
                                            <TouchableOpacity
                                                style={{ height:Constant.scale(55.5) }}
                                                onPress={() => {
                                                    this.confirmOrder.usePalletRemain(index, !item.usePalletRemain);
                                                }}
                                            >
                                                <View
                                                    style={{
                                                        height: '100%',
                                                        flexDirection:'row',
                                                        justifyContent:'space-between',
                                                        alignItems:'center',
                                                        paddingLeft: Constant.sizeMarginDefault,
                                                    }}
                                                >
                                                    <Text>使用托板余额</Text>
                                                    <Switch
                                                        style={{ marginRight:Constant.sizeMarginDefault }}
                                                        value={item.usePalletRemain}
                                                        onValueChange={(value) => {
                                                            this.confirmOrder.usePalletRemain(index, value);
                                                        }}
                                                    />
                                                </View>
                                            </TouchableOpacity>
                                            <View
                                                style={[
                                                    GlobalStyle.styleDividerDefault,
                                                    { marginLeft:Constant.sizeMarginDefault },
                                                ]}
                                            />
                                        </View>
                                    ) : null
                                }

                                {
                                    !this.props.user.isRMB() ?
                                        <View>
                                            <View
                                                style={{
                                                    flex:1, paddingTop:5,
                                                    paddingBottom:5,
                                                    paddingLeft:Constant.sizeMarginDefault,
                                                    backgroundColor:Constant.colorBackgroundDefault,
                                                }}
                                            >
                                                <Text
                                                    style={{
                                                        color:Constant.colorTxtAlert,
                                                        fontSize:Constant.fontSizeSmall,
                                                    }}
                                                >外币订单填写项</Text>

                                            </View>

                                            <InputFormCell
                                                value={freight}
                                                placeholder={'0.00'}
                                                underLine={1}
                                                title={'运费:' + this.props.user.getSymbol()}
                                                onChangeText={(text) => {
                                                    if (ValidateUtil.validatorNumberValue(text, true)) {
                                                        this.confirmOrder.setFreight(index, text);
                                                    }
                                                }}
                                            />
                                            <InputFormCell
                                                value={premium}
                                                underLine={1}
                                                placeholder={'0.00'}
                                                title={'保费:' + this.props.user.getSymbol()}
                                                onChangeText={(text) => {
                                                    if (ValidateUtil.validatorNumberValue(text, true)) {
                                                        this.confirmOrder.setPremium(index, text);
                                                    }
                                                }}
                                            />
                                            <InputFormCell
                                                value={incidentals}
                                                underLine={1}
                                                placeholder={'0.00'}
                                                title={'杂费:' + this.props.user.getSymbol()}
                                                onChangeText={(text) => {
                                                    if (ValidateUtil.validatorNumberValue(text, true)) {
                                                        this.confirmOrder.setIncidentals(index, text);
                                                    }
                                                }}
                                            />
                                            <InputFormCell
                                                maxLength={80}
                                                underLine={1}
                                                placeholder={'请输入合同编码'}
                                                value={contractCode}
                                                title={'合同编码:'}
                                                onChangeText={(text) => {
                                                    this.confirmOrder.setContractCode(index, text);
                                                }}
                                            />

                                            <LabelCell
                                                underLine={1}
                                                title={'外币订单运保杂费:'}
                                                showRightIcon={false}
                                                extra={<PriceText price={total} />}
                                            />
                                        </View> : null
                                }
                                <InputFormCell
                                    underLine={1}
                                    title={'备注'}
                                    onChangeText={(text) => {
                                        this.confirmOrder.setRemark(index, text);
                                    }}
                                    maxLength={100}
                                    placeholder={'请填写备注'}
                                />
                                <LabelCell
                                    title={
                                        <Text
                                            style={{
                                                color:Constant.colorTxtContent,
                                                fontSize:Constant.fontSizeNormal,
                                            }}
                                        >
                                            实付款：
                                            <PriceText

                                                price={item.dealAmount}
                                            />

                                        </Text>
                                    }
                                    underLine={2}
                                    extra={
                                        <Text
                                            style={{ color:Constant.colorTxtAlert, fontSize:Constant.fontSizeNormal }}
                                        >
                                            明细
                                        </Text>
                                    }
                                    onClick={() => {
                                        this.showOutPocket(item);
                                    }}
                                />

                            </View>


                        );
                    })
                }

            </View>
        );

    }

    /**
     * 使用营销费用商品列表
     * @param index
     * @param item
     */
    showMarketingAmountPanel = (index, item) => {
        Widget.Popup.show(<MarketingAmountPanel
                store={this.confirmOrder}
                orderIndex={index}
            />,
            {
                animationType:'slide-up', backgroundColor:'#00000000',
                onMaskClose:() => {
                    Widget.Popup.hide();
                },
            });
    };

    /**
     * 发票类型
     */
    chooseInvoiceType = () => {
        let identificationCode = this.confirmOrder && this.confirmOrder.orderInvoice && this.confirmOrder.orderInvoice.identificationCode;
        let selectKey = 'type';
        let labelName = 'title';
        let selectId = this.state.type;
        Log('tt == ',toJS(this.confirmOrder.orderInvoice));
        Widget.Popup.show(
            <SelectPop
                popTitle={'发票类型'}
                listData={identificationCode ? invoicetypeData : invoicetypeData2}
                labelName={labelName}
                selectId={selectId}
                selectKey={selectKey}
                showPleaseChooseBtn={false}
                selectCallBack={(selectData) => {
                    // Widget.Popup.hide();
                    InteractionManager.runAfterInteractions(() => {
                        if (this.state.title == '专票' && selectData.type == 1) {
                            Alert.alert('温馨提示',
                                '您是一般纳税人，建议选择专票，如果选择普票，税务风险自行承担。'
                                , [
                                    { text:'取消' },
                                    {
                                        text:'确定', onPress:() => {
                                            this.setState({
                                                title:selectData.title,
                                                type:selectData.type,
                                            }, Widget.Popup.hide());
                                        },
                                    },
                                ],
                            );
                        } else {
                            this.setState({
                                title:selectData.title,
                                type:selectData.type,
                            }, Widget.Popup.hide());
                        }

                    });


                }}
            />
            ,
            {
                animationType:'slide-up', backgroundColor:'#00000000',
                onMaskClose:() => {
                    Widget.Popup.hide();
                },
            });
    };

    /**
     * 展示发票
     * */

    showInvoiceInfo = () => {
        Widget.Popup.show(<InvoiceInfo
                data={this.confirmOrder.orderInvoice}
                cancel={() => {
                    Widget.Popup.hide();
                }}
            />,
            {
                animationType:'slide-up', backgroundColor:'#00000000',
                onMaskClose:() => {
                    Widget.Popup.hide();
                },
            });
    };

    /**
     * 展示瓶箱明细
     * */

    showBottleInfo = (orderIndex, data,btlUseFlag) => {
        Widget.Popup.show(<BottleBanlanceDetail
                data={data}
                btlUseFlag= {btlUseFlag}
            />,
            {
                animationType:'slide-up', backgroundColor:'#00000000',
                onMaskClose:() => {
                    Widget.Popup.hide();
                },
            });
    };

    /**
     * 实付金额
     * */
    showOutPocket = (data) => {
        let zpOrderType = _.get(this.getNavState(), 'params.data.zpOrderType', 1);

        Widget.Popup.show(<OutPocketDetail
                data={data}
                zpOrderType={zpOrderType}
                pageType={0}
            />,
            {
                animationType:'slide-up', backgroundColor:'#00000000',
                onMaskClose:() => {
                    Widget.Popup.hide();
                },
            });
    };

    /**
     * 提交订单
     * */
    submit = () => {
        let orderInvoiceData = this.confirmOrder.orderInvoice || {};
        let orders = [];
        let type = _.get(this.getNavState(), 'params.type', 1);//1：立即购买（包含费用赠酒，瓶盖赠酒，快捷下单）、2：购物车 3：转开单
        let data = _.get(this.getNavState(), 'params.data', {});
        let useMarketingAmountItemIds = [];
        let invoiceDto = {
            invoiceType:this.state.title,
            orgName:orderInvoiceData.orgName,
            IdentificationCode:orderInvoiceData.identificationCode,
            address:orderInvoiceData.address,
            phone:orderInvoiceData.phone,
            bankName:orderInvoiceData.bankName,
            accountNumber:orderInvoiceData.accountNumber,
        };


        let channelName = null;
        this.confirmOrder.orders.map((item) => {
            let items = [];
            let useMarketingAmountCartNos = [];
            item.itemDtos && item.itemDtos.map((goods, index) => {
                channelName = this.props.user.getChancelFullName(goods.salesChannelCode, goods.subSalesChannelCode);
                if (type == 1) {
                    let newGoods = {
                        itemId:goods.itemId,
                        itemNum:goods.itemNum,
                        priceType:goods.priceType,
                        orgId:goods.orgId,
                        awards:goods.awards || '',
                        awardsId:goods.awardsId || '',
                        salesChannelCode:goods.salesChannelCode || '',
                        subSalesChannelCode:goods.subSalesChannelCode || '',
                        withPlateFlag:goods.withPlateFlag,
                        useMarketingAmountFlag:goods.useMarketingAmountFlag,
                        batchSaleNum:goods.batchSaleNum,
                        accountId:goods.accountId || '',
                    };
                    items.push(newGoods);
                } else if (type == 2) {
                    items.push(goods.shoppingCartNo);
                    if (goods.useMarketingAmountFlag == 1) {
                        useMarketingAmountCartNos.push(goods.shoppingCartNo);
                    }
                } else if (type == 3) {
                    if (goods.useMarketingAmountFlag == 1) {
                        useMarketingAmountItemIds.push(goods.itemId);
                    }
                }


            });

            let extendObj = {};
            if (type == 1) {
                extendObj = {
                    itemBuyDtos:items,
                };
            } else if (type == 2) {
                extendObj = {
                    shoppingCartNos:items,
                    useMarketingAmountCartNos:useMarketingAmountCartNos,
                };
            }
            if (!this.props.user.isRMB()) {
                //外币订单保杂费
                Object.assign(extendObj, {
                    freight:item.foreignCurrency.freight,
                    premium:item.foreignCurrency.premium,
                    incidentals:item.foreignCurrency.incidentals,
                    contractCode:item.foreignCurrency.contractCode,
                });
            }

            let obj = {
                ...extendObj,
                orgId:item.supplierOrgId || '',
                expectedDeliveryDate:item.expectedDeliveryDate || '',
                useBottleRemain:item.useBottleRemain ? 1 : 0,
                usePalletRemain:item.usePalletRemain ? 1 : 0,
                remark:item.remark || '',


            };

            orders.push(obj);
        });

        let params = {
            orderSubmitDtos:orders,
            receivingAddressId:this.confirmOrder.address && this.confirmOrder.address.id,
            zpOrderType:data.zpOrderType,
            invoiceDto:invoiceDto,
        };
        if (type == 3) {
            //转开单
            const orderId = _.get(this.getNavState(), 'params.orderId', {});
            let useBottleRemain = 0, remark = '';
            if (orders && orders.length > 0) {
                useBottleRemain = orders[ 0 ].useBottleRemain;
                remark = orders[ 0 ].remark;
            }
            let trunOrderParams =  _.get(this.getNavState(), 'params.trunOrderParams', {});
            params = {
                orderId:trunOrderParams.orderId,
                useMarketingAmountItemIds:useMarketingAmountItemIds,
                useBottleRemain:useBottleRemain,
                remark:remark,
                transferItemDtos:trunOrderParams.transferItemDtos,
                invoiceDto:invoiceDto,
            };
        }

        // console.log('new - 结算页点击确认 = ' + JSON.stringify(params));
        // Log('参数----',toJS(params))
        // return;
        if(channelName === '暂无渠道') {
            Alert.alert('提示', '请先选择渠道')
            return;
        }
        this.confirmOrder.submit(params, type, (data) => {

            Log('======this.confirmOrder.hasMarginBalance', toJS(this.confirmOrder.hasMarginBalance));

            if (data) {

                if (this.confirmOrder.hasMarginBalance) {
                    this.setState({
                        modalVisible:true,
                        marginListDataArray:data,
                    });
                    return;
                }

                let goToOrder = () => {
                    let resetAction = NavigationActions.reset({
                        index:1,
                        actions:[
                            NavigationActions.navigate({ routeName:'Tab' }),
                            NavigationActions.navigate({ routeName:'OrderList', params:{ type:1 } }),
                        ],
                    });
                    this.dispatch(resetAction);
                };
                let goToHome = () => {
                    let resetAction = NavigationActions.reset({
                        index:0,
                        actions:[
                            NavigationActions.navigate({ routeName:'Tab' }),
                        ],
                    });
                    this.dispatch(resetAction);
                };
                if (this.props.user.isDealer()) {


                    if (this.props.user.canPay()) {
                        if (data.length == 1) {
                            // if (data[0] && data[0].zeroOrderFlag == true) {
                            //     this.props.navigation.replace('PaySuccess')
                            // } else {
                            this.props.navigation.replace('PayOrder', { order:data });
                            // }

                        } else {
                            goToOrder();
                            // ToastUtil.show('多组织确认订单要跳转我的订单列表')
                        }
                    } else {
                        Alert.alert('提示', '订单已提交成功，请联系财务付款', [ { text:'确定' } ]);
                        goToHome();
                    }

                } else {
                    goToOrder();
                }

                //转单后刷新订单列表
                if (this.type == 3) {
                    this.props.navigation.state.params.callback && this.props.navigation.state.params.callback();
                }

            }

        }).then().catch((e) => {

        });
    };

    checkSubmit = () => {

        let waterFlag = false;
        if (this.confirmOrder.zpOrderType == 1)
        {
            for (let item of  this.confirmOrder.orders) {
                if (item && item.currency == "RMB" && item.itemDtos) {
                    for (let goods of item && item.itemDtos) {
                        let str = '';
                        if (goods.itemCode)
                        {
                            str = goods.itemCode.substr(goods.itemCode.length-3,1);
                        }
                        if (goods.salesChannelCode == '1' && goods.itemCode.startsWith('05-1-1') && (str == '1' || str == '5' || str == '7')) {
                            waterFlag = true;
                        }
                    }
                }
            }
        }

        if (waterFlag) {
            Alert.alert('温馨提示', '订单中有含瓶品种，请检查确认！', [
                { text:'取消' },
                {
                    text:'继续下单', onPress:() => {
                        this.submit();
                    },
                },
            ]);

        } else {
            this.submit();
        }
    };


    //渲染
    render() {
        let totalAmount = 0;
        let isDealer = this.props.user && this.props.user.isDealer();
        this.confirmOrder.orders.map((item) => {
            totalAmount = totalAmount + item.dealAmount;
        });


        return (

            <SafeAreaView
                forceInset={{ top:0 }}
                style={{ flex:1, backgroundColor:'white' }}
            >
            <Header
                style={styles.container}
                title="结算确认"
            >
                <KeyboardAwareScrollView
                    contentContainerStyle={{ flex:1 }}
                    scrollEnabled={false}
                    keyboardShouldPersistTaps={'always'}
                    extraHeight={height < 667 ? Constant.scale(18) : Constant.scale(60)}
                >
                    <ScrollView style={{ flex:1 }}>
                        {
                            this.renderAddress()
                        }
                        {
                            this.renderOrder()
                        }
                        {
                            isDealer ?

                                <View>
                                    <LabelCell
                                        title={'发票类型'}
                                        onClick={this.confirmOrder.orderInvoice && this.confirmOrder.orderInvoice.invoiceType == '专票' ? this.chooseInvoiceType : null}
                                        extra={<Text>{this.state.title}</Text>}
                                    />
                                    <LabelCell
                                        title={'开票信息'}
                                        onClick={this.showInvoiceInfo}
                                        extra={
                                            <Text>{this.confirmOrder.orderInvoice ? this.confirmOrder.orderInvoice.orgName : ''}</Text>}
                                    />
                                </View>
                                : null

                        }
                        <View style={{ height:Constant.scale(44) }} />

                    </ScrollView>
                </KeyboardAwareScrollView>
                <View
                    style={{
                        flexDirection:'row',
                        height:Constant.scale(44),
                        backgroundColor:'white',
                    }}
                >
                    <View
                        style={[
                            {
                                flexDirection:'row',
                                flex:1.6,
                                borderColor:'white',
                                borderTopColor:Constant.colorDividerDefault,
                                borderWidth:Constant.sizeDividerNormal,
                                alignItems:'center',
                            },
                        ]}
                    >
                        <View style={{ flex:1 }} />
                        <Text style={{ color:Constant.colorTxtContent }}>实付款：</Text>

                        <PriceText price={totalAmount} />
                        <View style={{ width:Constant.sizeMarginDefault }} />
                    </View>
                    <TouchableOpacity
                        style={[
                            {
                                alignItems:'center',
                                justifyContent:'center',
                                flex:1,
                                backgroundColor:Constant.colorPrimary,
                            },
                        ]}
                        onPress={() => this.checkSubmit()}
                    >
                        <Text
                            style={{
                                color:'white',
                                fontSize:Constant.fontSizeBig,
                            }}
                        >{this.type == 3 ? '转单' : '确定'}</Text>
                    </TouchableOpacity>
                </View>
                <ModalMarginBalanceView
                    modalVisible={this.state.modalVisible}
                    data={this.state.marginListDataArray ? this.state.marginListDataArray:[]}
                    defaultClick={() => {
                        this.setState({ modalVisible:false });
                    }}
                >

                </ModalMarginBalanceView>

            </Header>
                {
                    Platform.OS == 'android' ?
                        <StatusBar
                        backgroundColor={'transparent'}
                        barStyle={'dark-content'}
                        translucent={true}
                    /> : null
                }

            </SafeAreaView>
        );
    }
};


const styles = StyleSheet.create({
    container:{
        flex:1,
    },


});
const addressStyles = StyleSheet.create({
    background:{
        backgroundColor:'white',
        width:width,
        minHeight:Constant.scale(100),
        paddingLeft:Constant.sizeMarginDefault,


        marginBottom:Constant.sizeMarginDefault,
    },
    nameText:{
        color:Constant.colorTxtContent,
        fontSize:Constant.fontSizeBig,
    },
    addressText:{
        color:Constant.colorTxtContent,
        fontSize:Constant.fontSizeNormal,
        width:'85%',
    },
});