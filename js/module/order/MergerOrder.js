/**
 * Created by lao.jian<PERSON> on 2017/5/26.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'

const {width, height} = Dimensions.get('window');
const {Text, CommonListView} = Widget;
const {Image, CheckBox, PriceText, ConfirmInput} = AppWidget;
import {toJS} from 'mobx';
import {inject, observer} from 'mobx-react/native';

@observer
export default class MergerOrder extends ReactNavComponent {
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数

    constructor(props) {
        super(props);
        //状态机变量声明
        this.state = {
            // itemArray: new Set()
        };
    }

    componentWillMount() {
    }

    componentDidMount() {
    }


    //渲染
    render() {
        // let {itemArray} = this.state;
        let mergerOrderListData = this.props.OrderList;
        Log('=======mergerOrderListData==========',mergerOrderListData)
        let simpleOrderInfoDtos = mergerOrderListData.simpleOrderInfoDtos;
        let callBack = this.props.callBack;
        let mergerOrderIds = [];

        return (
            <View style={{height: Constant.scale(430), width: width}}>
                <View style={{
                    flexDirection: 'row',
                    height: Constant.scale(44),
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    <Text style={{fontSize: 18, color: '#333333'}}>
                        可合并支付订单列表
                    </Text>
                    <View style={{
                        height: Constant.scale(15),
                        width: Constant.scale(15),
                        position: 'absolute',
                        top: 14.5,
                        right: 12
                    }}>
                        <TouchableOpacity style={{width: Constant.scale(15), height: Constant.scale(15)}}
                                          onPress={() => {
                                              Widget.Popup.hide()
                                          }}>
                            <Image
                                resizeMode={'stretch'}
                                style={{height: Constant.scale(15), width: Constant.scale(15)}}
                                source={require('../img/order/ic_popclose.png')}
                            />
                        </TouchableOpacity>
                    </View>
                </View>

                <ScrollView
                    // contentContainerStyle={{flex:1}}
                >
                    {simpleOrderInfoDtos && simpleOrderInfoDtos.map((item, index) => {
                        // let has = itemArray.has(item.orderId);
                        mergerOrderIds.push(item.orderId);
                        return (
                            <TouchableOpacity
                                style={{flex:1}}
                                key={'orderList' + index}
                                onPress={() => this.payAlone(item,callBack)
                                // {
                                //     if (has) {
                                //         itemArray.delete(item.orderId);
                                //         this.setState({itemArray:itemArray});
                                //     } else {
                                //         itemArray.add(item.orderId);
                                //         this.setState({itemArray:itemArray});
                                //     }
                                // }
                                }>
                                <View
                                    style={{
                                        width:width,
                                        flexDirection: 'row',
                                        height: Constant.scale(114),
                                        alignItems: 'center',
                                        padding: Constant.sizeMarginDefault,
                                    }}>
                                    {/*<CheckBox*/}
                                        {/*onClick={() => {*/}
                                            {/*if (has) {*/}
                                                {/*itemArray.delete(item.orderId);*/}
                                                {/*this.setState({itemArray:itemArray});*/}
                                            {/*} else {*/}
                                                {/*itemArray.add(item.orderId);*/}
                                                {/*this.setState({itemArray:itemArray});*/}
                                            {/*}*/}
                                        {/*}}*/}
                                        {/*style={styles.checkBox}*/}
                                        {/*isChecked={has}/>*/}
                                    <Image
                                        style={{
                                            marginRight: Constant.sizeMarginDefault,
                                            width: Constant.scale(84),
                                            height: Constant.scale(84),
                                            borderWidth: Constant.sizeDividerNormal,
                                            borderColor: Constant.colorDividerDefault
                                        }}
                                        source={{uri: item.imageUrl ? Constant.ossImgUrl(item.imageUrl,84) : null}}/>
                                    <View style={{flex:1}}>
                                        <Text style={[styles.fontTitle,{lineHeight:Constant.scale(22)}]}
                                              numberOfLines={1}>
                                            {
                                                item.itemName
                                            }
                                        </Text>
                                        <Text style={[styles.fontNumAndTotalCount,{lineHeight:Constant.scale(22)}]}>
                                            数量：{item.itemNum}
                                        </Text>
                                        <View style={{
                                            flexDirection: 'row',
                                            justifyContent: 'flex-start',
                                            alignItems: 'center'
                                        }}>
                                            <Text>总额:</Text>
                                            <PriceText price={item.payAmount} />
                                        </View>
                                    </View>
                                </View>
                                <Text style={{right:15,bottom:10,position:'absolute',padding:5,borderRadius:4,borderWidth:Constant.sizeDividerNormal,borderColor:Constant.colorPrimary,color:Constant.colorPrimary}}>去支付</Text>
                                <View style={{
                                    width:width,
                                    // marginLeft: Constant.scale(40),
                                    height: Constant.sizeDividerNormal,
                                    backgroundColor: Constant.colorDividerDefault
                                }}/>
                            </TouchableOpacity>
                        )
                    })}
                </ScrollView>

                <View style={{
                    height: Constant.scale(44),
                    flexDirection: 'row',
                    borderTopWidth: 1,
                    borderColor: Constant.colorDividerDefault
                }}>
                    <TouchableOpacity style={{
                        backgroundColor: '#ffffff',
                        height: Constant.scale(44),
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}
                                      onPress={() => {
                                          Widget.Popup.hide()
                                      }}>
                        <Text style={{color: '#999999', fontSize: 16}}>
                            取消
                        </Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={{
                        backgroundColor: '#df0522',
                        height: Constant.scale(44),
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}
                                      onPress={() => {
                                          let parentOrderId = mergerOrderListData.parentOrderId;
                                          let orderIds = mergerOrderIds;
                                          let businessType = 8;
                                          callBack([{
                                              parentOrderId: parentOrderId,
                                              orderIds: orderIds,
                                              businessType: businessType
                                          }]);
                                      }}>
                        <Text style={{color: '#ffffff', fontSize: 16}}>
                            合并支付
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }

    /**
     * 单独支付
     */
    payAlone = (item,callBack)=>{

        // let parentOrderId = mergerOrderListData.parentOrderId;
        let orderIds = [];
        orderIds.push(item.orderId)
        let businessType = 8;
        callBack([{
            // parentOrderId: parentOrderId,
            orderIds: orderIds,
            businessType: businessType
        }]);
        Log('单独支付')
    }
};
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Constant.colorBackgroundDefault
    },
    checkBox: {
        paddingRight: Constant.sizeMarginDefault,
        width: Constant.scale(15) + Constant.sizeMarginDefault,
        height: Constant.scale(15),
        // backgroundColor:'pink'
    },
    attrTxt: {
        fontSize: Constant.fontSizeSmall,
        color: Constant.colorTxtContent,
        marginRight: Constant.scale(5),
    },
    fontTitle: {
        fontSize: Constant.fontSizeNormal,
        color: Constant.colorTxtContent,
    },

    fontNumAndTotalCount: {
        fontSize: Constant.fontSizeNormal,
        color: Constant.colorTxtTitle,
    },
});

