/**
 * Created by whw on 2018/1/24.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    ScrollView
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget';
const {width, height} = Dimensions.get('window');
const {Image,Button,PriceText}=AppWidget;
const {Text} = Widget;
const IC_CLOSE = require('../img/order/ic_popclose.png');
/**
 * 订单列表Item
 */

export default class InvoiceInfo extends ReactNavComponent {
    //属性声名
    static propTypes = {
        cancel: PropType.func,

    };
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        this.state={//状态机变量声明
        };
    }
    componentWillMount(){}
    componentDidMount(){}
    componentWillUnmount(){}


    renderItem = (key,value)=> {
        return (
            <View style={{flexDirection: 'row',width: '100%',marginTop: Constant.sizeMarginDefault/2,marginBottom: Constant.sizeMarginDefault/2}}>
                <Text style={{color: Constant.colorTxtContent,fontSize: Constant.fontSizeNormal,textAlign:'right',width: 100}}>{key}</Text>
                <Text style={{color: Constant.colorTxtTitle,fontSize: Constant.fontSizeNormal,marginLeft: Constant.sizeMarginDefault,flex:1}}>{value}</Text>
            </View>
        );
    }

    //渲染
    render() {
        let data = this.props.data ? this.props.data : {};
        return (
            <View style={{width: width,backgroundColor: Constant.colorDefault}}>
                <View style={{width: width,height: Constant.scale(44),justifyContent: 'center',alignItems: 'center'}}>
                    <Text style={{fontSize: Constant.fontSizeCaption,color: Constant.colorTxtTitle}}>开票信息</Text>
                    <TouchableOpacity
                        style={{position: 'absolute',right: 0,top: 0,height: Constant.scale(44),width: Constant.scale(44),justifyContent: 'center',alignItems: 'center'}}
                        onPress={()=> {
                        this.props.cancel && this.props.cancel();
                        }}
                    >
                        <Image style={{width: Constant.scale(15),height: Constant.scale(15)}} source={IC_CLOSE}/>
                    </TouchableOpacity>
                </View>

                <View style={{width: width-2*Constant.sizeMarginDefault,margin: Constant.sizeMarginDefault}}>
                    {/*{this.renderItem('发票类型:',data.invoiceType || data.invoiceTypeName)}*/}
                    {this.renderItem('纳税人识别码:',data.identificationCode || data.taxCode)}
                    {this.renderItem('单位名称:',data.orgName)}
                    {this.renderItem('注册地址:',data.address || data.registerAddress)}
                    {this.renderItem('注册电话:',data.phone || data.registerPhone)}
                    {this.renderItem('开户银行:',data.bankName || data.openBank)}
                    {this.renderItem('银行账户:',data.accountNumber || data.bankAccount)}
                </View>
            </View>
        );
    }
};


const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    leftTxtStyle: {
        height: Constant.scale(24),
        textAlign: 'right',
        color: Constant.colorTxtContent,
        fontSize: Constant.fontSizeNormal
    },
    rightTxtStyle: {
        height: Constant.scale(24),
        color: Constant.colorTxtTitle,
        fontSize: Constant.fontSizeNormal
    }
});