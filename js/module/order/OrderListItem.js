/**
 * Created by whw on 2018/1/19.
 */
import React, { Component } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  InteractionManager,
  Alert
} from "react-native";
import PropType from "prop-types";
import { ReactNavComponent, Widget } from "rn-yunxi";
import AppWidget from "../../app-widget";

const { width, height } = Dimensions.get("window");
const { Image, Button, PriceText, SelectPop, CheckBox } = AppWidget;
const { Text } = Widget;
const IC_SHOP_ICON = require("../img/order/ic_shop_icon.png");
const IC_SPECIAL = require("../img/home-page/ic_special.png");
const IC_NEXT = require("../img/next.png");
import { withNavigation } from "react-navigation";
import OrderGoodsItem from "./OrderGoodsItem";
import { inject, observer } from "mobx-react/native";
import { action, toJS } from "mobx";
import EmText from "../../app-widget/em-text";
import MergerOrder from "./MergerOrder";
import QRCode from "react-native-qrcode-svg";
import ToastUtil from "../../util/ToastUtil";

@withNavigation
/**
 * 订单支付 
 */
@inject(stores => ({
  pay: stores.pay,
  orderUtil: stores.orderUtil,
  user: stores.user
}))
/**
 * 订单列表Item
 */
@observer
export default class OrderListItem extends ReactNavComponent {
  //属性声名
  static propTypes = {};
  //默认属性
  static defaultProps = {
    showCheckBox: false
  };

  //构造函数
  constructor(props) {
    super(props);
    this.state = {
      //状态机变量声明
      showCheckBox: this.props.showCheckBox
    };
  }

  componentWillMount() {}

  componentDidMount() {}

  componentWillUnmount() {}

  checkBoxClick = index => {
    if (this.props.checkBoxClick) {
      this.props.checkBoxClick(index);
    }
  };

  /**
   * 取消订单
   * */
  cancelOrder = () => {
    if (this.props.cancelOrder) {
      this.props.cancelOrder();
    }
  };

  /**
   * 去支付
   * */
  pay = () => {
    if (this.props.pay) {
      this.props.pay();
    }
  };

  /**
   * 再次购买
   * */
  buyAgain = () => {
    if (this.props.buyAgain) {
      this.props.buyAgain();
    }
  };

  /**
   * 确认收货
   * */
  confirmGoods = item => {
    if (this.props.confirmGoods) {
      this.props.confirmGoods(item);
    }
  };

  /**
   * 申请售后
   * */
  applyAfterSale = item => {
    if (this.props.applyAfterSale) {
      this.props.applyAfterSale(item);
    }
  };

  /**
   * 转单
   * */
  turnOrder = item => {
    if (this.props.turnOrder) {
      this.props.turnOrder(item);
    }
  };

  /**
   * 评价物流
   * */
  commentDelivery = data => {
    if (data) {
      this.navigate("DeliveryComment", {
        isEvaluated: data.isEvaluated,
        deliveryOrderId: data.deliveryOrderId,
        itemData: data,
        orderNo: this.props.data.orderNo,
        supplierOrgName: this.props.data.supplierOrgName
      });
    }
  };

  // evaluate = (item, index) => {
  //     if (this.props.evaluate) {
  //         this.props.evaluate(item);
  //     }
  // }

  goToApplyRecycleBottle = deliveryOrderNo => {
    this.navigate("ApplyRecycleBottle", {
      deliveryOrderNo: deliveryOrderNo
    });
  };

  /**
   * 可合并支付订单列表
   * */
  renderMergerPay = () => {
    this.props.orderUtil
      .getMergerOrderList(this.props.data.orderId)
      .then(data => {
        Widget.Popup.show(
          <MergerOrder
            OrderList={data}
            callBack={orderArray => {
              if (this.props.payOrder) {
                if (orderArray[0].orderIds.length === 0) {
                  Alert.alert(null, "请选择支付订单!", [{ text: "确定" }]);
                  return;
                }
                Widget.Popup.hide();
                InteractionManager.runAfterInteractions(() => {
                  this.props.payOrder(orderArray);
                });
              }
            }}
          />,
          {
            animationType: "slide-up",
            backgroundColor: "#ffffff",
            onMaskClose: () => {
              Widget.Popup.hide();
            }
          }
        );
      })
      .catch(err => {});
  };

  /**
   * 生成提货单号二维码
   * */
  qrCodeBtn = () => {
    let deliveryOrderNo = null;
    this.props.data.deliverys.map(item => {
      deliveryOrderNo = item.deliveryOrderNo;
    });
    if (!deliveryOrderNo) {
      ToastUtil.show("无效提货单");
      return;
    }

    return Widget.Popup.show(
      <TouchableOpacity
        activeOpacity={1.0}
        onPress={() => {
          Widget.Popup.hide();
        }}
        style={{
          justifyContent: "center",
          alignItems: "center",
          height: height,
          width
        }}
      >
        <View style={[styles.bgViewStyle]}>
          <Text
            style={[
              styles.titleStyle,
              { fontSize: 16, marginTop: Constant.scale(30) }
            ]}
          >
            提货单号二维码
          </Text>
          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
              marginTop: Constant.scale(30)
            }}
          >
            <QRCode value={deliveryOrderNo} />
          </View>
          <Text
            style={{
              color: Constant.colorTxtAlert,
              fontSize: Constant.fontSizeNormal,
              margin: Constant.scale(30),
              textAlign: "center"
            }}
          >
            {deliveryOrderNo}
          </Text>
        </View>
      </TouchableOpacity>,
      {
        animationType: "none",
        backgroundColor: "#00000000",
        onMaskClose: () => {
          this.setState({ showParamsPop: true });
        }
      }
    );
  };

  goToDeliveryOrderDetail = (deliveryOrderId, deliveryOrderStatus) => {
    this.props.navigation.navigate("DeliveryOrderDetail", {
      deliveryOrderId: deliveryOrderId,
      data: this.props.data,
      deliveryOrderStatus: deliveryOrderStatus
    });
  };

  goToOrderDetail = () => {
    if (this.props.user.isDealer()) {
      let data = this.props.data;
      this.props.navigation.navigate("OrderDetail", {
        orderId: this.props.data.orderId,
        orderType: this.props.orderType,
        turnOrderClickCallback: () => this.turnOrder(data.deliverys[0])
      });
    }
  };

  //欠款单
  renderArrears(item) {
    let arrearageNo = item.arrearageNo;
    if (!arrearageNo || arrearageNo == '') return null;
    let arrearageStatus = '';
    switch (item.uploadFileStatus) {
      case 0:
        arrearageStatus = '待提交';
        break;
      case 1:
        arrearageStatus = '已提交';
        break;
      case 2:
        arrearageStatus = '已驳回';
        
        break;
    }
    return <TouchableOpacity
      onPress={() => {
        this.goToPayMent(item);
      }}
    >
      <View
        style={{
          flex: 1,
          height: Constant.scale(40),
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
       <Text
          style={{
            flex: 1,
            color: Constant.colorTxtTitle,
            fontSize: Constant.fontSizeNormal,
            marginLeft: Constant.sizeMarginDefault,
          }}
        >
          欠款单号：{arrearageNo}
        </Text>
        
        <Text
          style={{
            color: "#FF7E00",
            fontSize: Constant.fontSizeNormal,
            marginLeft: Constant.sizeMarginDefault,
            marginRight: Constant.sizeMarginDefault
          }}
        >
          {arrearageStatus}
        </Text>
      </View>
      <View
          style={{
            width: width,
            height: Constant.sizeDividerNormal,
            backgroundColor: Constant.colorDivider
          }}
        />
    </TouchableOpacity>
  }
  //去欠款页面
  @action
  goToPayMent(data) {
    this.navigate("SubmitPayment", {
      orderId: this.props.data.orderId,
      orderNo: this.props.data.orderNo,
      orderType: this.props.orderType,
      arrearageNo: data.arrearageNo,
      uploadFileStatus: data.uploadFileStatus,
      orderStatus: this.renderStatus(data.status),
      callback:()=>{
        this.props.reloadListData();
      }
    });
  }

  renderItem = (item, index, buyerType, isDealer, status) => {

    //Log('************00',toJS(item));
    return (
      <View key={`orderListGoodsItem${index}`}>
        {item.deliveryOrderId && (
          <TouchableOpacity
            onPress={() => {
              this.goToDeliveryOrderDetail(item.deliveryOrderId, item.status);
            }}
          >
            <View
              style={{
                width: width,
                height: Constant.scale(40),
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between"
                // backgroundColor:'pink'
              }}
            >
              <View
                style={{
                  flex: 1,
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center"
                }}
              >
                {this.state.showCheckBox ? (
                  <CheckBox
                    isChecked={item.isChecked}
                    style={{ marginLeft: Constant.sizeMarginDefault }}
                    onClick={() => {
                      this.checkBoxClick(index);
                    }}
                  />
                ) : null}
                <Text
                  style={{
                    flex: 1,
                    color: Constant.colorTxtTitle,
                    fontSize: Constant.fontSizeNormal,
                    marginLeft: Constant.sizeMarginDefault
                  }}
                >
                  提货单号：{item.deliveryOrderNo}
                </Text>
                {item.status === 3 ? (
                  <TouchableOpacity
                    style={{ position: "absolute", left: Constant.scale(250) }}
                    onPress={() => {
                      this.qrCodeBtn();
                    }}
                  >
                    <Image
                      resizeMode={"stretch"}
                      style={{
                        height: Constant.scale(13),
                        width: Constant.scale(13)
                      }}
                      source={require("../img/recycle-bottle/check_qrcode.png")}
                    />
                  </TouchableOpacity>
                ) : (
                  <View />
                )}
              </View>
              <Text
                style={{
                  color: "#FF7E00",
                  fontSize: Constant.fontSizeNormal,
                  marginRight: Constant.sizeMarginDefault
                }}
              >
                {item.statusName}
              </Text>
            </View>
            {item.externalDeliveryOrderNo ? (
              <Text
                style={{
                  marginLeft: Constant.sizeMarginDefault,
                  marginBottom: Constant.scale(5)
                }}
              >
                ERP提货单号：{item.externalDeliveryOrderNo}
              </Text>
            ) : null}
          </TouchableOpacity>
        )}

        {/*{item.items && item.items.length == 1 ?
                     <OrderGoodsItem data={item.items[0]} underLine={2}
                     style={{backgroundColor: Constant.colorBackgroundDefault}}/> :
                     <View
                     style={{width: width,
                     height: Constant.scale(104),
                     flexDirection: 'row',
                     backgroundColor: Constant.colorBackgroundDefault,
                     alignItems: 'center',
                     justifyContent: 'center'}}
                     >
                     <ScrollView style={{flex: 1}} horizontal={true}
                     contentContainerStyle={{alignItems: 'center'}}>
                     {item.items && item.items.map((obj, objIndex)=> {
                     return (
                     <View key={`orderListGoodsImg${objIndex}`}
                     style={{marginLeft: Constant.sizeMarginDefault,}}>
                     <Image
                     style={{width: Constant.scale(84),height: Constant.scale(84)}}
                     source={{uri: obj.imgUrl}}
                     resizeMode="contain"
                     />
                     </View>
                     )
                     })}
                     </ScrollView>
                     <Text
                     style={{fontSize: Constant.fontSizeSmall,color: Constant.colorTxtAlert}}>共{item.items.length}种</Text>
                     <Image source={IC_NEXT} style={{marginRight: Constant.sizeMarginDefault}}/>
                     </View>
                     }*/}
        {item.items &&
          item.items.map((obj, index1) => {
            return (
              <TouchableOpacity
                key={`orderListGoods${index1}`}
                onPress={() => this.goToDetailClick(obj.itemId)}
              >
                <OrderGoodsItem
                  orderType={this.props.orderType}
                  data={obj}
                  buyerType={buyerType}
                  isDealer={isDealer}
                  underLine={2}
                  style={{ backgroundColor: Constant.colorBackgroundDefault }}
                />
              </TouchableOpacity>
            );
          })}

        {/*1：待配货 2：待发货 3：待收货 4：已完成*/}
        {(status == 2 && item.status == 1) ||
        (status == 31 && item.status == 1) ? (
          <View
            style={{
              width: width,
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "flex-end",
              marginTop: Constant.sizeMarginDefault,
              marginBottom: Constant.sizeMarginDefault
            }}
          >
            <Button
              style={{ borderRadius: 5, borderColor: Constant.colorTxtPrimary }}
              txtStyle={{
                fontSize: Constant.fontSizeSmall,
                color: Constant.colorTxtPrimary
              }}
              onPress={() => {
                this.applyAfterSale(item);
              }}
            >
              申请售后
            </Button>
          </View>
        ) : null}
        {item.status == 3 || item.status == 4 ? (
          //item.status == 3 || item.status == 1 ?//测试
          <View
            style={{
              width: width,
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "flex-end",
              marginTop: Constant.sizeMarginDefault,
              marginBottom: Constant.sizeMarginDefault
            }}
          >
            {item.status == 3 ? (
              <Button
                style={{
                  borderRadius: 5,
                  borderColor: Constant.colorTxtPrimary
                }}
                txtStyle={{
                  fontSize: Constant.fontSizeSmall,
                  color: Constant.colorTxtPrimary
                }}
                onPress={() => {
                  this.confirmGoods(item);
                }}
              >
                确认收货
              </Button>
            ) : (
              <View
                style={{
                  alignItems: "flex-end",
                  justifyContent: "center",
                  flexDirection: "row"
                }}
              >
                <Button
                  style={{
                    borderRadius: 5,
                    borderColor: Constant.colorTxtPrimary
                  }}
                  txtStyle={{
                    fontSize: Constant.fontSizeSmall,
                    color: Constant.colorTxtPrimary
                  }}
                  onPress={() => {
                    this.goToApplyRecycleBottle(item.deliveryOrderNo);
                  }}
                >
                  申请回瓶
                </Button>
                <Button
                  style={{
                    borderRadius: 5,
                    borderColor: Constant.colorTxtPrimary
                  }}
                  txtStyle={{
                    fontSize: Constant.fontSizeSmall,
                    color: Constant.colorTxtPrimary
                  }}
                  onPress={() => {
                    //this.evaluate(item) //调起H5页面
                    this.commentDelivery(item);
                  }}
                >
                  {item.isEvaluated ? "查看评价" : "评价物流"}
                </Button>
              </View>
            )}
          </View>
        ) : null}
        <View
          style={{
            width: width,
            height: Constant.sizeDividerNormal,
            backgroundColor: Constant.colorDivider
          }}
        />
      </View>
    );
  };

  //详情
  goToDetailClick = itemId => {
    this.props.navigation.navigate("GoodsDetail", { itemId: itemId });
  };
  //按钮
  renderBtn(judge, content, onPress) {
    return judge ?
      <Button
        style={{ borderRadius: 5, borderColor: Constant.colorTxtPrimary }}
        txtStyle={{
          fontSize: Constant.fontSizeSmall,
          color: Constant.colorTxtPrimary
        }}
        onPress={onPress}
      >
        {content}
      </Button>
      : null;
  }

  renderStatus = status => {
    let statusText = "";
    {
      /*0：待转单 1：待付款，2：待审核 31：待配货 4：待发货 41：待收货 6：已完成 9：已取消 91：已退单 */
    }
    switch (status) {
      case 0:
        statusText = "待转单";
        break;
      case 1:
        statusText = "待付款";
        break;
      case 2:
        statusText = "待审核";
        break;
      case 6:
        statusText = "已完成";
        break;
      case 9:
        statusText = "已取消";
        break;
      case 91:
        statusText = "已退单";
        break;
    }
    return statusText;
  };

  //渲染
  render() {
    let data = this.props.data;
    let itemNum = 0;
    let isDealer = this.props.user.isDealer();
    data.deliverys &&
      data.deliverys.map(obj => {
        obj.items &&
          obj.items.map(obj1 => {
            itemNum = itemNum + obj1.itemNum;
          });
      });
    // Log('status === ',toJS(data.status));
    let year = 2020;
    let month = 1;
    let day = 1;
    try {
      year = data.orderTime.substring(0, 4);
      month = data.orderTime.substring(5, 7);
      day = data.orderTime.substring(8, 10);
    } catch (e) { }
    return (
      <View
        style={{
          width: width,
          backgroundColor: Constant.colorDefault,
          marginBottom: Constant.sizeMarginDefault
        }}
      >
        <TouchableOpacity style={{ flex: 1 }} onPress={this.goToOrderDetail}>
          <View
            style={{
              width: width,
              flexDirection: "row",
              minHeight: Constant.scale(63),
              alignItems: "center",
              justifyContent: "space-between"
            }}
          >
            <View
              style={{
                flex: 1,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center",
                paddingBottom: Constant.scale(10),
                paddingTop: Constant.scale(10)
              }}
            >
              <Image
                style={{
                  width: Constant.scale(17),
                  height: Constant.scale(15),
                  marginLeft: Constant.sizeMarginDefault
                }}
                source={IC_SHOP_ICON}
              />
              <View style={{ flex: 1 }}>
                <Text
                  style={{
                    color: Constant.colorTxtTitle,
                    fontSize: Constant.fontSizeNormal,
                    marginLeft: Constant.sizeMarginDefault
                  }}
                >
                  {data.supplierOrgName}
                </Text>
                <Text
                  style={{
                    color: Constant.colorTxtTitle,
                    fontSize: Constant.fontSizeNormal,
                    marginLeft: Constant.sizeMarginDefault,
                    marginTop: Constant.scale(5)
                  }}
                >
                  订单号：{data.orderNo}
                </Text>

                <Text
                  style={{
                    color: Constant.colorTxtTitle,
                    fontSize: Constant.fontSizeNormal,
                    marginLeft: Constant.sizeMarginDefault,
                    marginTop: Constant.scale(5)
                  }}
                >
                  订单日期：{`${year}年${month}月${day}日`}
                </Text>

                {data.zpOrderTypeName ? (
                  <Text
                    style={{
                      color: Constant.colorTxtTitle,
                      fontSize: Constant.fontSizeNormal,
                      marginLeft: Constant.sizeMarginDefault,
                      marginTop: Constant.scale(5)
                    }}
                  >
                    {data.zpOrderTypeName}
                  </Text>
                ) : null}

                {this.props.user.isDealer() ? null : //     </Text> //         分销商：{data.distributorName} //     > //         }} //             marginTop: Constant.scale(5) //             marginLeft: Constant.sizeMarginDefault, //             fontSize: Constant.fontSizeNormal, //             color: Constant.colorTxtTitle, //         style={{ //     <Text // (data.buyerType == 5 ||  data.buyerType == 2||  data.buyerType == 4 ) && data.distributorName?
                  //
                  //     : null
                  data.buyerType == 5 ||
                    data.buyerType == 2 ||
                    data.buyerType == 1 ? (
                    <Text
                      style={{
                        color: Constant.colorTxtTitle,
                        fontSize: Constant.fontSizeNormal,
                        marginLeft: Constant.sizeMarginDefault,
                        marginTop: Constant.scale(5)
                      }}
                    >
                      经销商：{data.dealerName}
                    </Text>
                  ) : null}
              </View>
            </View>
            <Text
              style={{
                color: "#FF7E00",
                fontSize: Constant.fontSizeNormal,
                marginRight: Constant.sizeMarginDefault
              }}
            >
              {this.renderStatus(data.status)}
            </Text>
          </View>
        </TouchableOpacity>
        <View
          style={{
            width: width,
            height: Constant.sizeDividerNormal,
            backgroundColor: Constant.colorDivider
          }}
        />
        {/* 赊销功能 */}
        {this.renderArrears(data)}
        {data.deliverys && data.deliverys.length
          ? data.deliverys.map((item, index) => {
            return this.renderItem(
              item,
              index,
              data.buyerType,
              isDealer,
              data.status
            );
          })
          : null}
        {/*0：待转单 1：待付款，2：待审核 31：待配货 4：待发货 41：待收货 6：已完成 9：已取消 91：已退单 */}
        <View style={{ width: width }}>
          {!isDealer ? (
            <View
              style={{
                flexDirection: "row",
                alignItems: "flex-end",
                justifyContent: "flex-end",
                margin: Constant.sizeMarginDefault
              }}
            >
              <Text
                style={{
                  fontSize: Constant.fontSizeSmall,
                  color: Constant.colorTxtAlert
                }}
              >
                共{itemNum}件商品
              </Text>
            </View>
          ) : (
            <View
              style={{
                flexDirection: "row",
                alignItems: "flex-end",
                justifyContent: "flex-end",
                margin: Constant.sizeMarginDefault
              }}
            >
              <Text
                style={{
                  fontSize: Constant.fontSizeSmall,
                  color: Constant.colorTxtAlert
                }}
              >
                共{itemNum}件商品，实付款
              </Text>
              <PriceText price={data.payAmount} size={1} />
            </View>
          )}
          <View
            style={{
              width: width,
              alignItems: "center",
              justifyContent: "flex-end",
              flexDirection: "row",
              marginBottom: Constant.sizeMarginDefault
            }}
          >
            {this.renderBtn(isDealer &&  (data.uploadFileStatus == 0 || data.uploadFileStatus == 2)
              , '上传欠款单', () => { this.goToPayMent(data) })}
             
            {data.buyerType == 5 && !isDealer ? null : (
              <Button
                style={{ borderRadius: 5, borderColor: Constant.colorTxtAlert }}
                txtStyle={{
                  fontSize: Constant.fontSizeSmall,
                  color: Constant.colorTxtAlert
                }}
                onPress={this.buyAgain}
              >
                再次购买
              </Button>
            )}

            {isDealer ? (
              data.status == 0 || data.status == 1 ? (
                <Button
                  style={{
                    borderRadius: 5,
                    borderColor: Constant.colorTxtAlert
                  }}
                  txtStyle={{
                    fontSize: Constant.fontSizeSmall,
                    color: Constant.colorTxtAlert
                  }}
                  onPress={this.cancelOrder}
                >
                  取消订单
                </Button>
              ) : null
            ) : (data.status == 0 || data.status == 1) &&
              data.buyerType != 5 ? (
              <Button
                style={{ borderRadius: 5, borderColor: Constant.colorTxtAlert }}
                txtStyle={{
                  fontSize: Constant.fontSizeSmall,
                  color: Constant.colorTxtAlert
                }}
                onPress={this.cancelOrder}
              >
                取消订单
              </Button>
            ) : null}

            {/*0：待转单 1：待付款，2：待审核 31：待配货 4：待发货 41：待收货 6：已完成 9：已取消 91：已退单 */}

            {data.status == 1 && this.props.user.isDealer() ? (
              <Button
                style={{
                  borderRadius: 5,
                  borderColor: Constant.colorTxtPrimary
                }}
                txtStyle={{
                  fontSize: Constant.fontSizeSmall,
                  color: Constant.colorTxtPrimary
                }}
                onPress={this.renderMergerPay}
              >
                去付款
              </Button>
            ) : null}

            {data.status == 0 && this.props.user.isDealer() ? (
              <Button
                style={{
                  borderRadius: 5,
                  borderColor: Constant.colorTxtPrimary
                }}
                txtStyle={{
                  fontSize: Constant.fontSizeSmall,
                  color: Constant.colorTxtPrimary
                }}
                onPress={() => {
                  this.turnOrder(data.deliverys[0]);
                }}
              >
                转单
              </Button>
            ) : null}
          </View>
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  checkBox: {
    paddingRight: Constant.sizeMarginDefault,
    width: Constant.scale(15) + Constant.sizeMarginDefault,
    height: Constant.scale(15)
  },
  bgViewStyle: {
    alignItems: "center",
    justifyContent: "flex-start",
    overflow: "scroll",
    backgroundColor: "white",
    width: Constant.scale(250),
    height: Constant.scale(286.5)
  }
});
