import React, { Component } from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    Image,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    ListView,
    Platform
} from 'react-native';
import { ReactNavComponent, Widget, Util } from 'rn-yunxi';
import AppWidget from '../../app-widget'
const { width, height } = Dimensions.get('window');
const { Text, CommonListView } = Widget;
const { Header, PrimaryHeader, Button } = AppWidget;
import { DatePicker } from 'antd-mobile';
import Toast from 'react-native-root-toast'


const dateIcon = require('../img/date.png');

const DateFilterCell = (props, extra) => {
    return (
        <TouchableOpacity
            onPress={() => props.onClick()}>
            <View style={styles.leftFilterStyle}>
                <Image source={dateIcon} resizeMode={'contain'} style={styles.dateImgStyle} />
                <Text style={[styles.contentStyle, props.extra.indexOf('日期') < 0 && { color: Constant.colorTxtDefault }]}>{props.extra}</Text>
            </View>
        </TouchableOpacity>
    )
};


//模块声名并导出
export default class DateFilter extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {
        title: React.PropTypes.any,
        startDateText: React.PropTypes.any,
        startDate: React.PropTypes.any,
        endDateText: React.PropTypes.any,
        endDate: React.PropTypes.any,
        onStartDateChange: React.PropTypes.any,
        onEndDateChange: React.PropTypes.any,

    };
    //默认属性
    static defaultProps = {
    };
    //构造函数
    constructor(props) {
        super(props);
        //状态机变量声明
        this.state = {
            startDateText: this.props.startDateText || '', //开始时间 text
            startDate: this.props.startDate || null,        //开始时间
            endDateText: this.props.endDateText || '',     //结束时间 text
            endDate: this.props.endDate || null,            //结束时间
        };
    }

    componentWillReceiveProps(nextProps) {
        if (nextProps.startDateText != this.state.startDateText) {
            this.state.startDateText = nextProps.startDateText
        }
        if (nextProps.startDate != this.state.startDate) {
            this.state.startDate = nextProps.startDate
        }
        if (nextProps.endDateText != this.state.endDateText) {
            this.state.endDateText = nextProps.endDateText
        }
        if (nextProps.endDate != this.state.endDate) {
            this.state.endDate = nextProps.endDate
        }

        this.setState({

        })
    }

    //渲染
    render() {
        return (
            <View style={styles.container}>
                <View style={styles.topViewStyle}>
                    <Text style={styles.titleStyle}>{this.props.title || '标题'}</Text>
                </View>

                <View style={styles.filterStyle}>
                    <DatePicker
                        mode="date"
                        title="起始日期"
                        extra={this.state.startDateText || '起始日期'}
                        value={this.state.startDate}
                        onChange={(date) => {
                            let temp = Util.DateUtil.formatDate(new Date(date).getTime(), 'yyyy-MM-dd')
                            this.props.onStartDateChange && this.props.onStartDateChange(temp, date)
                            this.setState({
                                startDate: date,
                                startDateText: temp

                            }, () => this.compareDate(1))
                        }}
                    >
                        <DateFilterCell />
                    </DatePicker>

                    <Text style={{ marginTop: Constant.scale(0), marginLeft: Constant.scale(7), marginRight: Constant.scale(7) }}>__</Text>

                    <DatePicker
                        mode="date"
                        title="结束日期"
                        extra={this.state.endDateText || '结束日期'}
                        value={this.state.endDate}
                        onChange={(date) => {
                            let temp = Util.DateUtil.formatDate(new Date(date).getTime(), 'yyyy-MM-dd')
                            this.props.onEndDateChange && this.props.onEndDateChange(temp, date)
                            this.setState({
                                endDate: date,
                                endDateText: temp

                            }, () => this.compareDate(2))
                        }}
                    >
                        <DateFilterCell />
                    </DatePicker>
                </View>
                <View style={GlobalStyle.styleDividerDefault} />
            </View>
        );
    }

    compareDate = (type) => {
        if (this.state.startDateText && this.state.endDateText) {
            let beginDate = new Date(this.state.startDate);
            let endDate = new Date(this.state.endDate);
            if (this.state.startDateText != this.state.endDateText) {
                if (beginDate > endDate) {
                    Toast.show('开始日期不能大于结束日期', {
                        duration: Toast.durations.LONG,
                        position: Toast.positions.CENTER,
                        shadow: true,
                        animation: true,
                        hideOnPress: true,
                        delay: 0,
                        backgroundColor: '#000000'
                    })

                    if (type == 1) {
                        this.props.onStartDateChange && this.props.onStartDateChange(null, null)
                        this.setState({
                            startDate: null,
                            startDateText: null

                        })
                    } else if (type == 2) {
                        this.props.onEndDateChange && this.props.onEndDateChange(null, null)
                        this.setState({
                            endDate: null,
                            endDateText: null
                        })
                    }
                }

            }
        }
    }
};
const styles = StyleSheet.create({
    container: {
        width: width,
        height: Constant.scale(80),
        backgroundColor: 'white',
        paddingLeft: Constant.sizeMarginDefault

    },
    topViewStyle: {
        width: width,
        height: Constant.scale(40),
        justifyContent: 'center'
    },
    titleStyle: {
        fontSize: 14,
        color: '#333333'
    },
    filterStyle: {
        width: width,
        height: Constant.scale(39),
        flexDirection: 'row',
    },
    leftFilterStyle: {
        width: Constant.scale(164),
        height: Constant.scale(28),
        borderRadius: 2,
        borderColor: '#ddd8d8',
        borderWidth: Constant.sizeDividerNormal,
        flexDirection: 'row',
        alignItems: 'center'
    },
    rightFilterStyle: {
        width: Constant.scale(164),
        height: Constant.scale(28),
        borderRadius: 2,
        borderColor: '#ddd8d8',
        borderWidth: Constant.sizeDividerNormal,
        flexDirection: 'row',
        alignItems: 'center'
    },
    dateImgStyle: {
        marginLeft: Constant.scale(10),
        width: Constant.scale(12),
        height: Constant.scale(12)
    },
    contentStyle: {
        fontSize: Constant.fontSizeNormal,
        color: '#CCCCCC',
        marginLeft: Constant.scale(15)
    }

});