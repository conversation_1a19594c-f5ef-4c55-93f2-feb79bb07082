import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager,
    DeviceEventEmitter,
    RefreshControl
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'

const {width, height} = Dimensions.get('window');
const {Text, CommonListView, Popup,CommonFlatList} = Widget;
const {Header, PrimaryHeader, Button, Image, PriceText} = AppWidget;
import {toJS} from 'mobx';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
import QuickOrderItem from './QuickOrderItem'

//模块声名并导出

const IC_SELECT = require('../img/order/ic_red_select.png');
const IC_UN_SELECT = require('../img/order/ic_unselect.png');
const IC_INPUT_SELECTE = require('../img/order/ic_gray_selcec.png');
const IC_INPUT_DELETE = require('../img/order/ic_gray_delete.png');
import {inject, observer} from 'mobx-react/native';
import GoodsExtension from '../../store/GoodsExtension';
@inject(stores => ({
    user: stores.user,
    quickOrderStore: stores.quickOrder,
}))
@observer
export default class QuickOrder extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};

    //构造函数
    constructor(props) {
        super(props);
        //状态机变量声明
        this.state = {};
        this.goodsExtensionStore = new GoodsExtension()
    }

    componentWillMount() {
        this.loadListData();
        this.didBlurSubscription = this.props.navigation.addListener(
            'didBlur',
            payload => {
                console.debug('didBlur');
            }
        );

        this.willDidFocusSubscription = this.props.navigation.addListener(
            'didFocus',
            payload => {
                console.debug('didFocus');
                this.loadListData();
            }
        );

    }

  loadListData = ()=>{
      this.props.quickOrderStore.loadQuickData((itemIdList)=>{
          this.goodsExtensionStore.obtainAllItemOrgList(itemIdList,()=>{
              // 获取托板承载规格列表
              this.goodsExtensionStore.getPalletStruList().then(() => {

                // 托板承载规格列表
                const palletStruList = this.goodsExtensionStore.palletStruList || []
                this.props.quickOrderStore.makeOftenBuyDataList(null, palletStruList);
              });
          }).then().catch(e=>{})
      }).then().catch(e=>{});
  }

    componentWillUnmount() {

        this.didBlurSubscription.remove();
        this.willDidFocusSubscription.remove();
    }

    //渲染
    render() {
        const logArr = this.props.quickOrderStore.oftenListData || [];
        console.log('new - 商品列表 = ' + JSON.stringify(logArr));
        return (


            <Header showBackAction={false}
                    title={'快捷下单'}
                    rightAction={() => {
                        this.props.quickOrderStore.setEditStatus(this.props.quickOrderStore.isEditStatus);
                    }}
                    rightTitle={this.props.quickOrderStore.isEditStatus ? '完成' : '编辑'}>
                <View style={styles.container}>
                    <KeyboardAwareScrollView
                        contentContainerStyle={{flex: 1}}
                    >
                    {/**下单列表*/}
                    <ScrollView
                        style={{flex:1}}
                        // contentContainerStyle={styles.container}
                        refreshControl={
                            <RefreshControl
                                enabled={true}
                                refreshing={this.props.quickOrderStore.isLoading}
                                onRefresh={() => {
                                    this.loadListData()
                                }}
                                tintColor={Constant.colorPrimary}
                                title="加载中..."
                                titleColor={'black'}
                                progressBackgroundColor="white"
                            />}


                    >
                        { this.props.quickOrderStore.oftenListData && this.props.quickOrderStore.oftenListData.length>0 ?
                            this.props.quickOrderStore.oftenListData.map((item,index)=>{
                                // Log('查询商品itemId',item.itemId,toJS(this.goodsExtensionStore.obtainItemAwards(item.itemId)))
                                return(
                                    <QuickOrderItem
                                        navigation={this.props.navigation}
                                        orgList ={this.goodsExtensionStore.orgListMap.get(item.itemId)}
                                        awardsMap={this.goodsExtensionStore.obtainItemAwards(item.itemId)}
                                        isEditStatus={this.props.quickOrderStore.isEditStatus}
                                        onItemChoose={() => {
                                            this.props.quickOrderStore.selectItem(index)
                                        }}
                                        onItemNumConfirm={(text) => {
                                            this.props.quickOrderStore.modifyItemNum(index, text, false,item.isZeroFourGoods)
                                        }}
                                        palletStruList = {this.goodsExtensionStore.palletStruList || []}
                                        listIndex = {index}
                                        key={'item-' + '-' + index}
                                        data={item}/>
                                )
                            })
                            :
                            this.renderNoDataView()
                        }

                    </ScrollView>
                </KeyboardAwareScrollView>
                </View>
                {
                    this.props.quickOrderStore.isEditStatus ?
                        this.renderBottomChooseView()
                        :
                        this.renderBottomCountView()
                }
            </Header>
        );
    }

    renderNoDataView = ()=>{
        return(
            <View style={{flex: 1, marginTop:Constant.scale(250), alignItems: 'center'}}>
                <Text style={{fontSize: Constant.fontSizeBig, color: Constant.colorTxtContent}}>
                    暂无数据
                </Text>
            </View>
        )
    }




    //完成状态下底部统计View
    renderBottomCountView = () => {
        let {totalItem, totalCount, totalPrice, totalDiscountAmount} = this.props.quickOrderStore.selectGoodsCount;
        return (
            <View style={{
                width: width,
                height: Constant.scale(55),
                backgroundColor: 'white',
                flexDirection: 'row',
                borderColor: Constant.colorDivider,
                borderTopWidth: Constant.sizeDividerNormal
            }}>
                <TouchableOpacity onPress={() => this.clearItemNum()}>
                    <View style={{height: '100%', justifyContent: 'center'}}>
                        <Text style={{marginLeft: Constant.sizeMarginDefault}}>清空</Text>
                    </View>
                </TouchableOpacity>

                <View style={{flex: 1, paddingRight: Constant.scale(10), paddingTop: Constant.scale(10), alignItems: 'flex-end'}}>
                    <Text style={{color: Constant.colorTxtContent, fontSize: 12}}>共{totalItem}种商品 {totalCount}件</Text>
                    <Text style={{color:Constant.colorTxtPrimary}}>总价:<PriceText price={totalPrice}/></Text>

                </View>

                <TouchableOpacity onPress={() => this.confirmOrderClick()}>
                    <View style={{
                        width: Constant.scale(105),
                        height: '100%',
                        backgroundColor: Constant.colorPrimary,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>
                        <Text style={{color: 'white', fontSize: 16}}>结算</Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }

    //编辑状态下底部选择View
    renderBottomChooseView = () => {
        return (
            <View style={{
                width: width,
                height: Constant.scale(55),
                backgroundColor: 'white',
                flexDirection: 'row',
                borderColor: Constant.colorDivider,
                borderTopWidth: Constant.sizeDividerNormal
            }}>
                <TouchableOpacity style={{flex: 1}} onPress={() => this.allSelectClick()}>
                    <View style={{flex: 1, backgroundColor: '#FFFFFF', paddingLeft: Constant.sizeMarginDefault}}>
                        <Image source={this.props.quickOrderStore.isSelectAll ? IC_SELECT : IC_UN_SELECT}
                               style={{width: Constant.scale(15), height: Constant.scale(15), marginTop: Constant.scale(10)}}/>
                        <Text style={{marginTop: Constant.scale(5), color: Constant.colorTxtContent, fontSize: 12}}>全选</Text>
                    </View>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => this.removeClick()}>
                    <View style={{
                        width: Constant.scale(105),
                        height: '100%',
                        backgroundColor: Constant.colorPrimary,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>
                        <Text style={{color: 'white', fontSize: 16}}>移除</Text>
                    </View>
                </TouchableOpacity>
            </View>
        )
    }


    //清空操作
    clearItemNum = () => {
        this.props.quickOrderStore.clearItemNum()
    }

    //结算  确认下单
    confirmOrderClick = () => {
      this.props.quickOrderStore.confirmOrder().then(
          (json)=>{
              if (json && json.data){
                  this.navigate('ConfirmOrder', {data:json.data,type:1})
              }
          }
      ).catch((e)=>{})
    }

    //移除操作
    removeClick = () => {
        this.props.quickOrderStore.removeItem(()=>{this.loadListData()})
    }

    //全选
    allSelectClick = () => {
        this.props.quickOrderStore.allSelectClick(this.props.quickOrderStore.isSelectAll);
    }


};
const styles = StyleSheet.create({
    container: {
        flex:1,
        backgroundColor: Constant.colorBackgroundDefault
    }
});
