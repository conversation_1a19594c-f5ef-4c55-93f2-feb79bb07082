/**
 * Created by whw on 2018/1/23.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent, Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget';

const {width, height} = Dimensions.get('window');
const {Text} = Widget;
const {PriceText, Image, ArrowRotate,SelectPop} = AppWidget;
const IC_SPECIAL = require('../img/home-page/ic_special.png')
import {toJS} from 'mobx';
import {InteractionManager} from "react-native";
import { observer } from 'mobx-react/native';
import { inject } from 'mobx-react/native';

/**
 * 首页
 */
@inject(stores => ({
    user: stores.user
}))
@observer
export default class OrderGoodsItem extends ReactNavComponent {
    //属性声名
    static propTypes = {
        underLine: PropType.number,
        style: PropType.any
    };
    //默认属性
    static defaultProps = {
        underLine: 0
    };

    //构造函数
    constructor(props) {
        super(props);
        this.state = {//状态机变量声明
            defaultAwardsName:'暂无',
            defaultAwardsId:null,
            defaultPrice:null,
            defaultPriceListId:null,

        };
    }

    componentWillMount() {

        let data = this.props.data ? this.props.data : {};
        let priceList = this.props.priceList ? this.props.priceList:[]

        if (priceList.length>0){
            this.setState({
                defaultPriceListId:priceList[0].priceListId,
                defaultPrice:priceList[0].price
            },()=>{

                this.props.priceTypeCallback &&  this.props.priceTypeCallback(priceList[0].priceType)
                this.props.priceNatureCallback && this.props.priceNatureCallback(priceList[0].priceNature)
            })
        }
        this.setState({
            defaultAwardsName:data.awards,
            defaultAwardsId:data.awardsId,

        })


    }

    componentDidMount() {


    }

    componentWillUnmount() {
    }


    //处理价格
    handlePrice = (priceList,callback)=>{

        for (let priceItem of priceList){
            if(priceItem.priceType ==2){
                Object.assign(priceItem,{priceName:'特殊价：¥ '+ (priceItem.price).toFixed(2)})
            }else if (priceItem.priceType==3){
                Object.assign(priceItem,{priceName:'底价：¥ '+ (priceItem.price).toFixed(2)})
            }else {
                Object.assign(priceItem,{priceName:'标准价：¥ '+ (priceItem.price).toFixed(2)})
            }
        }
        callback && callback()
    }

    //渲染
    render() {
        let data = this.props.data ? this.props.data : {};
        // Log('data == ', toJS(data));
        let awardsList = this.props.awardsList ? this.props.awardsList : [];
        let priceList = this.props.priceList ? this.props.priceList:[];
        let isTurnOrder = this.props.isTurnOrder ? this.props.isTurnOrder : false;
        // let isTurnOrder =  false;
        let lineStyle;


        if (this.props.underLine == 0) {
            lineStyle = {
                height: Constant.sizeDividerNormal,
                marginLeft: Constant.sizeMarginDefault,
                width: width - Constant.sizeMarginDefault,
                backgroundColor: Constant.colorDivider
            };
        } else if (this.props.underLine == 1) {
            lineStyle = {height: Constant.sizeDividerNormal, width: width, backgroundColor: Constant.colorDivider};
        }

        let itemPrice = 0;
        if (isTurnOrder){
            if (this.state.defaultPrice){
                itemPrice = this.state.defaultPrice
            }else {
                itemPrice = data.price
            }

        }else {
            if (data && data.itemPrice){
                itemPrice = data.itemPrice
            }else {
                itemPrice = data.price
            }
        }

        return (
            <View>
                <View style={[{
                    width: width,
                    flexDirection: 'row',
                    backgroundColor: Constant.colorDefault
                    // backgroundColor: Constant.colorPrimary
                }, this.props.style]}>
                    <View style={{marginTop: Constant.sizeMarginDefault, marginLeft: Constant.sizeMarginDefault}}>
                        <Image
                            style={{width: Constant.scale(84), height: Constant.scale(84),backgroundColor:'white'}}
                            source={{uri: data.imgUrl}}
                            resizeMode="contain"
                        />
                    </View>

                    <View style={{flex: 1, margin: Constant.sizeMarginDefault}}>
                        <Text
                            style={{
                                fontSize: Constant.fontSizeNormal,
                                color: Constant.colorTxtTitle,
                                marginBottom: Constant.sizeMarginDefault / 2
                            }}
                        >
                            {data.name ? data.name : data.itemName ? data.itemName : ''}
                        </Text>
                        <View style={{justifyContent: 'space-between', flexDirection: 'row'}}>
                            <View style={{flex:4}}>
                                {data.awards ?
                                    <TouchableOpacity activeOpacity={1} onPress={isTurnOrder ? ()=>this.chooseAwardsClick(awardsList) : null} style={{flexDirection:'row',alignItems:'center',marginBottom: Constant.scale(5)}}>
                                        <Text style={{fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent,}} numberOfLines={2}>
                                            {this.state.defaultAwardsName}
                                        </Text>
                                        {
                                            isTurnOrder ? <ArrowRotate ref={(ref) => { this.awardsArrow = ref; }} style={{ marginLeft: Constant.scale(5) }} />:null
                                        }

                                    </TouchableOpacity>: null}
                                {data.salesChannel ?
                                    <Text style={{fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent, marginBottom: Constant.scale(5)}} numberOfLines={2}>
                                        {(data.salesChannel ? data.salesChannel : '') + (data.subSalesChannel ? '/' + data.subSalesChannel : '')}
                                    </Text> : null}
                                {data.orgName || data.supplierOrgName ?
                                    <Text style={{fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent}} numberOfLines={2}>
                                        {data.orgName || data.supplierOrgName}
                                    </Text> : null}
                            </View>
                            {
                                this.props.showWithPlate ?
                                    <View style={{height: '100%', alignItems: 'flex-end', marginLeft: Constant.scale(5),flex:1,}}>
                                        <Text style={{fontSize: Constant.fontSizeSmall, color: Constant.colorTxtContent}}>
                                            {data.withPlateFlag == 1 ? '带板' : '不带板'}
                                        </Text>
                                    </View> : null
                            }

                        </View>
                        <View style={{flexDirection: 'row', alignItems: 'flex-end', justifyContent: 'space-between', marginTop: Constant.sizeMarginDefault}}>
                            {
                                 !(this.props.user.isDealer()) && !this.props.isConfirmOrder? <View style = {{flex:1}}></View> :
                                    <TouchableOpacity onPress={isTurnOrder && this.props.canEditPrice? ()=>this.choosePriceClick(priceList) : null} style={{flexDirection: 'row', alignItems: 'center'}}>
                                        <PriceText price={itemPrice} />
                                        {/*<PriceText price={isTurnOrder ? ()=>this.handleDefaultPrice(priceList):data.itemPrice} />*/}
                                        {data.priceType == 2 && this.props.isDealer &&  this.props.orderType!=3 ? <Image style={{marginLeft: Constant.scale(3), width: 17, height: 14}} source={IC_SPECIAL}/> : null}
                                        { isTurnOrder? <ArrowRotate ref={(ref) => { this.priceArrow = ref; }} style={{ marginLeft: Constant.scale(5) }} />: null}

                                    </TouchableOpacity>
                            }
                            <Text style={{fontSize: Constant.fontSizeSmall, color: Constant.colorTxtAlert}}>x{data.itemNum}</Text>
                        </View>
                    </View>

                </View>
                <View style={lineStyle}/>
            </View>
        );
    }





    //选择奖项
    chooseAwardsClick = (awardsList,defaultAwardsId)=>{
        Widget.Popup.show(
            <SelectPop
                listData={awardsList}
                selectId={this.state.defaultAwardsId}
                popTitle={'奖项'}
                labelName={'name'}
                selectKey={'id'}
                selectCallBack={(selectData) => {
                    //TODO
                    Widget.Popup.hide();
                    InteractionManager.runAfterInteractions(() => {
                      this.setState({
                          defaultAwardsName:selectData.name,
                          defaultAwardsId:selectData.id
                          },()=>{
                          this.props.awardsIdCallback && this.props.awardsIdCallback(selectData.id)
                      })
                        // this.props.user.setSelectTakeDeliveryOrg(selectData)
                    })
                }}

            />,
            {
                animationType: 'slide-up', backgroundColor: '#00000000',
                onMaskClose: () => {
                    Widget.Popup.hide()
                }
            })
    }

    //选择价格
    choosePriceClick = (priceList)=>{
         {this.handlePrice(priceList,()=>{
             Widget.Popup.show(
                 <SelectPop
                     listData={priceList}
                     selectId={this.state.defaultPriceListId}
                     popTitle={'价格'}
                     labelName={'priceName'}
                     selectKey={'priceListId'}
                     selectCallBack={(selectData) => {
                         //TODO
                         Widget.Popup.hide();
                         InteractionManager.runAfterInteractions(() => {
                             this.setState({
                                 defaultPrice:selectData.price,
                                 defaultPriceListId:selectData.priceListId,
                             },()=>{
                                 // Log('= = = =selectData',selectData)
                                 this.props.priceTypeCallback && this.props.priceTypeCallback(selectData.priceType)
                                 this.props.priceNatureCallback && this.props.priceNatureCallback(selectData.priceNature)
                             })
                             // this.props.user.setSelectTakeDeliveryOrg(selectData)
                         })
                     }}

                 />,
                 {
                     animationType: 'slide-up', backgroundColor: '#00000000',
                     onMaskClose: () => {
                         Widget.Popup.hide()
                     }
                 })
         })}

    }
};

const styles = StyleSheet.create({
    container: {
        flex: 1
    },

});