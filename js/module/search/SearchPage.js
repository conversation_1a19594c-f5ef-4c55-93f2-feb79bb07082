/**
 *
 * Created by xiaowz on 2018/2/1.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'
const {width, height} = Dimensions.get('window');
import Drawer from 'react-native-drawer'
const {Text, CommonListView,CommonFlatList} = Widget;
const {Header,PrimaryHeader,Button,Image,CommonSearchBar} = AppWidget;
import FilterView from './FilterView'
import GoodsItem from './GoodsItem'
import {toJS} from 'mobx';
import SearchStore from '../../store/Search'
//模块声名并导出
const FILTER_GRAY = require('../img/search/ic_filter_gray.png');
const FILTER_RED = require('../img/search/ic_filter_red.png');
const PRICE_NORMAL = require('../img/search/ic_price_default.png');
const PRICE_UP = require('../img/search/ic_price_up.png');
const PRICE_DOWN = require('../img/search/ic_price_down.png');
const IC_DELETE = require('../img/search/ic_delete.png');

const dataArray= ['111','2','223333','111','2','223333','111','2','223333']
import {inject, observer} from 'mobx-react/native';

@inject(stores => ({

}))
@observer
export default class SearchPage extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {
    };
    //默认属性
    static defaultProps = {
    };
    //构造函数
    constructor(props) {
        super(props);
         //状态机变量声明
        this.searchStore = new SearchStore()
         this.state={
             searchValue:''
         };
    }

    componentWillMount() {
        this.loadHistory();
        this.searchStore.loadGoodsPropertyData().then().catch()
        let { params } = this.getNavState();
        if (params && params.catalogId){
            let param={
                catalogId: params.catalogId,  //类目ID
            }
           this.searchStore.handleFilteroperation(param,0)
        }

    }

    loadHistory() {
       this.searchStore.getLocalhistorySearchData().then().catch(e=>{})
    }

    //渲染
    render() {
        return (

            <Drawer
                ref={(ref) => this._drawer = ref}
                type="overlay"   //displace:overlay:static
                side={'right'}
                tapToClose={true}
                styles={drawerStyles}
                openDrawerOffset={0.12}
                closedDrawerOffset={-1}
                panCloseMask={0.2}
                negotiatePan
                tweenHandler={(ratio) => ({
                    main: { opacity: Math.max(0.54, 1 - ratio) },
                })}

                content={this.renderFilterPan()}
            >
                <PrimaryHeader navigation={this.props.navigation}
                               showBackAction={true}
                               callback={()=>this.searchStore.clearAllData()}
                               style={{backgroundColor:'#FFFFFF'}}
                               defaultStatusBar={true}
                               contentView={
                                   <CommonSearchBar
                                       ref={ (e) => this._searchBar = e }
                                       cancelBtnStyle={{color:Constant.colorPrimary}}
                                       placeholder={'输入搜索关键字'}
                                       placeholderTextColor={Constant.colorTxtAlert}
                                       onSearchBtnClick = {()=>this.searchBtnClick(this.searchStore.searchValue)}
                                       onChange={(text)=>this.searchStore.getSearchText(text)}
                                       // cancelClick={() => this.cancelClick()}
                                       text={this.searchStore.searchValue}
                                       clear={()=>this.searchStore.clearAllData()}
                                       onFocus={()=>this.searchStore.getSearchText(this.searchStore.searchValue)}
                                       isOnFocus={false}
                                   />}>
                    {
                        this.searchStore.searchPageType == 3 ? this.renderSearchResultView() :  //搜索结果页
                            // this.searchStore.searchPageType == 2 ? this.renderSearchKeywordList() :     //搜索关键字联想页
                                this.renderSearchRecordView()   //搜索记录页
                    }

                </PrimaryHeader>

            </Drawer>

        );
    }

    //搜索记录页  searchStore.searchPageType == 1
    renderSearchRecordView = ()=>{
        return(
            <View style={styles.initSearchPageViewStyle}>
                <View style={{width:width,height:Constant.scale(45),backgroundColor:'#FFFFFF',flexDirection:'row',alignItems:'center',padding:Constant.scale(15),justifyContent:'space-between'}}>
                    <Text style={{color:'#C1C1C1',fontSize:12}}>最近搜索</Text>
                    <TouchableOpacity onPress={()=>this.clearSearchRecord()}>
                    <Image resizeMode={'contain'} source={IC_DELETE} style={{width:15,height:15}}/>
                    </TouchableOpacity>
                </View>
                <ScrollView contentContainerStyle={{backgroundColor:'#FFFFFF',  flexDirection:'row', flexWrap:'wrap'}}>
                    { this.searchStore.lastestSearchArray.map((obj,i)=>{
                        return(
                            <TouchableOpacity onPress={()=>this.searchSignClick(obj)} key={i}>
                                <View style={styles.signViewStyle}>
                                    <Text style={{color:Constant.colorTxtAlert,fontSize:14}}>{obj}</Text>
                                </View>
                            </TouchableOpacity>
                        )
                    })}
                </ScrollView>

            </View>
        )
    }

    //点击搜索记录标签
    searchSignClick = (text)=> {
        this.searchStore.getSearchText(text)
    }

    //搜索关键字联想页  searchStore.searchPageType == 2
    renderSearchKeywordList = ()=>{
        return(
            <View style={styles.keywordsSearchListStyle}>
                <View  style={{width:width,height:Constant.scale(224),backgroundColor:"#FFFFFF"}}>
                <ScrollView contentContainerStyle={{backgroundColor:'#FFFFFF',paddingLeft:Constant.sizeMarginDefault}}>
                    { dataArray.map((obj,i)=>{
                        return(
                            <TouchableOpacity key={i}>
                                <View style={styles.aboutSearchTextStyle}>
                                    <Text style={{marginLeft:Constant.scale(70),color:Constant.colorTxtAlert,fontSize:14}}>{obj}</Text>
                                </View>
                            </TouchableOpacity>
                        )
                    })}
                </ScrollView>
                </View>
            </View>
        )
    }
    //搜索结果页  searchStore.searchPageType == 3
    renderSearchResultView = ()=>{
        return(
            <View style={styles.searchDataViewStyle}>
                {this.renderFilterTab()}
                {this.renderListView()}
            </View>
        )
    }

    //搜索结果页顶部筛选Tab
    renderFilterTab = ()=>{

        let { params } = this.getNavState();
        let param={}
        if (params && params.catalogId) {
            param.catalogId = params.catalogId //类目ID

        }
        return(
            <View style={styles.filterTabViewStyle}>
                <TouchableOpacity style={styles.tabBtnStyle} onPress={()=>this.searchStore.sortPrducts(0,param)}>
                    <Text style={this.searchStore.defaultSort?styles.tabSelectedTitleStyle:styles.tabTitleStyle}>默认</Text>
                </TouchableOpacity>

                {/*<TouchableOpacity style={styles.tabBtnStyle} onPress={()=>this.searchStore.sortPrducts(1,param)}>*/}
                    {/*<Text style={this.searchStore.priceSort?styles.tabSelectedTitleStyle:styles.tabTitleStyle}>价格</Text>*/}
                    {/*<Image resizeMode={'contain'} source={this.searchStore.priceImgSort == '1'?PRICE_UP:this.searchStore.priceImgSort == '2'?PRICE_DOWN:PRICE_NORMAL} style={{marginLeft:Constant.scale(5),width:Constant.scale(12),height:Constant.scale(15)}}/>*/}
                {/*</TouchableOpacity>*/}

                <TouchableOpacity style={styles.tabBtnStyle} onPress={()=>this.searchStore.sortPrducts(3,param)}>
                    <Text style={this.searchStore.vipPriceSort?styles.tabSelectedTitleStyle:styles.tabTitleStyle}>特殊价</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.tabBtnStyle} onPress={()=>this.openControlPanel()}>
                    <Text style={styles.tabTitleStyle}>筛选</Text>
                    <Image resizeMode={'contain'} source={FILTER_GRAY} style={{marginLeft:Constant.scale(5),width:Constant.scale(12),height:Constant.scale(12)}}/>
                </TouchableOpacity>

            </View>
        )
    }

    //搜索结果列表
    renderListView = (text)=>{
        let { params } = this.getNavState();
        let param={}
        if (params && params.catalogId){
            param.catalogId = params.catalogId //类目ID
        }
       return(
           <CommonListView
               style={{flex:1}}
               contentContainerStyle={{flexDirection:'row',flexWrap:'wrap',justifyContent:'space-between'}}
               listViewRef={ref => this.listView = ref}
               onRefresh={() => this.searchStore.onRefresh(param)}
               onLoadMore={() => this.searchStore.onLoadMore(param)}
               renderRow={this.renderRow}
               dataSource={this.searchStore.dataSource}
                listState={this.searchStore.listState}
               enableLoadMore={this.searchStore.enableLoadMore}
               enableRefresh={false}
           >
               <View style={[{flex: 1, justifyContent: 'center', alignItems: 'center',}]}>

                   <Text style={{fontSize: Constant.fontSizeBig, color: Constant.colorTxtContent, marginTop: Constant.scale(25)}}>
                       暂无数据
                   </Text>
               </View>
           </CommonListView>
       )

    }
    renderRow = (rowData, sectionID, rowID)=>{
        return(
            <TouchableOpacity onPress={()=>this.goodsDetailClick(rowData.itemId,rowData.defaultOrgId)}>
            <GoodsItem data={rowData}/>
            </TouchableOpacity>
        )
    }

    //商品详情
    goodsDetailClick = (itemId,orgId)=>{
        this.navigate('GoodsDetail',{itemId:itemId,defaultOrgId:orgId})
        // this.props.navigation.navigate('GoodsDetail',{itemId:this.props.data.itemId});
    }

    //筛选页面
    renderFilterPan = ()=>{
        return(
           <FilterView
               searchStore ={this.searchStore}
               seriesIdList = {toJS(this.searchStore.seriesIdList)}
               capacityIdList = {toJS(this.searchStore.capacityIdList)}
               technologyIdList = {toJS(this.searchStore.technologyIdList)}
               concentrationIdList = {toJS(this.searchStore.concentrationIdList)}
               packingIdList = {toJS(this.searchStore.packingIdList)}
               specificationsIdList = {toJS(this.searchStore.specificationsIdList)}
               containerIdList = {toJS(this.searchStore.containerIdList)}
               callBack={()=>this.closeControlPanel()}
           />
        )

    }

    //产品筛选  打开抽屉
    openControlPanel = () => {
        this._drawer.open()
    };

    //产品筛选  关闭抽屉
    closeControlPanel = () => { // 关闭抽屉
        this._drawer.close()
    };


    //搜索结果排序
    searchResultFilterClick = (text)=>{
        Log(text)
    }


    //点击搜索
    searchBtnClick = (text)=>{
        Log('12123123')
        if (text){
            this.searchStore.saveHistoryKeyword(text).then().catch(e=>{})
        }
        this.searchStore.loadSearchData(text)
    }

    //清空搜索记录
    clearSearchRecord = ()=>{
      this.searchStore.clearSearchRecord()
    }

};

const drawerStyles = {
    drawer: { shadowColor: '#000000',  shadowOffset:{height:5,width:5}, shadowRadius:3, shadowOpacity:1, },
    main: { paddingLeft: 0 },
}
const styles = StyleSheet.create({
   container: {
        flex: 1,
        backgroundColor:Constant.colorBackgroundDefault
    },
    initSearchPageViewStyle:{
        flex:1,
        backgroundColor:'white'
    },
    keywordsSearchListStyle:{
       flex:1,
        backgroundColor:Constant.colorBackgroundDefault
    },
    searchDataViewStyle:{
        flex:1,
        backgroundColor:Constant.colorBackgroundDefault
    },
    signViewStyle:{
        justifyContent:'center',
        alignItems:'center',
        overflow:'hidden',
        borderRadius:Constant.scale(4),
        borderWidth:Constant.sizeDividerNormal,
        borderColor:'#E1E1E1',
        minWidth:Constant.scale(80),
        height:Constant.scale(27),
        backgroundColor:'#FFFFFF',
        marginBottom:Constant.scale(15),
        marginLeft:Constant.scale(15),
        paddingLeft:Constant.scale(10),
        paddingRight:Constant.scale(10),
    },
    aboutSearchTextStyle:{
        width:'100%',
        height:Constant.scale(44),
        borderBottomWidth:Constant.sizeDividerNormal,
        borderColor:Constant.colorDivider,
        justifyContent:'center'
    },
    filterTabViewStyle:{
        width:width,
        height:Constant.scale(40),
        flexDirection:'row',
        borderBottomWidth:Constant.sizeDividerNormal,
        borderColor:Constant.colorDivider,
        backgroundColor:'#FFFFFF',

    },
    tabBtnStyle:{
        flex:1,
        flexDirection:'row',
        alignItems:'center',
        justifyContent:'center'

    },
    tabTitleStyle:{
        color:'#666666',
        fontSize:14
    },
    tabSelectedTitleStyle:{
        color:Constant.colorPrimary,
        fontSize:14
    },

});
