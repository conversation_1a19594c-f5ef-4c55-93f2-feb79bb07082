/**
 *
 * Created by xiaowz on 2018/3/1.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'
const {width, height} = Dimensions.get('window');
const {Text, CommonListView,CommonFlatList} = Widget;
const {Header,PrimaryHeader,Button,Image} = AppWidget;
import {toJS} from 'mobx';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
import GoodsPropertyButton from './GoodsPropertyButton'
const IC_SHOW = require('../img/search/ic_show.png')
const IC_HIDE = require('../img/search/ic_hide.png')
const dataArray= ['关键字','关键字','关键字','关','关','关键字关键字','关键字关键字关键字','关键字']
//模块声名并导出

import {inject, observer} from 'mobx-react/native';

@inject(stores => ({

}))
@observer

export default class GoodsPropertyView extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {
    };
    //默认属性
    static defaultProps = {
    };
    //构造函数
    constructor(props) {
        super(props);
         //状态机变量声明
         this.state={
             isShowAll:false,

         };
    }

    //渲染
    render() {
        let dataArray = this.props.data
        return (
            <View>
                <TouchableOpacity onPress={()=>this.showItemClick()}>
                <View style={{height:Constant.scale(30),flexDirection:'row',justifyContent:'space-between',alignItems:'center'}}>
                    <Text style={{color:'#333333',fontSize:16}}>{this.props.title || '标题'}</Text>
                    <Image source={this.state.isShowAll?IC_HIDE : IC_SHOW} style={{resizeMode:'cover',width:Constant.scale(10),height:Constant.scale(5.5)}}/>
                </View>
                </TouchableOpacity>
                <View style={{flexDirection:'row',flexWrap:'wrap',justifyContent:'flex-start'}}>
                    {dataArray && dataArray.map((obj, i) => {
                        if (!this.state.isShowAll) {
                            if (i < 3) {
                                return (
                                    <GoodsPropertyButton
                                        callBack={(isSelected) => this.handlePropertyItem(i,obj, isSelected)}
                                        // callBack={()=>Log('00000',dataArray)}
                                        data={obj}
                                        key={i}
                                    />
                                )

                            }
                        } else {
                            return (
                                <GoodsPropertyButton
                                    // callBack={()=>Log('00000',dataArray)}
                                    callBack={(isSelected) => this.handlePropertyItem(i,obj, isSelected)}
                                    data={obj}
                                    key={i}
                                />
                            )
                        }


                    })}
                </View>
            </View>
        );
    }



    showItemClick = ()=>{
        this.setState({
            isShowAll :!this.state.isShowAll
        })
    }


    handlePropertyItem = (i,obj,isSelected)=>{
        // let dataArray = this.props.data
        // obj.isSelected = isSelected
       if(this.props.callBack){
            this.props.callBack(i,obj,isSelected)
       }
        // Log('点击后',toJS(dataArray))
    }



};
const styles = StyleSheet.create({
   container: {
        flex: 1,
        backgroundColor:Constant.colorBackgroundDefault
    },
    itemStyle:{
        width:Constant.scale(90),
        height:Constant.scale(27.5),
        borderWidth:Constant.sizeDividerNormal,
        borderColor:Constant.colorDivider,
        justifyContent:'center',
        alignItems:'center',
        borderRadius:Constant.scale(4),
        overflow:'hidden',
        marginRight:Constant.scale(10),
        marginBottom:Constant.scale(15),
        paddingLeft:Constant.scale(5),
        paddingRight:Constant.scale(5)

    }
});
