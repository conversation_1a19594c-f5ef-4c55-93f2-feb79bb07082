/**
 *
 * Created by xiaowz on 2018/3/2.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'
const {width, height} = Dimensions.get('window');
const {Text, CommonListView,CommonFlatList} = Widget;
const {Header,PrimaryHeader,Button,Image} = AppWidget;
import {toJS} from 'mobx';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
//模块声名并导出
export default class GoodsPropertyButton extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {
    };
    //默认属性
    static defaultProps = {
    };
    //构造函数
    constructor(props) {
        super(props);
         //状态机变量声明
         this.state={

        };
    }

    // componentWillReceiveProps(nextProps) {
    //     this.setState({
    //         select:nextProps.data.isSelected
    //     })
    // }

    //渲染
    render() {
        let data  = this.props.data   // { propValueId: 708, propValue: '鲜啤' }
        return (
            <TouchableOpacity onPress={()=>this.goodsItemChooseClick()}>
                <View style={data.isSelected?styles.itemSelectedStyle:styles.itemNormalStyle}>
                    <Text numberOfLines={1} style={data.isSelected?styles.selectedStyle:styles.btnNormalStyle}>{data.propValue}</Text>
                </View>
            </TouchableOpacity>
        );
    }

    goodsItemChooseClick = ()=>{
        let data  = this.props.data

        if (this.props.callBack){
                this.props.callBack(!data.isSelected)
            }



    }



};
const styles = StyleSheet.create({
   container: {
        flex: 1,
        backgroundColor:Constant.colorBackgroundDefault
    },
    itemNormalStyle:{
        width:Constant.scale(90),
        height:Constant.scale(27.5),
        borderWidth:Constant.sizeDividerNormal,
        borderColor:Constant.colorDivider,
        justifyContent:'center',
        alignItems:'center',
        borderRadius:Constant.scale(4),
        overflow:'hidden',
        marginRight:Constant.scale(10),
        marginBottom:Constant.scale(15),
        paddingLeft:Constant.scale(5),
        paddingRight:Constant.scale(5)

    },
    itemSelectedStyle:{
        width:Constant.scale(90),
        height:Constant.scale(27.5),
        borderWidth:Constant.sizeDividerNormal,
        borderColor:Constant.colorPrimary,
        justifyContent:'center',
        alignItems:'center',
        borderRadius:Constant.scale(4),
        overflow:'hidden',
        marginRight:Constant.scale(10),
        marginBottom:Constant.scale(15),
        paddingLeft:Constant.scale(5),
        paddingRight:Constant.scale(5)

    },
    btnNormalStyle:{
       color:Constant.colorTxtAlert,
        fontSize:14
   },
    selectedStyle:{
        color:Constant.colorPrimary,
        fontSize:14
    }
});
