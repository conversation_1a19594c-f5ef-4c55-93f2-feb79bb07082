/**
 *
 * Created by xiaowz on 2018/2/1.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'
const {width, height} = Dimensions.get('window');
const {Text, CommonListView} = Widget;
const {Header,PrimaryHeader,Button,Image} = AppWidget;
import {toJS} from 'mobx';
import GoodsPropertyView from './GoodsPropertyView'
const dataArray=['seriesIdList','2','3','4']

import {inject, observer} from 'mobx-react/native';


@inject(stores => ({

}))
@observer

//模块声名并导出
export default class FilterView extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {
    };
    //默认属性
    static defaultProps = {
    };
    //构造函数
    constructor(props) {
        super(props);
         //状态机变量声明
            this.state={
                seriesIdList:[], //系列
                capacityIdList:[], //容量
                technologyIdList:[], //工艺
                concentrationIdList:[], //浓度
                packingIdList:[], //储运包装
                specificationsIdList:[], //规格
                containerIdList:[], //容器
            };



    }

    componentWillReceiveProps(nextProps) {
        this.setState({
            seriesIdList:nextProps.seriesIdList,
            capacityIdList:nextProps.capacityIdList,
            technologyIdList:nextProps.technologyIdList,
            concentrationIdList:nextProps.concentrationIdList,
            packingIdList:nextProps.packingIdList,
            specificationsIdList:nextProps.specificationsIdList,
            containerIdList:nextProps.containerIdList,
        })
    }

    componentDidMount() {

    }

    //渲染
    render() {

        let data =  this.props.searchStore.goodsPropertyData
        if (!data){
            return(
                <View style={{justifyContent:'center',alignItems:'center',flex:1}}>
                    <Text style={{ fontSize: 14, color: Constant.colorTxtContent,}}>数据加载中~</Text>
                </View>
            )

        }



        return (

            <View style={styles.container}>
                <ScrollView style={{paddingLeft:Constant.scale(15),paddingRight:Constant.scale(15)}}
                            contentContainerStyle={{paddingTop:Constant.scale(60),paddingBottom:Constant.scale(60)}}
                >
                    {/*系列*/}
                    {data.seriesList && data.seriesList.length > 0 ? <GoodsPropertyView callBack={(i, obj, isSelected) => this.handleSelectData(i, obj, isSelected,'seriesIdList')} title={'系列'} data={this.state.seriesIdList} /> :null}
                    {/*容量*/}
                    {data.capacityList && data.capacityList.length>0? <GoodsPropertyView callBack={(i, obj, isSelected) => this.handleSelectData(i, obj, isSelected,'capacityIdList')}  title={'容量'} data={this.state.capacityIdList} /> :null }
                    {/*工艺*/}
                    {data.technologyList && data.technologyList.length>0? <GoodsPropertyView callBack={(i, obj, isSelected) => this.handleSelectData(i, obj, isSelected,'technologyIdList')} title={'工艺'} data={this.state.technologyIdList}  /> :null }
                    {/*浓度*/}
                    {data.concentrationList && data.concentrationList.length>0? <GoodsPropertyView callBack={(i, obj, isSelected) => this.handleSelectData(i, obj, isSelected,'concentrationIdList')}  title={'浓度'} data={this.state.concentrationIdList}  /> :null }
                    {/*储运包装*/}
                    {data.packingList && data.packingList.length>0? <GoodsPropertyView callBack={(i, obj, isSelected) => this.handleSelectData(i, obj, isSelected,'packingIdList')}  title={'储运包装'} data={this.state.packingIdList}  /> :null }
                    {/*规格*/}
                    {data.specificationsList && data.specificationsList.length>0?  <GoodsPropertyView  callBack={(i, obj, isSelected) => this.handleSelectData(i, obj, isSelected,'specificationsIdList')} title={'规格'} data={this.state.specificationsIdList}  /> :null }
                    {/*容器*/}
                    {data.containerList && data.containerList.length>0? <GoodsPropertyView  callBack={(i, obj, isSelected) => this.handleSelectData(i, obj, isSelected,'containerIdList')} title={'容器'} data={this.state.containerIdList} /> :null }

                </ScrollView>
                {/*底部button*/}
                <View style={styles.bottomViewStyle}>
                    <TouchableOpacity onPress={()=>this.resetClick()} style={styles.bottomBtnStyle}>
                        <Text style={{color:'#333333',fontSize:16}}>重置</Text>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={()=>this.defaultClick()} style={[styles.bottomBtnStyle,{backgroundColor:Constant.colorPrimary}]}>
                        <Text  style={{color:'#FFFFFF',fontSize:16}}>确定</Text>
                    </TouchableOpacity>

                </View>

            </View>

        );
    }

    //处理点击按钮之后
    handleSelectData = (i, obj, isSelected,name)=>{
        let newArray = this.state[name].map((item, index) => {
            if (index == i) {
                return Object.assign({}, item, {isSelected: isSelected})
            }else {
                return item
            }

        })
        this.setState({
            [name]:newArray
        })
    }


    //重置点击
    resetClick = ()=>{
        let seriesIdListArray = this.state.seriesIdList.map((item, index) => {
            return Object.assign({}, item, {isSelected: false})

        })
        let capacityIdListArray = this.state.capacityIdList.map((item, index) => {
            return Object.assign({}, item, {isSelected: false})

        })
        let technologyIdListArray = this.state.technologyIdList.map((item, index) => {
            return Object.assign({}, item, {isSelected: false})

        })
        let concentrationIdListArray = this.state.concentrationIdList.map((item, index) => {
            return Object.assign({}, item, {isSelected: false})

        })
        let packingIdListArray = this.state.packingIdList.map((item, index) => {
            return Object.assign({}, item, {isSelected: false})

        })
        let specificationsIdListArray = this.state.specificationsIdList.map((item, index) => {
            return Object.assign({}, item, {isSelected: false})

        })
        let containerIdListArray = this.state.containerIdList.map((item, index) => {
            return Object.assign({}, item, {isSelected: false})

        })

        this.setState({
            seriesIdList:seriesIdListArray,
            capacityIdList:capacityIdListArray,
            technologyIdList:technologyIdListArray,
            concentrationIdList:concentrationIdListArray,
            packingIdList:packingIdListArray,
            specificationsIdList:specificationsIdListArray,
            containerIdList:containerIdListArray,
        })

    }
    //确定点击
    defaultClick = ()=>{
        this.props.callBack && this.props.callBack()
        this.props.searchStore.handleChooseProperty(this.state.seriesIdList,this.state.capacityIdList,this.state.technologyIdList,this.state.concentrationIdList,this.state.packingIdList,this.state.specificationsIdList,this.state.containerIdList)

    }
};
const styles = StyleSheet.create({
   container: {
        flex: 1,
        backgroundColor:'#FFFFFF'
    },
    bottomViewStyle:{
        position:'absolute',
        width:'100%',
        height:Constant.scale(44),
        flexDirection:'row',
        bottom:0,
        left:0
    },
    bottomBtnStyle:{
       width:'50%',
        height:'100%',
        backgroundColor:'#F5F5F5',
        justifyContent:'center',
        alignItems:'center'
    },


});
