/**
 *
 * Created by xiaowz on 2018/3/1.
 * Copyright (c) 2017, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    Dimensions,
    TouchableOpacity,
    ScrollView,
    StatusBar,
    TextInput,
    Alert,
    Platform,
    InteractionManager
} from 'react-native';
import PropType from 'prop-types';
import {ReactNavComponent,Widget} from 'rn-yunxi';
import AppWidget from '../../app-widget'
const {width, height} = Dimensions.get('window');
const {Text, CommonListView,CommonFlatList} = Widget;
const {Header,PrimaryHeader,Button,Image,PriceText} = AppWidget;
import {toJS} from 'mobx';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view'
const IC_SPECIAL = require('../img/home-page/ic_special.png')
//模块声名并导出
export default class GoodsItem extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {
    };
    //默认属性
    static defaultProps = {
    };
    //构造函数
    constructor(props) {
        super(props);
         //状态机变量声明
         this.state={

         };
    }

    //渲染
    render() {
        let data=this.props.data;
        let showPrice = 0
        let isVipPrice = false
        let stock = '暂无库存';
        let priceList = data.priceList ? data.priceList:[];
        for (let item of priceList){
            if (item.stock || item.stock+'' =='0'){
                stock = item.stock+'';
            }else {
                stock = '库存充足';
            }
        }

        if ( data && data.priceList){
                if (data.priceList.length == 1 ) {
                    //只有一个价格
                    showPrice = data.priceList[0].price;  //展示价格
                    if (data.priceList[0].priceType == 2) {
                        isVipPrice = true   //是否特殊价
                    }
                    // if (isVipPrice) {
                    //     stock = data.priceList[0].stock //库存
                    // }


                } else {
                    //多个价格
                    data.priceList.map((obj,i)=>{
                        if (obj.priceType == 1){
                            showPrice = obj.price
                        }
                    })
                }

        }

        return (
            <View style={styles.container}>
                <View  style={{marginTop:Constant.scale(10),marginLeft:Constant.scale(10),width:Constant.scale(165),height:Constant.scale(165)}} >
                    <Image  source={{uri:data.imgUrl}} style={{width:'100%',height:'100%',resizeMode:'cover'}}/>
                </View>
                <View style={{width:Constant.scale(165),height:Constant.scale(40),marginLeft:Constant.scale(10),marginTop:Constant.scale(10)}}>
                    <Text numberOfLines={2} style={{fontSize:14,color:Constant.colorTxtContent}}>{data.name}</Text>
                </View>

                <View style={styles.bottomStyle}>
                    <PriceText price={showPrice} size={1}/>
                    {isVipPrice?<Image source={IC_SPECIAL} resizeMode={'contain'} style={{width:Constant.scale(20),height:Constant.scale(20),marginLeft:Constant.scale(10)}}/>:null}
                </View>

                <Text style={{marginTop:Constant.scale(5),marginLeft:Constant.scale(10),color:Constant.colorTxtAlert,fontSize:12}}>{isVipPrice?'库存：'+(stock):''}</Text>


            </View>
        );
    }
};
const styles = StyleSheet.create({
   container: {
       backgroundColor:'#FFFFFF',
       width:(width-Constant.scale(5))/2,
       height:Constant.scale(280.5),
       marginTop:Constant.scale(5)
    },
    bottomStyle: {
        // backgroundColor:'yellow',
        marginTop:Constant.scale(7),
        marginLeft:Constant.scale(10),
        flexDirection:'row',
        alignItems:'center'
    }
});
