
###构建项目时，git clone源码后

解压目录node_modules.zip包，不需要执行install安装操作、不需要执行install安装操作、不需要执行install安装操作




###react-native更新后授权

执行cd 当前项目/node_modules/react-native

chmod -R 777 scripts/

chmod 755 local-cli/setup_env.sh

### Android 运行测试react-native run-android --variant +渠道名

渠道：在android/app/build.gradle 里的productFlavors字段声名

打包：gradlew assemble+渠道名

清除缓存：gradlew clean

例如渠道名:yunxi

开发：react-native run-android --variant yunxiDebug

打包APK：gradlew assembleYunxi
