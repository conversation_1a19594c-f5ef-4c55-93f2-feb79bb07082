rootProject.name = 'Pearlriver-B2B'
include ':react-native-code-push'
project(':react-native-code-push').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-code-push/android/app')

include ':app'
include ':zxing'
project(':zxing').projectDir = new File(
        rootProject.projectDir, '../../rn-yunxi/android/zxing')
include ':APPayAssistEx'
project(':APPayAssistEx').projectDir = new File(
        rootProject.projectDir, '../../rn-yunxi/android/APPayAssistEx')
include ':qrcodecore'
project(':qrcodecore').projectDir = new File(
        rootProject.projectDir, '../../rn-yunxi/android/qrcodecore')
include ':zxing'
project(':zxing').projectDir = new File(
        rootProject.projectDir, '../../rn-yunxi/android/zxing')
//include ':umengshare'
//project(':umengshare').projectDir = new File(
//        rootProject.projectDir, '../../rn-yunxi/android/umengshare')
include ':image-crop-picker'
project(':image-crop-picker').projectDir = new File(
        rootProject.projectDir, '../../rn-yunxi/android/image-crop-picker')
include ':jpush'
project(':jpush').projectDir = new File(
        rootProject.projectDir, '../../rn-yunxi/android/jpush')
include ':react-native-svg'
project(':react-native-svg').projectDir = new File(
        rootProject.projectDir, '../node_modules/react-native-svg/android')

include ':rn-yunxi'
project(':rn-yunxi').projectDir = new File(
        rootProject.projectDir, '../../rn-yunxi/android/rn-yunxi')
include ':alipay'
project(':alipay').projectDir = new File(
        rootProject.projectDir, '../../rn-yunxi/android/alipay')
include ':wechat'
project(':wechat').projectDir = new File(
        rootProject.projectDir, '../../rn-yunxi/android/wechat')
include ':ReactAndroid'
project(':ReactAndroid').projectDir = new File(
        rootProject.projectDir, '../node_modules/react-native/ReactAndroid')


