<resources>

    <style name="tab">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">1</item>
        <item name="android:padding">10dp</item>
        <item name="android:text">请选择</item>
        <item name="android:textColor">@color/selector_text_color_tab</item>
        <item name="android:textSize">14sp</item>
        <item name="android:visibility">gone</item>
    </style>

    <style name="bottom_dialog" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation_Bottom_Dialog</item>
    </style>

    <style name="Animation_Bottom_Dialog">
        <item name="android:windowEnterAnimation">@anim/bottom_dialog_enter</item>
        <item name="android:windowExitAnimation">@anim/bottom_dialog_exit</item>
    </style>

</resources>
