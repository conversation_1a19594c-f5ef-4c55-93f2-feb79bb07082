<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <HorizontalScrollView
            android:scrollbars="none"
            android:id="@+id/layout_hs_tab"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/layout_tab"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">


            </LinearLayout>
        </HorizontalScrollView>


        <View
            android:id="@+id/indicator"
            android:layout_width="0dp"
            android:layout_height="2dp"
            android:layout_below="@+id/layout_hs_tab"
            android:background="@android:color/holo_red_light" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#e8e8e8" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <chihane.jdaddressselector.widget.UninterceptableListView
            android:id="@+id/listView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:divider="@null" />

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center" />

    </FrameLayout>

</LinearLayout>