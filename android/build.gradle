// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {
        jcenter()
        //maven { url 'https://maven.google.com/' }
        maven {
            url 'https://maven.aliyun.com/repository/google'
            name 'Google'
        }
        google()
    }
    dependencies {
        classpath 'de.undercouch:gradle-download-task:3.1.2'
        classpath 'com.android.tools.build:gradle:3.1.4'
        classpath 'com.sensorsdata.analytics.android:android-gradle-plugin:1.0.6'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        mavenLocal()
        jcenter()
        maven { url "$rootDir/../node_modules/react-native/android" }
        maven { url "https://jitpack.io" }
        // maven { url 'https://maven.google.com/' }
        maven { url 'https://dl.google.com/dl/android/maven2/' }
        maven { url 'http://maven.aliyun.com/nexus/content/repositories/releases/' }
        maven { url 'https://dl.bintray.com/zouyuhan/maven' }
    }
    configurations.all {
        resolutionStrategy {
            force "com.facebook.react:react-native:0.48.4"
        }
    }
}

