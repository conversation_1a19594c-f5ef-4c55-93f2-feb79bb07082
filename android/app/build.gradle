apply plugin: "com.android.application"
apply from: "../../node_modules/react-native/react.gradle"
apply from: "../../node_modules/react-native-code-push/android/codepush.gradle"

import com.android.build.OutputFile


/**
 * The react.gradle file registers a task for each build variant (e.g. bundleDebugJsAndAssets
 * and bundleReleaseJsAndAssets).
 * These basically call `react-native bundle` with the correct arguments during the Android build
 * cycle. By default, bundleDebugJsAndAssets is skipped, as in debug/dev mode we prefer to load the
 * bundle directly from the development server. Below you can see all the possible configurations
 * and their defaults. If you decide to add a configuration block, make sure to add it before the
 * `apply from: "../../node_modules/react-native/react.gradle"` line.
 *
 * project.ext.react = [
 *   // the name of the generated asset file containing your JS bundle
 *   bundleAssetName: "index.android.bundle",
 *
 *   // the entry file for bundle generation
 *   entryFile: "index.android.js",
 *
 *   // whether to bundle JS and assets in debug mode
 *   bundleInDebug: false,
 *
 *   // whether to bundle JS and assets in release mode
 *   bundleInRelease: true,
 *
 *   // whether to bundle JS and assets in another build variant (if configured).
 *   // See http://tools.android.com/tech-docs/new-build-system/user-guide#TOC-Build-Variants
 *   // The configuration property can be in the following formats
 *   //         'bundleIn${productFlavor}${buildType}'
 *   //         'bundleIn${buildType}'
 *   // bundleInFreeDebug: true,
 *   // bundleInPaidRelease: true,
 *   // bundleInBeta: true,
 *
 *   // the root of your project, i.e. where "package.json" lives
 *   root: "../../",
 *
 *   // where to put the JS bundle asset in debug mode
 *   jsBundleDirDebug: "$buildDir/intermediates/assets/debug",
 *
 *   // where to put the JS bundle asset in release mode
 *   jsBundleDirRelease: "$buildDir/intermediates/assets/release",
 *
 *   // where to put drawable resources / React Native assets, e.g. the ones you use via
 *   // require('./image.png')), in debug mode
 *   resourcesDirDebug: "$buildDir/intermediates/res/merged/debug",
 *
 *   // where to put drawable resources / React Native assets, e.g. the ones you use via
 *   // require('./image.png')), in release mode
 *   resourcesDirRelease: "$buildDir/intermediates/res/merged/release",
 *
 *   // by default the gradle tasks are skipped if none of the JS files or assets change; this means
 *   // that we don't look at files in android/ or ios/ to determine whether the tasks are up to
 *   // date; if you have any other folders that you want to ignore for performance reasons (gradle
 *   // indexes the entire tree), add them here. Alternatively, if you have JS files in android/
 *   // for example, you might want to remove it from here.
 *   inputExcludes: ["android/**", "ios/**"],
 *
 *   // override which node gets called and with what additional arguments
 *   nodeExecutableAndArgs: ["node"]
 *
 *   // supply additional arguments to the packager
 *   extraPackagerArgs: []
 * ]
 */



/**
 * Set this to true to create two separate APKs instead of one:
 *   - An APK that only works on ARM devices
 *   - An APK that only works on x86 devices
 * The advantage is the size of the APK is reduced by about 4MB.
 * Upload all the APKs to the Play Store and people will download
 * the correct one based on the CPU architecture of their device.
 */
def enableSeparateBuildPerCPUArchitecture = false

/**
 * Run Proguard to shrink the Java bytecode in release builds.
 */
def enableProguardInReleaseBuilds = false





android {
    compileSdkVersion 25
    buildToolsVersion '25.0.3'
    signingConfigs {
        pearlriver{
            keyAlias 'pearlriver'
            keyPassword 'pearlriver2018'
            storeFile file('../keystores/pearlriver.jks')
            storePassword 'pearlriver2018'
        }

    }
    def DEF_VERSION_CODE = 21
    def DEF_VERSION_NAME ='1.1.7'
    defaultConfig {
        applicationId "com.yunxi.android"
        minSdkVersion 16
        targetSdkVersion 25
        flavorDimensions ""
        versionCode project.hasProperty('VERSION_CODE') ? Integer.parseInt(VERSION_CODE) : DEF_VERSION_CODE
        versionName project.hasProperty('VERSION_NAME') ? VERSION_NAME : "${DEF_VERSION_NAME}"
        multiDexEnabled true
        ndk {
            abiFilters "armeabi-v7a", "x86"
        }
        buildConfigField 'String','SA_URL','""'
        buildConfigField 'String','SA_CONFIG','""'
        buildConfigField 'String','PUSH_COMPONENT','"JPUSH"'//MI_PUSH、Ali_PUSH、JPUSH //TODO 注意
        buildConfigField 'String','HOT_FIX_APP_ID','""'//TODO 注意
        buildConfigField 'String','HOT_FIX_APP_SECRET','""'//TODO 注意
        buildConfigField 'String','HOT_FIX_RSA','""'//TODO 注意
        manifestPlaceholders = [
                JPUSH_PKGNAME : applicationId,
                JPUSH_CHANNEL : "developer-default", //暂时填写默认值即可.
                JPUSH_APPKEY : "22429563879068359374ed4a", //JPush上注册的包名对应的appkey.
                UMENG_APP_KEY: "",
                CODEPUSH_KEY: "irrzkDY55yqNKHkQ7uTc9c9OlB1Zf3ff89f9-ede8-4c9b-aa19-edbc44897b18",
                AMAP_KEY: "",
                ALIPUSH_APP_KEY: "",
                ALIPUSH_APP_SECRET: "",
        ]
    }
    splits {
        abi {
            reset()
            enable enableSeparateBuildPerCPUArchitecture
            universalApk false  // If true, also generate a universal APK
            include "armeabi-v7a", "x86"
        }
    }

    buildTypes {
        release {
            debuggable false
            signingConfig signingConfigs.pearlriver
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            buildConfigField "String", "CODEPUSH_KEY", '"irrzkDY55yqNKHkQ7uTc9c9OlB1Zf3ff89f9-ede8-4c9b-aa19-edbc44897b18"'
        }
        debug {
            signingConfig signingConfigs.pearlriver
            buildConfigField "String", "CODEPUSH_KEY", '"irrzkDY55yqNKHkQ7uTc9c9OlB1Zf3ff89f9-ede8-4c9b-aa19-edbc44897b18"'
        }

    }
    // applicationVariants are e.g. debug, release
    applicationVariants.all { variant ->
        variant.outputs.each { output ->
            // For each separate APK per architecture, set a unique version code as described here:
            // http://tools.android.com/tech-docs/new-build-system/user-guide/apk-splits
            def versionCodes = ["armeabi-v7a":1, "x86":2]
            def abi = output.getFilter(OutputFile.ABI)
            def oldFile = output.packageApplication.outputDirectory.toPath().relativize(rootDir.toPath()).toFile()
            if (abi != null) {  // null for the universal-debug, universal-release variants
                output.versionCodeOverride =
                        versionCodes.get(abi) * 1048576 + defaultConfig.versionCode
            }


            def file = output.outputFileName
            def date=releaseTime();

//            def fileName = file.name.replace(".apk", "-v" + versionName + "-c" + versionCode + "-d"+date+".apk")
            if(variant.buildType.name.contains('release')){
                output.outputFileName  = new File(oldFile.parentFile, "PearlRiver-B2B-${variant.productFlavors[0].name}-${buildType.name}-v"+versionName+"-c"+versionCode+"-d"+date+".apk")
            }

        }
    }


    productFlavors {
        yunxi {
            //公司环境
            buildConfigField 'String','SA_URL','""'
            buildConfigField 'String','SA_CONFIG','""'
            buildConfigField 'boolean','USE_DEFAULT_SA','false'
            buildConfigField 'boolean','isCanChangeEvn','true'
            buildConfigField 'boolean','isShowEvn','true'
            buildConfigField 'String','hostKey','"ZP_TEST"'
        }
       qa{
            //测试环境
            buildConfigField 'String','SA_URL','""'
            buildConfigField 'String','SA_CONFIG','""'
            buildConfigField 'boolean','USE_DEFAULT_SA','true'
            buildConfigField 'boolean','isCanChangeEvn','false'
            buildConfigField 'boolean','isShowEvn','false'
            buildConfigField 'String','hostKey','"QA"'
        }
        pro {
            //生产环境
            buildConfigField 'String', 'SA_URL', '""'
            buildConfigField 'String', 'SA_CONFIG', '""'
            buildConfigField 'boolean', 'USE_DEFAULT_SA', 'true'
            buildConfigField 'boolean', 'isCanChangeEvn', 'false'
            buildConfigField 'boolean', 'isShowEvn', 'true'
            buildConfigField 'String', 'hostKey', '"PRO"'
        }

    }

    dexOptions {
        preDexLibraries = false
        jumboMode true
        javaMaxHeapSize "2g"
    }
}
repositories {
    jcenter()
}
dependencies {
    implementation project(':react-native-code-push')
    implementation project(':react-native-svg')
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'com.kaopiz:kprogresshud:1.0.2'
    implementation 'com.android.support:appcompat-v7:25.3.0'
    implementation 'com.android.support:multidex:1.0.1'
    implementation 'com.jaredrummler:android-processes:1.0.9'
    //    compile 'com.facebook.react:react-native:+'
    implementation project(':ReactAndroid')
    //源码引入
    // 如果你需要支持GIF动图
    implementation project(':rn-yunxi')
    implementation 'com.aliyun.ams:alicloud-android-hotfix:3.1.8'
    implementation project(':APPayAssistEx')
    implementation project(':jpush')
    //    compile 'com.jaeger.statusbarutil:library:1.5.1'
//    compile 'com.gyf.barlibrary:barlibrary:2.3.0'
    implementation project(':alipay')
    implementation project(':wechat')
}

// Run this once to be able to run the application with BUCK
// puts all compile dependencies into folder libs for BUCK to use
task copyDownloadableDepsToLibs(type: Copy) {
    from configurations.compile
    into 'libs'
}
def releaseTime() {
    return new Date().format("yyyyMMddHHmm")
}

configurations.all {
    exclude group: 'com.facebook.react', module: 'react-native'
}

