<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.yunxi.android"
    android:versionCode="1"
    android:versionName="1.0">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!--阿里推送增加权限-->
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="REORDER_TASKS" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <!--用于进行网络定位-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"></uses-permission>
    <!--用于访问GPS定位-->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"></uses-permission>
    <!--用于获取运营商信息，用于支持提供运营商信息相关的接口-->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"></uses-permission>
    <!--用于访问wifi网络信息，wifi信息会用于进行网络定位-->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"></uses-permission>
    <!--用于获取wifi的获取权限，wifi信息会用来进行网络定位-->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"></uses-permission>
    <!--用于读取手机当前的状态-->
    <uses-permission android:name="android.permission.READ_PHONE_STATE"></uses-permission>
    <!--用于申请调用A-GPS模块-->
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS"></uses-permission>
    <!--用于申请获取蓝牙信息进行室内定位-->
    <uses-permission android:name="android.permission.BLUETOOTH"></uses-permission>
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"></uses-permission>


    <uses-sdk
        android:minSdkVersion="16"
        android:targetSdkVersion="22" />

    <application
        android:name="com.yunxi.android.SophixStubApplication"
        android:allowBackup="true"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:theme="@style/AppTheme">
        <activity android:name="com.facebook.react.devsupport.DevSettingsActivity" />

        <activity
            android:name="com.yunxi.android.activity.YunXiReactNativeActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:screenOrientation="sensorPortrait"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>


        <activity
            android:name="com.yunxi.common.activity.qrcode.QRCodeActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:screenOrientation="sensorPortrait">

        </activity>

        <activity-alias
            android:name="com.yunxi.android.MainActivity"
            android:targetActivity="com.yunxi.android.activity.YunXiReactNativeActivity" />
        <!--微信 配置开始 -->
        <!--<activity android:name="${applicationId}.wxapi.WXEntryActivity" />-->
        <activity android:name="${applicationId}.wxapi.WXPayEntryActivity" />
        <!--<activity android:name="com.yunxi.common.activity.wxapi.WXEntryActivity" />-->
        <activity android:name="com.yunxi.wechat.wxapi.WXPayEntryActivity" />

        <!--<activity-alias-->
            <!--android:name="${applicationId}.wxapi.WXEntryActivity"-->
            <!--android:exported="true"-->
            <!--android:label="@string/app_name"-->
            <!--android:targetActivity="com.yunxi.common.activity.wxapi.WXEntryActivity" />-->
        <activity-alias
            android:name="${applicationId}.wxapi.WXPayEntryActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:targetActivity="com.yunxi.wechat.wxapi.WXPayEntryActivity" />
        <!--高德定位-->
        <service android:name="com.amap.api.location.APSService" />

        <!--有盟分享 配置开始-->
        <!--<activity-->
            <!--android:name="com.umeng.qq.tencent.AuthActivity"-->
            <!--android:launchMode="singleTask"-->
            <!--android:noHistory="true">-->

            <!--<intent-filter>-->
                <!--<action android:name="android.intent.action.VIEW" />-->

                <!--<category android:name="android.intent.category.DEFAULT" />-->
                <!--<category android:name="android.intent.category.BROWSABLE" />-->

                <!--<data android:scheme="tencent100424468" />-->
            <!--</intent-filter>-->
        <!--</activity>-->

        <!--<activity-->
            <!--android:name="com.umeng.qq.tencent.AssistActivity"-->
            <!--android:configChanges="orientation|keyboardHidden|screenSize"-->
            <!--android:screenOrientation="portrait"-->
            <!--android:theme="@android:style/Theme.Translucent.NoTitleBar" />-->


        <!--<activity-->
            <!--android:name="com.umeng.socialize.media.WBShareCallBackActivity"-->
            <!--android:configChanges="keyboardHidden|orientation"-->
            <!--android:exported="false"-->
            <!--android:screenOrientation="portrait"-->
            <!--android:theme="@android:style/Theme.Translucent.NoTitleBar"/>-->

        <!--<activity-->
            <!--android:name="com.sina.weibo.sdk.share.WbShareTransActivity"-->
            <!--android:launchMode="singleTask"-->
            <!--android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen">-->
            <!--<intent-filter>-->
                <!--<action android:name="com.sina.weibo.sdk.action.ACTION_SDK_REQ_ACTIVITY" />-->
                <!--<category android:name="android.intent.category.DEFAULT" />-->
            <!--</intent-filter>-->

        <!--</activity>-->
        <!--有盟分享 配置结束-->
        <!--支付宝 配置开始-->
        <activity
            android:name="com.alipay.sdk.app.H5PayActivity"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustResize|stateHidden"/>
        <activity
            android:name="com.alipay.sdk.app.H5AuthActivity"
            android:configChanges="orientation|keyboardHidden|navigation"
            android:exported="false"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustResize|stateHidden"/>
        <!--支付宝 配置结束-->



        <!--高德地图-->
        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="${AMAP_KEY}" />
        <!--神策埋点-->
        <meta-data
            android:name="com.sensorsdata.analytics.android.ResourcePackageName"
            android:value="${applicationId}" />

        <!--有盟分享-->
        <meta-data
            android:name="UMENG_APPKEY"
            android:value="${UMENG_APP_KEY}" />
        <!--网络监听-->
        <receiver android:name="com.yunxi.common.receiver.NetworkConnectChangedReceiver"/>
    </application>

</manifest>
