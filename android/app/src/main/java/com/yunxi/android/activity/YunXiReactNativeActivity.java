package com.yunxi.android.activity;

import android.app.ActivityManager;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkRequest;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.support.v4.app.NavUtils;
import android.support.v4.content.LocalBroadcastManager;
import android.support.v7.app.AlertDialog;
import android.support.v7.app.AppCompatActivity;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.Toast;

import com.facebook.react.ReactInstanceManager;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.modules.core.DefaultHardwareBackBtnHandler;
import com.facebook.react.modules.core.PermissionAwareActivity;
import com.facebook.react.modules.core.PermissionListener;
//import com.umeng.socialize.UMShareAPI;
import com.yunxi.android.BuildConfig;
import com.yunxi.android.YunXiApplication;
import com.yunxi.common.module.YunXiReactPackage;
import com.yunxi.android.rn.ReactActivityDelegate;
import com.yunxi.common.receiver.NetworkConnectChangedReceiver;
//import com.yunxi.common.service.DownloadAPKService;

import javax.annotation.Nullable;

/**
 * Created by lao.jianfeng on 2017/3/16.
 */

public class YunXiReactNativeActivity extends AppCompatActivity implements DefaultHardwareBackBtnHandler, PermissionAwareActivity {
    public static final String TAG = "YunXiReactNative";
    public static final String KEY_BUNDLE = "key_bundle";
    public static final String KEY_VIEW = "key_view";
    public static final String KEY_FOR_RESULT = "key_for_result";
    private String mCurrentView = "";
    private final ReactActivityDelegate mDelegate;
    private long mLastDoublePressBackTime = 0;
    private NetworkConnectChangedReceiver networkConnectChangedReceiver = new NetworkConnectChangedReceiver();
    boolean isExitApplication = false;
    private BroadcastReceiver hotFixReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null && intent.getAction().equals("sophix_request_update")) {
                isExitApplication = true;
                showHotFixDialog();
            }
        }
    };

    private void showHotFixDialog() {
        new AlertDialog.Builder(this).setMessage("发现补订更新，是否重启应用").setPositiveButton("立刻升级 ", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                restartApplication();
            }
        }).setNegativeButton("稍后升级", null).show();
    }

    public YunXiReactNativeActivity() {
        mDelegate = createReactActivityDelegate();
    }

    private void restartApplication() {
        //重启应用
        Intent intent = new Intent(this, YunXiReactNativeActivity.class);
        PendingIntent restartIntent = PendingIntent.getActivity(this, 0, intent, Intent.FLAG_ACTIVITY_NEW_TASK);
        AlarmManager mgr = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
        mgr.set(AlarmManager.RTC, System.currentTimeMillis() + 1000, restartIntent);
        android.os.Process.killProcess(android.os.Process.myPid());
    }


    /**
     * Returns the name of the main component registered from JavaScript.
     * This is used to schedule rendering of the component.
     * e.g. "MoviesApp"
     */
    protected
    @Nullable
    String getMainComponentName() {
        return "YunXiRN";
    }

    /**
     * Called at construction time, override if you have a custom delegate implementation.
     */
    protected ReactActivityDelegate createReactActivityDelegate() {
        return new ReactActivityDelegate(this, getMainComponentName());
    }


    public YunXiApplication getYunXiApplication() {
        return (YunXiApplication) getApplication();
    }

    public String getCurrentView() {
        return mCurrentView;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.i(TAG, "onCreate>>" + (getIntent() != null && getIntent().getExtras() != null ? getIntent().getExtras() : ""));

        Bundle bundle = new Bundle();
        bundle.putString("hostKey", BuildConfig.hostKey);
        bundle.putBoolean("isCanChangeEvn", BuildConfig.isCanChangeEvn);
        bundle.putBoolean("isShowEvn", BuildConfig.isShowEvn);
//        Log.v(TAG,"onCreate>>"+this);

        mDelegate.setLaunchOptions(bundle);
        mDelegate.onCreate(savedInstanceState);
        try {
//            if(Build.VERSION.SDK_INT<24){
            IntentFilter filter = new IntentFilter();
            filter.addAction("android.net.conn.CONNECTIVITY_CHANGE");
            filter.addAction("android.net.wifi.WIFI_STATE_CHANGED");
            filter.addAction("android.net.wifi.STATE_CHANGE");
            registerReceiver(networkConnectChangedReceiver, filter);
            IntentFilter filter2 = new IntentFilter();
            filter2.addAction("sophix_request_update");
            LocalBroadcastManager.getInstance(this).registerReceiver(hotFixReceiver, filter2);
        } catch (Exception e) {

        }
//
    }

    @Override
    protected void onPause() {
        super.onPause();
        mDelegate.onPause();
    }

    @Override
    protected void onResume() {
        super.onResume();
        mDelegate.onResume();

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mDelegate.onDestroy();
        try {
            LocalBroadcastManager.getInstance(this).unregisterReceiver(hotFixReceiver);
            unregisterReceiver(networkConnectChangedReceiver);
        } catch (Exception e) {
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        mDelegate.onActivityResult(requestCode, resultCode, data);
//        UMShareAPI.get(this).onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        return mDelegate.onKeyUp(keyCode, event) || super.onKeyUp(keyCode, event);
    }

    @Override
    public void onBackPressed() {
        Log.d(TAG, "onBackPressed");
        if (!mDelegate.onBackPressed()) {
            super.onBackPressed();
        }
    }

    @Override
    public void invokeDefaultOnBackPressed() {
        Log.d(TAG, "invokeDefaultOnBackPressed");
        if (System.currentTimeMillis() - mLastDoublePressBackTime < 2000) {
            super.onBackPressed();
        } else {
            Toast.makeText(this, "再次按返回键退出应用", Toast.LENGTH_LONG).show();
            mLastDoublePressBackTime = System.currentTimeMillis();
        }

    }

    @Override
    public void onNewIntent(Intent intent) {
        if (!mDelegate.onNewIntent(intent)) {
            super.onNewIntent(intent);
        }
    }

    @Override
    public void requestPermissions(
            String[] permissions,
            int requestCode,
            PermissionListener listener) {
        mDelegate.requestPermissions(permissions, requestCode, listener);
    }

    @Override
    public void finish() {
        super.finish();
        if (isExitApplication) {
            android.os.Process.killProcess(android.os.Process.myPid());
        }
    }

    @Override
    public void onRequestPermissionsResult(
            int requestCode,
            String[] permissions,
            int[] grantResults) {
        YunXiReactPackage.onRequestPermissionsResult(requestCode, permissions, grantResults); // very important event callback
        mDelegate.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    protected final ReactNativeHost getReactNativeHost() {
        return mDelegate.getReactNativeHost();
    }

    protected final ReactInstanceManager getReactInstanceManager() {
        return mDelegate.getReactInstanceManager();
    }

    protected final void loadApp(String appKey) {
        mDelegate.loadApp(appKey);
    }


    /**
     * 获取点击事件
     */
    @Override

    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_DOWN) {
            View view = getCurrentFocus();
            if (isHideInput(view, ev)) {
                HideSoftInput(view.getWindowToken());
                view.clearFocus();
            }
        }
        return super.dispatchTouchEvent(ev);
    }

    /**
     * 判定是否需要隐藏
     */
    private boolean isHideInput(View v, MotionEvent ev) {
        if (v != null && (v instanceof EditText)) {
            int[] l = {0, 0};
            v.getLocationInWindow(l);
            int left = l[0], top = l[1], bottom = top + v.getHeight(), right = left + v.getWidth();
            if (ev.getX() > left && ev.getX() < right && ev.getY() > top && ev.getY() < bottom) {
                return false;
            } else {
                return true;
            }
        }
        return false;
    }

    /**
     * 隐藏软键盘
     */
    private void HideSoftInput(IBinder token) {
        if (token != null) {
            InputMethodManager manager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            manager.hideSoftInputFromWindow(token, InputMethodManager.HIDE_NOT_ALWAYS);
        }
    }

}
