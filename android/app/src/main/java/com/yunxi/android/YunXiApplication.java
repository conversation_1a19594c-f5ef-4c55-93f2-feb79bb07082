package com.yunxi.android;

import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;

import android.content.Intent;
import android.content.IntentFilter;
import android.support.multidex.MultiDex;
import android.support.v4.content.LocalBroadcastManager;
import android.util.Log;
import com.facebook.react.ReactApplication;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.react.shell.MainReactPackage;
import com.facebook.soloader.SoLoader;
import com.horcrux.svg.SvgPackage;
import com.reactnative.ivpusic.imagepicker.PickerPackage;
import com.yunxi.alipay.AlipayPackage;
import com.yunxi.android.activity.YunXiReactNativeActivity;
import com.yunxi.appayassistex.APPayAssistExPackage;
import com.yunxi.common.module.YunXiReactPackage;
import com.yunxi.common.push.PushBuilder;
import com.yunxi.common.push.PushUtil;
import com.yunxi.common.util.AppSpUtil;
import com.yunxi.jpush.JPushBuilder;
import com.yunxi.wechat.WeChatPackage;
import com.microsoft.codepush.react.CodePush;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class YunXiApplication extends Application implements ReactApplication {
    public static final String TAG = "YunXiApplication";

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
//        MultiDex.install(this);
//        initSophix();
    }

    private final ReactNativeHost mReactNativeHost = new ReactNativeHost(this) {

       @Override
       protected String getJSBundleFile() {
           return CodePush.getJSBundleFile();
       }

        @Override
        public boolean getUseDeveloperSupport() {
            return BuildConfig.DEBUG;
        }


        @Override
        protected List<ReactPackage> getPackages() {

            List<ReactPackage> result = new ArrayList<>();
            result.add(new MainReactPackage());
            result.add(new PickerPackage());
            result.add(new YunXiReactPackage());
            result.add(new SvgPackage());
            result.add(new APPayAssistExPackage());
            result.add(new AlipayPackage());
            result.add(new WeChatPackage());
            result.add(new CodePush(BuildConfig.CODEPUSH_KEY, YunXiApplication.this, BuildConfig.DEBUG));
//            result.add(new CodePush(getResources().getString(R.string.reactNativeCodePush_androidDeploymentKey), YunXiApplication.this, BuildConfig.DEBUG));
            Log.v(TAG, "getPackages>>"+result);
            return result;
        }
    };

    @Override
    public ReactNativeHost getReactNativeHost() {
        return mReactNativeHost;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.v(TAG, "onCreate>>>" + System.currentTimeMillis() + "," + this);
        AppSpUtil.init(this);


        //埋点初始化
//        SensorsDataModule.initSA(this,BuildConfig.SA_URL,BuildConfig.SA_CONFIG,BuildConfig.DEBUG);
        IntentFilter intentFilter=new IntentFilter();
        intentFilter.addAction("sophix_no_update");
        LocalBroadcastManager.getInstance(this).registerReceiver(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                SoLoader.init(getApplicationContext(), /* native exopackage */ false);
                LocalBroadcastManager.getInstance(getApplicationContext()).unregisterReceiver(this);
            }
        },intentFilter);
//        Log.v(TAG,"onCreate>>>RN SoLoader.init>>"+System.currentTimeMillis());
        if (PushUtil.shouldInit(this)) {
            PushBuilder pushBuilder = null;
            if (BuildConfig.PUSH_COMPONENT.equals(PushUtil.MI_PUSH)) {
//                pushBuilder = new MiPushBuilder();
            } else if (BuildConfig.PUSH_COMPONENT.equals(PushUtil.ALI_PUSH)) {
//                pushBuilder = new AliPushBuilder();
            } else if (BuildConfig.PUSH_COMPONENT.equals(PushUtil.JPUSH)) {
                JPushBuilder.OpenMainActivityClass= YunXiReactNativeActivity.class;
                pushBuilder = new JPushBuilder();
            }
            //推送初始化
            PushUtil.init(this,pushBuilder);
        }
    }


}
