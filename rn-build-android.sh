#!/bin/sh
branches=$(git symbolic-ref --short -q HEAD)
tag=$(git describe --tags --abbrev=0)
gitVersion=${tag#*v}
date=$(date +%Y%m%d%H%M)
echo "当前分支:${branches}，当前TAG：${tag}，打包时间：${date}"
echo "请输入打包版本名：x.x.x"
#VersionName='1.0.0'
read -p "例如：1.0.0 > " VersionName
echo "打包VersionName：${VersionName}"
#请配置如下打包信息
echo "选择打包渠道?"
select inputTarget in "yunxi" "qa" "pro"; do
break;
done
#inputTarget=$1
echo "你选择渠道是${inputTarget}"
#渠道名
targetName=${inputTarget}
#默认蒲公英账号
APIKey=""
UserKey=""
buildFlavor=""
buildVersion=""
#蒲公英配置的参数
if [ $inputTarget = "yunxi" ]; then
APIKey="416fba750458a6f32f4a7ebc0913a2cf"
UserKey="cdeaa480d7bce7315ef74b1f39e7a8e6"
buildFlavor=assembleYunxi
buildVersion="${VersionName}-alpha.${date}"
elif [ $inputTarget = "qa" ]; then
APIKey="7f06a0d483dec1a59e488f31b45ca84c"
UserKey="322636aeb3afb532b2a20695ad32eb95"
buildFlavor=assembleQa
buildVersion="${VersionName}-qa.${date}"
elif [ $inputTarget = "pro" ]; then
APIKey="c9ddf5159884f41c6b171bc05e48d729"
UserKey="3d21c40c887af0bad6672dd867e70ec2"
buildFlavor=assemblePro
buildVersion=${VersionName}
fi
echo "APIKey:${APIKey},打包版本名：${buildVersion}"
#编译条件 Release Debug 两种
configuration=Release
#
#echo "请输入此版本更新的内容描述:\n"
#answer=''
#read answer

cd android
./gradlew ${buildFlavor} -PVERSION_NAME=${buildVersion}
