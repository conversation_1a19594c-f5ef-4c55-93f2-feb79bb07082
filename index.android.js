/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 * @flow
 */
import { AppRegistry, Text, TextInput } from "react-native";
import Root from "./js/Root";

// 修改Text默认属性，不跟随系统字体大小(加上fontFamily: 'System'是为了解决设置粗体的时候一加5t手机不显示的问题)
TextInput.defaultProps = Object.assign({}, TextInput.defaultProps, {
  allowFontScaling: false,
  fontFamily: "System"
});

// 重写Text组件的render方法，添加默认的fontFamily样式属性，解决部分手机数字加粗后显示不全的问题；
const oldRender = Text.render;
Text.render = function(...args) {
  const originText = oldRender.call(this, ...args);
  return React.cloneElement(originText, {
    allowFontScaling: false,
    style: [originText.props.style, { fontFamily: "System" }]
  });
};

AppRegistry.registerComponent("YunXiRN", () => Root);
