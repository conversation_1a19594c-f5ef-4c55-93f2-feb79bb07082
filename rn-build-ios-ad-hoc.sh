#!/bin/sh
branches=$(git symbolic-ref --short -q HEAD)
tag=$(git describe --tags --abbrev=0)
gitVersion=${tag#*v}
date=$(date +%Y%m%d%H%M)
echo "当前分支:${branches}，当前TAG：${tag}，打包时间：${date}"
echo "请输入打包版本名：x.x.x"
#VersionName='1.0.0'
read -p "例如：1.0.0 > " VersionName
echo "打包VersionName：${VersionName}"
#请配置如下打包信息
echo "选择打包渠道?"
select inputTarget in "yunxi" "qa" "pro"; do
break;
done
#inputTarget=$1
echo "你选择渠道是${inputTarget}"
#项目中文昵称
projectChineseName=pearlriver-b2b
#项目工程名字
projectName=BaseRN
#渠道名
targetName=${inputTarget}
#默认蒲公英账号
APIKey=""
UserKey=""
buildVersion=""
#app文件中Info.plist文件路径
AppInfoPlistPath=./ios/${projectName}/constants/${inputTarget}/Info.plist
# 获取version值原来值
VersionInitial=$(/usr/libexec/PlistBuddy -c "print CFBundleShortVersionString" ${AppInfoPlistPath})
#蒲公英配置的参数
if [ $inputTarget = "yunxi" ]; then
APIKey="416fba750458a6f32f4a7ebc0913a2cf"
UserKey="cdeaa480d7bce7315ef74b1f39e7a8e6"
buildVersion="${VersionName}-alpha.${date}"
elif [ $inputTarget = "qa" ]; then
APIKey="7f06a0d483dec1a59e488f31b45ca84c"
UserKey="322636aeb3afb532b2a20695ad32eb95"
buildVersion="${VersionName}-qa.${date}"
elif [ $inputTarget = "pro" ]; then
APIKey="c9ddf5159884f41c6b171bc05e48d729"
UserKey="3d21c40c887af0bad6672dd867e70ec2"
buildVersion=${VersionName}
fi
echo "APIKey:${APIKey},打包版本名：${buildVersion}"
#编译条件 Release Debug 两种
configuration=Release
#设置打包Version
/usr/libexec/Plistbuddy -c "Set CFBundleShortVersionString ${buildVersion}" "${AppInfoPlistPath}"
bundle=./ios/bundle

if [ -d "${bundle}" ]; then
rm -rf ${bundle}
fi

#mkdir ${bundle}

#react-native bundle --platform ios --assets-dest ${bundle} --dev false --entry-file index.ios.js --bundle-output ${bundle}/main.jsbundle

cd ios
#记录一下当前jsbundle路径
jsbundlePath=`pwd`
xcodebuild -target ${targetName} clean
xcodebuild archive -project ${projectName}.xcodeproj -scheme ${targetName}  -configuration ${configuration}  -archivePath ../${projectName}.xcarchive

#创建文件夹
exportPathDir=~/Desktop/${projectChineseName}-ipa包历史记录

if [ ! -d "${exportPathDir}" ]; then
  mkdir ${exportPathDir}
fi

#根据时间创建对应的文件目录
exportPath=${exportPathDir}/${targetName}-$(date "+%Y-%m-%d日%H:%M:%S")

if [ ! -d "${exportPath}" ]; then
  mkdir ${exportPath}
fi

xcodebuild -exportArchive -archivePath ../${projectName}.xcarchive -exportOptionsPlist ../AdHocOptions.plist -exportPath "${exportPath}"


#还原Version
cd ..
/usr/libexec/Plistbuddy -c "Set CFBundleShortVersionString ${VersionInitial}" "${AppInfoPlistPath}"

cd ${exportPath}

#压缩拷贝jsbundle到文件目录中去
#cd ${jsbundlePath}
#zipFile=${projectName}_iOS_jsbundle.zip
#zip -r ${zipFile} bundle
#mv ${zipFile} ${exportPath}

#保留xcarchive文件
cd ${jsbundlePath}
cd ..
mv ./${projectName}.xcarchive ${exportPath}
rm -rf ${bundle}
cd ${exportPath}
pwd
uploadFile=$(find *.ipa)
echo "上传文件名：${uploadFile}"
#上传至蒲公英
curl -i -F "file=@${uploadFile}" \
-F "uKey=$UserKey" \
-F "_api_key=$APIKey" \
-F "updateDescription=" \
http://qiniu-storage.pgyer.com/apiv1/app/upload \


open .
