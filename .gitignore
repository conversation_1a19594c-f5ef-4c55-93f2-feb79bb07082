# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml
.vscode
__tests__

# node.js
#
#node_modules/
npm-debug.log
yarn-error.log
typings/

# BUCK
buck-out/
\.buckd/
#*.keystore

# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://github.com/fastlane/fastlane/blob/master/fastlane/docs/Gitignore.md

fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
#rn-yunxi lib
rn-yunxi
node_modules.zip
*.log
node_modules/*

!node_modules/react-native-svg/
node_modules/react-native/ReactAndroid/downloads/._.DS_Store
