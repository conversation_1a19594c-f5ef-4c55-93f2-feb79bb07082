//
//  PrefixHeader.pch
//  BaseRN
//
//  Created by 云徙科技 on 17/5/9.
//  Copyright © 2017年 Facebook. All rights reserved.
//

#ifndef PrefixHeader_pch
#define PrefixHeader_pch
#import <UIKit/UIKit.h>

// Include any system framework and library headers here that should be included in all compilation units.
// You will also need to set the Prefix Header build setting of one or more of your targets to reference this file.
//#define AMap_Key @"13498e6c72ba5dbe799bfe2f82e2ffcb"//高德地图key(自己)
#define AMap_Key @""//高德地图key（茅台）
#define <PERSON><PERSON>_PushKey @""//阿里云推送key
#define Aliyun_PushSecret @""//阿里云推送secret
#define J_Push<PERSON><PERSON> @"22429563879068359374ed4a"//极光推送key（自己）
#define J_PushSecret @""//极光推送secret
#define USHARE_DEMO_APPKEY @"59816cab7f2c7428e900095c"//友盟分享
#define AliPay_Scheme @"Alipay2016122604626182"
#define PUSH_COMPONENT @"J_PUSH"
//神策默认配置
#define DEFAULT_SA_CONFIG_URL @""
#define DEFAULT_SA_URL @""
#define USE_DEFAULT_SA YES
#define DEFAULT_SA_DEBUG [NSNumber numberWithBool:NO]

#define IOS11 @available(iOS 11.0, *)  //判断iOS11
#define is_iPhoneX [UIScreen mainScreen].bounds.size.width == 375.0f && [UIScreen mainScreen].bounds.size.height == 812.0f   //判断是否iPhoneX

//打包host配置
#define USE_DEFAULT_SA YES
#define isCanChangeEvn NO
#define isShowEvn NO
#define hostKey @"PRO"


#ifdef DEBUG
#define NSLog(format, ...) printf("[%s] %s [第%d行] %s\n", __TIME__, __FUNCTION__, __LINE__, [[NSString stringWithFormat:format, ## __VA_ARGS__] UTF8String]);
#else
#define NSLog(format, ...)
#endif

#endif /* PrefixHeader_pch */
