<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>zh_CN</string>
	<key>CFBundleDisplayName</key>
	<string>珠啤易购Test</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIcons</key>
	<dict/>
	<key>CFBundleIcons~ipad</key>
	<dict/>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string></string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0.8</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wx84b4d83101d38d47</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weibo</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wb2548619362</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>qq</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>tencent1105124390</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>alipay</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>Alipay2016122604626182</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>11</string>
	<key>CodePushDeploymentKey</key>
	<string>$(CODEPUSH_KEY)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>wechat</string>
		<string>weixin</string>
		<string>sinaweibohd</string>
		<string>sinaweibo</string>
		<string>sinaweibosso</string>
		<string>weibosdk</string>
		<string>weibosdk2.5</string>
		<string>mqqapi</string>
		<string>mqq</string>
		<string>mqqOpensdkSSoLogin</string>
		<string>mqqconnect</string>
		<string>mqqopensdkdataline</string>
		<string>mqqopensdkgrouptribeshare</string>
		<string>mqqopensdkfriend</string>
		<string>mqqopensdkapi</string>
		<string>mqqopensdkapiV2</string>
		<string>mqqopensdkapiV3</string>
		<string>mqqopensdkapiV4</string>
		<string>mqzoneopensdk</string>
		<string>wtloginmqq</string>
		<string>wtloginmqq2</string>
		<string>mqqwpa</string>
		<string>mqzone</string>
		<string>mqzonev2</string>
		<string>mqzoneshare</string>
		<string>wtloginqzone</string>
		<string>mqzonewx</string>
		<string>mqzoneopensdkapiV2</string>
		<string>mqzoneopensdkapi19</string>
		<string>mqzoneopensdkapi</string>
		<string>mqqbrowser</string>
		<string>mttbrowser</string>
		<string>tim</string>
		<string>timapi</string>
		<string>timopensdkfriend</string>
		<string>timwpa</string>
		<string>timgamebindinggroup</string>
		<string>timapiwallet</string>
		<string>timOpensdkSSoLogin</string>
		<string>wtlogintim</string>
		<string>timopensdkgrouptribeshare</string>
		<string>timopensdkapiV4</string>
		<string>timgamebindinggroup</string>
		<string>timopensdkdataline</string>
		<string>wtlogintimV1</string>
		<string>timapiV1</string>
		<string>alipay</string>
		<string>alipayshare</string>
		<string>dingtalk</string>
		<string>dingtalk-open</string>
		<string>linkedin</string>
		<string>linkedin-sdk2</string>
		<string>linkedin-sdk</string>
		<string>laiwangsso</string>
		<string>yixin</string>
		<string>yixinopenapi</string>
		<string>instagram</string>
		<string>whatsapp</string>
		<string>line</string>
		<string>fbapi</string>
		<string>fb-messenger-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
		<string>kakaofa63a0b2356e923f3edd6512d531f546</string>
		<string>kakaokompassauth</string>
		<string>storykompassauth</string>
		<string>kakaolink</string>
		<string>kakaotalk-4.5.0</string>
		<string>kakaostory-2.9.0</string>
		<string>pinterestsdk.v1</string>
		<string>tumblr</string>
		<string>evernote</string>
		<string>en</string>
		<string>enx</string>
		<string>evernotecid</string>
		<string>evernotemsg</string>
		<string>youdaonote</string>
		<string>ynotedictfav</string>
		<string>com.youdao.note.todayViewNote</string>
		<string>ynotesharesdk</string>
		<string>gplus</string>
		<string>pocket</string>
		<string>readitlater</string>
		<string>pocket-oauth-v1</string>
		<string>fb131450656879143</string>
		<string>en-readitlater-5776</string>
		<string>com.ideashower.ReadItLaterPro3</string>
		<string>com.ideashower.ReadItLaterPro</string>
		<string>com.ideashower.ReadItLaterProAlpha</string>
		<string>com.ideashower.ReadItLaterProEnterprise</string>
		<string>vk</string>
		<string>vk-share</string>
		<string>vkauthorize</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>MiSDKAppID</key>
	<string>2882303761517604618</string>
	<key>MiSDKAppKey</key>
	<string>5141760472618</string>
	<key>MiSDKRun</key>
	<string>debug</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
			<key>sina.cn</key>
			<dict>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.0</string>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSThirdPartyExceptionRequiresForwardSecrecy</key>
				<false/>
			</dict>
			<key>sina.com.cn</key>
			<dict>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSThirdPartyExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSThirdPartyExceptionRequiresForwardSecrecy</key>
				<false/>
			</dict>
			<key>sinaimg.cn</key>
			<dict>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSThirdPartyExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSThirdPartyExceptionRequiresForwardSecrecy</key>
				<false/>
			</dict>
			<key>sinajs.cn</key>
			<dict>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSThirdPartyExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSThirdPartyExceptionRequiresForwardSecrecy</key>
				<false/>
			</dict>
			<key>weibo.cn</key>
			<dict>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.0</string>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSThirdPartyExceptionRequiresForwardSecrecy</key>
				<false/>
			</dict>
			<key>weibo.com</key>
			<dict>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.0</string>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSThirdPartyExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSThirdPartyExceptionRequiresForwardSecrecy</key>
				<false/>
			</dict>
		</dict>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>cameraDesciption</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string></string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>photoLibraryDesciption</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>location</string>
	</array>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>Yunxi</key>
	<dict>
		<key>JPushAppKey</key>
		<string>22429563879068359374ed4a</string>
	</dict>
</dict>
</plist>
