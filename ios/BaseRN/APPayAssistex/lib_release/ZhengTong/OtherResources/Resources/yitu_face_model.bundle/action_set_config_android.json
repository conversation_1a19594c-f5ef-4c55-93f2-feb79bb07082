{"common_path": "../../../models/face_model/", "object_key_set": ["mouth_open_verifier", "eye_close_verifier", "face_preprocessor_4_action_verifier", "face_preprocessor_left", "face_preprocessor_right", "yaw_nose_3d_classifier", "pitch_nose_3d_classifier", "sequence_based_mouth_open_classifier", "patch_consistence_based_hole_mask", "color_histogram_based_face_continuity", "face_rect_based_face_continuity", "face_pose_based_face_continuity"], "is_multi_view": true, "is_landmark_based_pose": false, "num_frames_wait_to_start": 3, "max_num_consistent_frames_without_face": 3, "max_num_nonconsistent_frames_for_id_verification": 4, "max_num_nonconsistent_frames_for_head_rot": 3, "max_num_is_pen_frames": 2, "max_num_eye_occ_frames": 3, "max_num_non_face_continuity_frame": 3, "non_face_continuity_threshold": 0.5, "is_pk_inversed_action": false, "is_fix_eye_align": true, "is_pen_vs_eye_close": true, "threshold_of_y_pose_for_yaw_3d_nose": 20, "threshold_of_x_pose_for_pitch_3d_nose": 10, "frontal_view_det_thre": 1.5, "left_view_det_thre": 1.5, "right_view_det_thre": 1.5, "frontal_view_det_add": 1.0, "left_view_det_add": 0, "right_view_det_add": 0, "mouth_open_verifier": {"type": "mouth_open_verifier", "config": {"angle_x_threshold": 20, "angle_y_threshold": 20, "min_mouth_close_frame": 1, "min_mouth_open_frame": 1, "min_detect_rect_overlap": 0.5, "object_key_set": ["mouth_close_expression", "mouth_open_expression"], "mouth_close_expression": {"type": "mouth_close_expression", "config": {"threshold": 0.3, "attribute_name": " mouth_close", "open_or_close": "close", "udd_threshold": 0.13, "output_stage_debug_image": false, "output_random_forest_debug_image": false, "output_random_forest_shift": false}}, "mouth_open_expression": {"type": "mouth_open_expression", "config": {"threshold": 0.623181, "attribute_name": " mouth_open", "open_or_close": "open", "udd_threshold": 0.07, "output_stage_debug_image": false, "output_random_forest_debug_image": false, "output_random_forest_shift": false}}}}, "eye_close_verifier": {"type": "eye_close_verifier", "config": {"angle_x_limit": [-8, 8, 10, 20], "angle_y_limit": [-15, 15, -15, 15], "detect_alignment_overlap_thres": 0.5, "minimum_angle_movement": 6, "minimum_increase_times": 2, "is_estimate_detected_rect": true, "n_frame_to_get_stable_detection_size": 3, "est_size_ratio_limit": 0.78, "est_angle_x_limit": 10, "est_angle_y_limit": 25, "est_angle_z_limit": 10, "sample_frame_every_n": 1, "is_restrict_detection_overlap_ratio": false, "detection_overlap_ratio": 0.5, "minimum_movement_ratio": 0.08, "num_frames_wait_to_pass": 3, "is_check_rot_down": true, "angle_x_threshold": 20, "angle_y_threshold": 15, "min_detect_rect_overlap": 0.5, "object_key_set": ["eye_close_expression", "eye_open_expression", "pen_vs_eye_close_classifier"], "eye_close_expression": {"type": "eye_close_expression", "config": {"threshold": -0.5, "convert_result": false, "left_or_right_eye": "both", "attribute_name": "eye_close"}}, "eye_open_expression": {"type": "eye_open_expression", "config": {"threshold": 0.3, "convert_result": false, "left_or_right_eye": "both", "attribute_name": "eye_open"}}, "pen_vs_eye_close_classifier": {"type": "pen_vs_eye_close_classifier", "config": "./f_attr/pen_vs_eye_close.P8.json"}}}, "yaw_nose_3d_classifier": {"type": "nose_3d_classifier", "config": {"threshold": 0.5}}, "pitch_nose_3d_classifier": {"type": "nose_3d_classifier", "config": {"threshold": 1.0}}, "face_occlusion_verifier": {"type": "face_occlusion_verifier", "config": {"affine_transform_width": 120, "affine_transform_height": 80, "bin_size": 8, "rect_left_upper_x": 8, "rect_left_upper_y": 8, "rect_right_bottom_x": 112, "rect_right_bottom_y": 72, "frame_step": 5, "valid_rect_id": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 40, 44, 45, 46, 50, 53, 57, 58, 59, 63, 67, 68, 69, 70, 71, 72, 73, 74, 75, 81, 82, 83, 84, 85, 86, 87], "match_range": 5, "corr_threshold": 0.35, "diff_threshold": 40, "mean_shape": {"faceType": "", "face_rect": {"h": 0, "w": 0, "x": 0, "y": 0}, "left_eye_left": {"score": 1.0, "x": 29.24779449613459, "y": 39.98618069984776}, "left_eye_right": {"score": 1.0, "x": 44.77440505339285, "y": 40.45907212209196}, "mouth_top_lip_down": {"score": 1.0, "x": 60.29742497388993, "y": 87.62477716087395}, "nose_left": {"score": 1.0, "x": 48.61567685010301, "y": 71.70510079568447}, "nose_right": {"score": 1.0, "x": 71.64274534545686, "y": 71.56092682465056}, "right_eye_left": {"score": 1.0, "x": 75.18197590648893, "y": 40.22833030363246}, "right_eye_right": {"score": 1.0, "x": 90.8913693656381, "y": 39.61785433241192}}}}, "color_histogram_based_face_continuity": {"type": "color_histogram_based_face_continuity", "config": {"threshold_for_discontinuity": 0.15, "threshold_for_continuity": 0.02, "block_split_num": 2, "color_bin_size": 32, "rect_shift_ratio_for_y_rot": 0.25}}, "face_rect_based_face_continuity": {"type": "face_rect_based_face_continuity", "config": {"min_overlap_ratio_threshold": 0.3, "min_area_ratio_threshold": 0.5, "min_face_pos_x": 100, "max_face_pos_x": 400, "min_face_pos_y": 0, "max_face_pos_y": 400, "landmark_ids_bound": [1, 3, 4, 6, 7, 9, 10, 11, 26, 27, 91, 93, 29, 96, 98]}}, "face_pose_based_face_continuity": {"type": "face_pose_based_face_continuity", "config": {"x_max_delta_frontal": 20, "y_max_delta_frontal": 25, "z_max_delta_frontal": 15, "y_min_for_side": 9}}, "face_preprocessor_4_action_verifier": {"type": "face_preprocessor_4_action_verifier", "config": {"face_detector_with_alignment": {"type": "face_detector_with_alignment", "config": {"config_path": "./m/detalign_android_F.json"}}, "face_normalizer": {"type": "face_normalizer", "config": {"config_path": "./m/pose_estiomation_su_ave_16.json"}}, "object_key_set": ["face_detector_with_alignment", "face_normalizer"]}}, "face_preprocessor_left": {"type": "face_preprocessor_4_action_verifier", "config": {"is_3d_pose": false, "face_detector_with_alignment_left": {"type": "face_detector_with_alignment", "config": {"config_path": "./m/detalign_android_L.json"}}, "face_normalizer": {"type": "face_normalizer", "config": {"config_path": "./m/pose_estiomation_su_ave_16.json"}}, "object_key_set": ["face_detector_with_alignment", "face_normalizer"], "model_key_map": {"face_detector_with_alignment": "face_detector_with_alignment_left"}, "param_key_map": {"face_detector_with_alignment": "face_detector_with_alignment_left"}}}, "face_preprocessor_right": {"type": "face_preprocessor_4_action_verifier", "config": {"is_3d_pose": false, "face_detector_with_alignment_right": {"type": "face_detector_with_alignment", "config": {"config_path": "./m/detalign_android_R.json"}}, "face_normalizer": {"type": "face_normalizer", "config": {"config_path": "./m/pose_estiomation_su_ave_16.json"}}, "object_key_set": ["face_detector_with_alignment", "face_normalizer"], "model_key_map": {"face_detector_with_alignment": "face_detector_with_alignment_right"}, "param_key_map": {"face_detector_with_alignment": "face_detector_with_alignment_right"}}}, "sequence_based_mouth_open_classifier": {"type": "sequence_based_mouth_open_classifier", "config": {"threshold": 0.12}}, "patch_consistence_based_hole_mask": {"type": "patch_consistence_based_hole_mask", "config": {"similarity_mode": "haar", "landmark_ids": [1, 3, 4, 6, 7, 9, 10, 11, 26, 27, 91, 93, 29, 96, 98, 70], "threshold": 0.53, "bin_size": 16, "mean_shape": {"chin": {"score": 1.0, "x": 0.5036943879036465, "y": 1.008894067862306}, "down_out_profile_01": {"score": 1.0, "x": 0.1138044594605391, "y": 0.6329157033824905}, "down_out_profile_03": {"score": 1.0, "x": 0.2576859798710668, "y": 0.8865099503596099}, "down_out_profile_06": {"score": 1.0, "x": 0.7513928487947279, "y": 0.8856440852869628}, "down_out_profile_08": {"score": 1.0, "x": 0.8937542014360337, "y": 0.6281807991511253}, "faceType": "", "face_rect": {"h": 0, "w": 0, "x": 0, "y": 0}, "left_eye_left": {"score": 1.0, "x": 0.2437316208011216, "y": 0.3332181724987313}, "left_eye_right": {"score": 1.0, "x": 0.3731200421116071, "y": 0.3371589343507663}, "mouth_bottom_lip_up": {"score": 1.0, "x": 0.502322127719606, "y": 0.7713881898899084}, "mouth_left": {"score": 1.0, "x": 0.349300618073853, "y": 0.7371467205069459}, "mouth_right": {"score": 1.0, "x": 0.6568716735160492, "y": 0.7340116501923828}, "mouth_top_lip_down": {"score": 1.0, "x": 0.5024785414490828, "y": 0.7302064763406162}, "nose_left": {"score": 1.0, "x": 0.4051306404175251, "y": 0.597542506630704}, "nose_right": {"score": 1.0, "x": 0.5970228778788071, "y": 0.596341056872088}, "right_eye_left": {"score": 1.0, "x": 0.6265164658874077, "y": 0.3352360858636038}, "right_eye_right": {"score": 1.0, "x": 0.7574280780469842, "y": 0.3301487861034327}, "up_out_profile_06": {"score": 1.0, "x": 0.4990723857920574, "y": -0.1283283651556442}}}}}