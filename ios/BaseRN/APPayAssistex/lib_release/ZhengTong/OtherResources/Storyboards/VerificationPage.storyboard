<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="14109" targetRuntime="iOS.CocoaTouch" variant="6xAndEarlier" propertyAccessControl="none" useAutolayout="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment version="2064" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="Aspect ratio constraints" minToolsVersion="5.1"/>
        <capability name="Constraints with non-1.0 multipliers" minToolsVersion="5.1"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Liveness Detection View Controller-->
        <scene sceneID="w0o-ME-RLB">
            <objects>
                <viewController storyboardIdentifier="livenessDetectionStoryboard" id="6Bm-YZ-0lt" customClass="LivenessDetectionViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="Dlq-d2-iJs"/>
                        <viewControllerLayoutGuide type="bottom" id="GLZ-l0-aqL"/>
                    </layoutGuides>
                    <view key="view" opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" id="jEt-5P-JQc">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="lbO-I1-hJP" userLabel="cameraPreviewImageView">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="380"/>
                            </imageView>
                            <imageView opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="4BJ-mD-O0X" userLabel="hintBackgroundImageView">
                                <rect key="frame" x="0.0" y="380" width="375" height="287"/>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </imageView>
                            <imageView userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="iI3-6r-bTY" userLabel="countdownIndicatorImageView">
                                <rect key="frame" x="307" y="388" width="60" height="60"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="iI3-6r-bTY" secondAttribute="height" multiplier="1:1" id="gdc-Z7-FIY"/>
                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="40" id="iSC-sI-YXg"/>
                                    <constraint firstAttribute="height" relation="lessThanOrEqual" constant="60" id="tDP-gF-WX6"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uHo-7Q-8QB" userLabel="stepHintTextLabel">
                                <rect key="frame" x="187.5" y="407" width="0.0" height="45"/>
                                <constraints>
                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="45" id="7an-QT-Uf4"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="20"/>
                                <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fZb-SW-NlG" userLabel="startWarningLabel">
                                <rect key="frame" x="85" y="407" width="206" height="45"/>
                                <constraints>
                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="45" id="9lx-e6-2fZ"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="20"/>
                                <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="I7A-dH-wKJ" userLabel="stepHintAnimationImageView">
                                <rect key="frame" x="108" y="454" width="159" height="159.5"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="I7A-dH-wKJ" secondAttribute="height" multiplier="1:1" id="xWt-g4-tTo"/>
                                </constraints>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="yitu_face_without_skeleton" translatesAutoresizingMaskIntoConstraints="NO" id="gwZ-vh-XX3">
                                <rect key="frame" x="66.5" y="31.5" width="242" height="317"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="gwZ-vh-XX3" secondAttribute="height" multiplier="242:317" id="Hmd-Nq-Gam"/>
                                </constraints>
                            </imageView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="mbn-7h-wJS">
                                <rect key="frame" x="12" y="625" width="37" height="34"/>
                                <state key="normal" title="取消">
                                    <color key="titleColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                </state>
                                <connections>
                                    <action selector="onCancelVerification:" destination="6Bm-YZ-0lt" eventType="touchUpInside" id="uOM-70-Ktl"/>
                                </connections>
                            </button>
                        </subviews>
                        <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="lbO-I1-hJP" secondAttribute="trailing" id="453-NB-chR"/>
                            <constraint firstItem="4BJ-mD-O0X" firstAttribute="centerY" secondItem="I7A-dH-wKJ" secondAttribute="centerY" constant="-10" id="77R-fG-K0d"/>
                            <constraint firstItem="lbO-I1-hJP" firstAttribute="height" secondItem="gwZ-vh-XX3" secondAttribute="height" multiplier="1.2" id="7Tw-Yt-QMu"/>
                            <constraint firstItem="lbO-I1-hJP" firstAttribute="top" secondItem="jEt-5P-JQc" secondAttribute="top" id="7tI-i7-faV"/>
                            <constraint firstItem="GLZ-l0-aqL" firstAttribute="top" secondItem="mbn-7h-wJS" secondAttribute="bottom" constant="8" id="ARc-Iq-Hmr"/>
                            <constraint firstItem="I7A-dH-wKJ" firstAttribute="top" secondItem="uHo-7Q-8QB" secondAttribute="bottom" constant="2" id="IFv-2v-Fk1"/>
                            <constraint firstItem="mbn-7h-wJS" firstAttribute="leading" secondItem="jEt-5P-JQc" secondAttribute="leading" constant="12" id="Iok-ua-x9B"/>
                            <constraint firstItem="4BJ-mD-O0X" firstAttribute="leading" secondItem="jEt-5P-JQc" secondAttribute="leading" id="JWK-m5-94s"/>
                            <constraint firstAttribute="centerX" secondItem="4BJ-mD-O0X" secondAttribute="centerX" id="KzA-yt-hs3"/>
                            <constraint firstItem="4BJ-mD-O0X" firstAttribute="top" secondItem="lbO-I1-hJP" secondAttribute="bottom" id="U1u-Y3-avM"/>
                            <constraint firstAttribute="trailing" secondItem="iI3-6r-bTY" secondAttribute="trailing" constant="8" id="U5b-s4-RJK"/>
                            <constraint firstAttribute="trailing" secondItem="4BJ-mD-O0X" secondAttribute="trailing" id="VNY-AO-9eM"/>
                            <constraint firstItem="uHo-7Q-8QB" firstAttribute="top" relation="greaterThanOrEqual" secondItem="4BJ-mD-O0X" secondAttribute="top" constant="2" id="Xjx-uA-dEl"/>
                            <constraint firstItem="4BJ-mD-O0X" firstAttribute="height" secondItem="I7A-dH-wKJ" secondAttribute="height" multiplier="1.8" id="YQR-8q-fbo"/>
                            <constraint firstItem="iI3-6r-bTY" firstAttribute="leading" secondItem="fZb-SW-NlG" secondAttribute="trailing" constant="16" id="Yy7-NT-Wh0"/>
                            <constraint firstAttribute="centerX" secondItem="uHo-7Q-8QB" secondAttribute="centerX" id="aKZ-SC-zkC"/>
                            <constraint firstAttribute="centerX" secondItem="gwZ-vh-XX3" secondAttribute="centerX" id="aTk-PH-alk"/>
                            <constraint firstItem="lbO-I1-hJP" firstAttribute="height" secondItem="jEt-5P-JQc" secondAttribute="height" multiplier="0.57" id="d2g-g1-IbU"/>
                            <constraint firstItem="lbO-I1-hJP" firstAttribute="leading" secondItem="jEt-5P-JQc" secondAttribute="leading" id="dNT-VC-1BJ"/>
                            <constraint firstItem="iI3-6r-bTY" firstAttribute="top" secondItem="4BJ-mD-O0X" secondAttribute="top" constant="8" id="gSY-bL-JJN"/>
                            <constraint firstItem="GLZ-l0-aqL" firstAttribute="top" relation="greaterThanOrEqual" secondItem="I7A-dH-wKJ" secondAttribute="bottom" constant="8" id="h3a-yf-7g9"/>
                            <constraint firstItem="lbO-I1-hJP" firstAttribute="centerY" secondItem="gwZ-vh-XX3" secondAttribute="centerY" id="i4y-Pp-qHN"/>
                            <constraint firstItem="I7A-dH-wKJ" firstAttribute="top" secondItem="fZb-SW-NlG" secondAttribute="bottom" constant="2" id="jdH-55-3os"/>
                            <constraint firstItem="fZb-SW-NlG" firstAttribute="centerX" secondItem="4BJ-mD-O0X" secondAttribute="centerX" id="kT3-a5-aeT"/>
                            <constraint firstItem="fZb-SW-NlG" firstAttribute="top" relation="greaterThanOrEqual" secondItem="4BJ-mD-O0X" secondAttribute="top" constant="2" id="nEp-vt-kTb"/>
                            <constraint firstItem="4BJ-mD-O0X" firstAttribute="centerX" secondItem="I7A-dH-wKJ" secondAttribute="centerX" id="rie-R9-UQG"/>
                            <constraint firstItem="GLZ-l0-aqL" firstAttribute="top" secondItem="4BJ-mD-O0X" secondAttribute="bottom" id="yav-QD-36t"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="cameraAperture" destination="gwZ-vh-XX3" id="teQ-2L-KO8"/>
                        <outlet property="cameraPreviewView" destination="lbO-I1-hJP" id="2lB-N5-P3V"/>
                        <outlet property="progressIndicator" destination="iI3-6r-bTY" id="6L8-J7-dxN"/>
                        <outlet property="sampleActionImage" destination="I7A-dH-wKJ" id="iBY-Td-fwi"/>
                        <outlet property="startWarningLabel" destination="fZb-SW-NlG" id="8pF-CO-qUW"/>
                        <outlet property="stepHintTextLabel" destination="uHo-7Q-8QB" id="mZj-8L-ypc"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Wsw-kk-MXE" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1225" y="449"/>
        </scene>
    </scenes>
    <resources>
        <image name="yitu_face_without_skeleton" width="234" height="316"/>
    </resources>
</document>
