//
//  RCTAPPayAssistEx.m
//  BaseRN
//
//  Created by 吴宏伟 on 2018/4/17.
//  Copyright © 2018年 Facebook. All rights reserved.
//

#import "RCTAPPayAssistEx.h"
#if (TARGET_IPHONE_SIMULATOR)
// 在模拟器的情况下
#import "APay.h"
#else
// 在真机情况下
#import "APay.h"
#endif

@interface RCTAPPayAssistEx ()
#if (TARGET_IPHONE_SIMULATOR)
//<APayDelegate>
#else
<APayDelegate>
#endif
@property (nonatomic,copy) RCTPromiseResolveBlock resolve;

@property (nonatomic,copy) RCTPromiseRejectBlock reject;

@end
@implementation RCTAPPayAssistEx
RCT_EXPORT_MODULE()

RCT_REMAP_METHOD(pay,
                  payData:(NSString *)payData
                  serverMode:(NSString *)serverMode
                 resolver:(RCTPromiseResolveBlock)resolve
                 rejecter:(RCTPromiseRejectBlock)reject)
{
#if (TARGET_IPHONE_SIMULATOR)
  // 在模拟器的情况下
#else
  // 在真机情况下
  UIViewController * viewController = [UIApplication sharedApplication].keyWindow.rootViewController;
  
  while (viewController.presentedViewController) {
    viewController = viewController.presentedViewController;
  }
  self.reject = reject;
  self.resolve = resolve;
  dispatch_sync(dispatch_get_main_queue(), ^{
    [APay startPay:payData viewController:viewController delegate:self mode:serverMode];
  });
#endif
}
#if (TARGET_IPHONE_SIMULATOR)
// 在模拟器的情况下
#else
- (void)APayResult:(NSString *)result{
  NSArray *parts = [result componentsSeparatedByString:@"="];
  NSError *error;
  NSData *data = [[parts lastObject] dataUsingEncoding:NSUTF8StringEncoding];
  NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&error];
  NSInteger payResult = [dic[@"payResult"] integerValue];
  if (payResult == APayResultSuccess) {
    self.resolve(@"支付成功");
  } else if (payResult == APayResultFail) {
    self.reject(@"-2", @"支付失败", nil);
  } else if (payResult == APayResultCancel) {
    self.reject(@"-1", @"用户取消", nil);
  }
}
#endif
@end
