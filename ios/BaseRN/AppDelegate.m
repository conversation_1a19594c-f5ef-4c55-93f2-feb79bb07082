/**
 * Copyright (c) 2015-present, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 */

#import "AppDelegate.h"

#import <React/RCTBundleURLProvider.h>
#import <React/RCTRootView.h>
#import <React/RCTLinkingManager.h>
#import <CloudPushSDK/CloudPushSDK.h>
// iOS 10 notification
#import <UserNotifications/UserNotifications.h>
//#import "AliPushTool.h"
//#import "MiPushTool.h"
#import <UMSocialCore/UMSocialCore.h>
#import <Push/PushTool.h>
#import <JPush/JPushTool.h>
#import <AlipaySDK/AlipaySDK.h>
#import <CodePush/CodePush.h>


@interface AppDelegate () <UNUserNotificationCenterDelegate>

@end
@implementation AppDelegate
{
  // iOS 10通知中心
  UNUserNotificationCenter *_notificationCenter;
}

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
  NSURL *jsCodeLocation;

#ifdef DEBUG
   jsCodeLocation = [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index.ios" fallbackResource:nil];
#else
  jsCodeLocation = [CodePush bundleURL];
#endif

  
  RCTRootView *rootView = [[RCTRootView alloc] initWithBundleURL:jsCodeLocation
                                                      moduleName:@"YunXiRN"
                                               initialProperties:@{@"hostKey": hostKey,@"isCanChangeEvn": [NSNumber numberWithBool:isCanChangeEvn],@"isShowEvn":[NSNumber numberWithBool:isShowEvn]}
                                                   launchOptions:launchOptions];
  rootView.backgroundColor = [[UIColor alloc] initWithRed:1.0f green:1.0f blue:1.0f alpha:1];
  if (@available(iOS 13.0, *)) {
     rootView.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
  }

//  if (is_iPhoneX)
//  {
//    self.window = [[UIWindow alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIScreen mainScreen].bounds.size.height-34)];
//    _rootViewController = [UIViewController new];
//    _rootViewController.view = rootView;
//    self.window.rootViewController = _rootViewController;
//    [self.window makeKeyAndVisible];
//  }else
//  {
    self.window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
    _rootViewController = [UIViewController new];
    _rootViewController.view = rootView;
    self.window.rootViewController = _rootViewController;
    [self.window makeKeyAndVisible];
//  }
  // APNs注册，获取deviceToken并上报
//    [self registerAPNS:application];
  //  [[AliPushTool shareInstance] registerAPNS:application];
  //  [[MiPushTool shareInstance] miPushInit];
  NSDictionary* userInfo = [launchOptions objectForKey:UIApplicationLaunchOptionsRemoteNotificationKey];
  if(userInfo){
//    [self setInfo:userInfo open:1];
    
  }
//  [[AppSpUtil shareInstance] initSA];
   [[PushTool shareInstance] initCloudWithApplication:application launchOptions:launchOptions pushObj:[[JPushTool alloc] init]];
//  id pushClass;
//  if ([PUSH_COMPONENT isEqualToString:@"MI_PUSH"]) {
////    pushClass = [MiPushTool class];
//  }
//  else if([PUSH_COMPONENT isEqualToString:@"ALI_PUSH"])
//  {
////    pushClass = [AliPushTool class];
//  }
//  else if([PUSH_COMPONENT isEqualToString:@"J_PUSH"])
//  {
//    pushClass = [JPushTool class];
//  }
//  [[PushTool shareInstance] initCloudWithApplication:application launchOptions:launchOptions PushClass:pushClass];
  
  // 初始化SDK
  //  [self initCloudPush];
  //  [[AliPushTool shareInstance] initCloud];
  
  // 监听推送通道打开动作
  //  [self listenerOnChannelOpened];
  //  [[AliPushTool shareInstance] listenerOnChannelOpened];
  // 监听推送消息到达
  //  [self registerMessageReceive];
  //  [[AliPushTool shareInstance] registerMessageReceive];
  // 点击通知将App从关闭状态启动时，将通知打开回执上报
  // [CloudPushSDK handleLaunching:launchOptions];(Deprecated from v1.8.1)
  //  [CloudPushSDK sendNotificationAck:launchOptions];
  //  [[AliPushTool shareInstance] sendNotificationAck:launchOptions];
  
  /* 打开调试日志 */
  [[UMSocialManager defaultManager] openLog:YES];
  
  /* 设置友盟appkey */
  [[UMSocialManager defaultManager] setUmSocialAppkey:USHARE_DEMO_APPKEY];
  
  return YES;
}

-(void)setInfo:(NSDictionary *)userInfo open:(NSInteger)open
{
  if (userInfo) {
    NSMutableDictionary * dict = [NSMutableDictionary new];
    NSMutableDictionary * extraDict = [NSMutableDictionary new];
    for (NSString * key in userInfo) {
      if ([key isEqualToString:@"aps"]) {
        NSDictionary * aps = userInfo[key];
        for (NSString * key1 in aps) {
          if ([key1 isEqualToString:@"alert"]) {
            if([aps[key1] isKindOfClass:[NSString class]])
            {
              [dict setObject:aps[key1] forKey:@"content"];
            }
            else if ([aps[key1] isKindOfClass:[NSDictionary class]])
            {
              NSDictionary * alert = aps[key1];
              for (NSString * key2 in alert) {
                if ([key2 isEqualToString:@"title"]) {
                  [dict setObject:alert[key2] forKey:@"title"];
                }
                if ([key2 isEqualToString:@"body"]) {
                  [dict setObject:alert[key2] forKey:@"content"];
                }
              }
            }
          }
        }
      }else
      {
        [extraDict setObject:userInfo[key] forKey:key];
      }
    }
    
    
    [dict setObject:extraDict forKey:@"extra"];
    
    //存储最后一次收到的消息
    [[NSUserDefaults standardUserDefaults] setObject:@{@"userInfo":dict,@"open": [NSNumber numberWithInteger:open]} forKey:@"pushObject"];
    [[NSUserDefaults standardUserDefaults] synchronize];
  }
}

- (void)applicationDidEnterBackground:(UIApplication *)application {
  // Use this method to release shared resources, save user data, invalidate
  // timers, and store enough application state information to restore your
  // application to its current state in case it is terminated later.
  // If your application supports background execution, this method is called
  // instead of applicationWillTerminate: when the user quits.
  
  //[[UIApplication sharedApplication] setApplicationIconBadgeNumber:1];
  
  [[UIApplication sharedApplication] setApplicationIconBadgeNumber:0];
}

- (void)applicationWillEnterForeground:(UIApplication *)application {
  [application setApplicationIconBadgeNumber:0];
  [application cancelAllLocalNotifications];
}

/*
 *  苹果推送注册成功回调，将苹果返回的deviceToken上传到CloudPush服务器
 */
- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken {
  //  [[AliPushTool shareInstance] application:application didRegisterForRemoteNotificationsWithDeviceToken:deviceToken];
  //  [[MiPushTool shareInstance] application:application didRegisterForRemoteNotificationsWithDeviceToken:deviceToken];
  [[PushTool shareInstance] application:application didRegisterForRemoteNotificationsWithDeviceToken:deviceToken];
}
/*
 *  苹果推送注册失败回调
 */
- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error {
  //  [[AliPushTool shareInstance] application:application didFailToRegisterForRemoteNotificationsWithError:error];
  //  [[MiPushTool shareInstance] application:application didFailToRegisterForRemoteNotificationsWithError:error];
  
  [[PushTool shareInstance] application:application didFailToRegisterForRemoteNotificationsWithError:error];
}

/*
 *  App处于启动状态时，通知打开回调
 */
- (void)application:(UIApplication*)application didReceiveRemoteNotification:(NSDictionary*)userInfo {
  //  [[AliPushTool shareInstance] application:application didReceiveRemoteNotification:userInfo];
  NSLog(@"ns%@",userInfo);
  //  NSLog(@"application.applicationState = %@ld",application.applicationState);
  if (application.applicationState == UIApplicationStateActive) {
    NSLog(@"application.applicationState = UIApplicationStateActive");
  }else if (application.applicationState == UIApplicationStateInactive)
  {
    NSLog(@"application.applicationState = UIApplicationStateInactive");
  }
  else if (application.applicationState == UIApplicationStateBackground)
  {
    NSLog(@"application.applicationState = UIApplicationStateBackground");
  }
  [[PushTool shareInstance] application:application didReceiveRemoteNotification:userInfo];
}



- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url
  sourceApplication:(NSString *)sourceApplication annotation:(id)annotation
{
  
  
  if ([url.host isEqualToString:@"safepay"]) {
    //跳转支付宝钱包进行支付，处理支付结果
    
    [[AlipaySDK defaultService] processOrderWithPaymentResult:url standbyCallback:^(NSDictionary *resultDic) {
      NSLog(@"result = %@",resultDic);
      
    }];
    return YES;
  }
  
  BOOL result = [[UMSocialManager defaultManager] handleOpenURL:url];
  if (result) {
    return result;
  }
  
  return [RCTLinkingManager application:application openURL:url
                      sourceApplication:sourceApplication annotation:annotation];
}

// NOTE: 9.0以后使用新API接口 //TODO 打开了Wechat无法回调
//- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<NSString*, id> *)options
//{
//  if ([url.host isEqualToString:@"safepay"]) {
//    //跳转支付宝钱包进行支付，处理支付结果
//    [[AlipaySDK defaultService] processOrderWithPaymentResult:url standbyCallback:^(NSDictionary *resultDic) {
//      NSLog(@"result = %@",resultDic);
//    }];
//  }
//  return YES;
//}


@end
