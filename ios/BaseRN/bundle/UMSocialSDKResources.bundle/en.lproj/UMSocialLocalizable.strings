/* 
  Localizable.strings
  SocialSDK

  Created by yeahugo on 13-6-17.
  Copyright (c) 2013年 Umeng. All rights reserved.
*/
"button_cancel" = "Cancel";
"button_ok" = "OK";
"button_start" = "Start";

"button_close" = "Close";
"button_back" = "Back";
"button_refresh" = "Refresh";

"label_request_account" = "Getting account data, please wait……";

"oauth_title" = "Authorization";
"oauth_text" = "Connecting to authorization web, please wait……";
"oauth_follow" = "Follow official micro-blog";
"mask_text" = "please wait……";

"oauth_error_title" = "authorise failed";
"oauth_error_message" = "authorise failed, you can try again later";
"oauth_error_not_approved" = "authorise failed, for your app haven't approved or you haven't add your weibo uid to test accounts";

"shareQQ_error" = "Error";
"shareQQ_notInstall" = "Mobile QQ havn't be installed in your device";
"shareQQ_openFail" = "Open Mobile QQ fail";
"shareQQ_ShareFail" = "Share To QQ fail";
"shareInstagram_openFail" = "Open Instagram fail";
"shareClient_notInstall" = "Client not installed in your device";

"custom_share_title" = "UMeng share";

"umsocial_shareUI_Title" = "Choose platform to share";
"umsocial_shareUI_CancelControlText" = "Cancel";

"shareList_title" = "Share To";
"shareEdit_title" = "Share To ";
"shareEdit_post" = "Post";
"shareEdit_change_account" = "Change Account";
"shareEdit_post_finish" = "Post Finished";
"shareEdit_post_fail" = "Post Failed";
"shareEdit_posting" = "Posting";
"shareEdit_delete_picture" = "Delete Picture";
"shareEdit_delete_video" = "Delete Video";
"shareEdit_delete_music" = "Delete Music";
"shareEdit_error_not_empty" = "Post content can not be empty";
"shareEdit_error_too_long" = "Post content's length too long";
"shareEdit_error_auth_expired" = "Post failed, for authorization expired";
"shareEdit_error_auth_again" = "authorize again";
"shareEdit_error_text_repeated" = "Text repeated";
"shareEdit_error_user_baned" = "Sorry, you have been banned";
"shareEdit_error_fail" = "Share fail";
"shareEdit_error_no_auth" = "Share fail, for no upload picture authority";
"shareEdit_error_empty_content" = "Share fail, for the content is empty";
"shareEdit_addFollow" = "Follow weibo";
"shareEdit_C" = "符";
"shareEdit_used" = "常";

"shareShake_button" = "Share";

"share_wechat" = "Share to Wechat";
"share_wechat_friend" = "Share to Wechat Friend";
"share_wechat_moments" = "Share to Wechat Moments";
"share_wechat_no_title" = "No Wechat";
"share_wechat_no_message" = "Wechat haven't been install in your device";
"share_facebook_not_login" = "No login Account";
"share_facebook_not_login_message" = "Your account haven't login";
"share_facebook_no_osSupport" = "iOS doesn't support";
"share_facebook_no_osSupport_message" = "The version of your iOS device doesn't support share the that media";

"share_alipay" = "Share to AlipayShare";
"share_alipay_friend" = "Share to AlipayShare friends";
"share_alipay_moments" = "Share to AlipayShare Timeline";
"share_alipay_no_title" = "No Alipay";
"share_alipay_no_message" = "Alipay not installed in device";
"share_alipay_version_not_supported" = "The version of Alipay not supported.";

"share_sms_no_osSupport" = "No SMS";
"share_sms_no_osSupport_message" = "The device doesn't support SMS";

"share_email_no_osSupport" = "No email";
"share_email_no_osSupport_message" = "Your email service have been turned off, please turn it on before you share your email";

"login_title" = "Login";
"login_as_tourists" = "Login as tourists";
"login_choose" = "choose your available account";

"commentList_title" = "Comment List";
"commentList_mask_text" = "Requesting comments, please wait……";
"commentList_comment_here" = "Comment here";

"commentDetail_title" = "Detail";

"commentEdit_no_location" = "No Location";
"commentEdit_post_fail" = "Post comment fail";
"commentEdit_post_success" = "Post comment success";
"commentEdit_noLocation_title" = "Location service have been turned off";
"commentEdit_noLocation_message" = "Your device's location service have been turned off, please turn on it before you share your location";
"commentEdit_title" = "New Comment";
"commentEdit_wait" = "Please wait";

"ego_pull_down" = "Pull down, refresh";
"ego_refresh" = "Loose to refresh";
"ego_load_more" = "Loose to load more";
"ego_pull_up" = "Pull up, load more";
"ego_refreshing" = "Refreshing";
"ego_loading_more" = "Loading more";

"loading" = "Loading";

"shareEdit_error_network_fail_message" = "Network Error";

"network_fail_title" = "Network Error";
"network_fail_message" = "The network state of your device is not available.";

"account_title" = "User Center";
"account_title_authorize_status" = "authorize status";
"account_label_login_tourist" = "login as tourist";
"account_label_login_account" = "login account";
"account_lable_not_auth" = "not authorized";
"account_lable_have_auth" = "authorized";
"account_title_sns_config" = "sns accounts";

//分享面板
"umsocial_sina" = "Sina";

"umsocial_wechat" = "Wechat";
"umsocial_wechat_session" = "Wechat";
"umsocial_wechat_timeline" = "Moments";
"umsocial_wechat_favorite" = "Wechat Favorite";

"umsocial_qq" = "QQ";
"umsocial_qzone" = "Qzone";
"umsocial_tencentWB" = "tencentWB";

"umsocial_alipay" = "alipay";
"umsocial_alipay_session" = "Alipay";
"umsocial_aliay_circle" = "LifeCircle";

"umsocial_lw_session" = "LaiWang";
"umsocial_lw_timeline" = "LW News";

"umsocial_yixin_session" = "YiXin";
"umsocial_yixin_timeline" = "YX Moments";

"umsocial_douban" = "Douban";

"umsocial_renren" = "Renren";

"umsocial_email" = "Email";
"umsocial_sms" = "SMS";

"umsocial_facebook" = "Facebook";
"umsocial_twitter" = "Twitter";
"umsocial_instagram" = "Instagram";
"umsocial_line" = "Line";
"umsocial_flickr" = "Flickr";
"umsocial_kakaoTalk" = "KakaoTalk";
"umsocial_pinterest" = "Pinterest";
"umsocial_tumblr" = "Tumblr";
"umsocial_linkedin" = "Linkedin";
"umsocial_whatsapp" = "Whatsapp";

"umsocial_youdaonote" = "ydnote";
"umsocial_evernote" = "evernote";
"umsocial_googleplus" = "Google+";
"umsocial_pocket" = "Pocket";
"umsocial_dropbox" = "Dropbox";
"umsocial_vkontakte" = "VKontakte";
"umsocial_facebookmessenger" = "Messenger";

"umsocial_default" = "template";

//云端分享认证的国际化字符串
"cloudShare_share_success" = "share success";
"cloudShare_share_failed" = "share failed";

"cloudShare_share_close" = "close";
"cloudShare_share_send" = "send";
"cloudShare_share" = "share";

"cloudShare_oauth_title" = "oauth";
"cloudShare_oauth_error" = "oauth error";
