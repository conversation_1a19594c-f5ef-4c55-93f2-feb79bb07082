/* A single strings file, whose title is specified in your preferences schema. The strings files provide the localized content to display to the user for each of your preferences. */


//core模块的平台相关
"core_platform_error_1" = "创建相应平台失败：%@\n请检查:\n1、平台类已实现<UMSocialPlatformProvider>协议\n2、此平台枚举值在正常枚举区间内，参考UMSocialPlatformConfig.h —> UMSocialPlatformType枚举。 参考链接检查 https://at.umeng.com/mKfWve?cid=2723";
"core_platform_error_2" = "创建平台失败:%@ 参考链接检查 https://at.umeng.com/mKfWve?cid=2723";

"core_platform_warn_1" = "平台检查失败:%@，请检查是否实现 @selector(socialPlatformType)，参考UMSocialPlatformConfig.h头文件说明。 参考链接检查 https://at.umeng.com/mKfWve?cid=2723";
"core_platform_warn_2" = "平台异常：%@ > 未实现相应方法：@selector(umSocial_setAppKey:withAppSecret:withRedirectURL:)";

//core模块认证相关
"core_auth_error_1" = "未发现平台相应类:%@\n请检查:\n1、平台类已实现<UMSocialPlatformProvider>协议\n2、此平台枚举值在正常枚举区间内，参考UMSocialPlatformConfig.h —> UMSocialPlatformType枚举。 参考链接检查 https://at.umeng.com/mKfWve?cid=2723";
"core_auth_error_2" = "未实现<UMSocialPlatformProvider>协议方法@selector(umSocial_AuthorizeWithUserInfo:withViewController:withCompletionHandler:)：%@";
"core_auth_error_3" = "未实现<UMSocialPlatformProvider>协议方法@selector(umSocial_cancelAuthWithCompletionHandler:)：%@";
"core_auth_error_4" = "未发现平台相应类:%@\n请检查:\n1、平台类已实现<UMSocialPlatformProvider>协议\n2、此平台枚举值在正常枚举区间内，参考UMSocialPlatformConfig.h —> UMSocialPlatformType枚举。 参考链接检查 https://at.umeng.com/mKfWve?cid=2723";
"core_auth_error_5" = "未实现<UMSocialPlatformProvider>协议方法@selector(umSocial_AuthorizeWithUserInfo:withCompletionHandler:)：%@";
"core_auth_error_6" = "平台%@分享时，传入的参数currentViewController应该是nil或者是继承UIViewController的子类";

//core模块获得用户资料相关
"core_getuserinfo_error_1" = "未发现平台相应类:%@\n请检查:\n1、平台类已实现<UMSocialPlatformProvider>协议\n2、此平台枚举值在正常枚举区间内，参考UMSocialPlatformConfig.h —> UMSocialPlatformType枚举。 参考链接检查 https://at.umeng.com/mKfWve?cid=2723";
"core_getuserinfo_error_2" = "未实现<UMSocialPlatformProvider>协议方法@selector(umSocial_RequestForUserProfileWithViewController:completion:)：%@";
"core_getuserinfo_error_3" = "平台%@分享时，传入的参数currentViewController应该是nil或者是继承UIViewController的子类";

//core模块分享相关
"core_share_error_1" = "传入平台('%@')的UMSocialMessageObject类型参数messageObject的数据类型无效，请检查\n1.messageObject是否空。\n2.messageObject.text和messageObject.shareObject是否同时为空";
"core_share_error_2" = "未发现平台相应类:%@\n请检查:\n1、平台类已实现<UMSocialPlatformProvider>协议\n2、此平台枚举值在正常枚举区间内，参考UMSocialPlatformConfig.h —> UMSocialPlatformType枚举。 参考链接检查 https://at.umeng.com/mKfWve?cid=2723";
"core_share_error_3" = "未实现<UMSocialPlatformProvider>协议方法@selector(umSocial_ShareWithObject:withCompletionHandler:)：%@";
"core_share_error_4" = "未发现平台相应类:%@\n请检查:\n1、平台类已实现<UMSocialPlatformProvider>协议\n2、此平台枚举值在正常枚举区间内，参考UMSocialPlatformConfig.h —> UMSocialPlatformType枚举。 参考链接检查 https://at.umeng.com/mKfWve?cid=2723";
"core_share_error_5" = "未实现<UMSocialPlatformProvider>协议方法@selector(umSocial_ShareWithObject:withViewController:withCompletionHandler:)：%@";
"core_share_error_6" = "平台%@分享时，传入的参数currentViewController应该是nil或者是继承UIViewController的子类";


//wechat
"wechat_auth_error_1" = "请检查是否设置了微信的URLSchema。参考链接检查 https://at.umeng.com/O1Lryy?cid=2723";

"wechat_getuserinfo_error_1" = "请检查是否设置了微信的URLSchema。参考链接检查 https://at.umeng.com/O1Lryy?cid=2723";


"wechat_share_error_1" = "请检查是否设置了微信的URLSchema。参考链接检查 https://at.umeng.com/O1Lryy?cid=2723";
"wechat_share_error_2" = "分享前，请检查微信是否安装。参考链接检查 https://at.umeng.com/1DCO5D?cid=2723";
"wechat_share_error_3" = "当前的sdk不支持微信的OpenAPI,请更新最新的微信SDK。";
"wechat_share_error_4" = "微信分享不支持的分享类型，微信的分享类型为：文本，图片，网络链接，音乐链接，视频链接，Gif表情，文件。";
"wechat_share_error_5" = "下载UMShareImageObject的shareImage失败，请检查图片参数是否正确。（本地图片，请检查是否赋值，网络图片请检查是否为https，防止下载失败）。";

"wechat_shareWebPage_warn_1" = "微信分享网页链接的时候，提供的缩略图为错误的下载url或者下载失败,具体的原因如下:%@";
"wechat_shareWebPage_warn_2" = "微信分享网页链接的时候，提供的缩略图为错误的下载url或者下载失败。";

"wechat_shareImage_warn_1" = "微信分享图片的时候，提供的缩略图为为错误的下载url或者下载失败,具体的原因如下:%@";
"wechat_shareImage_warn_2" = "微信分享图片的时候，提供的缩略图为为错误的下载url或者下载失败。";

//qq
"qq_auth_error_1" = "请检查是否设置了QQ的URLSchema。参考链接检查 https://at.umeng.com/aeuC0D?cid=2723";
"qq_auth_error_2" = "授权失败，点击qq授权没有跳转，请查看是否设置了appid,查看初始化函数:[[UMSocialManager defaultManager] setPlaform:UMSocialPlatformType_QQ appKey:???  appSecret:nil redirectURL:???];";

"qq_getuserinfo_error_1" = "请检查是否设置了QQ的URLSchema。 参考链接检查 https://at.umeng.com/aeuC0D?cid=2723";
"qq_getuserinfo_info_1" = "提示获得用户信息的时候是否清除缓存，可以设置UMSocialGlobal的isClearCacheWhenGetUserInfo变量来改变,默认是每次都清除用户的授权缓存";


"qq_share_error_1" = "请检查是否设置了QQ的URLSchema。 参考链接检查 https://at.umeng.com/aeuC0D?cid=2723";
"qq_share_error_2" = "请检查是否安装了QQ。参考链接检查 https://at.umeng.com/1nu05b?cid=2723";
"qq_share_error_3" = "请检查当前的SDK是否支持API调用，如果不能请升级SDK或者QQ的版本。";
"qq_share_error_4" = "QQ分享不支持的分享类型，QQ的分享类型为：文本，图片，网络链接，音乐链接，视频链接。";

"qq_shareWebPage_warn_1" = "QQ分享网页链接的时候，提供的缩略图为错误的下载url或者下载失败,具体的原因如下:%@";
"qq_shareWebPage_warn_2" = "QQ分享网页链接的时候，提供的缩略图为错误的下载url或者下载失败";
//sina
"sina_auth_error_1" = "请检查是否设置了sina的URLSchema。 参考链接检查 https://at.umeng.com/ju49Du?cid=2723";

"sina_getuserinfo_error_1" = "请检查是否设置了sina的URLSchema。 参考链接检查 https://at.umeng.com/ju49Du?cid=2723";

"sina_share_error_1" = "请检查是否设置了sina的URLSchema。 参考链接检查 https://at.umeng.com/ju49Du?cid=2723";
"sina_share_error_2" = "新浪分享不支持的分享类型，新浪的分享类型为：文本，图片，图文，网络链接，音乐链接，视频链接。";
"sina_shareWebPage_error_1"="新浪分享webpage类型(需要强制加入缩略图)错误，具体原因如下：%@";
"sina_shareText_Info_1" = "新浪文本分享最大的字是140个，如果超过就不能分享成功，sdk默认开启截断功能，如果需要停止截断需要在调用分享前加入代码[UMSocialGlobal shareInstance].isTruncateShareText=NO";

//facebook
"facebook_share_error_1" = "facebook分享不支持的分享类型，facebook的分享类型为：文本，图片，网络链接，音乐链接，视频链接。(新版的facebook采用的是对话框的形式分享的，如果设置文本的话需要有publish_actions权限调用OpenAPI)";
