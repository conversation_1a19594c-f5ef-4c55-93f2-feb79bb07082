<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/CGGeometry+RSKImageCropper.h</key>
		<data>
		hDkO+WQfa5EYLxhwbPUhd6AEXKc=
		</data>
		<key>Headers/RSKImageCropViewController+Protected.h</key>
		<data>
		3sm9vBVzC33PczPU06vlpnSdF3g=
		</data>
		<key>Headers/RSKImageCropViewController.h</key>
		<data>
		gtHueZnLHivmlmcKGw2ufwUdp+g=
		</data>
		<key>Headers/RSKImageCropper.h</key>
		<data>
		c9L6NVv7FAK4RBifBnXmLVwbhhQ=
		</data>
		<key>Headers/RSKImageScrollView.h</key>
		<data>
		Bsk+86XigFW1UTQ5mVbz/jMB4bc=
		</data>
		<key>Headers/RSKInternalUtility.h</key>
		<data>
		aRsvDHcI9aHF0V3jysPW9N59WKU=
		</data>
		<key>Headers/RSKTouchView.h</key>
		<data>
		dDKJBEb3eQ9eDmnhosAdfPW7k+U=
		</data>
		<key>Headers/UIApplication+RSKImageCropper.h</key>
		<data>
		cNSLIKWryyZKPb0gRWIA8fiYgDw=
		</data>
		<key>Headers/UIImage+RSKImageCropper.h</key>
		<data>
		S+ABoAJ3Bw3lukP1wLxzly7OG0o=
		</data>
		<key>Info.plist</key>
		<data>
		BlDjYXV5QwzLnikkELLsOZRi6Bw=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		LC3vRCQMcsbHzJe6+8/htZGNCX8=
		</data>
		<key>RSKImageCropperStrings.bundle/ar.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			KRP5wWwCj9s1qV1nVr/MOo/x01I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/ca.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8zZoXauX6I1ftj+F3D5eMvAMTXc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/cs.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cukPjVeX5e4yIYCOYNjpywgSc6c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/da.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			b7oA//Bhw44B7+vIpX65TOM0Pyk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/de.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vIo9QfVbqLXmEvBsNwcpEpjA3s0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/el.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ejHOmZ0YRkT66oVSOa/+q8xwplg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/en.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			wC7fgfMYe/UJLzf095t6Z0SmHdg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/es.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			AYWW7pgXvcot6lrZux495Kfoz5w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/fi.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			v5Dc1KM00rxmnD1XtYC4brKD/fI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/fr.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1Vo7WQ002cJnfSGJDUAETHpNM/0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/he.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NlQvxwTvR2cp3MhY8ReLmZH3hY8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/hi.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7xCLs50cv9NCZzUdr5TFHaSvoiU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/hr.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kAehbyC5Xq+72CS6/CeOHFAfDJo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/hu.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iUVXE77b9WE08mtbcEFVhtkhoN4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/id.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TLTlBmpGMx/hPImhMexr8YPj0TU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/it.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			4reikpoRRnwKSkSypIVHbGHlDBo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/ja.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			w3FQ6m5nvANm5d7x7bDd+7yzDQ4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/ms.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iSIn5+shBvxY0/Bc6i0GdMcdKP8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/nb.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zgn7FkIFruOTU8tBORwD0tAl+UE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/nl.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/tv2/vK8l2k31SkmHKVEI8OQzDw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/pl.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fbR9pyb7rLxbI6T37y3gxRer6EA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/pt_BR.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dFxTeXicC9xQNB7qQ36+UBJQ0e8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/pt_PT.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OxDqqdn7juh0hnMzbtraFCUrGNE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/ro.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			y/WFLLgaErtSY1Z+ckIZtQH3MvQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/ru.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PbiVh/+XMhwd6X0DJ072CXvJqQ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/sl.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jFNzvbE7B8ir+v/Fv/cRuHYRJQc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/sv.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			oaNMQzrpcAhA7qnaKHuaxyf3E1E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/tr.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TsIMaUDu/aHd+fArxSj3c5QjFqU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/uk.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			J5zx3AHF9HxCRRHBSloul8xB3Sk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/vi.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			G63eIlfouDVI82p7ZaYBfmvRyq0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/zh-Hans.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PPELUpdxERx2+AcCQjL9kyO1U1E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/CGGeometry+RSKImageCropper.h</key>
		<dict>
			<key>hash</key>
			<data>
			hDkO+WQfa5EYLxhwbPUhd6AEXKc=
			</data>
			<key>hash2</key>
			<data>
			SLqWWM0pqCeqPxs+sXZ95FoTcQvPj2ZDbBtLCjJ0kIY=
			</data>
		</dict>
		<key>Headers/RSKImageCropViewController+Protected.h</key>
		<dict>
			<key>hash</key>
			<data>
			3sm9vBVzC33PczPU06vlpnSdF3g=
			</data>
			<key>hash2</key>
			<data>
			2rZxlqqQhI3zEgPMDbMPZYvvMzDbHyad/gtpKYItMQA=
			</data>
		</dict>
		<key>Headers/RSKImageCropViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			gtHueZnLHivmlmcKGw2ufwUdp+g=
			</data>
			<key>hash2</key>
			<data>
			UqBswINXoeonEe825hjx7fia0aFU6qKnuFsiEM70fkg=
			</data>
		</dict>
		<key>Headers/RSKImageCropper.h</key>
		<dict>
			<key>hash</key>
			<data>
			c9L6NVv7FAK4RBifBnXmLVwbhhQ=
			</data>
			<key>hash2</key>
			<data>
			PBNYm3gLzn2rtfl8LRZds4MGNcHJqZs/JEHzSwWtFzw=
			</data>
		</dict>
		<key>Headers/RSKImageScrollView.h</key>
		<dict>
			<key>hash</key>
			<data>
			Bsk+86XigFW1UTQ5mVbz/jMB4bc=
			</data>
			<key>hash2</key>
			<data>
			//90faR4mCdj4Pgnr68r3pmdZ5+z/iOV9cF2l9SJ210=
			</data>
		</dict>
		<key>Headers/RSKInternalUtility.h</key>
		<dict>
			<key>hash</key>
			<data>
			aRsvDHcI9aHF0V3jysPW9N59WKU=
			</data>
			<key>hash2</key>
			<data>
			MztFVu6R3GjWsf5VeZ/ADNqAIj4Yasup6AgN6AjETyo=
			</data>
		</dict>
		<key>Headers/RSKTouchView.h</key>
		<dict>
			<key>hash</key>
			<data>
			dDKJBEb3eQ9eDmnhosAdfPW7k+U=
			</data>
			<key>hash2</key>
			<data>
			dI3lc1yIXDONY3q3GFXd5vZZvNCF73CZkEA5k7CZOg0=
			</data>
		</dict>
		<key>Headers/UIApplication+RSKImageCropper.h</key>
		<dict>
			<key>hash</key>
			<data>
			cNSLIKWryyZKPb0gRWIA8fiYgDw=
			</data>
			<key>hash2</key>
			<data>
			ppHdwJJnR/axIFqlqgqM4juyn6zjR1Y9byMKJejPPro=
			</data>
		</dict>
		<key>Headers/UIImage+RSKImageCropper.h</key>
		<dict>
			<key>hash</key>
			<data>
			S+ABoAJ3Bw3lukP1wLxzly7OG0o=
			</data>
			<key>hash2</key>
			<data>
			XHwJ/2mAoOMqIsuTMh8zYI5B3WeGi8wUiD5IeV7bb2E=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			LC3vRCQMcsbHzJe6+8/htZGNCX8=
			</data>
			<key>hash2</key>
			<data>
			Mwwu99MOi4PkdbrItN+7wui6GYqOoZMekFTq31oXRUM=
			</data>
		</dict>
		<key>RSKImageCropperStrings.bundle/ar.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			KRP5wWwCj9s1qV1nVr/MOo/x01I=
			</data>
			<key>hash2</key>
			<data>
			uoaVpD5PoYQ0Z6+L3uQOpuex+/FqScOiSRo2j2fNsRI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/ca.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8zZoXauX6I1ftj+F3D5eMvAMTXc=
			</data>
			<key>hash2</key>
			<data>
			LQUxhXbEFc0TBUqzWxFzg/qYB9MOwiP0zSUb45fbh9k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/cs.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cukPjVeX5e4yIYCOYNjpywgSc6c=
			</data>
			<key>hash2</key>
			<data>
			qT4zwWwpxdyyIju7XhPFpdPUp8vJOD0pp+RPqo24V90=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/da.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			b7oA//Bhw44B7+vIpX65TOM0Pyk=
			</data>
			<key>hash2</key>
			<data>
			2P3rUFnADuNKt+663U8EDADwBLkkiim6ZdEoeMxn8JI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/de.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vIo9QfVbqLXmEvBsNwcpEpjA3s0=
			</data>
			<key>hash2</key>
			<data>
			WovYQwD+nYfAbWwRhMvdpy9haZxUsOhLcCkNbgVAI90=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/el.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ejHOmZ0YRkT66oVSOa/+q8xwplg=
			</data>
			<key>hash2</key>
			<data>
			o+Df+LZ1Pn/I0tPuj9zYWFnNqoxp4MEFgABIargslu0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/en.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			wC7fgfMYe/UJLzf095t6Z0SmHdg=
			</data>
			<key>hash2</key>
			<data>
			65B9BHzfy237FH4Nw6/Dtd2afnFb7tBCxLYnOA+YKMg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/es.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			AYWW7pgXvcot6lrZux495Kfoz5w=
			</data>
			<key>hash2</key>
			<data>
			/jISapBjdkX6FVB9quZmoE3ANjYTRcJOjgpos2JIciY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/fi.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			v5Dc1KM00rxmnD1XtYC4brKD/fI=
			</data>
			<key>hash2</key>
			<data>
			x/aRjP93NsMVgv9vLTizZRWmHFLeyvGYthxsQz/Z4dA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/fr.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1Vo7WQ002cJnfSGJDUAETHpNM/0=
			</data>
			<key>hash2</key>
			<data>
			Kw5lVovthikWQdMt7TDYdZEY+cK5jWNMgGEBrLO31bU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/he.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NlQvxwTvR2cp3MhY8ReLmZH3hY8=
			</data>
			<key>hash2</key>
			<data>
			V2TAjEYgwTJBGd+7bBzpth8yMYg9+j5Mxq4vWOKFsAM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/hi.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7xCLs50cv9NCZzUdr5TFHaSvoiU=
			</data>
			<key>hash2</key>
			<data>
			We//WNWH8rDEiM7Jzl6Lpclo9aKlcgunocPtSNgSA5c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/hr.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kAehbyC5Xq+72CS6/CeOHFAfDJo=
			</data>
			<key>hash2</key>
			<data>
			mYFx0VMGHGGEpXzSTimqtw+aXts0dvkV3MNhayHwA2M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/hu.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iUVXE77b9WE08mtbcEFVhtkhoN4=
			</data>
			<key>hash2</key>
			<data>
			KzJcuyzx5/osg2XjKwHVamV9aF3fxfL4X2Unmz6v0/o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/id.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TLTlBmpGMx/hPImhMexr8YPj0TU=
			</data>
			<key>hash2</key>
			<data>
			ds1neA5w+9+RhZeheufCQ3DHtz3ImDCSpROEemxp/+o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/it.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			4reikpoRRnwKSkSypIVHbGHlDBo=
			</data>
			<key>hash2</key>
			<data>
			YY0UNSqzX700GmVRPXuEiYLBsTCY5XBJ1KmapNMBDaU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/ja.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			w3FQ6m5nvANm5d7x7bDd+7yzDQ4=
			</data>
			<key>hash2</key>
			<data>
			lph6j1nNg1d43MrcW7xQX88kHUz+eNemQtJozZhhUdM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/ms.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iSIn5+shBvxY0/Bc6i0GdMcdKP8=
			</data>
			<key>hash2</key>
			<data>
			O1Ar27XtCWPaDOmuHrvnGQ5+b19PSqAD0Mvqtp2OHdE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/nb.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zgn7FkIFruOTU8tBORwD0tAl+UE=
			</data>
			<key>hash2</key>
			<data>
			BNsZ/pBd6F9kQMaclJB2huzhN+rW3fLY0lC5rh50dQs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/nl.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/tv2/vK8l2k31SkmHKVEI8OQzDw=
			</data>
			<key>hash2</key>
			<data>
			cdgO2d3MKXlzOsiK+I6NaJZGYBO4X9vJ7sSdzdLa02U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/pl.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fbR9pyb7rLxbI6T37y3gxRer6EA=
			</data>
			<key>hash2</key>
			<data>
			TEK6UwWGiubFr//tqPYJJBcHVOS2wrfZgyOEK+7bACM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/pt_BR.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dFxTeXicC9xQNB7qQ36+UBJQ0e8=
			</data>
			<key>hash2</key>
			<data>
			1fr5QSxaBQdzA0sZAXFMJ4a8dlJbafnU8E3GIOicLc0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/pt_PT.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OxDqqdn7juh0hnMzbtraFCUrGNE=
			</data>
			<key>hash2</key>
			<data>
			47RvxCPna8FieeKha0280ZCB0VSvA1TimFED8etS2Ao=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/ro.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			y/WFLLgaErtSY1Z+ckIZtQH3MvQ=
			</data>
			<key>hash2</key>
			<data>
			gh90oS7e/mXmuAK+9coQixrycveccZqCkCnqXX8pXKQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/ru.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PbiVh/+XMhwd6X0DJ072CXvJqQ0=
			</data>
			<key>hash2</key>
			<data>
			9rTlJbr6933R8t1vo+FVy3cK/6dlI0ryg2G/SlEwzC0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/sl.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jFNzvbE7B8ir+v/Fv/cRuHYRJQc=
			</data>
			<key>hash2</key>
			<data>
			DYyeo0xdAQt+aMoXPZlAHt+xn3Wp2cBWKuZVDFY0xv0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/sv.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			oaNMQzrpcAhA7qnaKHuaxyf3E1E=
			</data>
			<key>hash2</key>
			<data>
			17B4qwjRKQPRmBJpcUUCkKHsXhMULybPbDvt7INgDxs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/tr.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TsIMaUDu/aHd+fArxSj3c5QjFqU=
			</data>
			<key>hash2</key>
			<data>
			T/TwIv83S2TVnepcKuwKTxXSKl57m1yKH44UGTzW4EI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/uk.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			J5zx3AHF9HxCRRHBSloul8xB3Sk=
			</data>
			<key>hash2</key>
			<data>
			Q8oYr04gVCKeeJkA+KVLKDcYtGtB973uh+7ZxKFa5Ck=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/vi.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			G63eIlfouDVI82p7ZaYBfmvRyq0=
			</data>
			<key>hash2</key>
			<data>
			HRQHJNSUejO9Koycdft7nejqb7YxB5iopPOXlDppmgw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RSKImageCropperStrings.bundle/zh-Hans.lproj/RSKImageCropper.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PPELUpdxERx2+AcCQjL9kyO1U1E=
			</data>
			<key>hash2</key>
			<data>
			7D6IiGqy3RMypHN4uAE1lomX3Gu794O1dJ9vwl/dNLA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
