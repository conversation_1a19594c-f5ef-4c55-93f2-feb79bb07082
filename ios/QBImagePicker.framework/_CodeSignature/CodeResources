<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/QBImagePicker.h</key>
		<data>
		wIb/otiZ7bd2vyPJQF6JEeoqhqo=
		</data>
		<key>Headers/QBImagePickerController.h</key>
		<data>
		Pj/03SZs3R39/KDJC/Sw28hOcV4=
		</data>
		<key>Info.plist</key>
		<data>
		IcCz/8UZ07kkFONGwju3BBUrdSA=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		b8XazA/6z0jSXdJ08gOuLFgiplM=
		</data>
		<key>QBImagePicker.storyboardc/Info.plist</key>
		<data>
		iwZHbHPu3m3FYRI+FCDLbHeFs44=
		</data>
		<key>QBImagePicker.storyboardc/QBAlbumsNavigationController.nib</key>
		<data>
		5MCnX6u0y0SAVdmtiddUomh9GyY=
		</data>
		<key>QBImagePicker.storyboardc/QBAlbumsViewController.nib</key>
		<data>
		pY80pANgQzeIQuYM9GWFeoXUSxQ=
		</data>
		<key>QBImagePicker.storyboardc/QBAssetsViewController.nib</key>
		<data>
		7+OaszKW0bhc6RotWxC0o0SijKw=
		</data>
		<key>QBImagePicker.storyboardc/QL5-wR-LYt-view-66K-TS-Yoc.nib</key>
		<data>
		0xNFW/713RpJkWqwnLEP8bUWcuI=
		</data>
		<key>QBImagePicker.storyboardc/QiH-NZ-ZGN-view-sD2-zK-ryo.nib</key>
		<data>
		Fj2aIfr+Es6yrxOSQzP/3Uhvvvs=
		</data>
		<key>de.lproj/QBImagePicker.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aEwah75TRZyK1k7zsKbitGOHsEE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/QBImagePicker.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/bN8mAXwM04zgDQj19s7yTHxKQs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/QBImagePicker.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3E8xykBQDKx9XRx4cigrfFeoUwo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/QBImagePicker.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7j4cVfYmTo9Yfo4fA9g4AvzTLhw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pl.lproj/QBImagePicker.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LfadkhKMCL8TGzZnnEP7Pbsb0I4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/QBImagePicker.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0F5a3/dLVWvW3skcaXrrOpnhPv0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/QBImagePicker.h</key>
		<dict>
			<key>hash</key>
			<data>
			wIb/otiZ7bd2vyPJQF6JEeoqhqo=
			</data>
			<key>hash2</key>
			<data>
			Fh9fdGZCY2P54hZbKvAZwwcML6IHufU9QAmHXrcmAMI=
			</data>
		</dict>
		<key>Headers/QBImagePickerController.h</key>
		<dict>
			<key>hash</key>
			<data>
			Pj/03SZs3R39/KDJC/Sw28hOcV4=
			</data>
			<key>hash2</key>
			<data>
			WcCD5/uhcGaZMLBPYHrRY/JkiB7BPQZqn5rkufhmY8c=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			b8XazA/6z0jSXdJ08gOuLFgiplM=
			</data>
			<key>hash2</key>
			<data>
			t0ZEP5nTTzs177dyaKMwGF/sZUJu1h9MPbMCcGQ7acw=
			</data>
		</dict>
		<key>QBImagePicker.storyboardc/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			iwZHbHPu3m3FYRI+FCDLbHeFs44=
			</data>
			<key>hash2</key>
			<data>
			gyFhkTx7M92K1jKJpSG0Yl9R+mQfrd/YtnmTaP54ukY=
			</data>
		</dict>
		<key>QBImagePicker.storyboardc/QBAlbumsNavigationController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			5MCnX6u0y0SAVdmtiddUomh9GyY=
			</data>
			<key>hash2</key>
			<data>
			SLNRGZnuwERXFNcJLu1791ZVDMDsNnnk49Xb01ZjoZo=
			</data>
		</dict>
		<key>QBImagePicker.storyboardc/QBAlbumsViewController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			pY80pANgQzeIQuYM9GWFeoXUSxQ=
			</data>
			<key>hash2</key>
			<data>
			KiZiF6NMbTf2YFOEQs4U1Yef9CSUk8GUiApZAjwtnOM=
			</data>
		</dict>
		<key>QBImagePicker.storyboardc/QBAssetsViewController.nib</key>
		<dict>
			<key>hash</key>
			<data>
			7+OaszKW0bhc6RotWxC0o0SijKw=
			</data>
			<key>hash2</key>
			<data>
			PLJdoPEFJT4ItZgiMWCnK0GDoCTxbI/2wAwBBb8l3wM=
			</data>
		</dict>
		<key>QBImagePicker.storyboardc/QL5-wR-LYt-view-66K-TS-Yoc.nib</key>
		<dict>
			<key>hash</key>
			<data>
			0xNFW/713RpJkWqwnLEP8bUWcuI=
			</data>
			<key>hash2</key>
			<data>
			ey42W/ZWm6W0Eea8Q4i9o79eTvpujgSK8HU9PTkbS/4=
			</data>
		</dict>
		<key>QBImagePicker.storyboardc/QiH-NZ-ZGN-view-sD2-zK-ryo.nib</key>
		<dict>
			<key>hash</key>
			<data>
			Fj2aIfr+Es6yrxOSQzP/3Uhvvvs=
			</data>
			<key>hash2</key>
			<data>
			0iH5b4oLNN+sXyFq/BdGICtJzSnj4ll873t0puG4aqM=
			</data>
		</dict>
		<key>de.lproj/QBImagePicker.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aEwah75TRZyK1k7zsKbitGOHsEE=
			</data>
			<key>hash2</key>
			<data>
			5NH6glXlP7txoHgxKL83/BqWApGAb0poqKByJ1yo+9c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/QBImagePicker.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/bN8mAXwM04zgDQj19s7yTHxKQs=
			</data>
			<key>hash2</key>
			<data>
			xZGH0lOWeKlYEMMZNToG0j+vQVTgUTNoydCOJBG+Kjs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/QBImagePicker.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3E8xykBQDKx9XRx4cigrfFeoUwo=
			</data>
			<key>hash2</key>
			<data>
			T0LWvjKc9Wmuxznj7gCFXRNeGYuFiF92kDcjOVI/WPo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/QBImagePicker.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7j4cVfYmTo9Yfo4fA9g4AvzTLhw=
			</data>
			<key>hash2</key>
			<data>
			jxfNgX8MVlO7C53RGOjJwPc1dcSj9DxTyWW8N37BkaE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>pl.lproj/QBImagePicker.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LfadkhKMCL8TGzZnnEP7Pbsb0I4=
			</data>
			<key>hash2</key>
			<data>
			BQCDVBKWaLio8bCJuurR2oc3ENu7K8c6N9ECEh98owU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/QBImagePicker.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0F5a3/dLVWvW3skcaXrrOpnhPv0=
			</data>
			<key>hash2</key>
			<data>
			smMz2/UyQzPo54Zq6lLJj+H42kPbfO4Bfy0iP1jCyf8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
