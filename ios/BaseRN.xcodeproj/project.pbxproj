// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		00C302E51ABCBA2D00DB3ED1 /* libRCTActionSheet.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302AC1ABCB8CE00DB3ED1 /* libRCTActionSheet.a */; };
		00C302E71ABCBA2D00DB3ED1 /* libRCTGeolocation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302BA1ABCB90400DB3ED1 /* libRCTGeolocation.a */; };
		00C302E81ABCBA2D00DB3ED1 /* libRCTImage.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302C01ABCB91800DB3ED1 /* libRCTImage.a */; };
		00C302E91ABCBA2D00DB3ED1 /* libRCTNetwork.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302DC1ABCB9D200DB3ED1 /* libRCTNetwork.a */; };
		00C302EA1ABCBA2D00DB3ED1 /* libRCTVibration.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302E41ABCB9EE00DB3ED1 /* libRCTVibration.a */; };
		133E29F31AD74F7200F7D852 /* libRCTLinking.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 78C398B91ACF4ADC00677621 /* libRCTLinking.a */; };
		139105C61AF99C1200B5F7CC /* libRCTSettings.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 139105C11AF99BAD00B5F7CC /* libRCTSettings.a */; };
		139FDEF61B0652A700C62182 /* libRCTWebSocket.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 139FDEF41B06529B00C62182 /* libRCTWebSocket.a */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		146834051AC3E58100842450 /* libReact.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 146834041AC3E56700842450 /* libReact.a */; };
		4202614E212D01AB001B3A50 /* libAFNetworking.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260D3212CFE56001B3A50 /* libAFNetworking.a */; };
		4202614F212D01AB001B3A50 /* libAliOSS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260D9212CFE69001B3A50 /* libAliOSS.a */; };
		42026150212D01AB001B3A50 /* libAliPay.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260DF212CFE76001B3A50 /* libAliPay.a */; };
		42026151212D01AB001B3A50 /* libAliyunSLS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260E5212CFE85001B3A50 /* libAliyunSLS.a */; };
		42026152212D01AB001B3A50 /* libAMap.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260EB212CFE96001B3A50 /* libAMap.a */; };
		42026154212D01AB001B3A50 /* libAppTool.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260F7212CFED3001B3A50 /* libAppTool.a */; };
		42026155212D01AB001B3A50 /* libCaptcha.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260FD212CFEE1001B3A50 /* libCaptcha.a */; };
		42026156212D01AB001B3A50 /* libConfigStorage.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026103212CFEEF001B3A50 /* libConfigStorage.a */; };
		42026157212D01AB001B3A50 /* libDeviceInfo.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026109212CFEFE001B3A50 /* libDeviceInfo.a */; };
		42026158212D01AB001B3A50 /* libImageCropPicker.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4202610F212CFF0E001B3A50 /* libImageCropPicker.a */; };
		42026159212D01AB001B3A50 /* libJPush.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026115212CFF1B001B3A50 /* libJPush.a */; };
		4202615A212D01AB001B3A50 /* libMBProgressHUD.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4202611B212CFF29001B3A50 /* libMBProgressHUD.a */; };
		4202615B212D01AB001B3A50 /* libPush.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026121212CFF5A001B3A50 /* libPush.a */; };
		4202615C212D01AB001B3A50 /* libSGQRCode.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026127212CFF72001B3A50 /* libSGQRCode.a */; };
		4202615D212D01AB001B3A50 /* libSocialShare.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4202612D212CFF83001B3A50 /* libSocialShare.a */; };
		4202615E212D01AB001B3A50 /* libStatusBarUtil.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026133212CFF95001B3A50 /* libStatusBarUtil.a */; };
		4202615F212D01AB001B3A50 /* libWeChat.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026139212CFFA2001B3A50 /* libWeChat.a */; };
		42026161212D01C2001B3A50 /* UShareUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026160212D01C2001B3A50 /* UShareUI.framework */; };
		42026166212D01D6001B3A50 /* AMapLocationKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026162212D01D6001B3A50 /* AMapLocationKit.framework */; };
		42026167212D01D6001B3A50 /* AMapFoundationKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026163212D01D6001B3A50 /* AMapFoundationKit.framework */; };
		42026168212D01D6001B3A50 /* MAMapKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026164212D01D6001B3A50 /* MAMapKit.framework */; };
		42026169212D01D6001B3A50 /* AMapSearchKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026165212D01D6001B3A50 /* AMapSearchKit.framework */; };
		4202616B212D01E8001B3A50 /* AlipaySDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4202616A212D01E8001B3A50 /* AlipaySDK.framework */; };
		4202616D212D01EE001B3A50 /* GLKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4202616C212D01EE001B3A50 /* GLKit.framework */; };
		42026174212D0240001B3A50 /* QBImagePicker.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 42026173212D022E001B3A50 /* QBImagePicker.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		42026175212D0248001B3A50 /* RSKImageCropper.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 42026172212D022E001B3A50 /* RSKImageCropper.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		420261E5212D0514001B3A50 /* libAFNetworking.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260D3212CFE56001B3A50 /* libAFNetworking.a */; };
		420261E6212D0514001B3A50 /* libAliOSS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260D9212CFE69001B3A50 /* libAliOSS.a */; };
		420261E7212D0514001B3A50 /* libAliPay.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260DF212CFE76001B3A50 /* libAliPay.a */; };
		420261E8212D0514001B3A50 /* libAliyunSLS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260E5212CFE85001B3A50 /* libAliyunSLS.a */; };
		420261E9212D0514001B3A50 /* libAMap.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260EB212CFE96001B3A50 /* libAMap.a */; };
		420261EB212D0514001B3A50 /* libAppTool.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260F7212CFED3001B3A50 /* libAppTool.a */; };
		420261EC212D0514001B3A50 /* libCaptcha.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260FD212CFEE1001B3A50 /* libCaptcha.a */; };
		420261ED212D0514001B3A50 /* libConfigStorage.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026103212CFEEF001B3A50 /* libConfigStorage.a */; };
		420261EE212D0514001B3A50 /* libDeviceInfo.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026109212CFEFE001B3A50 /* libDeviceInfo.a */; };
		420261EF212D0514001B3A50 /* libImageCropPicker.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4202610F212CFF0E001B3A50 /* libImageCropPicker.a */; };
		420261F0212D0514001B3A50 /* libJPush.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026115212CFF1B001B3A50 /* libJPush.a */; };
		420261F1212D0514001B3A50 /* libMBProgressHUD.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4202611B212CFF29001B3A50 /* libMBProgressHUD.a */; };
		420261F2212D0514001B3A50 /* libPush.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026121212CFF5A001B3A50 /* libPush.a */; };
		420261F3212D0514001B3A50 /* libSGQRCode.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026127212CFF72001B3A50 /* libSGQRCode.a */; };
		420261F4212D0514001B3A50 /* libSocialShare.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4202612D212CFF83001B3A50 /* libSocialShare.a */; };
		420261F5212D0514001B3A50 /* libStatusBarUtil.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026133212CFF95001B3A50 /* libStatusBarUtil.a */; };
		420261F6212D0514001B3A50 /* libWeChat.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026139212CFFA2001B3A50 /* libWeChat.a */; };
		420261F7212D0528001B3A50 /* UShareUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026160212D01C2001B3A50 /* UShareUI.framework */; };
		420261F8212D0539001B3A50 /* AMapFoundationKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026163212D01D6001B3A50 /* AMapFoundationKit.framework */; };
		420261F9212D0539001B3A50 /* AMapLocationKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026162212D01D6001B3A50 /* AMapLocationKit.framework */; };
		420261FA212D0539001B3A50 /* AMapSearchKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026165212D01D6001B3A50 /* AMapSearchKit.framework */; };
		420261FB212D0539001B3A50 /* MAMapKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026164212D01D6001B3A50 /* MAMapKit.framework */; };
		420261FC212D0546001B3A50 /* AlipaySDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4202616A212D01E8001B3A50 /* AlipaySDK.framework */; };
		420261FD212D054F001B3A50 /* GLKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4202616C212D01EE001B3A50 /* GLKit.framework */; };
		420261FE212D056A001B3A50 /* QBImagePicker.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 42026173212D022E001B3A50 /* QBImagePicker.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		420261FF212D0573001B3A50 /* RSKImageCropper.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 42026172212D022E001B3A50 /* RSKImageCropper.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		42026200212D0704001B3A50 /* QBImagePicker.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 42026173212D022E001B3A50 /* QBImagePicker.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		42026201212D070A001B3A50 /* RSKImageCropper.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 42026172212D022E001B3A50 /* RSKImageCropper.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		42026202212D072F001B3A50 /* libAFNetworking.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260D3212CFE56001B3A50 /* libAFNetworking.a */; };
		42026203212D072F001B3A50 /* libAliOSS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260D9212CFE69001B3A50 /* libAliOSS.a */; };
		42026204212D072F001B3A50 /* libAliPay.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260DF212CFE76001B3A50 /* libAliPay.a */; };
		42026205212D072F001B3A50 /* libAliyunSLS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260E5212CFE85001B3A50 /* libAliyunSLS.a */; };
		42026206212D072F001B3A50 /* libAMap.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260EB212CFE96001B3A50 /* libAMap.a */; };
		42026208212D072F001B3A50 /* libAppTool.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260F7212CFED3001B3A50 /* libAppTool.a */; };
		42026209212D072F001B3A50 /* libCaptcha.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 420260FD212CFEE1001B3A50 /* libCaptcha.a */; };
		4202620A212D072F001B3A50 /* libConfigStorage.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026103212CFEEF001B3A50 /* libConfigStorage.a */; };
		4202620B212D072F001B3A50 /* libDeviceInfo.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026109212CFEFE001B3A50 /* libDeviceInfo.a */; };
		4202620C212D072F001B3A50 /* libImageCropPicker.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4202610F212CFF0E001B3A50 /* libImageCropPicker.a */; };
		4202620D212D072F001B3A50 /* libJPush.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026115212CFF1B001B3A50 /* libJPush.a */; };
		4202620E212D072F001B3A50 /* libMBProgressHUD.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4202611B212CFF29001B3A50 /* libMBProgressHUD.a */; };
		4202620F212D072F001B3A50 /* libPush.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026121212CFF5A001B3A50 /* libPush.a */; };
		42026210212D072F001B3A50 /* libSGQRCode.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026127212CFF72001B3A50 /* libSGQRCode.a */; };
		42026211212D072F001B3A50 /* libSocialShare.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4202612D212CFF83001B3A50 /* libSocialShare.a */; };
		42026212212D072F001B3A50 /* libStatusBarUtil.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026133212CFF95001B3A50 /* libStatusBarUtil.a */; };
		42026213212D072F001B3A50 /* libWeChat.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026139212CFFA2001B3A50 /* libWeChat.a */; };
		42026214212D0744001B3A50 /* UShareUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026160212D01C2001B3A50 /* UShareUI.framework */; };
		42026215212D0756001B3A50 /* AMapFoundationKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026163212D01D6001B3A50 /* AMapFoundationKit.framework */; };
		42026216212D0756001B3A50 /* AMapLocationKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026162212D01D6001B3A50 /* AMapLocationKit.framework */; };
		42026217212D0756001B3A50 /* AMapSearchKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026165212D01D6001B3A50 /* AMapSearchKit.framework */; };
		42026218212D0756001B3A50 /* MAMapKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026164212D01D6001B3A50 /* MAMapKit.framework */; };
		42026219212D0765001B3A50 /* AlipaySDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4202616A212D01E8001B3A50 /* AlipaySDK.framework */; };
		4202621A212D0780001B3A50 /* GLKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4202616C212D01EE001B3A50 /* GLKit.framework */; };
		42026446212E8BC4001B3A50 /* AlipaySDK.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026445212E8BC3001B3A50 /* AlipaySDK.bundle */; };
		42026447212E8BC4001B3A50 /* AlipaySDK.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026445212E8BC3001B3A50 /* AlipaySDK.bundle */; };
		42026448212E8BC4001B3A50 /* AlipaySDK.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026445212E8BC3001B3A50 /* AlipaySDK.bundle */; };
		4202644A212E8BD7001B3A50 /* SGQRCode.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026449212E8BD7001B3A50 /* SGQRCode.bundle */; };
		4202644B212E8BD7001B3A50 /* SGQRCode.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026449212E8BD7001B3A50 /* SGQRCode.bundle */; };
		4202644C212E8BD7001B3A50 /* SGQRCode.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026449212E8BD7001B3A50 /* SGQRCode.bundle */; };
		4202644E212E8BE9001B3A50 /* UMSocialSDKResources.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4202644D212E8BE9001B3A50 /* UMSocialSDKResources.bundle */; };
		4202644F212E8BE9001B3A50 /* UMSocialSDKResources.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4202644D212E8BE9001B3A50 /* UMSocialSDKResources.bundle */; };
		42026450212E8BE9001B3A50 /* UMSocialSDKResources.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4202644D212E8BE9001B3A50 /* UMSocialSDKResources.bundle */; };
		42026452212E8C01001B3A50 /* UMSocialSDKPromptResources.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026451212E8C01001B3A50 /* UMSocialSDKPromptResources.bundle */; };
		42026453212E8C01001B3A50 /* UMSocialSDKPromptResources.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026451212E8C01001B3A50 /* UMSocialSDKPromptResources.bundle */; };
		42026454212E8C01001B3A50 /* UMSocialSDKPromptResources.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026451212E8C01001B3A50 /* UMSocialSDKPromptResources.bundle */; };
		42026488212E8CE5001B3A50 /* RCTAPPayAssistEx.m in Sources */ = {isa = PBXBuildFile; fileRef = 42026457212E8CE3001B3A50 /* RCTAPPayAssistEx.m */; };
		42026489212E8CE5001B3A50 /* RCTAPPayAssistEx.m in Sources */ = {isa = PBXBuildFile; fileRef = 42026457212E8CE3001B3A50 /* RCTAPPayAssistEx.m */; };
		4202648A212E8CE5001B3A50 /* RCTAPPayAssistEx.m in Sources */ = {isa = PBXBuildFile; fileRef = 42026457212E8CE3001B3A50 /* RCTAPPayAssistEx.m */; };
		4202648B212E8CE5001B3A50 /* TrustDefenderMobile.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026459212E8CE5001B3A50 /* TrustDefenderMobile.framework */; };
		4202648C212E8CE5001B3A50 /* TrustDefenderMobile.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026459212E8CE5001B3A50 /* TrustDefenderMobile.framework */; };
		4202648D212E8CE5001B3A50 /* TrustDefenderMobile.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026459212E8CE5001B3A50 /* TrustDefenderMobile.framework */; };
		4202648E212E8CE5001B3A50 /* APMPPassGuardCtrlBundle.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4202645A212E8CE5001B3A50 /* APMPPassGuardCtrlBundle.bundle */; };
		4202648F212E8CE5001B3A50 /* APMPPassGuardCtrlBundle.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4202645A212E8CE5001B3A50 /* APMPPassGuardCtrlBundle.bundle */; };
		42026490212E8CE5001B3A50 /* APMPPassGuardCtrlBundle.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 4202645A212E8CE5001B3A50 /* APMPPassGuardCtrlBundle.bundle */; };
		42026491212E8CE5001B3A50 /* libAPayLib.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026464212E8CE5001B3A50 /* libAPayLib.a */; };
		42026492212E8CE5001B3A50 /* libAPayLib.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026464212E8CE5001B3A50 /* libAPayLib.a */; };
		42026493212E8CE5001B3A50 /* libAPayLib.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 42026464212E8CE5001B3A50 /* libAPayLib.a */; };
		42026494212E8CE5001B3A50 /* APayRes.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026465212E8CE5001B3A50 /* APayRes.bundle */; };
		42026495212E8CE5001B3A50 /* APayRes.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026465212E8CE5001B3A50 /* APayRes.bundle */; };
		42026496212E8CE5001B3A50 /* APayRes.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026465212E8CE5001B3A50 /* APayRes.bundle */; };
		42026497212E8CE5001B3A50 /* VerificationPage.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 42026469212E8CE5001B3A50 /* VerificationPage.storyboard */; };
		42026498212E8CE5001B3A50 /* VerificationPage.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 42026469212E8CE5001B3A50 /* VerificationPage.storyboard */; };
		42026499212E8CE5001B3A50 /* VerificationPage.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 42026469212E8CE5001B3A50 /* VerificationPage.storyboard */; };
		4202649A212E8CE5001B3A50 /* RegistrationPage.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 4202646A212E8CE5001B3A50 /* RegistrationPage.storyboard */; };
		4202649B212E8CE5001B3A50 /* RegistrationPage.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 4202646A212E8CE5001B3A50 /* RegistrationPage.storyboard */; };
		4202649C212E8CE5001B3A50 /* RegistrationPage.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 4202646A212E8CE5001B3A50 /* RegistrationPage.storyboard */; };
		4202649D212E8CE5001B3A50 /* StringList.plist in Resources */ = {isa = PBXBuildFile; fileRef = 4202646B212E8CE5001B3A50 /* StringList.plist */; };
		4202649E212E8CE5001B3A50 /* StringList.plist in Resources */ = {isa = PBXBuildFile; fileRef = 4202646B212E8CE5001B3A50 /* StringList.plist */; };
		4202649F212E8CE5001B3A50 /* StringList.plist in Resources */ = {isa = PBXBuildFile; fileRef = 4202646B212E8CE5001B3A50 /* StringList.plist */; };
		420264A0212E8CE5001B3A50 /* 张嘴.gif in Resources */ = {isa = PBXBuildFile; fileRef = 4202646E212E8CE5001B3A50 /* 张嘴.gif */; };
		420264A1212E8CE5001B3A50 /* 张嘴.gif in Resources */ = {isa = PBXBuildFile; fileRef = 4202646E212E8CE5001B3A50 /* 张嘴.gif */; };
		420264A2212E8CE5001B3A50 /* 张嘴.gif in Resources */ = {isa = PBXBuildFile; fileRef = 4202646E212E8CE5001B3A50 /* 张嘴.gif */; };
		420264A3212E8CE5001B3A50 /* 扭头-右.gif in Resources */ = {isa = PBXBuildFile; fileRef = 4202646F212E8CE5001B3A50 /* 扭头-右.gif */; };
		420264A4212E8CE5001B3A50 /* 扭头-右.gif in Resources */ = {isa = PBXBuildFile; fileRef = 4202646F212E8CE5001B3A50 /* 扭头-右.gif */; };
		420264A5212E8CE5001B3A50 /* 扭头-右.gif in Resources */ = {isa = PBXBuildFile; fileRef = 4202646F212E8CE5001B3A50 /* 扭头-右.gif */; };
		420264A6212E8CE5001B3A50 /* 低头.gif in Resources */ = {isa = PBXBuildFile; fileRef = 42026470212E8CE5001B3A50 /* 低头.gif */; };
		420264A7212E8CE5001B3A50 /* 低头.gif in Resources */ = {isa = PBXBuildFile; fileRef = 42026470212E8CE5001B3A50 /* 低头.gif */; };
		420264A8212E8CE5001B3A50 /* 低头.gif in Resources */ = {isa = PBXBuildFile; fileRef = 42026470212E8CE5001B3A50 /* 低头.gif */; };
		420264A9212E8CE5001B3A50 /* yitu_face_action_normal.gif in Resources */ = {isa = PBXBuildFile; fileRef = 42026471212E8CE5001B3A50 /* yitu_face_action_normal.gif */; };
		420264AA212E8CE5001B3A50 /* yitu_face_action_normal.gif in Resources */ = {isa = PBXBuildFile; fileRef = 42026471212E8CE5001B3A50 /* yitu_face_action_normal.gif */; };
		420264AB212E8CE5001B3A50 /* yitu_face_action_normal.gif in Resources */ = {isa = PBXBuildFile; fileRef = 42026471212E8CE5001B3A50 /* yitu_face_action_normal.gif */; };
		420264AC212E8CE5001B3A50 /* 抬头.gif in Resources */ = {isa = PBXBuildFile; fileRef = 42026472212E8CE5001B3A50 /* 抬头.gif */; };
		420264AD212E8CE5001B3A50 /* 抬头.gif in Resources */ = {isa = PBXBuildFile; fileRef = 42026472212E8CE5001B3A50 /* 抬头.gif */; };
		420264AE212E8CE5001B3A50 /* 抬头.gif in Resources */ = {isa = PBXBuildFile; fileRef = 42026472212E8CE5001B3A50 /* 抬头.gif */; };
		420264AF212E8CE5001B3A50 /* 闭眼.gif in Resources */ = {isa = PBXBuildFile; fileRef = 42026473212E8CE5001B3A50 /* 闭眼.gif */; };
		420264B0212E8CE5001B3A50 /* 闭眼.gif in Resources */ = {isa = PBXBuildFile; fileRef = 42026473212E8CE5001B3A50 /* 闭眼.gif */; };
		420264B1212E8CE5001B3A50 /* 闭眼.gif in Resources */ = {isa = PBXBuildFile; fileRef = 42026473212E8CE5001B3A50 /* 闭眼.gif */; };
		420264B2212E8CE5001B3A50 /* 扭头-左.gif in Resources */ = {isa = PBXBuildFile; fileRef = 42026474212E8CE5001B3A50 /* 扭头-左.gif */; };
		420264B3212E8CE5001B3A50 /* 扭头-左.gif in Resources */ = {isa = PBXBuildFile; fileRef = 42026474212E8CE5001B3A50 /* 扭头-左.gif */; };
		420264B4212E8CE5001B3A50 /* 扭头-左.gif in Resources */ = {isa = PBXBuildFile; fileRef = 42026474212E8CE5001B3A50 /* 扭头-左.gif */; };
		420264B5212E8CE5001B3A50 /* yitu_face_model.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026475212E8CE5001B3A50 /* yitu_face_model.bundle */; };
		420264B6212E8CE5001B3A50 /* yitu_face_model.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026475212E8CE5001B3A50 /* yitu_face_model.bundle */; };
		420264B7212E8CE5001B3A50 /* yitu_face_model.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026475212E8CE5001B3A50 /* yitu_face_model.bundle */; };
		420264B8212E8CE5001B3A50 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 42026476212E8CE5001B3A50 /* Images.xcassets */; };
		420264B9212E8CE5001B3A50 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 42026476212E8CE5001B3A50 /* Images.xcassets */; };
		420264BA212E8CE5001B3A50 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 42026476212E8CE5001B3A50 /* Images.xcassets */; };
		420264BB212E8CE5001B3A50 /* yitu_eye_close_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026478212E8CE5001B3A50 /* yitu_eye_close_audio.mp3 */; };
		420264BC212E8CE5001B3A50 /* yitu_eye_close_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026478212E8CE5001B3A50 /* yitu_eye_close_audio.mp3 */; };
		420264BD212E8CE5001B3A50 /* yitu_eye_close_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026478212E8CE5001B3A50 /* yitu_eye_close_audio.mp3 */; };
		420264BE212E8CE5001B3A50 /* yitu_head_right_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026479212E8CE5001B3A50 /* yitu_head_right_audio.mp3 */; };
		420264BF212E8CE5001B3A50 /* yitu_head_right_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026479212E8CE5001B3A50 /* yitu_head_right_audio.mp3 */; };
		420264C0212E8CE5001B3A50 /* yitu_head_right_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026479212E8CE5001B3A50 /* yitu_head_right_audio.mp3 */; };
		420264C1212E8CE5001B3A50 /* yitu_verification_fail_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647A212E8CE5001B3A50 /* yitu_verification_fail_audio.mp3 */; };
		420264C2212E8CE5001B3A50 /* yitu_verification_fail_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647A212E8CE5001B3A50 /* yitu_verification_fail_audio.mp3 */; };
		420264C3212E8CE5001B3A50 /* yitu_verification_fail_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647A212E8CE5001B3A50 /* yitu_verification_fail_audio.mp3 */; };
		420264C4212E8CE5001B3A50 /* yitu_head_up_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647B212E8CE5001B3A50 /* yitu_head_up_audio.mp3 */; };
		420264C5212E8CE5001B3A50 /* yitu_head_up_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647B212E8CE5001B3A50 /* yitu_head_up_audio.mp3 */; };
		420264C6212E8CE5001B3A50 /* yitu_head_up_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647B212E8CE5001B3A50 /* yitu_head_up_audio.mp3 */; };
		420264C7212E8CE5001B3A50 /* yitu_mouth_open_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647C212E8CE5001B3A50 /* yitu_mouth_open_audio.mp3 */; };
		420264C8212E8CE5001B3A50 /* yitu_mouth_open_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647C212E8CE5001B3A50 /* yitu_mouth_open_audio.mp3 */; };
		420264C9212E8CE5001B3A50 /* yitu_mouth_open_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647C212E8CE5001B3A50 /* yitu_mouth_open_audio.mp3 */; };
		420264CA212E8CE5001B3A50 /* yitu_next_action_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647D212E8CE5001B3A50 /* yitu_next_action_audio.mp3 */; };
		420264CB212E8CE5001B3A50 /* yitu_next_action_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647D212E8CE5001B3A50 /* yitu_next_action_audio.mp3 */; };
		420264CC212E8CE5001B3A50 /* yitu_next_action_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647D212E8CE5001B3A50 /* yitu_next_action_audio.mp3 */; };
		420264CD212E8CE5001B3A50 /* yitu_verification_pass_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647E212E8CE5001B3A50 /* yitu_verification_pass_audio.mp3 */; };
		420264CE212E8CE5001B3A50 /* yitu_verification_pass_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647E212E8CE5001B3A50 /* yitu_verification_pass_audio.mp3 */; };
		420264CF212E8CE5001B3A50 /* yitu_verification_pass_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647E212E8CE5001B3A50 /* yitu_verification_pass_audio.mp3 */; };
		420264D0212E8CE5001B3A50 /* yitu_time_out_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647F212E8CE5001B3A50 /* yitu_time_out_audio.mp3 */; };
		420264D1212E8CE5001B3A50 /* yitu_time_out_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647F212E8CE5001B3A50 /* yitu_time_out_audio.mp3 */; };
		420264D2212E8CE5001B3A50 /* yitu_time_out_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4202647F212E8CE5001B3A50 /* yitu_time_out_audio.mp3 */; };
		420264D3212E8CE5001B3A50 /* yitu_get_ready_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026480212E8CE5001B3A50 /* yitu_get_ready_audio.mp3 */; };
		420264D4212E8CE5001B3A50 /* yitu_get_ready_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026480212E8CE5001B3A50 /* yitu_get_ready_audio.mp3 */; };
		420264D5212E8CE5001B3A50 /* yitu_get_ready_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026480212E8CE5001B3A50 /* yitu_get_ready_audio.mp3 */; };
		420264D6212E8CE5001B3A50 /* yitu_head_left_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026481212E8CE5001B3A50 /* yitu_head_left_audio.mp3 */; };
		420264D7212E8CE5001B3A50 /* yitu_head_left_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026481212E8CE5001B3A50 /* yitu_head_left_audio.mp3 */; };
		420264D8212E8CE5001B3A50 /* yitu_head_left_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026481212E8CE5001B3A50 /* yitu_head_left_audio.mp3 */; };
		420264D9212E8CE5001B3A50 /* yitu_head_down_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026482212E8CE5001B3A50 /* yitu_head_down_audio.mp3 */; };
		420264DA212E8CE5001B3A50 /* yitu_head_down_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026482212E8CE5001B3A50 /* yitu_head_down_audio.mp3 */; };
		420264DB212E8CE5001B3A50 /* yitu_head_down_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026482212E8CE5001B3A50 /* yitu_head_down_audio.mp3 */; };
		420264DC212E8CE5001B3A50 /* yitu_not_user_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026483212E8CE5001B3A50 /* yitu_not_user_audio.mp3 */; };
		420264DD212E8CE5001B3A50 /* yitu_not_user_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026483212E8CE5001B3A50 /* yitu_not_user_audio.mp3 */; };
		420264DE212E8CE5001B3A50 /* yitu_not_user_audio.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 42026483212E8CE5001B3A50 /* yitu_not_user_audio.mp3 */; };
		420264DF212E8CE5001B3A50 /* staging.public.der in Resources */ = {isa = PBXBuildFile; fileRef = 42026484212E8CE5001B3A50 /* staging.public.der */; };
		420264E0212E8CE5001B3A50 /* staging.public.der in Resources */ = {isa = PBXBuildFile; fileRef = 42026484212E8CE5001B3A50 /* staging.public.der */; };
		420264E1212E8CE5001B3A50 /* staging.public.der in Resources */ = {isa = PBXBuildFile; fileRef = 42026484212E8CE5001B3A50 /* staging.public.der */; };
		420264E2212E8CE5001B3A50 /* MBProgressHUD.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026486212E8CE5001B3A50 /* MBProgressHUD.bundle */; };
		420264E3212E8CE5001B3A50 /* MBProgressHUD.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026486212E8CE5001B3A50 /* MBProgressHUD.bundle */; };
		420264E4212E8CE5001B3A50 /* MBProgressHUD.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026486212E8CE5001B3A50 /* MBProgressHUD.bundle */; };
		420264E5212E8CE5001B3A50 /* Image.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026487212E8CE5001B3A50 /* Image.bundle */; };
		420264E6212E8CE5001B3A50 /* Image.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026487212E8CE5001B3A50 /* Image.bundle */; };
		420264E7212E8CE5001B3A50 /* Image.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 42026487212E8CE5001B3A50 /* Image.bundle */; };
		4206F149205E0CDB001C21E4 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		4206F15C205E0CDB001C21E4 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		4206F191205E0CDB001C21E4 /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = F03E74A21EC1760E008B4B70 /* libz.tbd */; };
		4206F193205E0CDB001C21E4 /* libc++abi.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 42C740201FB2E8BD000A1C6B /* libc++abi.tbd */; };
		4206F198205E0CDB001C21E4 /* libRNSVG.a in Frameworks */ = {isa = PBXBuildFile; fileRef = AE4783ED1F4ADCFC00FDBD75 /* libRNSVG.a */; };
		4206F199205E0CDB001C21E4 /* UserNotifications.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AEBCE5831F44368A00B4DC66 /* UserNotifications.framework */; };
		4206F19B205E0CDB001C21E4 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AEBCE5811F44366B00B4DC66 /* Security.framework */; };
		4206F19C205E0CDB001C21E4 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AE30700E1F3446940069FB92 /* UIKit.framework */; };
		4206F19D205E0CDB001C21E4 /* MobileCoreServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AE25209E1F301A8900479CE1 /* MobileCoreServices.framework */; };
		4206F19E205E0CDB001C21E4 /* libxml2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = AE25209C1F301A7A00479CE1 /* libxml2.tbd */; };
		4206F19F205E0CDB001C21E4 /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AECAA7101F2F0D990048F2F3 /* CoreMotion.framework */; };
		4206F1A0205E0CDB001C21E4 /* CFNetwork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AECAA70E1F2F0D8F0048F2F3 /* CFNetwork.framework */; };
		4206F1A4205E0CDB001C21E4 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AECAA70C1F2F0D840048F2F3 /* CoreGraphics.framework */; };
		4206F1A5205E0CDB001C21E4 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AECAA70A1F2F0D770048F2F3 /* QuartzCore.framework */; };
		4206F1A6205E0CDB001C21E4 /* CoreText.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AECAA7081F2F0D580048F2F3 /* CoreText.framework */; };
		4206F1A8205E0CDB001C21E4 /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = F03E749E1EC175E4008B4B70 /* libc++.tbd */; };
		4206F1A9205E0CDB001C21E4 /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = F0B562F31EBC1F710000AB74 /* libresolv.tbd */; };
		4206F1AA205E0CDB001C21E4 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F0B562F51EBC1F7C0000AB74 /* SystemConfiguration.framework */; };
		4206F1AD205E0CDB001C21E4 /* libsqlite3.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = EB080EB21EF8FA5D00EA73A0 /* libsqlite3.tbd */; };
		4206F1B2205E0CDB001C21E4 /* libicucore.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = EB080E961EF8FA5500EA73A0 /* libicucore.tbd */; };
		4206F1B3205E0CDB001C21E4 /* libsqlite3.0.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = F063DEE81ECB0316000A5D4F /* libsqlite3.0.tbd */; };
		4206F1BC205E0CDB001C21E4 /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F03E74A01EC17603008B4B70 /* CoreTelephony.framework */; };
		4206F1BF205E0CDB001C21E4 /* libReact.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 146834041AC3E56700842450 /* libReact.a */; };
		4206F1C0205E0CDB001C21E4 /* libRCTAnimation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 5E9157331DD0AC6500FF2AA8 /* libRCTAnimation.a */; };
		4206F1C1205E0CDB001C21E4 /* libRCTActionSheet.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302AC1ABCB8CE00DB3ED1 /* libRCTActionSheet.a */; };
		4206F1C2205E0CDB001C21E4 /* libRCTGeolocation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302BA1ABCB90400DB3ED1 /* libRCTGeolocation.a */; };
		4206F1C3205E0CDB001C21E4 /* libRCTImage.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302C01ABCB91800DB3ED1 /* libRCTImage.a */; };
		4206F1C5205E0CDB001C21E4 /* libRCTLinking.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 78C398B91ACF4ADC00677621 /* libRCTLinking.a */; };
		4206F1C6205E0CDB001C21E4 /* libRCTNetwork.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302DC1ABCB9D200DB3ED1 /* libRCTNetwork.a */; };
		4206F1C7205E0CDB001C21E4 /* libRCTSettings.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 139105C11AF99BAD00B5F7CC /* libRCTSettings.a */; };
		4206F1C9205E0CDB001C21E4 /* libRCTText.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 832341B51AAA6A8300B99B32 /* libRCTText.a */; };
		4206F1CA205E0CDB001C21E4 /* libRCTVibration.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302E41ABCB9EE00DB3ED1 /* libRCTVibration.a */; };
		4206F1CB205E0CDB001C21E4 /* libRCTWebSocket.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 139FDEF41B06529B00C62182 /* libRCTWebSocket.a */; };
		4206F1D1205E0CDB001C21E4 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		4219E0FB219D9B4F0080C095 /* libCodePush.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4219E0F8219D9B450080C095 /* libCodePush.a */; };
		421B084D212FFF810041D0F4 /* AMap.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 421B082D212FFF810041D0F4 /* AMap.bundle */; };
		421B084E212FFF810041D0F4 /* AMap.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 421B082D212FFF810041D0F4 /* AMap.bundle */; };
		421B084F212FFF810041D0F4 /* AMap.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 421B082D212FFF810041D0F4 /* AMap.bundle */; };
		422063801FF482A000C81AF1 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		422063921FF482A000C81AF1 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		422063C71FF482A000C81AF1 /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = F03E74A21EC1760E008B4B70 /* libz.tbd */; };
		422063C91FF482A000C81AF1 /* libc++abi.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 42C740201FB2E8BD000A1C6B /* libc++abi.tbd */; };
		422063CE1FF482A000C81AF1 /* libRNSVG.a in Frameworks */ = {isa = PBXBuildFile; fileRef = AE4783ED1F4ADCFC00FDBD75 /* libRNSVG.a */; };
		422063CF1FF482A000C81AF1 /* UserNotifications.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AEBCE5831F44368A00B4DC66 /* UserNotifications.framework */; };
		422063D11FF482A000C81AF1 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AEBCE5811F44366B00B4DC66 /* Security.framework */; };
		422063D21FF482A000C81AF1 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AE30700E1F3446940069FB92 /* UIKit.framework */; };
		422063D31FF482A000C81AF1 /* MobileCoreServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AE25209E1F301A8900479CE1 /* MobileCoreServices.framework */; };
		422063D41FF482A000C81AF1 /* libxml2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = AE25209C1F301A7A00479CE1 /* libxml2.tbd */; };
		422063D51FF482A000C81AF1 /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AECAA7101F2F0D990048F2F3 /* CoreMotion.framework */; };
		422063D61FF482A000C81AF1 /* CFNetwork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AECAA70E1F2F0D8F0048F2F3 /* CFNetwork.framework */; };
		422063DA1FF482A000C81AF1 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AECAA70C1F2F0D840048F2F3 /* CoreGraphics.framework */; };
		422063DB1FF482A000C81AF1 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AECAA70A1F2F0D770048F2F3 /* QuartzCore.framework */; };
		422063DC1FF482A000C81AF1 /* CoreText.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AECAA7081F2F0D580048F2F3 /* CoreText.framework */; };
		422063DE1FF482A000C81AF1 /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = F03E749E1EC175E4008B4B70 /* libc++.tbd */; };
		422063DF1FF482A000C81AF1 /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = F0B562F31EBC1F710000AB74 /* libresolv.tbd */; };
		422063E01FF482A000C81AF1 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F0B562F51EBC1F7C0000AB74 /* SystemConfiguration.framework */; };
		422063E31FF482A000C81AF1 /* libsqlite3.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = EB080EB21EF8FA5D00EA73A0 /* libsqlite3.tbd */; };
		422063E81FF482A000C81AF1 /* libicucore.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = EB080E961EF8FA5500EA73A0 /* libicucore.tbd */; };
		422063E91FF482A000C81AF1 /* libsqlite3.0.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = F063DEE81ECB0316000A5D4F /* libsqlite3.0.tbd */; };
		422063F21FF482A000C81AF1 /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F03E74A01EC17603008B4B70 /* CoreTelephony.framework */; };
		422063F51FF482A000C81AF1 /* libReact.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 146834041AC3E56700842450 /* libReact.a */; };
		422063F61FF482A000C81AF1 /* libRCTAnimation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 5E9157331DD0AC6500FF2AA8 /* libRCTAnimation.a */; };
		422063F71FF482A000C81AF1 /* libRCTActionSheet.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302AC1ABCB8CE00DB3ED1 /* libRCTActionSheet.a */; };
		422063F81FF482A000C81AF1 /* libRCTGeolocation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302BA1ABCB90400DB3ED1 /* libRCTGeolocation.a */; };
		422063F91FF482A000C81AF1 /* libRCTImage.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302C01ABCB91800DB3ED1 /* libRCTImage.a */; };
		422063FB1FF482A000C81AF1 /* libRCTLinking.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 78C398B91ACF4ADC00677621 /* libRCTLinking.a */; };
		422063FC1FF482A000C81AF1 /* libRCTNetwork.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302DC1ABCB9D200DB3ED1 /* libRCTNetwork.a */; };
		422063FD1FF482A000C81AF1 /* libRCTSettings.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 139105C11AF99BAD00B5F7CC /* libRCTSettings.a */; };
		422063FF1FF482A000C81AF1 /* libRCTText.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 832341B51AAA6A8300B99B32 /* libRCTText.a */; };
		422064001FF482A000C81AF1 /* libRCTVibration.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302E41ABCB9EE00DB3ED1 /* libRCTVibration.a */; };
		422064011FF482A000C81AF1 /* libRCTWebSocket.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 139FDEF41B06529B00C62182 /* libRCTWebSocket.a */; };
		422064061FF482A000C81AF1 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		429783A821C2055E006D1EFE /* libCodePush.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4219E0F8219D9B450080C095 /* libCodePush.a */; };
		42A2BB0C21AE251700F072FA /* libCodePush.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4219E0F8219D9B450080C095 /* libCodePush.a */; };
		42C740211FB2E8BD000A1C6B /* libc++abi.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 42C740201FB2E8BD000A1C6B /* libc++abi.tbd */; };
		5E9157361DD0AC6A00FF2AA8 /* libRCTAnimation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 5E9157331DD0AC6500FF2AA8 /* libRCTAnimation.a */; };
		832341BD1AAA6AB300B99B32 /* libRCTText.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 832341B51AAA6A8300B99B32 /* libRCTText.a */; };
		842B62C32A0A4B66009D78DC /* libstdc++.6.0.9.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 842B62A12A0A4B66009D78DC /* libstdc++.6.0.9.tbd */; };
		842B62C42A0A4B66009D78DC /* libstdc++.6.0.9.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 842B62A12A0A4B66009D78DC /* libstdc++.6.0.9.tbd */; };
		842B62C52A0A4B66009D78DC /* libstdc++.6.0.9.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 842B62A12A0A4B66009D78DC /* libstdc++.6.0.9.tbd */; };
		AE25209D1F301A7A00479CE1 /* libxml2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = AE25209C1F301A7A00479CE1 /* libxml2.tbd */; };
		AE25209F1F301A8900479CE1 /* MobileCoreServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AE25209E1F301A8900479CE1 /* MobileCoreServices.framework */; };
		AE30700F1F3446940069FB92 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AE30700E1F3446940069FB92 /* UIKit.framework */; };
		AE3FC7721FA8775100877C58 /* libRNSVG.a in Frameworks */ = {isa = PBXBuildFile; fileRef = AE4783ED1F4ADCFC00FDBD75 /* libRNSVG.a */; };
		AEBCE5821F44366B00B4DC66 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AEBCE5811F44366B00B4DC66 /* Security.framework */; };
		AEBCE5841F44368A00B4DC66 /* UserNotifications.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AEBCE5831F44368A00B4DC66 /* UserNotifications.framework */; };
		AECAA7091F2F0D580048F2F3 /* CoreText.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AECAA7081F2F0D580048F2F3 /* CoreText.framework */; };
		AECAA70B1F2F0D770048F2F3 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AECAA70A1F2F0D770048F2F3 /* QuartzCore.framework */; };
		AECAA70D1F2F0D850048F2F3 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AECAA70C1F2F0D840048F2F3 /* CoreGraphics.framework */; };
		AECAA70F1F2F0D8F0048F2F3 /* CFNetwork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AECAA70E1F2F0D8F0048F2F3 /* CFNetwork.framework */; };
		AECAA7111F2F0D990048F2F3 /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AECAA7101F2F0D990048F2F3 /* CoreMotion.framework */; };
		EB080E971EF8FA5500EA73A0 /* libicucore.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = EB080E961EF8FA5500EA73A0 /* libicucore.tbd */; };
		EB080EB31EF8FA5D00EA73A0 /* libsqlite3.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = EB080EB21EF8FA5D00EA73A0 /* libsqlite3.tbd */; };
		F03E749F1EC175E4008B4B70 /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = F03E749E1EC175E4008B4B70 /* libc++.tbd */; };
		F03E74A11EC17603008B4B70 /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F03E74A01EC17603008B4B70 /* CoreTelephony.framework */; };
		F03E74A31EC1760E008B4B70 /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = F03E74A21EC1760E008B4B70 /* libz.tbd */; };
		F063DEE91ECB0316000A5D4F /* libsqlite3.0.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = F063DEE81ECB0316000A5D4F /* libsqlite3.0.tbd */; };
		F0B562F41EBC1F710000AB74 /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = F0B562F31EBC1F710000AB74 /* libresolv.tbd */; };
		F0B562F61EBC1F7C0000AB74 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F0B562F51EBC1F7C0000AB74 /* SystemConfiguration.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00C302AB1ABCB8CE00DB3ED1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302A71ABCB8CE00DB3ED1 /* RCTActionSheet.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RCTActionSheet;
		};
		00C302B91ABCB90400DB3ED1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302B51ABCB90400DB3ED1 /* RCTGeolocation.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RCTGeolocation;
		};
		00C302BF1ABCB91800DB3ED1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 58B5115D1A9E6B3D00147676;
			remoteInfo = RCTImage;
		};
		00C302DB1ABCB9D200DB3ED1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 58B511DB1A9E6C8500147676;
			remoteInfo = RCTNetwork;
		};
		00C302E31ABCB9EE00DB3ED1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302DF1ABCB9EE00DB3ED1 /* RCTVibration.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 832C81801AAF6DEF007FA2F7;
			remoteInfo = RCTVibration;
		};
		139105C01AF99BAD00B5F7CC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RCTSettings;
		};
		139FDEF31B06529B00C62182 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3C86DF461ADF2C930047B81A;
			remoteInfo = RCTWebSocket;
		};
		146834031AC3E56700842450 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 83CBBA2E1A601D0E00E9B192;
			remoteInfo = React;
		};
		3DAD3E831DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A283A1D9B042B00D4039D;
			remoteInfo = "RCTImage-tvOS";
		};
		3DAD3E871DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28471D9B043800D4039D;
			remoteInfo = "RCTLinking-tvOS";
		};
		3DAD3E8B1DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28541D9B044C00D4039D;
			remoteInfo = "RCTNetwork-tvOS";
		};
		3DAD3E8F1DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28611D9B046600D4039D;
			remoteInfo = "RCTSettings-tvOS";
		};
		3DAD3E931DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A287B1D9B048500D4039D;
			remoteInfo = "RCTText-tvOS";
		};
		3DAD3E981DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28881D9B049200D4039D;
			remoteInfo = "RCTWebSocket-tvOS";
		};
		3DAD3EA21DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28131D9B038B00D4039D;
			remoteInfo = "React-tvOS";
		};
		3DAD3EA41DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D3C059A1DE3340900C268FA;
			remoteInfo = yoga;
		};
		3DAD3EA61DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D3C06751DE3340C00C268FA;
			remoteInfo = "yoga-tvOS";
		};
		3DAD3EA81DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D3CD9251DE5FBEC00167DC4;
			remoteInfo = cxxreact;
		};
		3DAD3EAA1DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D3CD9321DE5FBEE00167DC4;
			remoteInfo = "cxxreact-tvOS";
		};
		3DAD3EAC1DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D3CD90B1DE5FBD600167DC4;
			remoteInfo = jschelpers;
		};
		3DAD3EAE1DF850E9000B6D8A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D3CD9181DE5FBD800167DC4;
			remoteInfo = "jschelpers-tvOS";
		};
		420260D2212CFE56001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260CE212CFE56001B3A50 /* AFNetworking.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 33F177FB211C42E40092DBA2;
			remoteInfo = AFNetworking;
		};
		420260D8212CFE69001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260D4212CFE69001B3A50 /* AliOSS.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 9BA80CB720F7255A00B9EE52;
			remoteInfo = AliOSS;
		};
		420260DE212CFE76001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260DA212CFE76001B3A50 /* AliPay.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 9B94CA1920F4A5A700F979F9;
			remoteInfo = AliPay;
		};
		420260E4212CFE85001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260E0212CFE85001B3A50 /* AliyunSLS.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 33BBE96F20FC900C00843D0F;
			remoteInfo = AliyunSLS;
		};
		420260EA212CFE96001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260E6212CFE96001B3A50 /* AMap.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 9BD003A820FEE52D0046F557;
			remoteInfo = AMap;
		};
		420260F6212CFED3001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260F2212CFED3001B3A50 /* AppTool.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 9BA80E6420FCA38D00B9EE52;
			remoteInfo = AppTool;
		};
		420260FC212CFEE1001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260F8212CFEE1001B3A50 /* Captcha.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 33BBE94020FC8EE000843D0F;
			remoteInfo = Captcha;
		};
		42026102212CFEEF001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260FE212CFEEF001B3A50 /* ConfigStorage.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 9BD004E321002A7A0046F557;
			remoteInfo = ConfigStorage;
		};
		42026108212CFEFE001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026104212CFEFE001B3A50 /* DeviceInfo.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 9BA80D3C20FC36A700B9EE52;
			remoteInfo = DeviceInfo;
		};
		4202610E212CFF0E001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4202610A212CFF0E001B3A50 /* ImageCropPicker.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 9BA80D8F20FC4E1700B9EE52;
			remoteInfo = ImageCropPicker;
		};
		42026114212CFF1B001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026110212CFF1B001B3A50 /* JPush.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 335D00622110429000599505;
			remoteInfo = JPush;
		};
		4202611A212CFF29001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026116212CFF29001B3A50 /* MBProgressHUD.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3348ABD820F7466B002D688B;
			remoteInfo = MBProgressHUD;
		};
		42026120212CFF5A001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4202611C212CFF5A001B3A50 /* Push.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 33F17666211BD3F10092DBA2;
			remoteInfo = Push;
		};
		42026126212CFF72001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026122212CFF72001B3A50 /* SGQRCode.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 33FAFD8C20F85835009B8000;
			remoteInfo = SGQRCode;
		};
		4202612C212CFF83001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026128212CFF83001B3A50 /* SocialShare.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 33FAFDFE20F878BB009B8000;
			remoteInfo = SocialShare;
		};
		42026132212CFF95001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4202612E212CFF95001B3A50 /* StatusBarUtil.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 33BBE9DC20FC91D900843D0F;
			remoteInfo = StatusBarUtil;
		};
		42026138212CFFA2001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026134212CFFA2001B3A50 /* WeChat.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 33FAFE2B20F879EC009B8000;
			remoteInfo = WeChat;
		};
		420264E8212E8E49001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026134212CFFA2001B3A50 /* WeChat.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33FAFE2A20F879EC009B8000;
			remoteInfo = WeChat;
		};
		420264EA212E8E49001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4202612E212CFF95001B3A50 /* StatusBarUtil.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33BBE9DB20FC91D900843D0F;
			remoteInfo = StatusBarUtil;
		};
		420264EC212E8E49001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026128212CFF83001B3A50 /* SocialShare.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33FAFDFD20F878BB009B8000;
			remoteInfo = SocialShare;
		};
		420264EE212E8E49001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026122212CFF72001B3A50 /* SGQRCode.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33FAFD8B20F85835009B8000;
			remoteInfo = SGQRCode;
		};
		420264F0212E8E49001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4202611C212CFF5A001B3A50 /* Push.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33F17665211BD3F10092DBA2;
			remoteInfo = Push;
		};
		420264F2212E8E49001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026116212CFF29001B3A50 /* MBProgressHUD.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 3348ABD720F7466B002D688B;
			remoteInfo = MBProgressHUD;
		};
		420264F4212E8E49001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026110212CFF1B001B3A50 /* JPush.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 335D00612110429000599505;
			remoteInfo = JPush;
		};
		420264F6212E8E49001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4202610A212CFF0E001B3A50 /* ImageCropPicker.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BA80D8E20FC4E1700B9EE52;
			remoteInfo = ImageCropPicker;
		};
		420264F8212E8E49001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026104212CFEFE001B3A50 /* DeviceInfo.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BA80D3B20FC36A700B9EE52;
			remoteInfo = DeviceInfo;
		};
		420264FA212E8E49001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260FE212CFEEF001B3A50 /* ConfigStorage.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BD004E221002A7A0046F557;
			remoteInfo = ConfigStorage;
		};
		420264FC212E8E49001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260F8212CFEE1001B3A50 /* Captcha.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33BBE93F20FC8EE000843D0F;
			remoteInfo = Captcha;
		};
		420264FE212E8E49001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260F2212CFED3001B3A50 /* AppTool.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BA80E6320FCA38D00B9EE52;
			remoteInfo = AppTool;
		};
		42026500212E8E49001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260E6212CFE96001B3A50 /* AMap.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BD003A720FEE52D0046F557;
			remoteInfo = AMap;
		};
		42026502212E8E49001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260E0212CFE85001B3A50 /* AliyunSLS.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33BBE96E20FC900C00843D0F;
			remoteInfo = AliyunSLS;
		};
		42026504212E8E49001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260DA212CFE76001B3A50 /* AliPay.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9B94CA1820F4A5A700F979F9;
			remoteInfo = AliPay;
		};
		42026506212E8E49001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260D4212CFE69001B3A50 /* AliOSS.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BA80CB620F7255A00B9EE52;
			remoteInfo = AliOSS;
		};
		42026508212E8E49001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260CE212CFE56001B3A50 /* AFNetworking.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33F177FA211C42E40092DBA2;
			remoteInfo = AFNetworking;
		};
		4202650A212E938F001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026134212CFFA2001B3A50 /* WeChat.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33FAFE2A20F879EC009B8000;
			remoteInfo = WeChat;
		};
		4202650C212E938F001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4202612E212CFF95001B3A50 /* StatusBarUtil.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33BBE9DB20FC91D900843D0F;
			remoteInfo = StatusBarUtil;
		};
		4202650E212E938F001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026128212CFF83001B3A50 /* SocialShare.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33FAFDFD20F878BB009B8000;
			remoteInfo = SocialShare;
		};
		42026510212E938F001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026122212CFF72001B3A50 /* SGQRCode.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33FAFD8B20F85835009B8000;
			remoteInfo = SGQRCode;
		};
		42026512212E938F001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4202611C212CFF5A001B3A50 /* Push.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33F17665211BD3F10092DBA2;
			remoteInfo = Push;
		};
		42026514212E938F001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026116212CFF29001B3A50 /* MBProgressHUD.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 3348ABD720F7466B002D688B;
			remoteInfo = MBProgressHUD;
		};
		42026516212E938F001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026110212CFF1B001B3A50 /* JPush.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 335D00612110429000599505;
			remoteInfo = JPush;
		};
		42026518212E938F001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4202610A212CFF0E001B3A50 /* ImageCropPicker.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BA80D8E20FC4E1700B9EE52;
			remoteInfo = ImageCropPicker;
		};
		4202651A212E938F001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026104212CFEFE001B3A50 /* DeviceInfo.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BA80D3B20FC36A700B9EE52;
			remoteInfo = DeviceInfo;
		};
		4202651C212E938F001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260FE212CFEEF001B3A50 /* ConfigStorage.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BD004E221002A7A0046F557;
			remoteInfo = ConfigStorage;
		};
		4202651E212E938F001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260F8212CFEE1001B3A50 /* Captcha.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33BBE93F20FC8EE000843D0F;
			remoteInfo = Captcha;
		};
		42026520212E938F001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260F2212CFED3001B3A50 /* AppTool.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BA80E6320FCA38D00B9EE52;
			remoteInfo = AppTool;
		};
		42026522212E938F001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260E6212CFE96001B3A50 /* AMap.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BD003A720FEE52D0046F557;
			remoteInfo = AMap;
		};
		42026524212E938F001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260E0212CFE85001B3A50 /* AliyunSLS.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33BBE96E20FC900C00843D0F;
			remoteInfo = AliyunSLS;
		};
		42026526212E938F001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260DA212CFE76001B3A50 /* AliPay.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9B94CA1820F4A5A700F979F9;
			remoteInfo = AliPay;
		};
		42026528212E938F001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260D4212CFE69001B3A50 /* AliOSS.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BA80CB620F7255A00B9EE52;
			remoteInfo = AliOSS;
		};
		4202652A212E938F001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260CE212CFE56001B3A50 /* AFNetworking.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33F177FA211C42E40092DBA2;
			remoteInfo = AFNetworking;
		};
		4202652C212E93AD001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026134212CFFA2001B3A50 /* WeChat.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33FAFE2A20F879EC009B8000;
			remoteInfo = WeChat;
		};
		4202652E212E93AD001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4202612E212CFF95001B3A50 /* StatusBarUtil.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33BBE9DB20FC91D900843D0F;
			remoteInfo = StatusBarUtil;
		};
		42026530212E93AD001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026128212CFF83001B3A50 /* SocialShare.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33FAFDFD20F878BB009B8000;
			remoteInfo = SocialShare;
		};
		42026532212E93AD001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026122212CFF72001B3A50 /* SGQRCode.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33FAFD8B20F85835009B8000;
			remoteInfo = SGQRCode;
		};
		42026534212E93AD001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4202611C212CFF5A001B3A50 /* Push.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33F17665211BD3F10092DBA2;
			remoteInfo = Push;
		};
		42026536212E93AD001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026116212CFF29001B3A50 /* MBProgressHUD.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 3348ABD720F7466B002D688B;
			remoteInfo = MBProgressHUD;
		};
		42026538212E93AD001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026110212CFF1B001B3A50 /* JPush.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 335D00612110429000599505;
			remoteInfo = JPush;
		};
		4202653A212E93AD001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4202610A212CFF0E001B3A50 /* ImageCropPicker.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BA80D8E20FC4E1700B9EE52;
			remoteInfo = ImageCropPicker;
		};
		4202653C212E93AD001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 42026104212CFEFE001B3A50 /* DeviceInfo.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BA80D3B20FC36A700B9EE52;
			remoteInfo = DeviceInfo;
		};
		4202653E212E93AD001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260FE212CFEEF001B3A50 /* ConfigStorage.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BD004E221002A7A0046F557;
			remoteInfo = ConfigStorage;
		};
		42026540212E93AD001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260F8212CFEE1001B3A50 /* Captcha.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33BBE93F20FC8EE000843D0F;
			remoteInfo = Captcha;
		};
		42026542212E93AD001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260F2212CFED3001B3A50 /* AppTool.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BA80E6320FCA38D00B9EE52;
			remoteInfo = AppTool;
		};
		42026544212E93AD001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260E6212CFE96001B3A50 /* AMap.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BD003A720FEE52D0046F557;
			remoteInfo = AMap;
		};
		42026546212E93AD001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260E0212CFE85001B3A50 /* AliyunSLS.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33BBE96E20FC900C00843D0F;
			remoteInfo = AliyunSLS;
		};
		42026548212E93AD001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260DA212CFE76001B3A50 /* AliPay.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9B94CA1820F4A5A700F979F9;
			remoteInfo = AliPay;
		};
		4202654A212E93AD001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260D4212CFE69001B3A50 /* AliOSS.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 9BA80CB620F7255A00B9EE52;
			remoteInfo = AliOSS;
		};
		4202654C212E93AD001B3A50 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 420260CE212CFE56001B3A50 /* AFNetworking.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 33F177FA211C42E40092DBA2;
			remoteInfo = AFNetworking;
		};
		4219E0F7219D9B450080C095 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4219E0F2219D9B450080C095 /* CodePush.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = CodePush;
		};
		4219E0F9219D9B450080C095 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4219E0F2219D9B450080C095 /* CodePush.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = F886647B1F4ADB500036D01B;
			remoteInfo = "CodePush-tvOS";
		};
		427C2DD01FDA9252005D7C6E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = CA9223B2EB1D41968C942CCC /* RNSVG.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 427C2DA81FDA9212005D7C6E;
			remoteInfo = "RNSVG-tvOS";
		};
		5E9157321DD0AC6500FF2AA8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 5E91572D1DD0AC6500FF2AA8 /* RCTAnimation.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RCTAnimation;
		};
		5E9157341DD0AC6500FF2AA8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 5E91572D1DD0AC6500FF2AA8 /* RCTAnimation.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 2D2A28201D9B03D100D4039D;
			remoteInfo = "RCTAnimation-tvOS";
		};
		78C398B81ACF4ADC00677621 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RCTLinking;
		};
		832341B41AAA6A8300B99B32 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 58B5119B1A9E6C1200147676;
			remoteInfo = RCTText;
		};
		AE4783EC1F4ADCFC00FDBD75 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = CA9223B2EB1D41968C942CCC /* RNSVG.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 0CF68AC11AF0540F00FF9E5C;
			remoteInfo = RNSVG;
		};
		EBDBC2E11FDA87970004CA54 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 139D7ECE1E25DB7D00323FB7;
			remoteInfo = "third-party";
		};
		EBDBC2E31FDA87970004CA54 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D383D3C1EBD27B6005632C8;
			remoteInfo = "third-party-tvOS";
		};
		EBDBC2E51FDA87970004CA54 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 139D7E881E25C6D100323FB7;
			remoteInfo = "double-conversion";
		};
		EBDBC2E71FDA87970004CA54 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3D383D621EBD27B9005632C8;
			remoteInfo = "double-conversion-tvOS";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		4206F1D5205E0CDB001C21E4 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				42026201212D070A001B3A50 /* RSKImageCropper.framework in Embed Frameworks */,
				42026200212D0704001B3A50 /* QBImagePicker.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		422064091FF482A000C81AF1 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				420261FF212D0573001B3A50 /* RSKImageCropper.framework in Embed Frameworks */,
				420261FE212D056A001B3A50 /* QBImagePicker.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		AEF539771FA6C85C005985E3 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				42026175212D0248001B3A50 /* RSKImageCropper.framework in Embed Frameworks */,
				42026174212D0240001B3A50 /* QBImagePicker.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		008F07F21AC5B25A0029DE68 /* main.jsbundle */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = main.jsbundle; sourceTree = "<group>"; };
		00C302A71ABCB8CE00DB3ED1 /* RCTActionSheet.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTActionSheet.xcodeproj; path = "../node_modules/react-native/Libraries/ActionSheetIOS/RCTActionSheet.xcodeproj"; sourceTree = "<group>"; };
		00C302B51ABCB90400DB3ED1 /* RCTGeolocation.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTGeolocation.xcodeproj; path = "../node_modules/react-native/Libraries/Geolocation/RCTGeolocation.xcodeproj"; sourceTree = "<group>"; };
		00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTImage.xcodeproj; path = "../node_modules/react-native/Libraries/Image/RCTImage.xcodeproj"; sourceTree = "<group>"; };
		00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTNetwork.xcodeproj; path = "../node_modules/react-native/Libraries/Network/RCTNetwork.xcodeproj"; sourceTree = "<group>"; };
		00C302DF1ABCB9EE00DB3ED1 /* RCTVibration.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTVibration.xcodeproj; path = "../node_modules/react-native/Libraries/Vibration/RCTVibration.xcodeproj"; sourceTree = "<group>"; };
		139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTSettings.xcodeproj; path = "../node_modules/react-native/Libraries/Settings/RCTSettings.xcodeproj"; sourceTree = "<group>"; };
		139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTWebSocket.xcodeproj; path = "../node_modules/react-native/Libraries/WebSocket/RCTWebSocket.xcodeproj"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* yunxi.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = yunxi.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = BaseRN/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AppDelegate.m; path = BaseRN/AppDelegate.m; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = BaseRN/Images.xcassets; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = BaseRN/main.m; sourceTree = "<group>"; };
		146833FF1AC3E56700842450 /* React.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = React.xcodeproj; path = "../node_modules/react-native/React/React.xcodeproj"; sourceTree = "<group>"; };
		420260CE212CFE56001B3A50 /* AFNetworking.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = AFNetworking.xcodeproj; path = "../../rn-yunxi/ios/AFNetworking/AFNetworking.xcodeproj"; sourceTree = "<group>"; };
		420260D4212CFE69001B3A50 /* AliOSS.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = AliOSS.xcodeproj; path = "../../rn-yunxi/ios/AliOSS/AliOSS.xcodeproj"; sourceTree = "<group>"; };
		420260DA212CFE76001B3A50 /* AliPay.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = AliPay.xcodeproj; path = "../../rn-yunxi/ios/AliPay/AliPay.xcodeproj"; sourceTree = "<group>"; };
		420260E0212CFE85001B3A50 /* AliyunSLS.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = AliyunSLS.xcodeproj; path = "../../rn-yunxi/ios/AliyunSLS/AliyunSLS.xcodeproj"; sourceTree = "<group>"; };
		420260E6212CFE96001B3A50 /* AMap.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = AMap.xcodeproj; path = "../../rn-yunxi/ios/AMap/AMap.xcodeproj"; sourceTree = "<group>"; };
		420260F2212CFED3001B3A50 /* AppTool.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = AppTool.xcodeproj; path = "../../rn-yunxi/ios/AppTool/AppTool.xcodeproj"; sourceTree = "<group>"; };
		420260F8212CFEE1001B3A50 /* Captcha.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = Captcha.xcodeproj; path = "../../rn-yunxi/ios/Captcha/Captcha.xcodeproj"; sourceTree = "<group>"; };
		420260FE212CFEEF001B3A50 /* ConfigStorage.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = ConfigStorage.xcodeproj; path = "../../rn-yunxi/ios/ConfigStorage/ConfigStorage.xcodeproj"; sourceTree = "<group>"; };
		42026104212CFEFE001B3A50 /* DeviceInfo.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = DeviceInfo.xcodeproj; path = "../../rn-yunxi/ios/DeviceInfo/DeviceInfo.xcodeproj"; sourceTree = "<group>"; };
		4202610A212CFF0E001B3A50 /* ImageCropPicker.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = ImageCropPicker.xcodeproj; path = "../../rn-yunxi/ios/ImageCropPicker/ImageCropPicker.xcodeproj"; sourceTree = "<group>"; };
		42026110212CFF1B001B3A50 /* JPush.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = JPush.xcodeproj; path = "../../rn-yunxi/ios/JPush/JPush.xcodeproj"; sourceTree = "<group>"; };
		42026116212CFF29001B3A50 /* MBProgressHUD.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = MBProgressHUD.xcodeproj; path = "../../rn-yunxi/ios/MBProgressHUD/MBProgressHUD.xcodeproj"; sourceTree = "<group>"; };
		4202611C212CFF5A001B3A50 /* Push.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = Push.xcodeproj; path = "../../rn-yunxi/ios/Push/Push.xcodeproj"; sourceTree = "<group>"; };
		42026122212CFF72001B3A50 /* SGQRCode.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = SGQRCode.xcodeproj; path = "../../rn-yunxi/ios/SGQRCode/SGQRCode.xcodeproj"; sourceTree = "<group>"; };
		42026128212CFF83001B3A50 /* SocialShare.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = SocialShare.xcodeproj; path = "../../rn-yunxi/ios/SocialShare/SocialShare.xcodeproj"; sourceTree = "<group>"; };
		4202612E212CFF95001B3A50 /* StatusBarUtil.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = StatusBarUtil.xcodeproj; path = "../../rn-yunxi/ios/StatusBarUtil/StatusBarUtil.xcodeproj"; sourceTree = "<group>"; };
		42026134212CFFA2001B3A50 /* WeChat.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = WeChat.xcodeproj; path = "../../rn-yunxi/ios/WeChat/WeChat.xcodeproj"; sourceTree = "<group>"; };
		42026160212D01C2001B3A50 /* UShareUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UShareUI.framework; path = "../../rn-yunxi/ios/SocialShare/SocialShare/UMSocial/UMSocialSDK/UShareUI.framework"; sourceTree = "<group>"; };
		42026162212D01D6001B3A50 /* AMapLocationKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AMapLocationKit.framework; path = "../../rn-yunxi/ios/AMap/lib/AMapLocationKit.framework"; sourceTree = "<group>"; };
		42026163212D01D6001B3A50 /* AMapFoundationKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AMapFoundationKit.framework; path = "../../rn-yunxi/ios/AMap/lib/AMapFoundationKit.framework"; sourceTree = "<group>"; };
		42026164212D01D6001B3A50 /* MAMapKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MAMapKit.framework; path = "../../rn-yunxi/ios/AMap/lib/MAMapKit.framework"; sourceTree = "<group>"; };
		42026165212D01D6001B3A50 /* AMapSearchKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AMapSearchKit.framework; path = "../../rn-yunxi/ios/AMap/lib/AMapSearchKit.framework"; sourceTree = "<group>"; };
		4202616A212D01E8001B3A50 /* AlipaySDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AlipaySDK.framework; path = "../../rn-yunxi/ios/AliPay/lib/AlipaySDK.framework"; sourceTree = "<group>"; };
		4202616C212D01EE001B3A50 /* GLKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GLKit.framework; path = System/Library/Frameworks/GLKit.framework; sourceTree = SDKROOT; };
		42026172212D022E001B3A50 /* RSKImageCropper.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = RSKImageCropper.framework; path = "../../rn-yunxi/ios/ImageCropPicker/lib/RSKImageCropper.framework"; sourceTree = "<group>"; };
		42026173212D022E001B3A50 /* QBImagePicker.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QBImagePicker.framework; path = "../../rn-yunxi/ios/ImageCropPicker/lib/QBImagePicker.framework"; sourceTree = "<group>"; };
		42026445212E8BC3001B3A50 /* AlipaySDK.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = AlipaySDK.bundle; sourceTree = "<group>"; };
		42026449212E8BD7001B3A50 /* SGQRCode.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = SGQRCode.bundle; sourceTree = "<group>"; };
		4202644D212E8BE9001B3A50 /* UMSocialSDKResources.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = UMSocialSDKResources.bundle; sourceTree = "<group>"; };
		42026451212E8C01001B3A50 /* UMSocialSDKPromptResources.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = UMSocialSDKPromptResources.bundle; sourceTree = "<group>"; };
		42026456212E8CE2001B3A50 /* RCTAPPayAssistEx.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RCTAPPayAssistEx.h; sourceTree = "<group>"; };
		42026457212E8CE3001B3A50 /* RCTAPPayAssistEx.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RCTAPPayAssistEx.m; sourceTree = "<group>"; };
		42026459212E8CE5001B3A50 /* TrustDefenderMobile.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = TrustDefenderMobile.framework; sourceTree = "<group>"; };
		4202645A212E8CE5001B3A50 /* APMPPassGuardCtrlBundle.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = APMPPassGuardCtrlBundle.bundle; sourceTree = "<group>"; };
		4202645C212E8CE5001B3A50 /* PinyinHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PinyinHelper.h; sourceTree = "<group>"; };
		4202645D212E8CE5001B3A50 /* HanyuPinyinOutputFormat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HanyuPinyinOutputFormat.h; sourceTree = "<group>"; };
		4202645E212E8CE5001B3A50 /* ChineseToPinyinResource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ChineseToPinyinResource.h; sourceTree = "<group>"; };
		4202645F212E8CE5001B3A50 /* APay.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = APay.h; sourceTree = "<group>"; };
		42026460212E8CE5001B3A50 /* pinyin.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pinyin.h; sourceTree = "<group>"; };
		42026461212E8CE5001B3A50 /* PinyinFormatter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PinyinFormatter.h; sourceTree = "<group>"; };
		42026462212E8CE5001B3A50 /* NSString+PinYin4Cocoa.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+PinYin4Cocoa.h"; sourceTree = "<group>"; };
		42026463212E8CE5001B3A50 /* PinYin4Objc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PinYin4Objc.h; sourceTree = "<group>"; };
		42026464212E8CE5001B3A50 /* libAPayLib.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libAPayLib.a; sourceTree = "<group>"; };
		42026465212E8CE5001B3A50 /* APayRes.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = APayRes.bundle; sourceTree = "<group>"; };
		42026469212E8CE5001B3A50 /* VerificationPage.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = VerificationPage.storyboard; sourceTree = "<group>"; };
		4202646A212E8CE5001B3A50 /* RegistrationPage.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = RegistrationPage.storyboard; sourceTree = "<group>"; };
		4202646B212E8CE5001B3A50 /* StringList.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = StringList.plist; sourceTree = "<group>"; };
		4202646E212E8CE5001B3A50 /* 张嘴.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; path = "张嘴.gif"; sourceTree = "<group>"; };
		4202646F212E8CE5001B3A50 /* 扭头-右.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; path = "扭头-右.gif"; sourceTree = "<group>"; };
		42026470212E8CE5001B3A50 /* 低头.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; path = "低头.gif"; sourceTree = "<group>"; };
		42026471212E8CE5001B3A50 /* yitu_face_action_normal.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; path = yitu_face_action_normal.gif; sourceTree = "<group>"; };
		42026472212E8CE5001B3A50 /* 抬头.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; path = "抬头.gif"; sourceTree = "<group>"; };
		42026473212E8CE5001B3A50 /* 闭眼.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; path = "闭眼.gif"; sourceTree = "<group>"; };
		42026474212E8CE5001B3A50 /* 扭头-左.gif */ = {isa = PBXFileReference; lastKnownFileType = image.gif; path = "扭头-左.gif"; sourceTree = "<group>"; };
		42026475212E8CE5001B3A50 /* yitu_face_model.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = yitu_face_model.bundle; sourceTree = "<group>"; };
		42026476212E8CE5001B3A50 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		42026478212E8CE5001B3A50 /* yitu_eye_close_audio.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = yitu_eye_close_audio.mp3; sourceTree = "<group>"; };
		42026479212E8CE5001B3A50 /* yitu_head_right_audio.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = yitu_head_right_audio.mp3; sourceTree = "<group>"; };
		4202647A212E8CE5001B3A50 /* yitu_verification_fail_audio.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = yitu_verification_fail_audio.mp3; sourceTree = "<group>"; };
		4202647B212E8CE5001B3A50 /* yitu_head_up_audio.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = yitu_head_up_audio.mp3; sourceTree = "<group>"; };
		4202647C212E8CE5001B3A50 /* yitu_mouth_open_audio.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = yitu_mouth_open_audio.mp3; sourceTree = "<group>"; };
		4202647D212E8CE5001B3A50 /* yitu_next_action_audio.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = yitu_next_action_audio.mp3; sourceTree = "<group>"; };
		4202647E212E8CE5001B3A50 /* yitu_verification_pass_audio.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = yitu_verification_pass_audio.mp3; sourceTree = "<group>"; };
		4202647F212E8CE5001B3A50 /* yitu_time_out_audio.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = yitu_time_out_audio.mp3; sourceTree = "<group>"; };
		42026480212E8CE5001B3A50 /* yitu_get_ready_audio.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = yitu_get_ready_audio.mp3; sourceTree = "<group>"; };
		42026481212E8CE5001B3A50 /* yitu_head_left_audio.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = yitu_head_left_audio.mp3; sourceTree = "<group>"; };
		42026482212E8CE5001B3A50 /* yitu_head_down_audio.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = yitu_head_down_audio.mp3; sourceTree = "<group>"; };
		42026483212E8CE5001B3A50 /* yitu_not_user_audio.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = yitu_not_user_audio.mp3; sourceTree = "<group>"; };
		42026484212E8CE5001B3A50 /* staging.public.der */ = {isa = PBXFileReference; lastKnownFileType = file; path = staging.public.der; sourceTree = "<group>"; };
		42026486212E8CE5001B3A50 /* MBProgressHUD.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = MBProgressHUD.bundle; sourceTree = "<group>"; };
		42026487212E8CE5001B3A50 /* Image.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = Image.bundle; sourceTree = "<group>"; };
		4206F1DC205E0CDB001C21E4 /* pro.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = pro.app; sourceTree = BUILT_PRODUCTS_DIR; };
		4219E0F2219D9B450080C095 /* CodePush.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = CodePush.xcodeproj; path = "../node_modules/react-native-code-push/ios/CodePush.xcodeproj"; sourceTree = "<group>"; };
		421B082D212FFF810041D0F4 /* AMap.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = AMap.bundle; sourceTree = "<group>"; };
		422064101FF482A000C81AF1 /* qa.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = qa.app; sourceTree = BUILT_PRODUCTS_DIR; };
		4220641F1FF482CD00C81AF1 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		422064201FF482CD00C81AF1 /* PrefixHeader.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PrefixHeader.pch; sourceTree = "<group>"; };
		422064221FF482CD00C81AF1 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		422064231FF482CD00C81AF1 /* PrefixHeader.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PrefixHeader.pch; sourceTree = "<group>"; };
		42C740201FB2E8BD000A1C6B /* libc++abi.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++abi.tbd"; path = "usr/lib/libc++abi.tbd"; sourceTree = SDKROOT; };
		42F2D62D2068839D0042D3B4 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		42F2D62E2068839D0042D3B4 /* PrefixHeader.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PrefixHeader.pch; sourceTree = "<group>"; };
		5E91572D1DD0AC6500FF2AA8 /* RCTAnimation.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTAnimation.xcodeproj; path = "../node_modules/react-native/Libraries/NativeAnimation/RCTAnimation.xcodeproj"; sourceTree = "<group>"; };
		78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTLinking.xcodeproj; path = "../node_modules/react-native/Libraries/LinkingIOS/RCTLinking.xcodeproj"; sourceTree = "<group>"; };
		832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTText.xcodeproj; path = "../node_modules/react-native/Libraries/Text/RCTText.xcodeproj"; sourceTree = "<group>"; };
		842B62A12A0A4B66009D78DC /* libstdc++.6.0.9.tbd */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = "sourcecode.text-based-dylib-definition"; path = "libstdc++.6.0.9.tbd"; sourceTree = "<group>"; };
		93BF179620A43BB40027A378 /* yunxi.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = yunxi.entitlements; sourceTree = "<group>"; };
		93BF179720A440390027A378 /* qa.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = qa.entitlements; sourceTree = "<group>"; };
		93BF179820A440500027A378 /* pro.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = pro.entitlements; sourceTree = "<group>"; };
		AE25209C1F301A7A00479CE1 /* libxml2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libxml2.tbd; path = usr/lib/libxml2.tbd; sourceTree = SDKROOT; };
		AE25209E1F301A8900479CE1 /* MobileCoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MobileCoreServices.framework; path = System/Library/Frameworks/MobileCoreServices.framework; sourceTree = SDKROOT; };
		AE30700E1F3446940069FB92 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		AEB36B4F1F3B3499000D946F /* BaseRN.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = BaseRN.entitlements; path = BaseRN/BaseRN.entitlements; sourceTree = "<group>"; };
		AEBCE5811F44366B00B4DC66 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		AEBCE5831F44368A00B4DC66 /* UserNotifications.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotifications.framework; path = System/Library/Frameworks/UserNotifications.framework; sourceTree = SDKROOT; };
		AECAA7081F2F0D580048F2F3 /* CoreText.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreText.framework; path = System/Library/Frameworks/CoreText.framework; sourceTree = SDKROOT; };
		AECAA70A1F2F0D770048F2F3 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		AECAA70C1F2F0D840048F2F3 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		AECAA70E1F2F0D8F0048F2F3 /* CFNetwork.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CFNetwork.framework; path = System/Library/Frameworks/CFNetwork.framework; sourceTree = SDKROOT; };
		AECAA7101F2F0D990048F2F3 /* CoreMotion.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMotion.framework; path = System/Library/Frameworks/CoreMotion.framework; sourceTree = SDKROOT; };
		CA9223B2EB1D41968C942CCC /* RNSVG.xcodeproj */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = "wrapper.pb-project"; name = RNSVG.xcodeproj; path = "../node_modules/react-native-svg/ios/RNSVG.xcodeproj"; sourceTree = "<group>"; };
		EB080E961EF8FA5500EA73A0 /* libicucore.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libicucore.tbd; path = usr/lib/libicucore.tbd; sourceTree = SDKROOT; };
		EB080EB21EF8FA5D00EA73A0 /* libsqlite3.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.tbd; path = usr/lib/libsqlite3.tbd; sourceTree = SDKROOT; };
		EE7912785918448BB80EBE57 /* libRNSVG.a */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = archive.ar; path = libRNSVG.a; sourceTree = "<group>"; };
		F03E749E1EC175E4008B4B70 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		F03E74A01EC17603008B4B70 /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		F03E74A21EC1760E008B4B70 /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		F063DEE81ECB0316000A5D4F /* libsqlite3.0.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.0.tbd; path = usr/lib/libsqlite3.0.tbd; sourceTree = SDKROOT; };
		F06CA33E1EC1AC040089BA60 /* MapKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MapKit.framework; path = System/Library/Frameworks/MapKit.framework; sourceTree = SDKROOT; };
		F0B562F31EBC1F710000AB74 /* libresolv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.tbd; path = usr/lib/libresolv.tbd; sourceTree = SDKROOT; };
		F0B562F51EBC1F7C0000AB74 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4219E0FB219D9B4F0080C095 /* libCodePush.a in Frameworks */,
				4202616D212D01EE001B3A50 /* GLKit.framework in Frameworks */,
				4202616B212D01E8001B3A50 /* AlipaySDK.framework in Frameworks */,
				42026166212D01D6001B3A50 /* AMapLocationKit.framework in Frameworks */,
				42026167212D01D6001B3A50 /* AMapFoundationKit.framework in Frameworks */,
				842B62C32A0A4B66009D78DC /* libstdc++.6.0.9.tbd in Frameworks */,
				42026491212E8CE5001B3A50 /* libAPayLib.a in Frameworks */,
				42026168212D01D6001B3A50 /* MAMapKit.framework in Frameworks */,
				42026169212D01D6001B3A50 /* AMapSearchKit.framework in Frameworks */,
				42026161212D01C2001B3A50 /* UShareUI.framework in Frameworks */,
				4202614E212D01AB001B3A50 /* libAFNetworking.a in Frameworks */,
				4202614F212D01AB001B3A50 /* libAliOSS.a in Frameworks */,
				42026150212D01AB001B3A50 /* libAliPay.a in Frameworks */,
				4202648B212E8CE5001B3A50 /* TrustDefenderMobile.framework in Frameworks */,
				42026151212D01AB001B3A50 /* libAliyunSLS.a in Frameworks */,
				42026152212D01AB001B3A50 /* libAMap.a in Frameworks */,
				42026154212D01AB001B3A50 /* libAppTool.a in Frameworks */,
				42026155212D01AB001B3A50 /* libCaptcha.a in Frameworks */,
				42026156212D01AB001B3A50 /* libConfigStorage.a in Frameworks */,
				42026157212D01AB001B3A50 /* libDeviceInfo.a in Frameworks */,
				42026158212D01AB001B3A50 /* libImageCropPicker.a in Frameworks */,
				42026159212D01AB001B3A50 /* libJPush.a in Frameworks */,
				4202615A212D01AB001B3A50 /* libMBProgressHUD.a in Frameworks */,
				4202615B212D01AB001B3A50 /* libPush.a in Frameworks */,
				4202615C212D01AB001B3A50 /* libSGQRCode.a in Frameworks */,
				4202615D212D01AB001B3A50 /* libSocialShare.a in Frameworks */,
				4202615E212D01AB001B3A50 /* libStatusBarUtil.a in Frameworks */,
				4202615F212D01AB001B3A50 /* libWeChat.a in Frameworks */,
				F03E74A31EC1760E008B4B70 /* libz.tbd in Frameworks */,
				42C740211FB2E8BD000A1C6B /* libc++abi.tbd in Frameworks */,
				AE3FC7721FA8775100877C58 /* libRNSVG.a in Frameworks */,
				AEBCE5841F44368A00B4DC66 /* UserNotifications.framework in Frameworks */,
				AEBCE5821F44366B00B4DC66 /* Security.framework in Frameworks */,
				AE30700F1F3446940069FB92 /* UIKit.framework in Frameworks */,
				AE25209F1F301A8900479CE1 /* MobileCoreServices.framework in Frameworks */,
				AE25209D1F301A7A00479CE1 /* libxml2.tbd in Frameworks */,
				AECAA7111F2F0D990048F2F3 /* CoreMotion.framework in Frameworks */,
				AECAA70F1F2F0D8F0048F2F3 /* CFNetwork.framework in Frameworks */,
				AECAA70D1F2F0D850048F2F3 /* CoreGraphics.framework in Frameworks */,
				AECAA70B1F2F0D770048F2F3 /* QuartzCore.framework in Frameworks */,
				AECAA7091F2F0D580048F2F3 /* CoreText.framework in Frameworks */,
				F03E749F1EC175E4008B4B70 /* libc++.tbd in Frameworks */,
				F0B562F41EBC1F710000AB74 /* libresolv.tbd in Frameworks */,
				F0B562F61EBC1F7C0000AB74 /* SystemConfiguration.framework in Frameworks */,
				EB080EB31EF8FA5D00EA73A0 /* libsqlite3.tbd in Frameworks */,
				EB080E971EF8FA5500EA73A0 /* libicucore.tbd in Frameworks */,
				F063DEE91ECB0316000A5D4F /* libsqlite3.0.tbd in Frameworks */,
				F03E74A11EC17603008B4B70 /* CoreTelephony.framework in Frameworks */,
				146834051AC3E58100842450 /* libReact.a in Frameworks */,
				5E9157361DD0AC6A00FF2AA8 /* libRCTAnimation.a in Frameworks */,
				00C302E51ABCBA2D00DB3ED1 /* libRCTActionSheet.a in Frameworks */,
				00C302E71ABCBA2D00DB3ED1 /* libRCTGeolocation.a in Frameworks */,
				00C302E81ABCBA2D00DB3ED1 /* libRCTImage.a in Frameworks */,
				133E29F31AD74F7200F7D852 /* libRCTLinking.a in Frameworks */,
				00C302E91ABCBA2D00DB3ED1 /* libRCTNetwork.a in Frameworks */,
				139105C61AF99C1200B5F7CC /* libRCTSettings.a in Frameworks */,
				832341BD1AAA6AB300B99B32 /* libRCTText.a in Frameworks */,
				00C302EA1ABCBA2D00DB3ED1 /* libRCTVibration.a in Frameworks */,
				139FDEF61B0652A700C62182 /* libRCTWebSocket.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4206F190205E0CDB001C21E4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				429783A821C2055E006D1EFE /* libCodePush.a in Frameworks */,
				4202621A212D0780001B3A50 /* GLKit.framework in Frameworks */,
				42026219212D0765001B3A50 /* AlipaySDK.framework in Frameworks */,
				42026215212D0756001B3A50 /* AMapFoundationKit.framework in Frameworks */,
				42026216212D0756001B3A50 /* AMapLocationKit.framework in Frameworks */,
				42026493212E8CE5001B3A50 /* libAPayLib.a in Frameworks */,
				42026217212D0756001B3A50 /* AMapSearchKit.framework in Frameworks */,
				42026218212D0756001B3A50 /* MAMapKit.framework in Frameworks */,
				42026214212D0744001B3A50 /* UShareUI.framework in Frameworks */,
				42026202212D072F001B3A50 /* libAFNetworking.a in Frameworks */,
				42026203212D072F001B3A50 /* libAliOSS.a in Frameworks */,
				42026204212D072F001B3A50 /* libAliPay.a in Frameworks */,
				4202648D212E8CE5001B3A50 /* TrustDefenderMobile.framework in Frameworks */,
				42026205212D072F001B3A50 /* libAliyunSLS.a in Frameworks */,
				42026206212D072F001B3A50 /* libAMap.a in Frameworks */,
				42026208212D072F001B3A50 /* libAppTool.a in Frameworks */,
				42026209212D072F001B3A50 /* libCaptcha.a in Frameworks */,
				4202620A212D072F001B3A50 /* libConfigStorage.a in Frameworks */,
				4202620B212D072F001B3A50 /* libDeviceInfo.a in Frameworks */,
				4202620C212D072F001B3A50 /* libImageCropPicker.a in Frameworks */,
				4202620D212D072F001B3A50 /* libJPush.a in Frameworks */,
				4202620E212D072F001B3A50 /* libMBProgressHUD.a in Frameworks */,
				4202620F212D072F001B3A50 /* libPush.a in Frameworks */,
				42026210212D072F001B3A50 /* libSGQRCode.a in Frameworks */,
				42026211212D072F001B3A50 /* libSocialShare.a in Frameworks */,
				42026212212D072F001B3A50 /* libStatusBarUtil.a in Frameworks */,
				42026213212D072F001B3A50 /* libWeChat.a in Frameworks */,
				4206F191205E0CDB001C21E4 /* libz.tbd in Frameworks */,
				4206F193205E0CDB001C21E4 /* libc++abi.tbd in Frameworks */,
				4206F198205E0CDB001C21E4 /* libRNSVG.a in Frameworks */,
				4206F199205E0CDB001C21E4 /* UserNotifications.framework in Frameworks */,
				4206F19B205E0CDB001C21E4 /* Security.framework in Frameworks */,
				4206F19C205E0CDB001C21E4 /* UIKit.framework in Frameworks */,
				4206F19D205E0CDB001C21E4 /* MobileCoreServices.framework in Frameworks */,
				4206F19E205E0CDB001C21E4 /* libxml2.tbd in Frameworks */,
				4206F19F205E0CDB001C21E4 /* CoreMotion.framework in Frameworks */,
				4206F1A0205E0CDB001C21E4 /* CFNetwork.framework in Frameworks */,
				4206F1A4205E0CDB001C21E4 /* CoreGraphics.framework in Frameworks */,
				4206F1A5205E0CDB001C21E4 /* QuartzCore.framework in Frameworks */,
				4206F1A6205E0CDB001C21E4 /* CoreText.framework in Frameworks */,
				4206F1A8205E0CDB001C21E4 /* libc++.tbd in Frameworks */,
				4206F1A9205E0CDB001C21E4 /* libresolv.tbd in Frameworks */,
				4206F1AA205E0CDB001C21E4 /* SystemConfiguration.framework in Frameworks */,
				4206F1AD205E0CDB001C21E4 /* libsqlite3.tbd in Frameworks */,
				4206F1B2205E0CDB001C21E4 /* libicucore.tbd in Frameworks */,
				4206F1B3205E0CDB001C21E4 /* libsqlite3.0.tbd in Frameworks */,
				4206F1BC205E0CDB001C21E4 /* CoreTelephony.framework in Frameworks */,
				4206F1BF205E0CDB001C21E4 /* libReact.a in Frameworks */,
				4206F1C0205E0CDB001C21E4 /* libRCTAnimation.a in Frameworks */,
				4206F1C1205E0CDB001C21E4 /* libRCTActionSheet.a in Frameworks */,
				4206F1C2205E0CDB001C21E4 /* libRCTGeolocation.a in Frameworks */,
				4206F1C3205E0CDB001C21E4 /* libRCTImage.a in Frameworks */,
				4206F1C5205E0CDB001C21E4 /* libRCTLinking.a in Frameworks */,
				4206F1C6205E0CDB001C21E4 /* libRCTNetwork.a in Frameworks */,
				842B62C52A0A4B66009D78DC /* libstdc++.6.0.9.tbd in Frameworks */,
				4206F1C7205E0CDB001C21E4 /* libRCTSettings.a in Frameworks */,
				4206F1C9205E0CDB001C21E4 /* libRCTText.a in Frameworks */,
				4206F1CA205E0CDB001C21E4 /* libRCTVibration.a in Frameworks */,
				4206F1CB205E0CDB001C21E4 /* libRCTWebSocket.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		422063C61FF482A000C81AF1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				42A2BB0C21AE251700F072FA /* libCodePush.a in Frameworks */,
				420261FD212D054F001B3A50 /* GLKit.framework in Frameworks */,
				420261FC212D0546001B3A50 /* AlipaySDK.framework in Frameworks */,
				420261F8212D0539001B3A50 /* AMapFoundationKit.framework in Frameworks */,
				420261F9212D0539001B3A50 /* AMapLocationKit.framework in Frameworks */,
				42026492212E8CE5001B3A50 /* libAPayLib.a in Frameworks */,
				420261FA212D0539001B3A50 /* AMapSearchKit.framework in Frameworks */,
				420261FB212D0539001B3A50 /* MAMapKit.framework in Frameworks */,
				420261F7212D0528001B3A50 /* UShareUI.framework in Frameworks */,
				420261E5212D0514001B3A50 /* libAFNetworking.a in Frameworks */,
				420261E6212D0514001B3A50 /* libAliOSS.a in Frameworks */,
				420261E7212D0514001B3A50 /* libAliPay.a in Frameworks */,
				4202648C212E8CE5001B3A50 /* TrustDefenderMobile.framework in Frameworks */,
				420261E8212D0514001B3A50 /* libAliyunSLS.a in Frameworks */,
				420261E9212D0514001B3A50 /* libAMap.a in Frameworks */,
				420261EB212D0514001B3A50 /* libAppTool.a in Frameworks */,
				420261EC212D0514001B3A50 /* libCaptcha.a in Frameworks */,
				420261ED212D0514001B3A50 /* libConfigStorage.a in Frameworks */,
				420261EE212D0514001B3A50 /* libDeviceInfo.a in Frameworks */,
				420261EF212D0514001B3A50 /* libImageCropPicker.a in Frameworks */,
				420261F0212D0514001B3A50 /* libJPush.a in Frameworks */,
				420261F1212D0514001B3A50 /* libMBProgressHUD.a in Frameworks */,
				420261F2212D0514001B3A50 /* libPush.a in Frameworks */,
				420261F3212D0514001B3A50 /* libSGQRCode.a in Frameworks */,
				420261F4212D0514001B3A50 /* libSocialShare.a in Frameworks */,
				420261F5212D0514001B3A50 /* libStatusBarUtil.a in Frameworks */,
				420261F6212D0514001B3A50 /* libWeChat.a in Frameworks */,
				422063C71FF482A000C81AF1 /* libz.tbd in Frameworks */,
				422063C91FF482A000C81AF1 /* libc++abi.tbd in Frameworks */,
				422063CE1FF482A000C81AF1 /* libRNSVG.a in Frameworks */,
				422063CF1FF482A000C81AF1 /* UserNotifications.framework in Frameworks */,
				422063D11FF482A000C81AF1 /* Security.framework in Frameworks */,
				422063D21FF482A000C81AF1 /* UIKit.framework in Frameworks */,
				422063D31FF482A000C81AF1 /* MobileCoreServices.framework in Frameworks */,
				422063D41FF482A000C81AF1 /* libxml2.tbd in Frameworks */,
				422063D51FF482A000C81AF1 /* CoreMotion.framework in Frameworks */,
				422063D61FF482A000C81AF1 /* CFNetwork.framework in Frameworks */,
				422063DA1FF482A000C81AF1 /* CoreGraphics.framework in Frameworks */,
				422063DB1FF482A000C81AF1 /* QuartzCore.framework in Frameworks */,
				422063DC1FF482A000C81AF1 /* CoreText.framework in Frameworks */,
				422063DE1FF482A000C81AF1 /* libc++.tbd in Frameworks */,
				422063DF1FF482A000C81AF1 /* libresolv.tbd in Frameworks */,
				422063E01FF482A000C81AF1 /* SystemConfiguration.framework in Frameworks */,
				422063E31FF482A000C81AF1 /* libsqlite3.tbd in Frameworks */,
				422063E81FF482A000C81AF1 /* libicucore.tbd in Frameworks */,
				422063E91FF482A000C81AF1 /* libsqlite3.0.tbd in Frameworks */,
				422063F21FF482A000C81AF1 /* CoreTelephony.framework in Frameworks */,
				422063F51FF482A000C81AF1 /* libReact.a in Frameworks */,
				422063F61FF482A000C81AF1 /* libRCTAnimation.a in Frameworks */,
				422063F71FF482A000C81AF1 /* libRCTActionSheet.a in Frameworks */,
				422063F81FF482A000C81AF1 /* libRCTGeolocation.a in Frameworks */,
				422063F91FF482A000C81AF1 /* libRCTImage.a in Frameworks */,
				422063FB1FF482A000C81AF1 /* libRCTLinking.a in Frameworks */,
				422063FC1FF482A000C81AF1 /* libRCTNetwork.a in Frameworks */,
				842B62C42A0A4B66009D78DC /* libstdc++.6.0.9.tbd in Frameworks */,
				422063FD1FF482A000C81AF1 /* libRCTSettings.a in Frameworks */,
				422063FF1FF482A000C81AF1 /* libRCTText.a in Frameworks */,
				422064001FF482A000C81AF1 /* libRCTVibration.a in Frameworks */,
				422064011FF482A000C81AF1 /* libRCTWebSocket.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00C302A81ABCB8CE00DB3ED1 /* Products */ = {
			isa = PBXGroup;
			children = (
				00C302AC1ABCB8CE00DB3ED1 /* libRCTActionSheet.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		00C302B61ABCB90400DB3ED1 /* Products */ = {
			isa = PBXGroup;
			children = (
				00C302BA1ABCB90400DB3ED1 /* libRCTGeolocation.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		00C302BC1ABCB91800DB3ED1 /* Products */ = {
			isa = PBXGroup;
			children = (
				00C302C01ABCB91800DB3ED1 /* libRCTImage.a */,
				3DAD3E841DF850E9000B6D8A /* libRCTImage-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		00C302D41ABCB9D200DB3ED1 /* Products */ = {
			isa = PBXGroup;
			children = (
				00C302DC1ABCB9D200DB3ED1 /* libRCTNetwork.a */,
				3DAD3E8C1DF850E9000B6D8A /* libRCTNetwork-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		00C302E01ABCB9EE00DB3ED1 /* Products */ = {
			isa = PBXGroup;
			children = (
				00C302E41ABCB9EE00DB3ED1 /* libRCTVibration.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		139105B71AF99BAD00B5F7CC /* Products */ = {
			isa = PBXGroup;
			children = (
				139105C11AF99BAD00B5F7CC /* libRCTSettings.a */,
				3DAD3E901DF850E9000B6D8A /* libRCTSettings-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		139FDEE71B06529A00C62182 /* Products */ = {
			isa = PBXGroup;
			children = (
				139FDEF41B06529B00C62182 /* libRCTWebSocket.a */,
				3DAD3E991DF850E9000B6D8A /* libRCTWebSocket-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* BaseRN */ = {
			isa = PBXGroup;
			children = (
				42026455212E8C6E001B3A50 /* APPayAssistex */,
				42026444212E8BB2001B3A50 /* bundle */,
				4220641D1FF482CD00C81AF1 /* constants */,
				008F07F21AC5B25A0029DE68 /* main.jsbundle */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.m */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB71A68108700A75B9A /* main.m */,
			);
			name = BaseRN;
			sourceTree = "<group>";
		};
		146834001AC3E56700842450 /* Products */ = {
			isa = PBXGroup;
			children = (
				146834041AC3E56700842450 /* libReact.a */,
				3DAD3EA31DF850E9000B6D8A /* libReact.a */,
				3DAD3EA51DF850E9000B6D8A /* libyoga.a */,
				3DAD3EA71DF850E9000B6D8A /* libyoga.a */,
				3DAD3EA91DF850E9000B6D8A /* libcxxreact.a */,
				3DAD3EAB1DF850E9000B6D8A /* libcxxreact.a */,
				3DAD3EAD1DF850E9000B6D8A /* libjschelpers.a */,
				3DAD3EAF1DF850E9000B6D8A /* libjschelpers.a */,
				EBDBC2E21FDA87970004CA54 /* libthird-party.a */,
				EBDBC2E41FDA87970004CA54 /* libthird-party.a */,
				EBDBC2E61FDA87970004CA54 /* libdouble-conversion.a */,
				EBDBC2E81FDA87970004CA54 /* libdouble-conversion.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		420260CF212CFE56001B3A50 /* Products */ = {
			isa = PBXGroup;
			children = (
				420260D3212CFE56001B3A50 /* libAFNetworking.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		420260D5212CFE69001B3A50 /* Products */ = {
			isa = PBXGroup;
			children = (
				420260D9212CFE69001B3A50 /* libAliOSS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		420260DB212CFE76001B3A50 /* Products */ = {
			isa = PBXGroup;
			children = (
				420260DF212CFE76001B3A50 /* libAliPay.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		420260E1212CFE85001B3A50 /* Products */ = {
			isa = PBXGroup;
			children = (
				420260E5212CFE85001B3A50 /* libAliyunSLS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		420260E7212CFE96001B3A50 /* Products */ = {
			isa = PBXGroup;
			children = (
				420260EB212CFE96001B3A50 /* libAMap.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		420260F3212CFED3001B3A50 /* Products */ = {
			isa = PBXGroup;
			children = (
				420260F7212CFED3001B3A50 /* libAppTool.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		420260F9212CFEE1001B3A50 /* Products */ = {
			isa = PBXGroup;
			children = (
				420260FD212CFEE1001B3A50 /* libCaptcha.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		420260FF212CFEEF001B3A50 /* Products */ = {
			isa = PBXGroup;
			children = (
				42026103212CFEEF001B3A50 /* libConfigStorage.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		42026105212CFEFE001B3A50 /* Products */ = {
			isa = PBXGroup;
			children = (
				42026109212CFEFE001B3A50 /* libDeviceInfo.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		4202610B212CFF0E001B3A50 /* Products */ = {
			isa = PBXGroup;
			children = (
				4202610F212CFF0E001B3A50 /* libImageCropPicker.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		42026111212CFF1B001B3A50 /* Products */ = {
			isa = PBXGroup;
			children = (
				42026115212CFF1B001B3A50 /* libJPush.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		42026117212CFF29001B3A50 /* Products */ = {
			isa = PBXGroup;
			children = (
				4202611B212CFF29001B3A50 /* libMBProgressHUD.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		4202611D212CFF5A001B3A50 /* Products */ = {
			isa = PBXGroup;
			children = (
				42026121212CFF5A001B3A50 /* libPush.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		42026123212CFF72001B3A50 /* Products */ = {
			isa = PBXGroup;
			children = (
				42026127212CFF72001B3A50 /* libSGQRCode.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		42026129212CFF83001B3A50 /* Products */ = {
			isa = PBXGroup;
			children = (
				4202612D212CFF83001B3A50 /* libSocialShare.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		4202612F212CFF95001B3A50 /* Products */ = {
			isa = PBXGroup;
			children = (
				42026133212CFF95001B3A50 /* libStatusBarUtil.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		42026135212CFFA2001B3A50 /* Products */ = {
			isa = PBXGroup;
			children = (
				42026139212CFFA2001B3A50 /* libWeChat.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		42026444212E8BB2001B3A50 /* bundle */ = {
			isa = PBXGroup;
			children = (
				421B082D212FFF810041D0F4 /* AMap.bundle */,
				42026451212E8C01001B3A50 /* UMSocialSDKPromptResources.bundle */,
				4202644D212E8BE9001B3A50 /* UMSocialSDKResources.bundle */,
				42026449212E8BD7001B3A50 /* SGQRCode.bundle */,
				42026445212E8BC3001B3A50 /* AlipaySDK.bundle */,
			);
			name = bundle;
			path = BaseRN/bundle;
			sourceTree = "<group>";
		};
		42026455212E8C6E001B3A50 /* APPayAssistex */ = {
			isa = PBXGroup;
			children = (
				42026458212E8CE5001B3A50 /* lib_release */,
				42026456212E8CE2001B3A50 /* RCTAPPayAssistEx.h */,
				42026457212E8CE3001B3A50 /* RCTAPPayAssistEx.m */,
			);
			name = APPayAssistex;
			path = BaseRN/APPayAssistex;
			sourceTree = "<group>";
		};
		42026458212E8CE5001B3A50 /* lib_release */ = {
			isa = PBXGroup;
			children = (
				42026459212E8CE5001B3A50 /* TrustDefenderMobile.framework */,
				4202645A212E8CE5001B3A50 /* APMPPassGuardCtrlBundle.bundle */,
				4202645B212E8CE5001B3A50 /* include */,
				42026464212E8CE5001B3A50 /* libAPayLib.a */,
				42026465212E8CE5001B3A50 /* APayRes.bundle */,
				42026466212E8CE5001B3A50 /* ZhengTong */,
			);
			path = lib_release;
			sourceTree = "<group>";
		};
		4202645B212E8CE5001B3A50 /* include */ = {
			isa = PBXGroup;
			children = (
				4202645C212E8CE5001B3A50 /* PinyinHelper.h */,
				4202645D212E8CE5001B3A50 /* HanyuPinyinOutputFormat.h */,
				4202645E212E8CE5001B3A50 /* ChineseToPinyinResource.h */,
				4202645F212E8CE5001B3A50 /* APay.h */,
				42026460212E8CE5001B3A50 /* pinyin.h */,
				42026461212E8CE5001B3A50 /* PinyinFormatter.h */,
				42026462212E8CE5001B3A50 /* NSString+PinYin4Cocoa.h */,
				42026463212E8CE5001B3A50 /* PinYin4Objc.h */,
			);
			path = include;
			sourceTree = "<group>";
		};
		42026466212E8CE5001B3A50 /* ZhengTong */ = {
			isa = PBXGroup;
			children = (
				42026467212E8CE5001B3A50 /* OtherResources */,
			);
			path = ZhengTong;
			sourceTree = "<group>";
		};
		42026467212E8CE5001B3A50 /* OtherResources */ = {
			isa = PBXGroup;
			children = (
				42026468212E8CE5001B3A50 /* Storyboards */,
				4202646B212E8CE5001B3A50 /* StringList.plist */,
				4202646C212E8CE5001B3A50 /* Resources */,
				42026485212E8CE5001B3A50 /* MBProgressHUD */,
				42026487212E8CE5001B3A50 /* Image.bundle */,
			);
			path = OtherResources;
			sourceTree = "<group>";
		};
		42026468212E8CE5001B3A50 /* Storyboards */ = {
			isa = PBXGroup;
			children = (
				42026469212E8CE5001B3A50 /* VerificationPage.storyboard */,
				4202646A212E8CE5001B3A50 /* RegistrationPage.storyboard */,
			);
			path = Storyboards;
			sourceTree = "<group>";
		};
		4202646C212E8CE5001B3A50 /* Resources */ = {
			isa = PBXGroup;
			children = (
				4202646D212E8CE5001B3A50 /* GifResource */,
				42026475212E8CE5001B3A50 /* yitu_face_model.bundle */,
				42026476212E8CE5001B3A50 /* Images.xcassets */,
				42026477212E8CE5001B3A50 /* Sound */,
				42026484212E8CE5001B3A50 /* staging.public.der */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		4202646D212E8CE5001B3A50 /* GifResource */ = {
			isa = PBXGroup;
			children = (
				4202646E212E8CE5001B3A50 /* 张嘴.gif */,
				4202646F212E8CE5001B3A50 /* 扭头-右.gif */,
				42026470212E8CE5001B3A50 /* 低头.gif */,
				42026471212E8CE5001B3A50 /* yitu_face_action_normal.gif */,
				42026472212E8CE5001B3A50 /* 抬头.gif */,
				42026473212E8CE5001B3A50 /* 闭眼.gif */,
				42026474212E8CE5001B3A50 /* 扭头-左.gif */,
			);
			path = GifResource;
			sourceTree = "<group>";
		};
		42026477212E8CE5001B3A50 /* Sound */ = {
			isa = PBXGroup;
			children = (
				42026478212E8CE5001B3A50 /* yitu_eye_close_audio.mp3 */,
				42026479212E8CE5001B3A50 /* yitu_head_right_audio.mp3 */,
				4202647A212E8CE5001B3A50 /* yitu_verification_fail_audio.mp3 */,
				4202647B212E8CE5001B3A50 /* yitu_head_up_audio.mp3 */,
				4202647C212E8CE5001B3A50 /* yitu_mouth_open_audio.mp3 */,
				4202647D212E8CE5001B3A50 /* yitu_next_action_audio.mp3 */,
				4202647E212E8CE5001B3A50 /* yitu_verification_pass_audio.mp3 */,
				4202647F212E8CE5001B3A50 /* yitu_time_out_audio.mp3 */,
				42026480212E8CE5001B3A50 /* yitu_get_ready_audio.mp3 */,
				42026481212E8CE5001B3A50 /* yitu_head_left_audio.mp3 */,
				42026482212E8CE5001B3A50 /* yitu_head_down_audio.mp3 */,
				42026483212E8CE5001B3A50 /* yitu_not_user_audio.mp3 */,
			);
			path = Sound;
			sourceTree = "<group>";
		};
		42026485212E8CE5001B3A50 /* MBProgressHUD */ = {
			isa = PBXGroup;
			children = (
				42026486212E8CE5001B3A50 /* MBProgressHUD.bundle */,
			);
			path = MBProgressHUD;
			sourceTree = "<group>";
		};
		4219E0F3219D9B450080C095 /* Products */ = {
			isa = PBXGroup;
			children = (
				4219E0F8219D9B450080C095 /* libCodePush.a */,
				4219E0FA219D9B450080C095 /* libCodePush.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		4220641D1FF482CD00C81AF1 /* constants */ = {
			isa = PBXGroup;
			children = (
				422064211FF482CD00C81AF1 /* yunxi */,
				4220641E1FF482CD00C81AF1 /* qa */,
				42F2D62C2068839D0042D3B4 /* pro */,
			);
			name = constants;
			path = BaseRN/constants;
			sourceTree = "<group>";
		};
		4220641E1FF482CD00C81AF1 /* qa */ = {
			isa = PBXGroup;
			children = (
				4220641F1FF482CD00C81AF1 /* Info.plist */,
				422064201FF482CD00C81AF1 /* PrefixHeader.pch */,
			);
			path = qa;
			sourceTree = "<group>";
		};
		422064211FF482CD00C81AF1 /* yunxi */ = {
			isa = PBXGroup;
			children = (
				422064221FF482CD00C81AF1 /* Info.plist */,
				422064231FF482CD00C81AF1 /* PrefixHeader.pch */,
			);
			path = yunxi;
			sourceTree = "<group>";
		};
		42F2D62C2068839D0042D3B4 /* pro */ = {
			isa = PBXGroup;
			children = (
				42F2D62D2068839D0042D3B4 /* Info.plist */,
				42F2D62E2068839D0042D3B4 /* PrefixHeader.pch */,
			);
			path = pro;
			sourceTree = "<group>";
		};
		5E91572E1DD0AC6500FF2AA8 /* Products */ = {
			isa = PBXGroup;
			children = (
				5E9157331DD0AC6500FF2AA8 /* libRCTAnimation.a */,
				5E9157351DD0AC6500FF2AA8 /* libRCTAnimation.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		78C398B11ACF4ADC00677621 /* Products */ = {
			isa = PBXGroup;
			children = (
				78C398B91ACF4ADC00677621 /* libRCTLinking.a */,
				3DAD3E881DF850E9000B6D8A /* libRCTLinking-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
				4219E0F2219D9B450080C095 /* CodePush.xcodeproj */,
				42026134212CFFA2001B3A50 /* WeChat.xcodeproj */,
				4202612E212CFF95001B3A50 /* StatusBarUtil.xcodeproj */,
				42026128212CFF83001B3A50 /* SocialShare.xcodeproj */,
				42026122212CFF72001B3A50 /* SGQRCode.xcodeproj */,
				4202611C212CFF5A001B3A50 /* Push.xcodeproj */,
				42026116212CFF29001B3A50 /* MBProgressHUD.xcodeproj */,
				42026110212CFF1B001B3A50 /* JPush.xcodeproj */,
				4202610A212CFF0E001B3A50 /* ImageCropPicker.xcodeproj */,
				42026104212CFEFE001B3A50 /* DeviceInfo.xcodeproj */,
				420260FE212CFEEF001B3A50 /* ConfigStorage.xcodeproj */,
				420260F8212CFEE1001B3A50 /* Captcha.xcodeproj */,
				420260F2212CFED3001B3A50 /* AppTool.xcodeproj */,
				420260E6212CFE96001B3A50 /* AMap.xcodeproj */,
				420260E0212CFE85001B3A50 /* AliyunSLS.xcodeproj */,
				420260DA212CFE76001B3A50 /* AliPay.xcodeproj */,
				420260D4212CFE69001B3A50 /* AliOSS.xcodeproj */,
				420260CE212CFE56001B3A50 /* AFNetworking.xcodeproj */,
				5E91572D1DD0AC6500FF2AA8 /* RCTAnimation.xcodeproj */,
				146833FF1AC3E56700842450 /* React.xcodeproj */,
				00C302A71ABCB8CE00DB3ED1 /* RCTActionSheet.xcodeproj */,
				00C302B51ABCB90400DB3ED1 /* RCTGeolocation.xcodeproj */,
				00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */,
				78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */,
				00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */,
				139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */,
				832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */,
				00C302DF1ABCB9EE00DB3ED1 /* RCTVibration.xcodeproj */,
				139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */,
				CA9223B2EB1D41968C942CCC /* RNSVG.xcodeproj */,
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		832341B11AAA6A8300B99B32 /* Products */ = {
			isa = PBXGroup;
			children = (
				832341B51AAA6A8300B99B32 /* libRCTText.a */,
				3DAD3E941DF850E9000B6D8A /* libRCTText-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				AEB36B4F1F3B3499000D946F /* BaseRN.entitlements */,
				93BF179820A440500027A378 /* pro.entitlements */,
				93BF179720A440390027A378 /* qa.entitlements */,
				93BF179620A43BB40027A378 /* yunxi.entitlements */,
				13B07FAE1A68108700A75B9A /* BaseRN */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				83CBBA001A601CBA00E9B192 /* Products */,
				F0B562F21EBC1F710000AB74 /* Frameworks */,
				AE4D1BD11F6A262000C136CD /* Recovered References */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			wrapsLines = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* yunxi.app */,
				422064101FF482A000C81AF1 /* qa.app */,
				4206F1DC205E0CDB001C21E4 /* pro.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		AE4783CF1F4ADCFC00FDBD75 /* Products */ = {
			isa = PBXGroup;
			children = (
				AE4783ED1F4ADCFC00FDBD75 /* libRNSVG.a */,
				427C2DD11FDA9252005D7C6E /* libRNSVG-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		AE4D1BD11F6A262000C136CD /* Recovered References */ = {
			isa = PBXGroup;
			children = (
				EE7912785918448BB80EBE57 /* libRNSVG.a */,
			);
			name = "Recovered References";
			sourceTree = "<group>";
		};
		F0B562F21EBC1F710000AB74 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				42026173212D022E001B3A50 /* QBImagePicker.framework */,
				42026172212D022E001B3A50 /* RSKImageCropper.framework */,
				4202616C212D01EE001B3A50 /* GLKit.framework */,
				4202616A212D01E8001B3A50 /* AlipaySDK.framework */,
				42026163212D01D6001B3A50 /* AMapFoundationKit.framework */,
				42026162212D01D6001B3A50 /* AMapLocationKit.framework */,
				42026165212D01D6001B3A50 /* AMapSearchKit.framework */,
				42026164212D01D6001B3A50 /* MAMapKit.framework */,
				42026160212D01C2001B3A50 /* UShareUI.framework */,
				42C740201FB2E8BD000A1C6B /* libc++abi.tbd */,
				AEBCE5831F44368A00B4DC66 /* UserNotifications.framework */,
				AEBCE5811F44366B00B4DC66 /* Security.framework */,
				AE30700E1F3446940069FB92 /* UIKit.framework */,
				AE25209E1F301A8900479CE1 /* MobileCoreServices.framework */,
				AE25209C1F301A7A00479CE1 /* libxml2.tbd */,
				AECAA7101F2F0D990048F2F3 /* CoreMotion.framework */,
				AECAA70E1F2F0D8F0048F2F3 /* CFNetwork.framework */,
				AECAA70C1F2F0D840048F2F3 /* CoreGraphics.framework */,
				AECAA70A1F2F0D770048F2F3 /* QuartzCore.framework */,
				AECAA7081F2F0D580048F2F3 /* CoreText.framework */,
				EB080EB21EF8FA5D00EA73A0 /* libsqlite3.tbd */,
				EB080E961EF8FA5500EA73A0 /* libicucore.tbd */,
				F063DEE81ECB0316000A5D4F /* libsqlite3.0.tbd */,
				F06CA33E1EC1AC040089BA60 /* MapKit.framework */,
				842B62A12A0A4B66009D78DC /* libstdc++.6.0.9.tbd */,
				F03E74A21EC1760E008B4B70 /* libz.tbd */,
				F03E74A01EC17603008B4B70 /* CoreTelephony.framework */,
				F03E749E1EC175E4008B4B70 /* libc++.tbd */,
				F0B562F51EBC1F7C0000AB74 /* SystemConfiguration.framework */,
				F0B562F31EBC1F710000AB74 /* libresolv.tbd */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* yunxi */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "yunxi" */;
			buildPhases = (
				13B07F871A680F5B00A75B9A /* Sources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				AEF539771FA6C85C005985E3 /* Embed Frameworks */,
				42BEEEE21FEBD3F900C5AC18 /* Run Script */,
			);
			buildRules = (
			);
			dependencies = (
				420264E9212E8E49001B3A50 /* PBXTargetDependency */,
				420264EB212E8E49001B3A50 /* PBXTargetDependency */,
				420264ED212E8E49001B3A50 /* PBXTargetDependency */,
				420264EF212E8E49001B3A50 /* PBXTargetDependency */,
				420264F1212E8E49001B3A50 /* PBXTargetDependency */,
				420264F3212E8E49001B3A50 /* PBXTargetDependency */,
				420264F5212E8E49001B3A50 /* PBXTargetDependency */,
				420264F7212E8E49001B3A50 /* PBXTargetDependency */,
				420264F9212E8E49001B3A50 /* PBXTargetDependency */,
				420264FB212E8E49001B3A50 /* PBXTargetDependency */,
				420264FD212E8E49001B3A50 /* PBXTargetDependency */,
				420264FF212E8E49001B3A50 /* PBXTargetDependency */,
				42026501212E8E49001B3A50 /* PBXTargetDependency */,
				42026503212E8E49001B3A50 /* PBXTargetDependency */,
				42026505212E8E49001B3A50 /* PBXTargetDependency */,
				42026507212E8E49001B3A50 /* PBXTargetDependency */,
				42026509212E8E49001B3A50 /* PBXTargetDependency */,
			);
			name = yunxi;
			productName = "Hello World";
			productReference = 13B07F961A680F5B00A75B9A /* yunxi.app */;
			productType = "com.apple.product-type.application";
		};
		4206F146205E0CDB001C21E4 /* pro */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4206F1D9205E0CDB001C21E4 /* Build configuration list for PBXNativeTarget "pro" */;
			buildPhases = (
				4206F147205E0CDB001C21E4 /* Sources */,
				4206F190205E0CDB001C21E4 /* Frameworks */,
				4206F1CC205E0CDB001C21E4 /* Resources */,
				4206F1D4205E0CDB001C21E4 /* Bundle React Native code and images */,
				4206F1D5205E0CDB001C21E4 /* Embed Frameworks */,
				4206F1D8205E0CDB001C21E4 /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
				4202652D212E93AD001B3A50 /* PBXTargetDependency */,
				4202652F212E93AD001B3A50 /* PBXTargetDependency */,
				42026531212E93AD001B3A50 /* PBXTargetDependency */,
				42026533212E93AD001B3A50 /* PBXTargetDependency */,
				42026535212E93AD001B3A50 /* PBXTargetDependency */,
				42026537212E93AD001B3A50 /* PBXTargetDependency */,
				42026539212E93AD001B3A50 /* PBXTargetDependency */,
				4202653B212E93AD001B3A50 /* PBXTargetDependency */,
				4202653D212E93AD001B3A50 /* PBXTargetDependency */,
				4202653F212E93AD001B3A50 /* PBXTargetDependency */,
				42026541212E93AD001B3A50 /* PBXTargetDependency */,
				42026543212E93AD001B3A50 /* PBXTargetDependency */,
				42026545212E93AD001B3A50 /* PBXTargetDependency */,
				42026547212E93AD001B3A50 /* PBXTargetDependency */,
				42026549212E93AD001B3A50 /* PBXTargetDependency */,
				4202654B212E93AD001B3A50 /* PBXTargetDependency */,
				4202654D212E93AD001B3A50 /* PBXTargetDependency */,
			);
			name = pro;
			productName = "Hello World";
			productReference = 4206F1DC205E0CDB001C21E4 /* pro.app */;
			productType = "com.apple.product-type.application";
		};
		4220637D1FF482A000C81AF1 /* qa */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4220640D1FF482A000C81AF1 /* Build configuration list for PBXNativeTarget "qa" */;
			buildPhases = (
				4220637E1FF482A000C81AF1 /* Sources */,
				422063C61FF482A000C81AF1 /* Frameworks */,
				422064021FF482A000C81AF1 /* Resources */,
				422064081FF482A000C81AF1 /* Bundle React Native code and images */,
				422064091FF482A000C81AF1 /* Embed Frameworks */,
				4220640C1FF482A000C81AF1 /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
				4202650B212E938F001B3A50 /* PBXTargetDependency */,
				4202650D212E938F001B3A50 /* PBXTargetDependency */,
				4202650F212E938F001B3A50 /* PBXTargetDependency */,
				42026511212E938F001B3A50 /* PBXTargetDependency */,
				42026513212E938F001B3A50 /* PBXTargetDependency */,
				42026515212E938F001B3A50 /* PBXTargetDependency */,
				42026517212E938F001B3A50 /* PBXTargetDependency */,
				42026519212E938F001B3A50 /* PBXTargetDependency */,
				4202651B212E938F001B3A50 /* PBXTargetDependency */,
				4202651D212E938F001B3A50 /* PBXTargetDependency */,
				4202651F212E938F001B3A50 /* PBXTargetDependency */,
				42026521212E938F001B3A50 /* PBXTargetDependency */,
				42026523212E938F001B3A50 /* PBXTargetDependency */,
				42026525212E938F001B3A50 /* PBXTargetDependency */,
				42026527212E938F001B3A50 /* PBXTargetDependency */,
				42026529212E938F001B3A50 /* PBXTargetDependency */,
				4202652B212E938F001B3A50 /* PBXTargetDependency */,
			);
			name = qa;
			productName = "Hello World";
			productReference = 422064101FF482A000C81AF1 /* qa.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 610;
				ORGANIZATIONNAME = Facebook;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						DevelopmentTeam = QR69XCVFDP;
						ProvisioningStyle = Manual;
						SystemCapabilities = {
							com.apple.BackgroundModes = {
								enabled = 1;
							};
							com.apple.Maps.iOS = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 1;
							};
						};
					};
					4206F146205E0CDB001C21E4 = {
						DevelopmentTeam = QR69XCVFDP;
						SystemCapabilities = {
							com.apple.Push = {
								enabled = 1;
							};
						};
					};
					4220637D1FF482A000C81AF1 = {
						DevelopmentTeam = QR69XCVFDP;
						SystemCapabilities = {
							com.apple.Push = {
								enabled = 1;
							};
						};
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "BaseRN" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 420260CF212CFE56001B3A50 /* Products */;
					ProjectRef = 420260CE212CFE56001B3A50 /* AFNetworking.xcodeproj */;
				},
				{
					ProductGroup = 420260D5212CFE69001B3A50 /* Products */;
					ProjectRef = 420260D4212CFE69001B3A50 /* AliOSS.xcodeproj */;
				},
				{
					ProductGroup = 420260DB212CFE76001B3A50 /* Products */;
					ProjectRef = 420260DA212CFE76001B3A50 /* AliPay.xcodeproj */;
				},
				{
					ProductGroup = 420260E1212CFE85001B3A50 /* Products */;
					ProjectRef = 420260E0212CFE85001B3A50 /* AliyunSLS.xcodeproj */;
				},
				{
					ProductGroup = 420260E7212CFE96001B3A50 /* Products */;
					ProjectRef = 420260E6212CFE96001B3A50 /* AMap.xcodeproj */;
				},
				{
					ProductGroup = 420260F3212CFED3001B3A50 /* Products */;
					ProjectRef = 420260F2212CFED3001B3A50 /* AppTool.xcodeproj */;
				},
				{
					ProductGroup = 420260F9212CFEE1001B3A50 /* Products */;
					ProjectRef = 420260F8212CFEE1001B3A50 /* Captcha.xcodeproj */;
				},
				{
					ProductGroup = 4219E0F3219D9B450080C095 /* Products */;
					ProjectRef = 4219E0F2219D9B450080C095 /* CodePush.xcodeproj */;
				},
				{
					ProductGroup = 420260FF212CFEEF001B3A50 /* Products */;
					ProjectRef = 420260FE212CFEEF001B3A50 /* ConfigStorage.xcodeproj */;
				},
				{
					ProductGroup = 42026105212CFEFE001B3A50 /* Products */;
					ProjectRef = 42026104212CFEFE001B3A50 /* DeviceInfo.xcodeproj */;
				},
				{
					ProductGroup = 4202610B212CFF0E001B3A50 /* Products */;
					ProjectRef = 4202610A212CFF0E001B3A50 /* ImageCropPicker.xcodeproj */;
				},
				{
					ProductGroup = 42026111212CFF1B001B3A50 /* Products */;
					ProjectRef = 42026110212CFF1B001B3A50 /* JPush.xcodeproj */;
				},
				{
					ProductGroup = 42026117212CFF29001B3A50 /* Products */;
					ProjectRef = 42026116212CFF29001B3A50 /* MBProgressHUD.xcodeproj */;
				},
				{
					ProductGroup = 4202611D212CFF5A001B3A50 /* Products */;
					ProjectRef = 4202611C212CFF5A001B3A50 /* Push.xcodeproj */;
				},
				{
					ProductGroup = 00C302A81ABCB8CE00DB3ED1 /* Products */;
					ProjectRef = 00C302A71ABCB8CE00DB3ED1 /* RCTActionSheet.xcodeproj */;
				},
				{
					ProductGroup = 5E91572E1DD0AC6500FF2AA8 /* Products */;
					ProjectRef = 5E91572D1DD0AC6500FF2AA8 /* RCTAnimation.xcodeproj */;
				},
				{
					ProductGroup = 00C302B61ABCB90400DB3ED1 /* Products */;
					ProjectRef = 00C302B51ABCB90400DB3ED1 /* RCTGeolocation.xcodeproj */;
				},
				{
					ProductGroup = 00C302BC1ABCB91800DB3ED1 /* Products */;
					ProjectRef = 00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */;
				},
				{
					ProductGroup = 78C398B11ACF4ADC00677621 /* Products */;
					ProjectRef = 78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */;
				},
				{
					ProductGroup = 00C302D41ABCB9D200DB3ED1 /* Products */;
					ProjectRef = 00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */;
				},
				{
					ProductGroup = 139105B71AF99BAD00B5F7CC /* Products */;
					ProjectRef = 139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */;
				},
				{
					ProductGroup = 832341B11AAA6A8300B99B32 /* Products */;
					ProjectRef = 832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */;
				},
				{
					ProductGroup = 00C302E01ABCB9EE00DB3ED1 /* Products */;
					ProjectRef = 00C302DF1ABCB9EE00DB3ED1 /* RCTVibration.xcodeproj */;
				},
				{
					ProductGroup = 139FDEE71B06529A00C62182 /* Products */;
					ProjectRef = 139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */;
				},
				{
					ProductGroup = 146834001AC3E56700842450 /* Products */;
					ProjectRef = 146833FF1AC3E56700842450 /* React.xcodeproj */;
				},
				{
					ProductGroup = AE4783CF1F4ADCFC00FDBD75 /* Products */;
					ProjectRef = CA9223B2EB1D41968C942CCC /* RNSVG.xcodeproj */;
				},
				{
					ProductGroup = 42026123212CFF72001B3A50 /* Products */;
					ProjectRef = 42026122212CFF72001B3A50 /* SGQRCode.xcodeproj */;
				},
				{
					ProductGroup = 42026129212CFF83001B3A50 /* Products */;
					ProjectRef = 42026128212CFF83001B3A50 /* SocialShare.xcodeproj */;
				},
				{
					ProductGroup = 4202612F212CFF95001B3A50 /* Products */;
					ProjectRef = 4202612E212CFF95001B3A50 /* StatusBarUtil.xcodeproj */;
				},
				{
					ProductGroup = 42026135212CFFA2001B3A50 /* Products */;
					ProjectRef = 42026134212CFFA2001B3A50 /* WeChat.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* yunxi */,
				4220637D1FF482A000C81AF1 /* qa */,
				4206F146205E0CDB001C21E4 /* pro */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		00C302AC1ABCB8CE00DB3ED1 /* libRCTActionSheet.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTActionSheet.a;
			remoteRef = 00C302AB1ABCB8CE00DB3ED1 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		00C302BA1ABCB90400DB3ED1 /* libRCTGeolocation.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTGeolocation.a;
			remoteRef = 00C302B91ABCB90400DB3ED1 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		00C302C01ABCB91800DB3ED1 /* libRCTImage.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTImage.a;
			remoteRef = 00C302BF1ABCB91800DB3ED1 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		00C302DC1ABCB9D200DB3ED1 /* libRCTNetwork.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTNetwork.a;
			remoteRef = 00C302DB1ABCB9D200DB3ED1 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		00C302E41ABCB9EE00DB3ED1 /* libRCTVibration.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTVibration.a;
			remoteRef = 00C302E31ABCB9EE00DB3ED1 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		139105C11AF99BAD00B5F7CC /* libRCTSettings.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTSettings.a;
			remoteRef = 139105C01AF99BAD00B5F7CC /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		139FDEF41B06529B00C62182 /* libRCTWebSocket.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTWebSocket.a;
			remoteRef = 139FDEF31B06529B00C62182 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		146834041AC3E56700842450 /* libReact.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libReact.a;
			remoteRef = 146834031AC3E56700842450 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E841DF850E9000B6D8A /* libRCTImage-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTImage-tvOS.a";
			remoteRef = 3DAD3E831DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E881DF850E9000B6D8A /* libRCTLinking-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTLinking-tvOS.a";
			remoteRef = 3DAD3E871DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E8C1DF850E9000B6D8A /* libRCTNetwork-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTNetwork-tvOS.a";
			remoteRef = 3DAD3E8B1DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E901DF850E9000B6D8A /* libRCTSettings-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTSettings-tvOS.a";
			remoteRef = 3DAD3E8F1DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E941DF850E9000B6D8A /* libRCTText-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTText-tvOS.a";
			remoteRef = 3DAD3E931DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3E991DF850E9000B6D8A /* libRCTWebSocket-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRCTWebSocket-tvOS.a";
			remoteRef = 3DAD3E981DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EA31DF850E9000B6D8A /* libReact.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libReact.a;
			remoteRef = 3DAD3EA21DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EA51DF850E9000B6D8A /* libyoga.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libyoga.a;
			remoteRef = 3DAD3EA41DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EA71DF850E9000B6D8A /* libyoga.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libyoga.a;
			remoteRef = 3DAD3EA61DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EA91DF850E9000B6D8A /* libcxxreact.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libcxxreact.a;
			remoteRef = 3DAD3EA81DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EAB1DF850E9000B6D8A /* libcxxreact.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libcxxreact.a;
			remoteRef = 3DAD3EAA1DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EAD1DF850E9000B6D8A /* libjschelpers.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libjschelpers.a;
			remoteRef = 3DAD3EAC1DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		3DAD3EAF1DF850E9000B6D8A /* libjschelpers.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libjschelpers.a;
			remoteRef = 3DAD3EAE1DF850E9000B6D8A /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		420260D3212CFE56001B3A50 /* libAFNetworking.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libAFNetworking.a;
			remoteRef = 420260D2212CFE56001B3A50 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		420260D9212CFE69001B3A50 /* libAliOSS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libAliOSS.a;
			remoteRef = 420260D8212CFE69001B3A50 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		420260DF212CFE76001B3A50 /* libAliPay.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libAliPay.a;
			remoteRef = 420260DE212CFE76001B3A50 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		420260E5212CFE85001B3A50 /* libAliyunSLS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libAliyunSLS.a;
			remoteRef = 420260E4212CFE85001B3A50 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		420260EB212CFE96001B3A50 /* libAMap.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libAMap.a;
			remoteRef = 420260EA212CFE96001B3A50 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		420260F7212CFED3001B3A50 /* libAppTool.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libAppTool.a;
			remoteRef = 420260F6212CFED3001B3A50 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		420260FD212CFEE1001B3A50 /* libCaptcha.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libCaptcha.a;
			remoteRef = 420260FC212CFEE1001B3A50 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		42026103212CFEEF001B3A50 /* libConfigStorage.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libConfigStorage.a;
			remoteRef = 42026102212CFEEF001B3A50 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		42026109212CFEFE001B3A50 /* libDeviceInfo.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libDeviceInfo.a;
			remoteRef = 42026108212CFEFE001B3A50 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		4202610F212CFF0E001B3A50 /* libImageCropPicker.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libImageCropPicker.a;
			remoteRef = 4202610E212CFF0E001B3A50 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		42026115212CFF1B001B3A50 /* libJPush.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libJPush.a;
			remoteRef = 42026114212CFF1B001B3A50 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		4202611B212CFF29001B3A50 /* libMBProgressHUD.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libMBProgressHUD.a;
			remoteRef = 4202611A212CFF29001B3A50 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		42026121212CFF5A001B3A50 /* libPush.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libPush.a;
			remoteRef = 42026120212CFF5A001B3A50 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		42026127212CFF72001B3A50 /* libSGQRCode.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libSGQRCode.a;
			remoteRef = 42026126212CFF72001B3A50 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		4202612D212CFF83001B3A50 /* libSocialShare.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libSocialShare.a;
			remoteRef = 4202612C212CFF83001B3A50 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		42026133212CFF95001B3A50 /* libStatusBarUtil.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libStatusBarUtil.a;
			remoteRef = 42026132212CFF95001B3A50 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		42026139212CFFA2001B3A50 /* libWeChat.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libWeChat.a;
			remoteRef = 42026138212CFFA2001B3A50 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		4219E0F8219D9B450080C095 /* libCodePush.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libCodePush.a;
			remoteRef = 4219E0F7219D9B450080C095 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		4219E0FA219D9B450080C095 /* libCodePush.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libCodePush.a;
			remoteRef = 4219E0F9219D9B450080C095 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		427C2DD11FDA9252005D7C6E /* libRNSVG-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRNSVG-tvOS.a";
			remoteRef = 427C2DD01FDA9252005D7C6E /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		5E9157331DD0AC6500FF2AA8 /* libRCTAnimation.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTAnimation.a;
			remoteRef = 5E9157321DD0AC6500FF2AA8 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		5E9157351DD0AC6500FF2AA8 /* libRCTAnimation.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTAnimation.a;
			remoteRef = 5E9157341DD0AC6500FF2AA8 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		78C398B91ACF4ADC00677621 /* libRCTLinking.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTLinking.a;
			remoteRef = 78C398B81ACF4ADC00677621 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		832341B51AAA6A8300B99B32 /* libRCTText.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTText.a;
			remoteRef = 832341B41AAA6A8300B99B32 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		AE4783ED1F4ADCFC00FDBD75 /* libRNSVG.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRNSVG.a;
			remoteRef = AE4783EC1F4ADCFC00FDBD75 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		EBDBC2E21FDA87970004CA54 /* libthird-party.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libthird-party.a";
			remoteRef = EBDBC2E11FDA87970004CA54 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		EBDBC2E41FDA87970004CA54 /* libthird-party.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libthird-party.a";
			remoteRef = EBDBC2E31FDA87970004CA54 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		EBDBC2E61FDA87970004CA54 /* libdouble-conversion.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libdouble-conversion.a";
			remoteRef = EBDBC2E51FDA87970004CA54 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		EBDBC2E81FDA87970004CA54 /* libdouble-conversion.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libdouble-conversion.a";
			remoteRef = EBDBC2E71FDA87970004CA54 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				420264CD212E8CE5001B3A50 /* yitu_verification_pass_audio.mp3 in Resources */,
				420264AC212E8CE5001B3A50 /* 抬头.gif in Resources */,
				420264B5212E8CE5001B3A50 /* yitu_face_model.bundle in Resources */,
				420264E5212E8CE5001B3A50 /* Image.bundle in Resources */,
				420264A6212E8CE5001B3A50 /* 低头.gif in Resources */,
				420264A0212E8CE5001B3A50 /* 张嘴.gif in Resources */,
				420264AF212E8CE5001B3A50 /* 闭眼.gif in Resources */,
				42026494212E8CE5001B3A50 /* APayRes.bundle in Resources */,
				42026452212E8C01001B3A50 /* UMSocialSDKPromptResources.bundle in Resources */,
				42026497212E8CE5001B3A50 /* VerificationPage.storyboard in Resources */,
				420264C1212E8CE5001B3A50 /* yitu_verification_fail_audio.mp3 in Resources */,
				4202644A212E8BD7001B3A50 /* SGQRCode.bundle in Resources */,
				420264BB212E8CE5001B3A50 /* yitu_eye_close_audio.mp3 in Resources */,
				420264A3212E8CE5001B3A50 /* 扭头-右.gif in Resources */,
				4202644E212E8BE9001B3A50 /* UMSocialSDKResources.bundle in Resources */,
				420264E2212E8CE5001B3A50 /* MBProgressHUD.bundle in Resources */,
				420264D9212E8CE5001B3A50 /* yitu_head_down_audio.mp3 in Resources */,
				420264CA212E8CE5001B3A50 /* yitu_next_action_audio.mp3 in Resources */,
				420264C4212E8CE5001B3A50 /* yitu_head_up_audio.mp3 in Resources */,
				4202649D212E8CE5001B3A50 /* StringList.plist in Resources */,
				420264D3212E8CE5001B3A50 /* yitu_get_ready_audio.mp3 in Resources */,
				42026446212E8BC4001B3A50 /* AlipaySDK.bundle in Resources */,
				420264C7212E8CE5001B3A50 /* yitu_mouth_open_audio.mp3 in Resources */,
				420264DF212E8CE5001B3A50 /* staging.public.der in Resources */,
				420264D6212E8CE5001B3A50 /* yitu_head_left_audio.mp3 in Resources */,
				421B084D212FFF810041D0F4 /* AMap.bundle in Resources */,
				420264BE212E8CE5001B3A50 /* yitu_head_right_audio.mp3 in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				420264B2212E8CE5001B3A50 /* 扭头-左.gif in Resources */,
				420264D0212E8CE5001B3A50 /* yitu_time_out_audio.mp3 in Resources */,
				420264B8212E8CE5001B3A50 /* Images.xcassets in Resources */,
				420264A9212E8CE5001B3A50 /* yitu_face_action_normal.gif in Resources */,
				4202648E212E8CE5001B3A50 /* APMPPassGuardCtrlBundle.bundle in Resources */,
				420264DC212E8CE5001B3A50 /* yitu_not_user_audio.mp3 in Resources */,
				4202649A212E8CE5001B3A50 /* RegistrationPage.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4206F1CC205E0CDB001C21E4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				420264CF212E8CE5001B3A50 /* yitu_verification_pass_audio.mp3 in Resources */,
				420264AE212E8CE5001B3A50 /* 抬头.gif in Resources */,
				420264B7212E8CE5001B3A50 /* yitu_face_model.bundle in Resources */,
				420264E7212E8CE5001B3A50 /* Image.bundle in Resources */,
				420264A8212E8CE5001B3A50 /* 低头.gif in Resources */,
				420264A2212E8CE5001B3A50 /* 张嘴.gif in Resources */,
				420264B1212E8CE5001B3A50 /* 闭眼.gif in Resources */,
				42026496212E8CE5001B3A50 /* APayRes.bundle in Resources */,
				42026454212E8C01001B3A50 /* UMSocialSDKPromptResources.bundle in Resources */,
				42026499212E8CE5001B3A50 /* VerificationPage.storyboard in Resources */,
				420264C3212E8CE5001B3A50 /* yitu_verification_fail_audio.mp3 in Resources */,
				4202644C212E8BD7001B3A50 /* SGQRCode.bundle in Resources */,
				420264BD212E8CE5001B3A50 /* yitu_eye_close_audio.mp3 in Resources */,
				420264A5212E8CE5001B3A50 /* 扭头-右.gif in Resources */,
				42026450212E8BE9001B3A50 /* UMSocialSDKResources.bundle in Resources */,
				420264E4212E8CE5001B3A50 /* MBProgressHUD.bundle in Resources */,
				420264DB212E8CE5001B3A50 /* yitu_head_down_audio.mp3 in Resources */,
				420264CC212E8CE5001B3A50 /* yitu_next_action_audio.mp3 in Resources */,
				420264C6212E8CE5001B3A50 /* yitu_head_up_audio.mp3 in Resources */,
				4202649F212E8CE5001B3A50 /* StringList.plist in Resources */,
				420264D5212E8CE5001B3A50 /* yitu_get_ready_audio.mp3 in Resources */,
				42026448212E8BC4001B3A50 /* AlipaySDK.bundle in Resources */,
				420264C9212E8CE5001B3A50 /* yitu_mouth_open_audio.mp3 in Resources */,
				420264E1212E8CE5001B3A50 /* staging.public.der in Resources */,
				420264D8212E8CE5001B3A50 /* yitu_head_left_audio.mp3 in Resources */,
				421B084F212FFF810041D0F4 /* AMap.bundle in Resources */,
				420264C0212E8CE5001B3A50 /* yitu_head_right_audio.mp3 in Resources */,
				4206F1D1205E0CDB001C21E4 /* Images.xcassets in Resources */,
				420264B4212E8CE5001B3A50 /* 扭头-左.gif in Resources */,
				420264D2212E8CE5001B3A50 /* yitu_time_out_audio.mp3 in Resources */,
				420264BA212E8CE5001B3A50 /* Images.xcassets in Resources */,
				420264AB212E8CE5001B3A50 /* yitu_face_action_normal.gif in Resources */,
				42026490212E8CE5001B3A50 /* APMPPassGuardCtrlBundle.bundle in Resources */,
				420264DE212E8CE5001B3A50 /* yitu_not_user_audio.mp3 in Resources */,
				4202649C212E8CE5001B3A50 /* RegistrationPage.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		422064021FF482A000C81AF1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				420264CE212E8CE5001B3A50 /* yitu_verification_pass_audio.mp3 in Resources */,
				420264AD212E8CE5001B3A50 /* 抬头.gif in Resources */,
				420264B6212E8CE5001B3A50 /* yitu_face_model.bundle in Resources */,
				420264E6212E8CE5001B3A50 /* Image.bundle in Resources */,
				420264A7212E8CE5001B3A50 /* 低头.gif in Resources */,
				420264A1212E8CE5001B3A50 /* 张嘴.gif in Resources */,
				420264B0212E8CE5001B3A50 /* 闭眼.gif in Resources */,
				42026495212E8CE5001B3A50 /* APayRes.bundle in Resources */,
				42026453212E8C01001B3A50 /* UMSocialSDKPromptResources.bundle in Resources */,
				42026498212E8CE5001B3A50 /* VerificationPage.storyboard in Resources */,
				420264C2212E8CE5001B3A50 /* yitu_verification_fail_audio.mp3 in Resources */,
				4202644B212E8BD7001B3A50 /* SGQRCode.bundle in Resources */,
				420264BC212E8CE5001B3A50 /* yitu_eye_close_audio.mp3 in Resources */,
				420264A4212E8CE5001B3A50 /* 扭头-右.gif in Resources */,
				4202644F212E8BE9001B3A50 /* UMSocialSDKResources.bundle in Resources */,
				420264E3212E8CE5001B3A50 /* MBProgressHUD.bundle in Resources */,
				420264DA212E8CE5001B3A50 /* yitu_head_down_audio.mp3 in Resources */,
				420264CB212E8CE5001B3A50 /* yitu_next_action_audio.mp3 in Resources */,
				420264C5212E8CE5001B3A50 /* yitu_head_up_audio.mp3 in Resources */,
				4202649E212E8CE5001B3A50 /* StringList.plist in Resources */,
				420264D4212E8CE5001B3A50 /* yitu_get_ready_audio.mp3 in Resources */,
				42026447212E8BC4001B3A50 /* AlipaySDK.bundle in Resources */,
				420264C8212E8CE5001B3A50 /* yitu_mouth_open_audio.mp3 in Resources */,
				420264E0212E8CE5001B3A50 /* staging.public.der in Resources */,
				420264D7212E8CE5001B3A50 /* yitu_head_left_audio.mp3 in Resources */,
				421B084E212FFF810041D0F4 /* AMap.bundle in Resources */,
				420264BF212E8CE5001B3A50 /* yitu_head_right_audio.mp3 in Resources */,
				422064061FF482A000C81AF1 /* Images.xcassets in Resources */,
				420264B3212E8CE5001B3A50 /* 扭头-左.gif in Resources */,
				420264D1212E8CE5001B3A50 /* yitu_time_out_audio.mp3 in Resources */,
				420264B9212E8CE5001B3A50 /* Images.xcassets in Resources */,
				420264AA212E8CE5001B3A50 /* yitu_face_action_normal.gif in Resources */,
				4202648F212E8CE5001B3A50 /* APMPPassGuardCtrlBundle.bundle in Resources */,
				420264DD212E8CE5001B3A50 /* yitu_not_user_audio.mp3 in Resources */,
				4202649B212E8CE5001B3A50 /* RegistrationPage.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export NODE_BINARY=node\n../node_modules/react-native/scripts/react-native-xcode.sh\n";
		};
		4206F1D4205E0CDB001C21E4 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export NODE_BINARY=node\n../node_modules/react-native/scripts/react-native-xcode.sh";
		};
		4206F1D8205E0CDB001C21E4 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "APP_PATH=\"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\"\n\n# This script loops through the frameworks embedded in the application and\n# removes unused architectures.\nfind \"$APP_PATH\" -name '*.framework' -type d | while read -r FRAMEWORK\ndo\nFRAMEWORK_EXECUTABLE_NAME=$(defaults read \"$FRAMEWORK/Info.plist\" CFBundleExecutable)\nFRAMEWORK_EXECUTABLE_PATH=\"$FRAMEWORK/$FRAMEWORK_EXECUTABLE_NAME\"\necho \"Executable is $FRAMEWORK_EXECUTABLE_PATH\"\n\nEXTRACTED_ARCHS=()\n\nfor ARCH in $ARCHS\ndo\necho \"Extracting $ARCH from $FRAMEWORK_EXECUTABLE_NAME\"\nlipo -extract \"$ARCH\" \"$FRAMEWORK_EXECUTABLE_PATH\" -o \"$FRAMEWORK_EXECUTABLE_PATH-$ARCH\"\nEXTRACTED_ARCHS+=(\"$FRAMEWORK_EXECUTABLE_PATH-$ARCH\")\ndone\n\necho \"Merging extracted architectures: ${ARCHS}\"\nlipo -o \"$FRAMEWORK_EXECUTABLE_PATH-merged\" -create \"${EXTRACTED_ARCHS[@]}\"\nrm \"${EXTRACTED_ARCHS[@]}\"\n\necho \"Replacing original executable with thinned version\"\nrm \"$FRAMEWORK_EXECUTABLE_PATH\"\nmv \"$FRAMEWORK_EXECUTABLE_PATH-merged\" \"$FRAMEWORK_EXECUTABLE_PATH\"\n\ndone";
		};
		422064081FF482A000C81AF1 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export NODE_BINARY=node\n../node_modules/react-native/scripts/react-native-xcode.sh";
		};
		4220640C1FF482A000C81AF1 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "APP_PATH=\"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\"\n\n# This script loops through the frameworks embedded in the application and\n# removes unused architectures.\nfind \"$APP_PATH\" -name '*.framework' -type d | while read -r FRAMEWORK\ndo\nFRAMEWORK_EXECUTABLE_NAME=$(defaults read \"$FRAMEWORK/Info.plist\" CFBundleExecutable)\nFRAMEWORK_EXECUTABLE_PATH=\"$FRAMEWORK/$FRAMEWORK_EXECUTABLE_NAME\"\necho \"Executable is $FRAMEWORK_EXECUTABLE_PATH\"\n\nEXTRACTED_ARCHS=()\n\nfor ARCH in $ARCHS\ndo\necho \"Extracting $ARCH from $FRAMEWORK_EXECUTABLE_NAME\"\nlipo -extract \"$ARCH\" \"$FRAMEWORK_EXECUTABLE_PATH\" -o \"$FRAMEWORK_EXECUTABLE_PATH-$ARCH\"\nEXTRACTED_ARCHS+=(\"$FRAMEWORK_EXECUTABLE_PATH-$ARCH\")\ndone\n\necho \"Merging extracted architectures: ${ARCHS}\"\nlipo -o \"$FRAMEWORK_EXECUTABLE_PATH-merged\" -create \"${EXTRACTED_ARCHS[@]}\"\nrm \"${EXTRACTED_ARCHS[@]}\"\n\necho \"Replacing original executable with thinned version\"\nrm \"$FRAMEWORK_EXECUTABLE_PATH\"\nmv \"$FRAMEWORK_EXECUTABLE_PATH-merged\" \"$FRAMEWORK_EXECUTABLE_PATH\"\n\ndone";
		};
		42BEEEE21FEBD3F900C5AC18 /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "APP_PATH=\"${TARGET_BUILD_DIR}/${WRAPPER_NAME}\"\n\n# This script loops through the frameworks embedded in the application and\n# removes unused architectures.\nfind \"$APP_PATH\" -name '*.framework' -type d | while read -r FRAMEWORK\ndo\nFRAMEWORK_EXECUTABLE_NAME=$(defaults read \"$FRAMEWORK/Info.plist\" CFBundleExecutable)\nFRAMEWORK_EXECUTABLE_PATH=\"$FRAMEWORK/$FRAMEWORK_EXECUTABLE_NAME\"\necho \"Executable is $FRAMEWORK_EXECUTABLE_PATH\"\n\nEXTRACTED_ARCHS=()\n\nfor ARCH in $ARCHS\ndo\necho \"Extracting $ARCH from $FRAMEWORK_EXECUTABLE_NAME\"\nlipo -extract \"$ARCH\" \"$FRAMEWORK_EXECUTABLE_PATH\" -o \"$FRAMEWORK_EXECUTABLE_PATH-$ARCH\"\nEXTRACTED_ARCHS+=(\"$FRAMEWORK_EXECUTABLE_PATH-$ARCH\")\ndone\n\necho \"Merging extracted architectures: ${ARCHS}\"\nlipo -o \"$FRAMEWORK_EXECUTABLE_PATH-merged\" -create \"${EXTRACTED_ARCHS[@]}\"\nrm \"${EXTRACTED_ARCHS[@]}\"\n\necho \"Replacing original executable with thinned version\"\nrm \"$FRAMEWORK_EXECUTABLE_PATH\"\nmv \"$FRAMEWORK_EXECUTABLE_PATH-merged\" \"$FRAMEWORK_EXECUTABLE_PATH\"\n\ndone\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				42026488212E8CE5001B3A50 /* RCTAPPayAssistEx.m in Sources */,
				13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4206F147205E0CDB001C21E4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4202648A212E8CE5001B3A50 /* RCTAPPayAssistEx.m in Sources */,
				4206F149205E0CDB001C21E4 /* AppDelegate.m in Sources */,
				4206F15C205E0CDB001C21E4 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4220637E1FF482A000C81AF1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				42026489212E8CE5001B3A50 /* RCTAPPayAssistEx.m in Sources */,
				422063801FF482A000C81AF1 /* AppDelegate.m in Sources */,
				422063921FF482A000C81AF1 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		420264E9212E8E49001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = WeChat;
			targetProxy = 420264E8212E8E49001B3A50 /* PBXContainerItemProxy */;
		};
		420264EB212E8E49001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = StatusBarUtil;
			targetProxy = 420264EA212E8E49001B3A50 /* PBXContainerItemProxy */;
		};
		420264ED212E8E49001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SocialShare;
			targetProxy = 420264EC212E8E49001B3A50 /* PBXContainerItemProxy */;
		};
		420264EF212E8E49001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SGQRCode;
			targetProxy = 420264EE212E8E49001B3A50 /* PBXContainerItemProxy */;
		};
		420264F1212E8E49001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Push;
			targetProxy = 420264F0212E8E49001B3A50 /* PBXContainerItemProxy */;
		};
		420264F3212E8E49001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = MBProgressHUD;
			targetProxy = 420264F2212E8E49001B3A50 /* PBXContainerItemProxy */;
		};
		420264F5212E8E49001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = JPush;
			targetProxy = 420264F4212E8E49001B3A50 /* PBXContainerItemProxy */;
		};
		420264F7212E8E49001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = ImageCropPicker;
			targetProxy = 420264F6212E8E49001B3A50 /* PBXContainerItemProxy */;
		};
		420264F9212E8E49001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = DeviceInfo;
			targetProxy = 420264F8212E8E49001B3A50 /* PBXContainerItemProxy */;
		};
		420264FB212E8E49001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = ConfigStorage;
			targetProxy = 420264FA212E8E49001B3A50 /* PBXContainerItemProxy */;
		};
		420264FD212E8E49001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Captcha;
			targetProxy = 420264FC212E8E49001B3A50 /* PBXContainerItemProxy */;
		};
		420264FF212E8E49001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AppTool;
			targetProxy = 420264FE212E8E49001B3A50 /* PBXContainerItemProxy */;
		};
		42026501212E8E49001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AMap;
			targetProxy = 42026500212E8E49001B3A50 /* PBXContainerItemProxy */;
		};
		42026503212E8E49001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AliyunSLS;
			targetProxy = 42026502212E8E49001B3A50 /* PBXContainerItemProxy */;
		};
		42026505212E8E49001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AliPay;
			targetProxy = 42026504212E8E49001B3A50 /* PBXContainerItemProxy */;
		};
		42026507212E8E49001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AliOSS;
			targetProxy = 42026506212E8E49001B3A50 /* PBXContainerItemProxy */;
		};
		42026509212E8E49001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AFNetworking;
			targetProxy = 42026508212E8E49001B3A50 /* PBXContainerItemProxy */;
		};
		4202650B212E938F001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = WeChat;
			targetProxy = 4202650A212E938F001B3A50 /* PBXContainerItemProxy */;
		};
		4202650D212E938F001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = StatusBarUtil;
			targetProxy = 4202650C212E938F001B3A50 /* PBXContainerItemProxy */;
		};
		4202650F212E938F001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SocialShare;
			targetProxy = 4202650E212E938F001B3A50 /* PBXContainerItemProxy */;
		};
		42026511212E938F001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SGQRCode;
			targetProxy = 42026510212E938F001B3A50 /* PBXContainerItemProxy */;
		};
		42026513212E938F001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Push;
			targetProxy = 42026512212E938F001B3A50 /* PBXContainerItemProxy */;
		};
		42026515212E938F001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = MBProgressHUD;
			targetProxy = 42026514212E938F001B3A50 /* PBXContainerItemProxy */;
		};
		42026517212E938F001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = JPush;
			targetProxy = 42026516212E938F001B3A50 /* PBXContainerItemProxy */;
		};
		42026519212E938F001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = ImageCropPicker;
			targetProxy = 42026518212E938F001B3A50 /* PBXContainerItemProxy */;
		};
		4202651B212E938F001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = DeviceInfo;
			targetProxy = 4202651A212E938F001B3A50 /* PBXContainerItemProxy */;
		};
		4202651D212E938F001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = ConfigStorage;
			targetProxy = 4202651C212E938F001B3A50 /* PBXContainerItemProxy */;
		};
		4202651F212E938F001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Captcha;
			targetProxy = 4202651E212E938F001B3A50 /* PBXContainerItemProxy */;
		};
		42026521212E938F001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AppTool;
			targetProxy = 42026520212E938F001B3A50 /* PBXContainerItemProxy */;
		};
		42026523212E938F001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AMap;
			targetProxy = 42026522212E938F001B3A50 /* PBXContainerItemProxy */;
		};
		42026525212E938F001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AliyunSLS;
			targetProxy = 42026524212E938F001B3A50 /* PBXContainerItemProxy */;
		};
		42026527212E938F001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AliPay;
			targetProxy = 42026526212E938F001B3A50 /* PBXContainerItemProxy */;
		};
		42026529212E938F001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AliOSS;
			targetProxy = 42026528212E938F001B3A50 /* PBXContainerItemProxy */;
		};
		4202652B212E938F001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AFNetworking;
			targetProxy = 4202652A212E938F001B3A50 /* PBXContainerItemProxy */;
		};
		4202652D212E93AD001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = WeChat;
			targetProxy = 4202652C212E93AD001B3A50 /* PBXContainerItemProxy */;
		};
		4202652F212E93AD001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = StatusBarUtil;
			targetProxy = 4202652E212E93AD001B3A50 /* PBXContainerItemProxy */;
		};
		42026531212E93AD001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SocialShare;
			targetProxy = 42026530212E93AD001B3A50 /* PBXContainerItemProxy */;
		};
		42026533212E93AD001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SGQRCode;
			targetProxy = 42026532212E93AD001B3A50 /* PBXContainerItemProxy */;
		};
		42026535212E93AD001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Push;
			targetProxy = 42026534212E93AD001B3A50 /* PBXContainerItemProxy */;
		};
		42026537212E93AD001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = MBProgressHUD;
			targetProxy = 42026536212E93AD001B3A50 /* PBXContainerItemProxy */;
		};
		42026539212E93AD001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = JPush;
			targetProxy = 42026538212E93AD001B3A50 /* PBXContainerItemProxy */;
		};
		4202653B212E93AD001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = ImageCropPicker;
			targetProxy = 4202653A212E93AD001B3A50 /* PBXContainerItemProxy */;
		};
		4202653D212E93AD001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = DeviceInfo;
			targetProxy = 4202653C212E93AD001B3A50 /* PBXContainerItemProxy */;
		};
		4202653F212E93AD001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = ConfigStorage;
			targetProxy = 4202653E212E93AD001B3A50 /* PBXContainerItemProxy */;
		};
		42026541212E93AD001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Captcha;
			targetProxy = 42026540212E93AD001B3A50 /* PBXContainerItemProxy */;
		};
		42026543212E93AD001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AppTool;
			targetProxy = 42026542212E93AD001B3A50 /* PBXContainerItemProxy */;
		};
		42026545212E93AD001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AMap;
			targetProxy = 42026544212E93AD001B3A50 /* PBXContainerItemProxy */;
		};
		42026547212E93AD001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AliyunSLS;
			targetProxy = 42026546212E93AD001B3A50 /* PBXContainerItemProxy */;
		};
		42026549212E93AD001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AliPay;
			targetProxy = 42026548212E93AD001B3A50 /* PBXContainerItemProxy */;
		};
		4202654B212E93AD001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AliOSS;
			targetProxy = 4202654A212E93AD001B3A50 /* PBXContainerItemProxy */;
		};
		4202654D212E93AD001B3A50 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AFNetworking;
			targetProxy = 4202654C212E93AD001B3A50 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CODE_SIGN_ENTITLEMENTS = yunxi.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution: Guangzhou Zhujiang Brewery Group Co., Ltd.";
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = QR69XCVFDP;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/../../rn-yunxi/ios/**",
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
				);
				GCC_INPUT_FILETYPE = sourcecode.c.objc;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SRCROOT)/BaseRN/constants/yunxi/PrefixHeader.pch";
				HEADER_SEARCH_PATHS = "$(PROJECT_DIR)/$(PROJECT_NAME)/PrefixHeader.pch";
				INFOPLIST_FILE = "$(SRCROOT)/BaseRN/constants/yunxi/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
					"$(PROJECT_DIR)",
				);
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.pearlriver.b2b.ios.enterprise;
				PRODUCT_NAME = yunxi;
				PROVISIONING_PROFILE = "d10065c9-bf41-4145-a501-3a76d549059c";
				PROVISIONING_PROFILE_SPECIFIER = "Pearlriver_B2b_ Enterprise";
				USE_HEADERMAP = YES;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CODE_SIGN_ENTITLEMENTS = yunxi.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution: Guangzhou Zhujiang Brewery Group Co., Ltd.";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution: Guangzhou Zhujiang Brewery Group Co., Ltd.";
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QR69XCVFDP;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/../../rn-yunxi/ios/**",
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
				);
				GCC_INPUT_FILETYPE = sourcecode.c.objc;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SRCROOT)/BaseRN/constants/yunxi/PrefixHeader.pch";
				HEADER_SEARCH_PATHS = "$(PROJECT_DIR)/$(PROJECT_NAME)/PrefixHeader.pch";
				INFOPLIST_FILE = "$(SRCROOT)/BaseRN/constants/yunxi/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
					"$(PROJECT_DIR)",
				);
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.pearlriver.b2b.ios.enterprise;
				PRODUCT_NAME = yunxi;
				PROVISIONING_PROFILE = "43f5956a-094a-4479-85d4-843ece1d1fe2";
				PROVISIONING_PROFILE_SPECIFIER = PearlRiver_B2B_2021;
				USE_HEADERMAP = YES;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		4206F1DA205E0CDB001C21E4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CODE_SIGN_ENTITLEMENTS = pro.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution: Guangzhou Zhujiang Brewery Group Co., Ltd.";
				CURRENT_PROJECT_VERSION = 22;
				DEAD_CODE_STRIPPING = NO;
				DEVELOPMENT_TEAM = QR69XCVFDP;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/../../rn-yunxi/ios/**",
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
				);
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SRCROOT)/BaseRN/constants/pro/PrefixHeader.pch";
				HEADER_SEARCH_PATHS = "$(PROJECT_DIR)/$(PROJECT_NAME)/PrefixHeader.pch";
				INFOPLIST_FILE = "$(SRCROOT)/BaseRN/constants/pro/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
					"$(PROJECT_DIR)",
				);
				MARKETING_VERSION = 1.1.8;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.pearlriver.b2b.ios.enterprise;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "a938b539-b19c-40c5-a53e-5634761cc483";
				PROVISIONING_PROFILE_SPECIFIER = "Pearlriver_B2b_ Enterprise";
				USE_HEADERMAP = YES;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		4206F1DB205E0CDB001C21E4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CODE_SIGN_ENTITLEMENTS = pro.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution: Guangzhou Zhujiang Brewery Group Co., Ltd.";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution: Guangzhou Zhujiang Brewery Group Co., Ltd.";
				CURRENT_PROJECT_VERSION = 22;
				DEVELOPMENT_TEAM = QR69XCVFDP;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/../../rn-yunxi/ios/**",
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
				);
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SRCROOT)/BaseRN/constants/pro/PrefixHeader.pch";
				HEADER_SEARCH_PATHS = "$(PROJECT_DIR)/$(PROJECT_NAME)/PrefixHeader.pch";
				INFOPLIST_FILE = "$(SRCROOT)/BaseRN/constants/pro/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
					"$(PROJECT_DIR)",
				);
				MARKETING_VERSION = 1.1.8;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.pearlriver.b2b.ios.enterprise;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "43f5956a-094a-4479-85d4-843ece1d1fe2";
				PROVISIONING_PROFILE_SPECIFIER = PearlRiver_B2B_2021;
				USE_HEADERMAP = YES;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		4212209021807752004B15D1 /* Staging */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODEPUSH_KEY = "8wqAYU_zjSmd50abPnXRC2xAlqWef3ff89f9-ede8-4c9b-aa19-edbc44897b18";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Staging;
		};
		4212209121807752004B15D1 /* Staging */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CODE_SIGN_ENTITLEMENTS = yunxi.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution: Guangzhou Zhujiang Brewery Group Co., Ltd.";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution: Guangzhou Zhujiang Brewery Group Co., Ltd.";
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/Release$(EFFECTIVE_PLATFORM_NAME)";
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = QR69XCVFDP;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/../../rn-yunxi/ios/**",
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
				);
				GCC_INPUT_FILETYPE = sourcecode.c.objc;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SRCROOT)/BaseRN/constants/yunxi/PrefixHeader.pch";
				HEADER_SEARCH_PATHS = "$(PROJECT_DIR)/$(PROJECT_NAME)/PrefixHeader.pch";
				INFOPLIST_FILE = "$(SRCROOT)/BaseRN/constants/yunxi/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
					"$(PROJECT_DIR)",
				);
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.pearlriver.b2b.ios.enterprise;
				PRODUCT_NAME = yunxi;
				PROVISIONING_PROFILE = "43f5956a-094a-4479-85d4-843ece1d1fe2";
				PROVISIONING_PROFILE_SPECIFIER = "Pearlriver_B2b_ Enterprise";
				USE_HEADERMAP = YES;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Staging;
		};
		4212209221807752004B15D1 /* Staging */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CODE_SIGN_ENTITLEMENTS = qa.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution: Guangzhou Zhujiang Brewery Group Co., Ltd.";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution: Guangzhou Zhujiang Brewery Group Co., Ltd.";
				CURRENT_PROJECT_VERSION = 18;
				DEVELOPMENT_TEAM = QR69XCVFDP;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/../../rn-yunxi/ios/**",
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
				);
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SRCROOT)/BaseRN/constants/qa/PrefixHeader.pch";
				HEADER_SEARCH_PATHS = "$(PROJECT_DIR)/$(PROJECT_NAME)/PrefixHeader.pch";
				INFOPLIST_FILE = "$(SRCROOT)/BaseRN/constants/qa/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
					"$(PROJECT_DIR)",
				);
				MARKETING_VERSION = 1.1.7;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.pearlriver.b2b.ios.enterprise;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "43f5956a-094a-4479-85d4-843ece1d1fe2";
				PROVISIONING_PROFILE_SPECIFIER = "Pearlriver_B2b_ Enterprise";
				USE_HEADERMAP = YES;
				VALIDATE_WORKSPACE = YES;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Staging;
		};
		4212209321807752004B15D1 /* Staging */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CODE_SIGN_ENTITLEMENTS = pro.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution: Guangzhou Zhujiang Brewery Group Co., Ltd.";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution: Guangzhou Zhujiang Brewery Group Co., Ltd.";
				CURRENT_PROJECT_VERSION = 22;
				DEVELOPMENT_TEAM = QR69XCVFDP;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/../../rn-yunxi/ios/**",
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
				);
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SRCROOT)/BaseRN/constants/pro/PrefixHeader.pch";
				HEADER_SEARCH_PATHS = "$(PROJECT_DIR)/$(PROJECT_NAME)/PrefixHeader.pch";
				INFOPLIST_FILE = "$(SRCROOT)/BaseRN/constants/pro/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
					"$(PROJECT_DIR)",
				);
				MARKETING_VERSION = 1.1.8;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.pearlriver.b2b.ios.enterprise;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "43f5956a-094a-4479-85d4-843ece1d1fe2";
				PROVISIONING_PROFILE_SPECIFIER = "Pearlriver_B2b_ Enterprise";
				USE_HEADERMAP = YES;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Staging;
		};
		4220640E1FF482A000C81AF1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CODE_SIGN_ENTITLEMENTS = qa.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution: Guangzhou Zhujiang Brewery Group Co., Ltd.";
				CURRENT_PROJECT_VERSION = 18;
				DEAD_CODE_STRIPPING = NO;
				DEVELOPMENT_TEAM = QR69XCVFDP;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/../../rn-yunxi/ios/**",
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
				);
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SRCROOT)/BaseRN/constants/qa/PrefixHeader.pch";
				HEADER_SEARCH_PATHS = "$(PROJECT_DIR)/$(PROJECT_NAME)/PrefixHeader.pch";
				INFOPLIST_FILE = "$(SRCROOT)/BaseRN/constants/qa/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
					"$(PROJECT_DIR)",
				);
				MARKETING_VERSION = 1.1.7;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.pearlriver.b2b.ios.enterprise;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "a938b539-b19c-40c5-a53e-5634761cc483";
				PROVISIONING_PROFILE_SPECIFIER = "Pearlriver_B2b_ Enterprise";
				USE_HEADERMAP = YES;
				VALIDATE_WORKSPACE = YES;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		4220640F1FF482A000C81AF1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CODE_SIGN_ENTITLEMENTS = qa.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution: Guangzhou Zhujiang Brewery Group Co., Ltd.";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution: Guangzhou Zhujiang Brewery Group Co., Ltd.";
				CURRENT_PROJECT_VERSION = 18;
				DEVELOPMENT_TEAM = QR69XCVFDP;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/../../rn-yunxi/ios/**",
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
				);
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SRCROOT)/BaseRN/constants/qa/PrefixHeader.pch";
				HEADER_SEARCH_PATHS = "$(PROJECT_DIR)/$(PROJECT_NAME)/PrefixHeader.pch";
				INFOPLIST_FILE = "$(SRCROOT)/BaseRN/constants/qa/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(PROJECT_DIR)/BaseRN/APPayAssistex/lib_release",
					"$(PROJECT_DIR)",
				);
				MARKETING_VERSION = 1.1.7;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.pearlriver.b2b.ios.enterprise;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "43f5956a-094a-4479-85d4-843ece1d1fe2";
				PROVISIONING_PROFILE_SPECIFIER = PearlRiver_B2B_2021;
				USE_HEADERMAP = YES;
				VALIDATE_WORKSPACE = YES;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODEPUSH_KEY = "8wqAYU_zjSmd50abPnXRC2xAlqWef3ff89f9-ede8-4c9b-aa19-edbc44897b18";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODEPUSH_KEY = "8wqAYU_zjSmd50abPnXRC2xAlqWef3ff89f9-ede8-4c9b-aa19-edbc44897b18";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "yunxi" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
				4212209121807752004B15D1 /* Staging */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4206F1D9205E0CDB001C21E4 /* Build configuration list for PBXNativeTarget "pro" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4206F1DA205E0CDB001C21E4 /* Debug */,
				4206F1DB205E0CDB001C21E4 /* Release */,
				4212209321807752004B15D1 /* Staging */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4220640D1FF482A000C81AF1 /* Build configuration list for PBXNativeTarget "qa" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4220640E1FF482A000C81AF1 /* Debug */,
				4220640F1FF482A000C81AF1 /* Release */,
				4212209221807752004B15D1 /* Staging */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "BaseRN" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
				4212209021807752004B15D1 /* Staging */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
