#目录结构
| 作者 | 版本 | 日期 |
|:----:|:----:|:----:|
| 劳健锋 | v1 | 2017/04/14 |
| 劳健锋 | v2 | 2017/05/30 |
```
│  index.android.js （android进口会引用js/Root.js）
│  index.ios.js （android进口会引用js/Root.js）
│  package.json （React Native npm 配置）
├─android (android工程)
│
├─ios (ios工程)
│
├─doc (使用文档)
│
├─rn-yunxi （React Native 通用项目，npm本地安装，方便修改和引用）
│  │  CommonSearchBar.js (库模块导出文件)
│  │  package.json
│  │
│  └─lib (目录)
│      │
│      ├─template
│      │      ReactNavComponent.js (ReactNavigation抽象类)
│      │
│      └─widget (共用组件)
│
└─shop (业务项目)
    │  Global.js (全局样式,常量,配置声明)
    │  Root.js (程序入口)
    │  Router.js (路由配置)
    │
    ├─app-widget (项目组件目录)
    │  │  CommonSearchBar.js (组件声明)
    │  │
    │  └─ ... (更多项目组件)
    │
    ├─module (页务模块)
    │  ├─category
    │  │      BakCategoryList.js
    │  │      分类模块.md
    │  │
    │  ├─good
    │  │      GoodDetail.js
    │  │      GoodListItem.js
    │  │      商品模块.md
    │  │
    │  ├─home
    │  │      Home.js
    │  │      主页模块.md
    │  │
    │  ├─img (图片资源目录)
    │  ├─merchant
    │  │  │  Merchant.js
    │  │  │  商家模块.md
    │  │  │
    │  │  └─test (测试数据目录)
    │  │          test.json
    │  │
    │  ├─mine
    │  │      MineOld.js
    │  │      ModifyUserInfo.js
    │  │      我的模块.md
    │  │
    │  ├─order
    │  │  │  BakLogistic.js
    │  │  │  BakOrder.js
    │  │  │  BakOrderDetail.js
    │  │  │  BakOrderItem.js
    │  │  │  订单模块.md
    │  │  │
    │  │  └─test
    │  │          test.json
    │  │
    │  ├─pay
    │  │      PaySuccess.js
    │  │      支付.md
    │  │
    │  ├─shopcart
    │  │  │  ShopCartBak.js
    │  │  │  ShopCartItem.js
    │  │  │  购物车.md
    │  │  │
    │  │  └─test
    │  │          test.json
    │  │
    │  └─welcome
    │          Splash.js
    │
    └─redux (redux全局状态管理)
    │  │  Store.js (全局状态)
    │  │  ActionTypeConf.js (事件声明)
    │  ├─actions (事件发生)
    │  ├─middleware (中间件)
    │  └─reducers (处理事件,更新store)
    │
    └─util (工具类目录)
            Api.js



```

### 项目规范说明

- 组件目录：小写字母命名，多个单词以 - 分隔，例如：upload-image
- 组件命名：首字母大写并以驼高式命名，例如：UploadImage.js，如果组件目录只有一个文件，可以用index.js
- 业务模块：项目目录>module>...
- rn-yunxi：这个是收集通用的组件和常用方法，通用本地安装，方便模块导入使用。


#### 业务模块目录

建议在目录下新建一个以 **`模块中文名称.md`** 空文件

#### 组件版本
```
    "antd-mobile": "^1.1.3",
    "events": "^1.1.1",
    "rc-form": "^1.3.1",
    "react": "16.0.0-alpha.6",
    "react-native": "^0.44.0",
    "react-native-accordion": "^1.0.1",
    "react-native-animatable": "^1.2.0",
    "react-native-device-info": "^0.10.2",
    "react-native-drawer": "^2.3.0",
    "react-native-fetch-polyfill": "^1.1.2",
    "react-native-inputscrollview": "^2.0.1",
    "react-native-looped-carousel": "^0.1.5",
    "react-native-swiper": "^1.5.4",
    "react-navigation": "^1.0.0-beta.10",
    "react-redux": "^5.0.4",
    "react_native_countdowntimer": "^1.0.2",
    "redux": "^3.6.0",
    "redux-actions": "^2.0.2",
    "redux-thunk": "^2.2.0",
    "rn-yunxi": "file:./rn-yunxi"
```

### 开发流程
> 现在ReactNative 是使用ReactNavigation做为路由组件 [教程](https://reactnavigation.org/docs/intro/headers) ,[简单中文教程](http://www.jianshu.com/p/2ce08b92cf60)

开发流程

* Step1 新建业务页面(组件要求继承rn-yunxi下的ReactNavComponent),例如下面创建A模块
```
import React, {Component} from 'react';
import {
    StyleSheet,
    View,
    ...
} from 'react-native';
import {ReactNavComponent} from 'rn-yunxi';
const {width, height} = Dimensions.get('window');

//模块声名并导出
export default class A extends ReactNavComponent {
    static navigationOptions = {//路由配置
        title: '',
    };
    //属性声名
    static propTypes = {};
    //默认属性
    static defaultProps = {};
    //构造函数
    constructor(props) {
        super(props);
         //状态机变量声明
         this.state={};
    }

    //渲染
    render() {
        return (
            <View style={styles.container}>

            </View>
        );
    }
};
```

* Step2 配置路由,打开Router.js,引用你创建的模块
```
import A from '模块位置';

const AppStack = StackNavigator(
    {
       ...其它模块声明

        A:{//A模块声明
            screen:A,
        }
    },{
        initialRouteName:"Tab"//如果要马上运行页面,把初始化路由改成A
    }
);
```
* Step3 最后编写页面写逻辑

#### 自定义组件
- 创建AppWidget(业务组件)
    - Step1:业务项目app-widget下创建组件目录。
    - Step2:组件目录下创建组件名(如果导出模块只有一个,只要index就可以)。
    - Step3:在app-widget下index.js声明新建的组件。
    ```
    import ListItem from'./list-item/ListItem';//引用模块
    import ListInput from'./list-item/ListInput';
    import Stepper from'./stepper';
    class AppWidget{
        static ListItem=ListItem;//声明模块
        static ListInput=ListInput;
        static Stepper=Stepper;
    }
    export default AppWidget;//导出AppWidget
    ```
    - Step4:业务模块引用AppWidget组件
    ```
    import AppWidget from 'app-widget(模块位置)';
    const ListItem=AppWidget.ListItem;//声明组件
    ...
    class A extend ReactNavComponent{
        render(){
            return(
                 ...
                  <ListItem/>//使用组件
                 ...
            )
        }
     }
    ...
    ```
- 创建通用组件
    - Step1:在rn-yunxi>lib>widget下创建组件目录。
    - Step2、Step3同上。
    - Step4:npm install rn-yunxi 更新node_module下rn-yunxi代码。
    - Step5:业务模块引用Widget组件。
    ```
     import {ReactNavComponent, Widget} from 'rn-yunxi';
     const Button=Widget.Button;//声明组件
    ```

#### 全局常量、样式、配置 Global.js
>UI设计声明通用样式,字体大小,常量...

```
/**
 * 全局常量
 */
class Constant {
    //字体
    static fontSizeXSmall = 9;//超小号字体
    static fontSizeSmall = 12;//小号
    static fontSizeNormal = 14;//默认
    static fontSizeCaption = 18;//标题
    static fontSizeLarge = 20;//大号
    static fontSizeHeader = 24;//头部
    //颜色
    static colorPrimary = '#9900ff';//主体颜色
    static colorSecondary = '#bb00ff';//主体颜色
    static colorPrimaryTap = '#9900ff99';
    static colorDivider = '#ddd';//分隔线
    static colorDefaultBackground = '#fff';
    static colorTxtDefault = '#010101';
    //尺寸
    static sizeDividerNormal = 0.5;//分隔线大小
    static sizeDividerLarge = 1;
}
/**
 * 全局样式
 */
class GlobalStyle {
    //样式
    static styleTxtDefault = {
        fontSize: Constant.fontSizeNormal,
        color: Constant.colorTxtDefault,
    };
}
/**
 * 全局配置
 */
class Config {
    static HOST = "";
}

global.Config = Config;
global.Constant = Constant;
global.GlobalStyle = GlobalStyle;
```

引用全局对象,例如样式:任何地方GlobalStyle.属性名字就可以了.

```
<Text style={[GlobalStyle.styleTxtDefault]}>{title}</Text>
```
全局打印调试Log,禁止用console.log打印
```
Log(params);
```
接口层Api
```
Api.xxx().then(data=>{}).catch(err=>{});
```

#### 修改antd-mobile组件默认样式
>通用写文件覆盖node_modules>antd-mobile的样式实现
* 修改theme.js 覆盖要修改的字段[参考链接](https://github.com/ant-design/ant-design-mobile/blob/master/components/style/themes/default.tsx)
* 命令行运行`npm run theme`

#### 异步处理
>建议用 async/await和Promise,取消回调函数嵌套方式
[Promise参考链接](http://es6.ruanyifeng.com/#docs/promise)
[async/await参考链接](http://es6.ruanyifeng.com/#docs/async)
#### 快速定位
>建议在关键位置加上//TODO 注释方便定位代码

```
onNavigationStateChange(prevState, currentState,action) {
    // console.log('current state',currentState,action);
    const currentScreen = getCurrentRouteName(currentState);
    const prevScreen = getCurrentRouteName(prevState);
    DeviceEventEmitter.emit(Config.EVENT_SCREEN_CHANGE, {currentScreen: currentScreen, prevScreen: prevScreen});
    // console.log('onNavigationStateChange',currentScreen,prevScreen);
    //TODO 页面跳转埋点
}
componentWillMount(){
    this.authRequireListener=DeviceEventEmitter.addListener('auth_require',()=>{
        //TODO 服务器返回状态码401统一处理
        this.navigator&&this.navigator.dispatch({ type: NavigationActions.NAVIGATE, routeName:'UserLogin' });
    });
}

```
码定位命名规范
* 带流程代码定位以名称+事件 例如:`//TODO 登陆调用` `//TODO 登陆接口`
* 唯一代码定位以详细注意说明 例如:`TODO 服务器返回状态码401统一处理`