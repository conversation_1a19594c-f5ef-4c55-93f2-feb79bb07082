#React Navigation使用文档
> 官方文档[教程](https://reactnavigation.org/docs/intro/headers)

现在页面都要继承于ReactNavComponent.js
```
/**
 * Created by lao.jianfeng on 2017/4/11.
 */
import React, {Component} from 'react';
import {
    StyleSheet,
    Dimensions
} from 'react-native';

const {width, height} = Dimensions.get('window');
import {NavigationActions} from 'react-navigation';

//模块声名并导出
export default class ReactNavigationComponent extends Component {
    //构造函数
    constructor(props) {
        super(props);
    }

    getNavigationAction(){
        return NavigationActions;
    }

    navigate(...params) {
        let {navigate} = this.props.navigation;
        return navigate(...params);
    }

    goBack(...param) {
        let {goBack} = this.props.navigation;
        return goBack(...param);
    }

    dispatch(...param) {
        let {dispatch} = this.props.navigation;
        return dispatch(...param);
    }

    getNavState() {
        let {state} = this.props.navigation;
        return state;
    }

    setParams(params){
        let {setParams} = this.props.navigation;
        setParams(params);
    }
};

```
* 增加了`navigate` `goBack` `dispatch` `getNavState` `setParams` 方法,这些方法都是可以在this.props找到
    * `navigate` 
    ```
    goToSomePage=()=>{
        this.navigate('路由名','参数对象')
    }
    ```
    * `goBack` 
     ```
    someFunction=()=>{
        this.goBack()
        //或者增加参数
        this.goBack('参数对象')
    }
     ```
    * `dispatch`
    ```
    someFunction=()=>{
        this.dispatch('参数对象')
    }
     ```
     * `getNavState`
     ```
     someFunction=()=>{
        let {params}=this.getNavState();//获取转跳页面参数对象
     }
     ```
     * `setParams`
     ```
     someFunction=()=>{
        this.setParams('参数对象')
     }
     ```
     
###注意
1. 路由跳转的位置都要增加 `//TODO 目标页面js文件名` 
2. 其它内部组件要做跳转也继承 `ReactNavigationComponent` 使用组都<SomeComponent `navigation={this.props.navigation}`/>
3. 路由事件都可以在 `Router.js` AppStack.router.getStateForAction方法重载事件处理,如果返回null就不处理事件.[参考文档](https://reactnavigation.org/docs/routers/) 