#Redux App使用

| 作者 | 版本 | 日期 |
|:----:|:----:|:----:|
| 劳健锋 | v1 | 2017/05/30 |

Redux应用目录结构


```
│  index.android.js （android进口会引用js/Root.js）
│  index.ios.js （android进口会引用js/Root.js）
│  package.json （React Native npm 配置）
├─android (android工程)
├─ios (ios工程)
├─rn-yun<PERSON> （React Native 通用项目，npm本地安装，方便修改和引用）
└─shop (业务项目)
    │  Global.js (全局样式,常量,配置声明)
    │  Root.js (程序入口)
    │  Router.js (路由配置)
    ├─app-widget (项目组件目录)
    ├─module (页务模块)
    └─redux (redux全局状态管理)
    │  │  Store.js (全局状态)
    │  │  ActionTypeConf.js (事件声明)
    │  ├─actions (事件发生)
    │  ├─middleware (中间件)
    │  └─reducers (处理事件,更新store)
    └─util (工具类目录)
```
学习资料
* [Redux 中文文档](http://cn.redux.js.org/index.html)
* [Redux性能优化之重复渲染](http://www.jianshu.com/p/b9b311f04707)
* [ReactNative-Redux实际应用](http://blog.csdn.net/u014360817/article/details/52618115)
* [ReactNative 架构Redux研究](http://www.jianshu.com/p/14933fd9c312)
* [ReactNative+Redux Demo](https://github.com/alinz/example-react-native-redux)

###App使用Redux流程
* 第一步:程序入口增加Provide组件和全局Store对象,查看Root.js
    ```
    import { Provider } from 'react-redux';
    import configureStore from './redux/Store'
    const store = configureStore({});//参数为全局初始化对象,一般不用设置

    class Compontent{
        render(){
           return(
              <Provider store={store}>
                     //导航组件或其它组件
              </Provider>
           )
        }
    }
    ```
* 第二步:定义增加的事件,编辑ActionTypeConf.js
    ```
    //购物车
    export const SHOP_CART_LOAD_FROM_SERVICE="SHOP_CART_LOAD_FROM_SERVICE";//网络获取购物车数据
    export const SHOP_CART_LOAD_FROM_LOCAL="SHOP_CART_LOAD_FROM_LOCAL";//读取本地购物车数据
    export const SHOP_CART_ITEM_ADD="SHOP_CART_ITEM_ADD";//增加数据到购物车
    export const SHOP_CART_ITEM_DEL="SHOP_CART_ITEM_DEL";//删除购物车数据
    export const SHOP_CART_ITEM_QUANTITY="SHOP_CART_ITEM_QUANTITY";//购物车商品数量更新刷
    export const SHOP_CART_ITEM_CHECKED="SHOP_CART_ITEM_CHECKED";//选择购物车商品
    export const SHOP_CART_MERCHANT_CHECKED="SHOP_CART_MERCHANT_CHECKED";//商店选择
    
    ```
    1. 命名全部以大写字母.
    2. 事件命名规范 全局对像+事件名.最后增加注释,例如:全局对象shopCart,事件就命名为SHOP_CART+事件
    3. 增加事件注释
* 第三步:创建对应Action,在actions目录下创建,例如:ShopCartAction.js
    ```
    import * as types from '../ActionTypeConf';//引用Action
    
    export function loadShopCartListFromService(showLoading) {
        return dispatch => {
    
            Api.shopCartList(showLoading).then((result) => {
                if (result && result.data && result.data) {
                    dispatch({
                        type: types.SHOP_CART_LOAD_FROM_SERVICE,
                        list: result.data.list
                    });
                }
            });
        };
    }
    
    export function loadShopCartListFromLocal() {
        return dispatch => {
            Api.getLocalShopCart().then((result) => {
                dispatch({
                    type: types.SHOP_CART_LOAD_FROM_LOCAL,
                    list: result
                });
            });
        };
    }
    ```
    1. 因为使用了redux-thunk,所以这个文件可以处理异步数据,然后再发送Action给Reducer
    2. 方法都以驼峰式命名
    3. 注意dispatch发送的`type`是否正确
* 第四步:创建对应Reducer文件,在reducers目录下创建,例如ShopCart.js
    ```
    import * as types from "../ActionTypeConf";
    const initialState = {
        shopCartList: []
    };
    export default (state = initialState, action) => {
        //注意不用对原来的state直接赋值,不然会出现,
          使用组件绑定最新属性和之前属性一性,导致无法刷新UI
        let oldArray = Array.from(state.shopCartList);
        // let oldArray = state.shopCartList;
        switch (action.type) {
            case  types.SHOP_CART_LOAD_FROM_LOCAL :
                let result = state;
                if (action.list) {//Action带过来的参数
                    result = Object.assign({}, {shopCartList: action.list});
                }
                return result;
            case types.SHOP_CART_LOAD_FROM_SERVICE:
                return Object.assign({}, {shopCartList: action.list});
            default:
                return state;
        }
    
    }
    ```
    1. reducer初始状态可以在state引用一个默认对象,处理非空对象和保持数据契约
    2. 参数state对象不可以直接做赋值处理
    3. 返回的对象会刷新原来对象,所以要注意好保存数据契约的定义
    4. Reducer不做异步处理,但可以做数据处理
* 第五步:合并reducer,编辑reducers目录下的index.js
    ```
    import {combineReducers} from "redux";
    import user from "./User";
    import address from "./Address";
    import shopCart from "./ShopCart";
    
    //全局状态树根节点key定义
    const rootReducer = combineReducers({
        user,address,shopCart,
    });
    
    export default rootReducer;
    ```
    1. combineReducers()引用的对象对为全局状态树下的对象名
    2. 这个文件是创建全局Store对象引用的
* 第六步:组件绑定全局状态对象和Action函数绑定
    ```
    import {bindActionCreators} from 'redux';
    import {connect} from 'react-redux';
    import * as ShopCartAction from '../../redux/actions/ShopCartAction';
    
    class ShopCart extends ReactNavComponent {
        render(){
            return ()
        }
    }
    
    //绑定状态，state  变成this.props
    function mapStateToProps(state, ownProps) {
        Log("shopcartlist", state.shopCart.shopCartList.length);
        return {
            shopCartList: state.shopCart.shopCartList
        };
    }
    
    //绑定actions
    function mapDispatchToProps(dispatch) {
        return {
            shopCartAction: bindActionCreators(ShopCartAction, dispatch)
        };
    }
    //react组件连接redux
    export default connect(
        mapStateToProps,
        mapDispatchToProps
    )(ShopCart)
    ```
    1. 如果只要读取全局状态就不用绑定Action
    2. 要读取多个全局状态和Action,就在绑定状态定义和引用模块
    3. mapStateToProps(state, ownProps)第一个参数是全局状态树对象,绑定对象到组件属性要注意引用好对象
    4. 使用全局状态对象通过this.props.`绑定属性名字`,例如:this.props.shopCartList 
    5. 发送Action通知状态更新,例如:this.props.`shopCartAction`.loadShopCartListFromLocal()

### Store 初始化reducer对象
在创建Store对象时可以用手动发送Action,触发加载本地缓存数据

```
import {DeviceEventEmitter} from 'react-native';
import {createStore, applyMiddleware} from 'redux';
import thunk from 'redux-thunk';
import rootReducer from './reducers/index';
import * as types from './ActionTypeConf';

export default function configureStore(initialState) {

    const store = createStore(
        rootReducer,
        initialState,
        applyMiddleware(thunk), //applyMiddleware方法中传入需要使用的中间件
    );
    //初始化
    store.dispatch({type: types.ADDRESS_INIT});
    store.dispatch({type: types.SHOP_CART_LOAD_FROM_LOCAL});
    store.subscribe(() => {
        //监听全局状态变化
        // console.log("state change");
        DeviceEventEmitter.emit(Config.EVENT_GLOBAL_STATE_CHANGE, {})
    });
    return store;
}

```
FlatList有个问题就是用Redux数据,数据刷新后,组件绑定的属性已更新,但还要手动设置this.setState(),不然不刷新数据,
所以在组件`componentWillMount`,`componentWillUnmount`方法增加/移除事件监听
```
componentWillMount() {
    this.globalStateChangeListener = DeviceEventEmitter.addListener(Config.EVENT_GLOBAL_STATE_CHANGE,()=>{
        this.setState({});
    });
}

componentWillUnmount() {
    this.globalStateChangeListener.remove();
}

```
### 防止Redux重复刷新页面
可以重写`shouldComponentUpdate`方法判断新的属性和旧的属性是否有变化再刷新UI

```
shouldComponentUpdate(nextProps, nextState) {
    return this.props.addressList !== nextProps.addressList;

}
```

    
    
   