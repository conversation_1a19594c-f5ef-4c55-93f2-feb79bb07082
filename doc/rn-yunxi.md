# 公用库目录图
| 作者 | 版本 | 日期 |
|:----:|:----:|:----:|
| 劳健锋 | v1 | 2017/05/30 |
```
│  CommonSearchBar.js 引用入口
│  package.json 引用模块声明
└─lib
    ├─native
    │  ├─aliyun-oss oss上传
    │  ├─image-crop-picker 图片选择
    │  ├─loading 全局loading
    │  ├─location 高等定位 
    │  ├─qr-code 二维码
    │  └─wechat 微信
    ├─template
    │      ReactNavComponent.js ReactNavigation通用类
    ├─util
    │      EncryptUtil.js 加密工具
    │      NetUtil.js 网络请求工具
    │      StorageUtil.js 缓存工具
    │      StringUtil.js 字符串工具
    │      
    └─widget
        ├─button 通用按钮
        ├─checkbox 复择框 
        ├─common-flatlist 通用flatlist
        ├─common-listview 通用Listview
        ├─list-item 二次封装antd-mobile列表组件
        ├─photo-view 图片浏览
        ├─popup 二次封装antd-mobile Popup
        ├─stepper 步进器
        ├─text 全局Text组件
        └─webview 网页组件

```
## aliyun-oss 使用
>可能参考 [react-native-aliyun-oss](https://github.com/SpadeGod/react-native-aliyun-oss) ,此组件基于这个修改

### 使用方法
```
import {AliYunOSS} from 'rn-yunxi';

const config = {
  AccessKey: '',  // your accessKeyId
  SecretKey: '', // your accessKeySecret
  SecretToken: '', // your securityToken
};

const endPoint = ''; // your endPoint

// 初始化阿里云组件
AliyunOSS.initWithKey(config, endPoint);

// upload config
const uploadConfig = {
  bucketName: '',  //your bucketName
  sourceFile: '', // local file path
  ossFile: '' // the file path uploaded to oss
};

// 上传进度
const uploadProgress = p => console.log(p.currentSize / p.totalSize);

// 增加上传事件监听
AliyunOSS.addEventListener('uploadProgress', uploadProgress);

// 执行上传
AliyunOSS.uploadObjectAsync(uploadConfig).then((resp) => {
  // 去除事件监听
  AliyunOSS.removeEventListener('uploadProgress', uploadProgress);
  // 此处可以执行回调
  ... 
}).catch((err)=>{
  console.log(err);
  // 执行失败回调
});

```

## ImageCropPicker使用
>可能参考 [react-native-image-crop-picker](https://github.com/ivpusic/react-native-image-crop-picker) ,此组件基于这个修改

### 使用方法
```
import {ImageCropPicker} from 'rn-yunxi';

//选择图片
ImagePicker.openPicker({
  width: 300,
  height: 400,
  cropping: true//选择后是否裁剪图片
}).then(image => {
  console.log(image);
});

//拍照
ImagePicker.openCamera({
  width: 300,
  height: 400,
  cropping: true
}).then(image => {
  console.log(image);
});

```
## 全局Loading
>原生参考[android第三方库](https://github.com/Kaopiz/KProgressHUD),[IOS第三方库](https://github.com/jdg/MBProgressHUD)

### 使用方法
```
import {GlobalLoading} from 'rn-yunxi';

GlobalLoading.show();//显示
GlobalLoading.hide();//隐藏
```

## Location定位
>使用高德定位,使用要去申请key,并配置到项目里,[Android参考](http://lbs.amap.com/api/android-sdk/guide/create-project/dev-attention)
[IOS参考](http://lbs.amap.com/api/ios-sdk/guide/create-project/note)
* IOS key配置文件在PrefixHeader.pch
* Android key配置在AndroidManifest.xml

### 使用方法
```
import {Location} from 'rn-yunxi';
Location.ObtainLocation()
.then((data)=>{
    console.log(data)
}).catch((error)=>{
    // alert(error)
})

```

## QRCode 组件
> 扫二维码组件跳转到原生页面扫描[Android第三方库](https://github.com/bingoogolapple/BGAQRCode-Android),[IOS第三方库](https://github.com/kingsic/SGQRCode)
### 使用方法
```
QRCode.ScanQRCode()
.then((data)=>{
    Log(data)
}).catch((err)=>{
    Log(err)
})

```
## 微信组件
> 参考和注意事项[react-native-wechat](https://github.com/weflex/react-native-wechat)
### 使用方法
```
import {WeChat} from 'rn-yunxi';
//初始化
componentDidMount (){
  WeChat.registerApp('your appid')
}
//分享
WeChat.shareToSession({
    'title': "AAA",
    'description': "description",
    'thumbImage': "https://img6.bdstatic.com/img/image/smallpic/chongwutupianxiaotuxiaom.jpg",
    'type': "news",
    'webpageUrl': "www.baidu.com"
}).then((data)=>{
    Log(data);
    }).catch((err)=>{
    Log(err);
})

```
## 网络组件
>使用第三方fetch处理Timeout事件 [react-native-fetch-polyfill](https://github.com/robinpowered/react-native-fetch-polyfill)
### 使用方法
```
import {Util} from 'rn-yunxi';
const NetUtil = Util.NetUtil;
//统一处理接口数据是否有效
NetUtil.checkApiError = function checkApiError(result, url) {
    // Log('checkApiError',result.resultCode);
    if (result && result.resultCode == 0) {
        return false
    } else {
        alert(result.resultCode+":"+result.resultMsg+":" + url, "服务器出错");
        return true;
    }
};
//统一处理http错误码问题
NetUtil.handleHttpStatusError = function handleHttpStatusError(err) {
    alert(err);
};
//统一处理401授权问题
NetUtil.handleAuthError = function handleAuthError(err) {
    alert(err.resultMsg)
};
//统一处理http网络错误
NetUtil.handleNetError = function handleNetError(err) {
    Log(err);
    alert(err)
};
//Get请求
NetUtil.get(url, params, headers);
//Post请求
NetUtil.post(url, params, headers);
//Post Json请求
NetUtil.postJson(url, params, headers);
//Put请求
NetUtil.put(url, params, headers);
//Delete请求
NetUtil.delete(url, params, headers);

```

## StorageUtil缓存组件
>通过key,缓存数据
### 使用方法
```
import {Util} from 'rn-yunxi';
const StorageUtil = Util.StorageUtil;
//保存一个字符串
StorageUtil.saveString(key,value);
//通过key获取保存的字符串
StorageUtil.getString(key,defalutValue);
//保存一个Json对象
StorageUtil.saveJsonObject(key,json);
//通过key获取一个Json对象
StorageUtil.getJsonObject(key,defaultObject);
```
## UI组件
>UI基础组件或二次封装组件
### 使用方法
```
import {Widget} from 'rn-yunxi';
const Text=Widget.Text;

class View extends React.Compontent{
    render(){
        return (
            <Text>hello world</Text>
        )
    }
}
```