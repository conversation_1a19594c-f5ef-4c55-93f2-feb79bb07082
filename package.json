{"name": "o2oApp", "version": "0.0.1", "private": true, "scripts": {"start": "node node_modules/react-native/local-cli/cli.js start", "theme": "node scripts/custom-andt-theme", "test": "jest"}, "dependencies": {"antd-mobile": "2.0.3", "autobind-decorator": "2.4.0", "buffer": "5.1.0", "events": "1.1.1", "lodash": "4.17.15", "mobx": "3.6.2", "mobx-react": "4.4.3", "querystring": "0.2.0", "react": "16.0.0-alpha.12", "react-native": "file:../react-native-0.48.4", "react-native-actionsheet": "2.4.2", "react-native-animatable": "1.3.3", "react-native-code-push": "5.3.5", "react-native-drawer": "2.5.1", "react-native-fetch-polyfill": "1.1.3", "react-native-image-zoom-viewer": "2.2.27", "react-native-keyboard-aware-scroll-view": "0.2.9", "react-native-looped-carousel": "0.1.13", "react-native-qrcode-svg": "5.0.6", "react-native-root-toast": "1.1.2", "react-native-scrollable-tab-view": "0.7.4", "react-native-svg": "file:./node_modules/react-native-svg", "react-navigation": "1.5.11", "rn-yunxi": "file:../rn-yunxi", "vdjs": "1.0.0"}, "devDependencies": {"babel-plugin-import": "1.13.0", "babel-plugin-transform-decorators-legacy": "1.3.5", "babel-jest": "21.2.0", "babel-preset-react-native": "4.0.0", "jest": "21.2.1", "react-test-renderer": "16.0.0-alpha.12"}, "jest": {"preset": "react-native", "transformIgnorePatterns": ["node_modules/(?!(jest-)?react-native|react-navigation)"]}}